module.exports = {
    publicPath: "/commodities",
    outputDir: "dist",
    assetsDir: "assts",
    productionSourceMap: false,
    devServer: {
        port: 1024, // 1024
        host: "0.0.0.0",
        open: true,
        overlay: {
            warnings: true,
            errors: true,
        },
        proxy: {
            "/api/embedding/v3/completion/copywriting_generation" : {
                target : "https://chatgpt.vinehoo.com/emb",
                pathRewrite : {
                    "^/api" : ""
                }
            },
            // "/aaa": {
            //     target: "http://*************:8889", //http://api-gateway.wineyun.com
            //     changeOrigin: true,
            //     pathRewrite: {
            //         "^/aaa": "",
            //     },
            // },
            "/api": {
                target: "http://api-gateway.wineyun.com", //http://api-gateway.wineyun.com
                // target: "https://api-gateway.vinehoo.com",
                changeOrigin: true,
                pathRewrite: {
                    "^/api": "",
                },
            },
        },
    },
};
