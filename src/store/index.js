import Vue from "vue";
import Vuex from "vuex";
import approvalFlowApi from "@/services/approvalFlow";

Vue.use(Vuex);
const state = {
    routesList: [],
    roleList: [],
    adminList: [],
    copyWriterOptions: [],
    purchaseOptions: [],
    designOptions: [],
};

const getters = {
    routesList: (state) => state.routesList,
};
const mutations = {
    setRoutesList(state, value) {
        state.routesList = value;
    },
    SET_ROLE_LIST(state, list) {
        state.roleList = list;
    },
    SET_ADMIN_LIST(state, list) {
        state.adminList = list;
    },
    SET_COPY_WRITER_OPTIONS(state, options) {
        state.copyWriterOptions = options;
    },
    SET_PURCHASE_OPTIONS(state, options) {
        state.purchaseOptions = options;
    },
    SET_DESIGN_OPTIONS(state, options) {
        state.designOptions = options;
    },
};
const actions = {
    setRoutesList: (event, value) => {
        event.commit("setRoutesList", value);
    },
    getRoleList({ commit, state }) {
        if (state.roleList.length) return;
        approvalFlowApi.getRoleList().then((res) => {
            if (res.data.error_code == 0) {
                commit("SET_ROLE_LIST", res.data.data.list);
            }
        });
    },
    getAdminList({ commit, state }) {
        if (state.adminList.length) return;
        approvalFlowApi.getAdminList().then((res) => {
            if (res.data.error_code == 0) {
                commit("SET_ADMIN_LIST", res.data.data.list);
            }
        });
    },
    initCopyWriterOptions({ commit, state }) {
        if (state.copyWriterOptions.length) return;
        Vue.prototype.$request.article
            .purchaseList({
                type: 3,
            })
            .then((res) => {
                if (res.data.error_code === 0) {
                    const { list = [] } = res.data.data;
                    const options = list.map(({ id, realname }) => ({
                        value: id,
                        label: realname,
                    }));
                    commit("SET_COPY_WRITER_OPTIONS", options);
                }
            });
    },
    initPurchaseOptions({ commit, state }) {
        if (state.purchaseOptions.length) return;
        Vue.prototype.$request.article
            .purchaseList({
                type: 3,
            })
            .then((res) => {
                if (res.data.error_code === 0) {
                    const { list = [] } = res.data.data;
                    const options = list.map(({ id, realname }) => ({
                        value: id,
                        label: realname,
                    }));
                    commit("SET_PURCHASE_OPTIONS", options);
                }
            });
    },
    initDesignOptions({ commit, state }) {
        if (state.designOptions.length) return;
        Vue.prototype.$request.article
            .purchaseList({
                type: 67,
            })
            .then((res) => {
                if (res.data.error_code === 0) {
                    const { list = [] } = res.data.data;
                    const options = list.map(({ id, realname }) => ({
                        value: id,
                        label: realname,
                    }));
                    commit("SET_DESIGN_OPTIONS", options);
                }
            });
    },
};

const store = new Vuex.Store({
    state,
    getters,
    mutations,
    actions,
});
export default store;
