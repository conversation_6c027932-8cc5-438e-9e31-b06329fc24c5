.btn_visib_wrap {
     position: absolute;
     background-color: #FFFFFF;
     box-shadow: 0 1px 3px rgb(0 0 0 / 30%);
     z-index: 10;
     margin-top: 10px;
     animation: btnwrap .5s;
 }

 .btn_visib_wrap::before {
     content: "";
     position: absolute;
     left: 20px;
     top: -6px;
     width: 10px;
     height: 10px;
     transform: rotate(45deg);
     background: #fff;
     z-index: 0;
     border-top: 2px solid #eee;
     border-left: 2px solid #eee;
 }

 .btn_visib_wrap .btn_close {
     cursor: pointer;
     display: flex;
 }

 .btn_visib_title {
     display: flex;
     justify-content: space-between;
     padding: 5px 10px 5px 8px;
 }

 .btn_visib_content {
     padding: 10px 10px 10px 8px;
     position: relative;
 }

 @keyframes btnwrap {
     0% {
         opacity: 0;
     }

     20% {
         opacity: 0.2;
     }

     50% {
         opacity: 0.5;
     }

     70% {
         opacity: 0.7;
     }

     100% {
         opacity: 1;
     }
 }
 
 .code_wrap {
     padding: 0px 10px;
     margin-bottom: 5px;
     cursor: pointer;
     min-width: 189px;
     height: 20px;
     line-height: 20px;
	 white-space: nowrap;
 }
 
 .code_wrap:hover {
     color: #007AFF;
 }
 
 .code_content {
     max-height: 140px;
     overflow-y: auto;
     margin-top: 10px;
 }
 
 /*滚动条样式*/
 .code_content::-webkit-scrollbar {
     width: 4px;
     /*height: 4px;*/
 }
 
 .code_content::-webkit-scrollbar-thumb {
     border-radius: 10px;
     -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.2);
     background: rgba(0, 0, 0, 0.2);
 }
 
 .code_content::-webkit-scrollbar-track {
     -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.2);
     border-radius: 0;
     background: rgba(0, 0, 0, 0.1);
 
 }
 
 .code_close {
     position: absolute;
     top: 1px;
     right: 4px;
     display: flex;
     cursor: pointer;
 }
 .el-card{
     overflow: inherit;
 }
 .box_flex{
     display: flex;
 }
 .box_wrap{
     flex-wrap: wrap;
 }
