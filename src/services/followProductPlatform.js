import axios from "axios";

const followProductPlatformApi = {};

followProductPlatformApi.searchFollowList = (params) => {
    return axios({
        url: "/api/commodities/v3/productSaleFollow/list",
        params,
    });
};

followProductPlatformApi.searchInfoByShortCode = (params) => {
    return axios({
        url: "/api/commodities/v3/productSaleFollow/shortCodeSearch",
        params,
    });
};

followProductPlatformApi.addFollowItem = (data) => {
    return axios({
        url: "/api/commodities/v3/productSaleFollow/add",
        method: "POST",
        data,
    });
};

followProductPlatformApi.removeFollowItem = (data) => {
    return axios({
        url: "/api/commodities/v3/productSaleFollow/remove",
        method: "POST",
        data,
    });
};

followProductPlatformApi.getRemarkList = (params) => {
    return axios({
        url: "/api/commodities/v3/productSaleFollow/remarkList",
        params,
    });
};

followProductPlatformApi.addRemark = (data) => {
    return axios({
        url: "/api/commodities/v3/productSaleFollow/addRemark",
        method: "POST",
        data,
    });
};

followProductPlatformApi.importFollowList = (data) => {
    return axios({
        url: "/api/commodities/v3/productSaleFollow/import",
        method: "POST",
        data,
    });
};

followProductPlatformApi.exportSalesDashboardFrom = (params) => {
    return axios({
        url: "/api/commodities/v3/productSaleFollow/export",
        params,
        responseType: "blob",
    });
};

export default followProductPlatformApi;
