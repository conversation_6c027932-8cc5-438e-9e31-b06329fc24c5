import axios from "axios";

function getPurchaseList(data) {
    // 获取采购列表
    return axios({
        url: "/api/purchase/v3/period/list",
        method: "get",
        params: data,
    });
}

const purchaseListSalesStatistics = (params) => {
    // 采购列表销量统计
    return axios({
        url: "/api/purchase/v3/period/list_sales_statistics",
        method: "get",
        params,
    });
};

function getStockList(data) {
    // 获取采购库存
    return axios({
        url: "/api/purchase/v3/product/inventory",
        method: "get",
        params: data,
    });
}
function getSetTarget(data) {
    // 获取采购目标列表
    return axios({
        url: "/api/purchase/v3/sale/target/list",
        method: "get",
        params: data,
    });
}
function saleGroupConfigList(data) {
    // 获取采购人员分组列表
    return axios({
        url: "/api/authority/v3/admin/specifyList",
        method: "get",
        params: data,
    });
}

function addTarget(data) {
    // 设置采购目标
    return axios({
        url: "/api/purchase/v3/sale/target/create",
        method: "post",
        data,
    });
}
// function getStatistical(data) {
//     // 获取采购统计
//     return axios({
//         url: "/api/purchase/v3/purchase/statistics",
//         method: "get",
//         params: data,
//     });
// }
function getStatistical(data) {
    // 获取采购统计
    return axios({
        url: "/api/purchase/v3/sale/target/statistics",
        method: "get",
        params: data,
    });
}
function getTplusOrder(data) {
    // t+ 采购单列表
    return axios({
        url: "/api/purchase/v3/tplus/purchaseorder",
        method: "get",
        params: data,
    });
}
function pushOrder(data) {
    // 手动推送仓库
    return axios({
        url: "/api/purchase/v3/push/purchaseorder",
        method: "post",
        data,
    });
}
function createRealWarehouse(data) {
    // 添加实体仓库
    return axios({
        url: "/api/commodities/v3/warehouse/createPhysicalWarehouse",
        method: "post",
        data,
    });
}
function updateRealWarehouse(data) {
    // 更新实体仓库
    return axios({
        url: "/api/commodities/v3/warehouse/upPhysicalWarehouse",
        method: "post",
        data,
    });
}
function getRealWarehouseList(data) {
    // 实体仓库列表
    return axios({
        url: "/api/commodities/v3/warehouse/getPhysicalWarehouseList",
        method: "get",
        params: data,
    });
}
function getWarehouseList(data) {
    // 虚拟仓列表
    return axios({
        url: "/api/commodities/v3/warehouse/getVirtualWarehouseList",
        method: "get",
        params: data,
    });
}
function getWithoutWarehouseList(data) {
    // 通过实体仓获取虚拟仓
    return axios({
        url: "/api/pushorders/v3/warehouse/info",
        method: "get",
        params: data,
    });
}
function createVirtualWarehouse(data) {
    // 添加虚拟仓
    return axios({
        url: "/api/commodities/v3/warehouse/createVirtualWarehouse",
        method: "post",
        data,
    });
}
function updateVirtualWarehouse(data) {
    // 更新虚拟仓
    return axios({
        url: "/api/commodities/v3/warehouse/upVirtualWarehouse",
        method: "post",
        data,
    });
}
function getDetail(data) {
    // 更新虚拟仓
    return axios({
        url: "/api/pushtplus/v3/purchaseOrder/detail",
        method: "get",
        params: data,
    });
}
function updatePeriodsProductInventory(data) {
    // 修改成本
    return axios({
        url: "/api/commodities/v3/other/updatePeriodsProductInventory",
        method: "post",
        data,
    });
}
function getProductListForKeywords(data) {
    // 关键字获取产品关键字
    return axios({
        url: "/api/wiki/v3/producttype/keywordquery",
        method: "get",
        params: data,
    });
}
function getLastVersionGoodsInfo(data) {
    // 获取v2数据商品
    return axios({
        url: "/api/commodities/v3/other/getV2GoodsBase",
        method: "get",
        params: data,
    });
}
function purchasesalessava(data) {
    // 修改分组销售人员
    return axios({
        url: "/api/purchase/v3/sale/updateAdminLabel",
        method: "post",
        data,
    });
}
function updateAdminLabel(data) {
    // 修改分组销售人员
    return axios({
        url: "/api/authority/v3/admin/UpdateAdminLabel",
        method: "post",
        data,
    });
}
function exportPurchaseSaleStats(data) {
    return axios({
        url: "/api/purchase/v3/saleStatics/export",
        method: "post",
        data,
    });
}

function deletePackage(data) {
    return axios({
        url: "/api/commodities/v3/package/del",
        method: "post",
        data,
    });
}
function batchOnSaleChannel(data) {
    return axios({
        url: "/api/commodities/v3/leftover/batchOnSaleChannel",
        method: "post",
        data,
    });
}
function getPaymentList(data) {
    return axios({
        url: "/api/erp/v3/arap/fk/list",
        method: "get",
        params: data,
    });
}
function getPrintInfo(data) {
    return axios({
        url: "/api/erp/v3/arap/fk/getPrintInfo",
        method: "get",
        params: data,
    });
}
function getPrintCount(data) {
    return axios({
        url: "/api/erp/v3/arap/fk/print",
        method: "get",
        params: data,
    });
}
function getProductsByPage(data) {
    return axios({
        url: "/api/commodities/v3/evSort/getProductsByPage",
        method: "get",
        params: data,
    });
}
function getPeriodsList(data) {
    return axios({
        url: "/api/commodities/v3/periods/periodsList",
        method: "get",
        params: data,
    });
}
function getPeriodsSortList(data) {
    return axios({
        url: "/api/commodities/v3/evSort/sortList",
        method: "get",
        params: data,
    });
}

function getSortConfig() {
    return axios({
        url: "/api/commodities/v3/evSort/config",
        method: "get",
    });
}

function getUserLimit() {
    return axios({
        url: "/api/commodities/v3/evSort/getUserLimit",
        method: "get",
    });
}

function addSort(data) {
    return axios({
        url: "/api/commodities/v3/evSort/addSort",
        method: "post",
        data,
    });
}

function cancelSort(data) {
    return axios({
        url: "/api/commodities/v3/evSort/cancelSort",
        method: "post",
        data,
    });
}

export default {
    getPaymentList,
    getPeriodsList,
    batchOnSaleChannel,
    getProductsByPage,
    getPrintInfo,
    getPeriodsSortList,
    getSortConfig,
    getUserLimit,
    addSort,
    cancelSort,
    updateRealWarehouse,
    getPrintCount,
    getProductListForKeywords,
    getWarehouseList,
    updatePeriodsProductInventory,
    createVirtualWarehouse,
    deletePackage,
    getPurchaseList,
    purchaseListSalesStatistics,
    getRealWarehouseList,
    getDetail,
    getWithoutWarehouseList,
    addTarget,
    updateVirtualWarehouse,
    saleGroupConfigList,
    pushOrder,
    createRealWarehouse,
    getStatistical,
    getTplusOrder,
    getSetTarget,
    getStockList,
    getLastVersionGoodsInfo,
    purchasesalessava,
    exportPurchaseSaleStats,
    updateAdminLabel
};
