import axios from "axios";

//酒庄列表
const getWineryList = (params) => {
    return axios({
        url: "/api/wiki/v3/winery",
        method: "get",
        params,
    });
};
function getTailInventoryList(params) {
    // 尾货库存
    return axios({
        url: "/api/commodities_server/v3/data_analysis/GetTailInventory",
        method: "get",
        params,
    });
}
function getGoodsparameters(params) {
    // 尾货库存
    return axios({
        url: "/api/commodities/v3/parameters/list",
        method: "get",
        params,
    });
}
//更新附件
function updateGoodsParameters(data) {
    return axios({
        url: "/api/commodities/v3/parameters/save",
        method: "post",
        data,
    });
}

function getOperateRelatedList(params) {
    // 
    return axios({
        url: "/api/commodities/v3/operateRelated/list",
        method: "get",
        params,
    });
}
function addOperateRelated(data) {
    return axios({
        url: "/api/commodities/v3/operateRelated/add",
        method: "post",
        data,
    });
}
function deleteOperateRelated(data) {
    return axios({
        url: "/api/commodities/v3/operateRelated/del",
        method: "post",
        data,
    });
}
function addBuyerRelated(data) {
    return axios({
        url: "/api/commodities/v3/buyerRelated/add",
        method: "post",
        data,
    });
}
function delBuyerRelatedLog(data) {
    return axios({
        url: "/api/commodities/v3/buyerRelated/del",
        method: "post",
        data,
    });
}
function OneFlowerOneWorldKanban(params) {
    return axios({
        url: "/api/commodities_server/v3/data_analysis/OneFlowerOneWorldKanban",
        method: "get",
        params,
    });
}
function ExportOneFlowerOneWorldKanban(params) {
    return axios({
        url: "/api/commodities_server/v3/data_analysis/ExportOneFlowerOneWorldKanban",
        method: "get",
        params,
    });
}

function shortCodeLastSaleDate(data) {
    return axios({
        url: "/api/orders_server/v3/orders/query/short_code_last_sale_date",
        method: "post",
        data
    });
}
export default {
    getWineryList,
    getTailInventoryList,
    getGoodsparameters,
    updateGoodsParameters,
    getOperateRelatedList,
    addOperateRelated,
    deleteOperateRelated,
    addBuyerRelated,
    delBuyerRelatedLog,
    OneFlowerOneWorldKanban,
    ExportOneFlowerOneWorldKanban,
    shortCodeLastSaleDate
};
