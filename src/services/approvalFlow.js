import axios from "axios";

const approvalFlowApi = {};

approvalFlowApi.getAdminList = () => {
    return axios({
        url: "/api/authority/v3/admin/getAlladmins",
    });
};

approvalFlowApi.getRoleList = () => {
    return axios({
        url: "/api/authority/v3/admin/getAllRoles",
    });
};

approvalFlowApi.createFlow = (data) => {
    return axios({
        url: "/api/go-flow/v3/flow/create",
        data,
        method: "POST",
    });
};

approvalFlowApi.updateFlow = (data) => {
    return axios({
        url: "/api/go-flow/v3/flow/update",
        data,
        method: "POST",
    });
};

approvalFlowApi.searchFlowList = (params) => {
    return axios({
        url: "/api/go-flow/v3/flow/list",
        params,
    });
};

export default approvalFlowApi;
