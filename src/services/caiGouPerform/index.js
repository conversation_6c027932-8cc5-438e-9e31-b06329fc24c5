import axios from "axios";

//采购执行看板列表
function caiGouPerformList(data) {
    return axios({
        url: "/api/commodities/v3/purchase/getPeriodsList",
        method: "get",
        params: data,
    });
}
function caiGouPerformpPyeeMerchantList(data) {
    return axios({
        url: "/api/commodities/v3/purchase/payeeMerchantList",
        method: "get",
        params: data,
    });
}
//存订货量列表
function getInventoryOrderList(data) {
    return axios({
        url: "/api/commodities/v3/purchase/getInventoryOrderList",
        method: "get",
        params: data,
    });
}
//更新订货量
function updateInventoryOrder(data) {
    return axios({
        url: "/api/commodities/v3/purchase/updateInventoryOrder",
        method: "post",
        data,
    });
}
//采购单列表
function getPurchaseOrdernoList(data) {
    return axios({
        url: "/api/commodities/v3/purchase/getPurchaseOrdernoList",
        method: "get",
        params: data,
    });
}
//添加采购单
function addPurchaseOrderno(data) {
    return axios({
        url: "/api/commodities/v3/purchase/addPurchaseOrderno",
        method: "post",
        data,
    });
}
//移除采购记录
function delRecord(data) {
    return axios({
        url: "/api/commodities/v3/purchase/delRecord",
        method: "post",
        data,
    });
}
//删除采购单
function delPurchaseOrderno(data) {
    return axios({
        url: "/api/commodities/v3/purchase/delPurchaseOrderno",
        method: "post",
        data,
    });
}
//更新预计采购时间
function updateEstimatePurchase(data) {
    return axios({
        url: "/api/commodities/v3/purchase/updateEstimatePurchase",
        method: "post",
        data,
    });
}

function getHistoryCostprice(params) {
    return axios({
        url: "/api/commodities/v3/purchase/getProductHistoricalCostprice",
        params,
    });
}

function addPurchaseRecord(data) {
    return axios({
        url: "/api/commodities/v3/purchase/addRecord",
        method: "post",
        data,
    });
}

function getWmsStockByPeriod(params) {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/goodsGetFictitiousCount",
        params,
    });
}

function getPoNo() {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/getPoNo",
    });
}

function createPurchaseOrder(data) {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/add",
        method: "POST",
        data,
    });
}

function getSettlementMethodList() {
    return axios({
        url: "/api/prepared/v3/erp/settlementMethod",
    });
}

function getWarehouseList() {
    return axios({
        url: "/api/prepared/v3/prepareds/warehouseUseOptions",
    });
}

function getDepartmentList(params) {
    return axios({
        url: "/api/supplychain/v3/department/list",
        params,
    });
}

function getEmployeesList(params) {
    return axios({
        url: "/api/supplychain/v3/staff/list",
        params,
    });
}

function getPartnerentityList(params) {
    return axios({
        url: "/api/supplychain/v3/partnerentity/alllist",
        params,
    });
}

function queryPurchaseOrderApproveList(params) {
    return axios({
        url: "/api/erp/v3/purchaseOrder/queryApproveInfo",
        params,
    });
}

//更新附件
function updateAnnex(data) {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/updateAnnex",
        method: "post",
        data,
    });
}

//删除商品产品库存
function deletePeriodsProductInventory(data) {
    return axios({
        url: "/api/commodities/v3/periods/deletePeriodsProductInventory",
        method: "post",
        data,
    });
}

function getHistoryPrice(params) {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/getHistoryPrice",
        params,
    });
}
function getNewProductDetails(data) {
    // 查询产品详情
    return axios({
        url: "/api/orders/v3/offline/productDetail",
        method: "get",
        params: data
    });
}
function getCorpMappingList(data) {
    return axios({
        url: "/api/erp/v3/saleOrder/corpMappingList",
        method: "get",
        params: data,
    });
}
function pushPurchaseOrderToWMS(data) {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/pushPurchaseOrderToWMS",
        method: "post",
        data,
    });
}
function getPaymentInfo(data) {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/getPaymentInfo",
        method: "get",
        params: data,
    });
}
function sendFkApproval(data) {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/sendFkApproval",
        method: "post",
        data,
    });
}

export default {
    caiGouPerformList,
    getInventoryOrderList,
    updateInventoryOrder,
    getPurchaseOrdernoList,
    addPurchaseOrderno,
    delRecord,
    delPurchaseOrderno,
    updateEstimatePurchase,
    getHistoryCostprice,
    addPurchaseRecord,
    getWmsStockByPeriod,
    getPoNo,
    createPurchaseOrder,
    getSettlementMethodList,
    getWarehouseList,
    getDepartmentList,
    getEmployeesList,
    getPartnerentityList,
    queryPurchaseOrderApproveList,
    caiGouPerformpPyeeMerchantList,
    updateAnnex,
    deletePeriodsProductInventory,
    getHistoryPrice,
    getNewProductDetails,
    getCorpMappingList,
    pushPurchaseOrderToWMS,
    getPaymentInfo,
    sendFkApproval
};
