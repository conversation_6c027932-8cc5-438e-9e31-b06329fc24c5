import axios from "axios";

const liveApi = {};

liveApi.getLiveList = () => {
    return axios({
        url: "/api/go-minilive/v3/room/list",
    });
};

liveApi.getGoodsList = (data) => {
    return axios({
        url: "/api/go-minilive/v3/room/goodsList",
        method: "POST",
        data,
        isCloseLoading: true,
    });
};

liveApi.batchPutawayGoods = (data) => {
    return axios({
        url: "/api/go-minilive/v3/room/batchOnsale",
        method: "POST",
        data,
    });
};

liveApi.updateGoodsRemark = (data) => {
    return axios({
        url: "/api/go-minilive/v3/room/updateRemarks",
        method: "POST",
        data,
    });
};

liveApi.updateGoodsStatus = (data) => {
    return axios({
        url: "/api/go-minilive/v3/room/onsale",
        method: "POST",
        data,
    });
};

liveApi.updateGoodsSort = (data) => {
    return axios({
        url: "/api/go-minilive/v3/room/goodsSort",
        method: "POST",
        data,
    });
};

liveApi.getSkuList = (params) => {
    return axios({
        url: "/api/go-minilive/v3/room/goodsProductList",
        params,
        isCloseLoading: true,
    });
};

liveApi.getVhGoods = (params) => {
    return axios({
        url: "/api/commodities/v3/periods/getESPeriodInfoById",
        params,
    });
};

liveApi.addGoods = (data) => {
    return axios({
        url: "/api/go-minilive/v3/room/addGoods",
        method: "POST",
        data,
    });
};

liveApi.getImgMediaId = (data) => {
    return axios({
        url: "/api/go-minilive/v3/room/mediaUploadByImage",
        method: "POST",
        data,
    });
};

liveApi.deleteGoods = (data) => {
    return axios({
        url: "/api/go-minilive/v3/room/delete",
        method: "POST",
        data,
    });
};

export default liveApi;
