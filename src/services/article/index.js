import { dataFix } from "ali-oss/lib/common/utils/dataFix";
import axios from "axios";

function articleList(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/periods/list",
        method: "get",
        params: data,
    });
}

function flashAdd(data, type = 1) {
    // 测试
    return axios({
        url:
            type == 1
                ? "/api/commodities/v3/flash/create"
                : "/api/commodities/v3/flash/update",
        method: "post",
        data,
    });
}
function getVestLabelList(data) {
    return axios({
        url: "/api/user/v3/vestuser/labelList",
        method: "get",
        params: data,
    });
}
function leftoverAdd(data, type = 1) {
    // 测试
    return axios({
        url:
            type == 1
                ? "/api/commodities/v3/leftover/create"
                : "/api/commodities/v3/leftover/update",
        method: "post",
        data,
    });
}

function secondAdd(data, type = 1) {
    // 测试
    return axios({
        url:
            type == 1
                ? "/api/commodities/v3/second/create"
                : "/api/commodities/v3/second/update",
        method: "post",
        data,
    });
}

function crossAdd(data, type = 1) {
    // 测试
    return axios({
        url:
            type == 1
                ? "/api/commodities/v3/cross/create"
                : "/api/commodities/v3/cross/update",
        method: "post",
        data,
    });
}

function updateStatus1(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/review/review",
        method: "post",
        data,
    });
}
function updateStatus(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/review/submitCopyWriter",
        method: "post",
        data,
    });
}
function getFlashDetail(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/flash/detail",
        method: "get",
        params: data,
    });
}

function getSecondDetail(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/second/detail",
        method: "get",
        params: data,
    });
}

function getCrossDetail(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/cross/detail",
        method: "get",
        params: data,
    });
}

function getLeftoverDetail(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/leftover/detail",
        method: "get",
        params: data,
    });
}

function getProducts(data) {
    // 测试
    return axios({
        url: "/api/wiki/v3/product/query",
        method: "get",
        params: data,
    });
}

function articalDelete(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/period/delete",
        method: "post",
        data,
    });
}

function updateBuyerInfo(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/flash/updateBuyerInfo",
        method: "post",
        data,
    });
}

function packageAdd(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/package/create",
        method: "post",
        data,
    });
}

function updateGoodsTimes(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/periods/updateTimes",
        method: "post",
        data,
    });
}
function goodsUpdate(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/periods/update",
        method: "post",
        data,
    });
}

function updateOnsale(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/review/onsale",
        method: "post",
        data,
    });
}

function updateoffSale(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/review/offSale",
        method: "post",
        data,
    });
}
function remarkList(data) {
    // 备注列表
    return axios({
        url: "/api/commodities/v3/periods/remarkList",
        method: "get",
        params: data,
    });
}
function createRemark(data) {
    // 新增备注
    return axios({
        url: "/api/commodities/v3/periods/createRemark",
        method: "post",
        data,
    });
}
function remarkUserList(data) {
    // 备注人列表
    return axios({
        url: "/api/commodities/v3/periods/remarkUserList",
        method: "get",
        params: data,
    });
}
function createStock(data) {
    // 更新采购信息
    return axios({
        url: "/api/commodities/v3/periods/createPeriodsProductInventory",
        method: "post",
        data,
    });
}

function purchaseList(data) {
    // 采购员列表
    return axios({
        url: "/api/authority/v3/admin/specifyList",
        method: "get",
        params: data,
    });
}
function getReviewLog(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/review/getReviewLog",
        method: "get",
        params: data,
    });
}
function inventoryList(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/periods/inventoryList",
        method: "get",
        params: data,
    });
}
function updateInventory(data) {
    // 测试
    return axios({
        url: "/api/inventory_service/v3/inventory/sys/update",
        method: "post",
        data,
    });
}
function updateInventory1(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/inventory/update",
        method: "post",
        data,
    });
}
function getWarehouse(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/products/warehouse",
        method: "get",
        params: data,
    });
}
function addWarehouse(data, type) {
    // 测试
    return axios({
        url:
            type == 1
                ? "/api/commodities/v3/fictitious/add"
                : "/api/commodities/v3/fictitious/up",
        method: "post",
        data,
    });
}
function updateWarehouse(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/fictitious/up",
        method: "post",
        data,
    });
}
function isPreView(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/period/objectExist",
        method: "get",
        params: data,
    });
}

function warehouseList(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/fictitious/list",
        method: "get",
        params: data,
    });
}
function getAddress(data) {
    return axios({
        url: "https://vinehoo-test.oss-cn-zhangjiakou.aliyuncs.com/vinehoo/client/common/area.json",
        method: "get",
        params: data,
    });
}
function updatePurchaseInfo(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/periods/updatePurchaseInfo",
        method: "post",
        data,
    });
}

function vestAdd(data) {
    // 测试
    return axios({
        url: "/api/vest/v3/goods/add",
        method: "post",
        data,
    });
}
function vestStop(data) {
    // 测试
    return axios({
        url: "/api/vest/v3/goods/stop",
        method: "post",
        data,
    });
}
function vestList(data) {
    // 测试
    return axios({
        url: "/api/vest/v3/goods/list",
        method: "get",
        params: data,
    });
}
function stopVestOp(data) {
    // 测试
    return axios({
        url: "/api/vest/v3/vest/stopVest",
        method: "post",
        data,
    });
}

function createVestOp(data) {
    // 测试
    return axios({
        url: "/api/vest/v3/vest/add",
        method: "post",
        data,
    });
}

function addFlashVestOp(data) {
    // 秒杀添加马甲
    return axios({
        url: "/api/seckill/v3/vest/add",
        method: "post",
        data,
    });
}
function flashStopVestOp(data) {
    // 测试
    return axios({
        url: "/api/seckill/v3/vest/stop",
        method: "post",
        data,
    });
}
function getFlashVestLists(data) {
    // 测试
    return axios({
        url: "/api//seckill/v3/vest/list",
        method: "get",
        params: data,
    });
}

function getVestLists(data) {
    // 测试
    return axios({
        url: "/api/vest/v3/vest/vestLst",
        method: "get",
        params: data,
    });
}

function getVestProgress(data) {
    // 获取马甲执行进度详情
    return axios({
        url: "/api/vest/v3/vest/vestProgress",
        method: "get",
        params: data,
    });
}

function getSoldPurchasedOrderList(data) {
    // 测试
    return axios({
        url: "/api/orders/v3/order/getSoldPurchasedOrderList",
        method: "get",
        params: data,
    });
}
function getConfigList(data) {
    // 获取配置列表
    return axios({
        url: "/api/orders/v3/order/getConfig",
        method: "get",
        params: data,
    });
}
function getOrderDetail(data) {
    return axios({
        url: "/api/orders/v3/order/detail",
        method: "get",
        params: data,
    });
}
function getLog(data) {
    return axios({
        url: "/api/commodities/v3/periods/periodsStatusChangeRecord",
        method: "get",
        params: data,
    });
}
function packList(data) {
    return axios({
        url: "/api/commodities/v3/package/list",
        method: "get",
        params: data,
    });
}
function productList(data) {
    return axios({
        url: "/api/commodities/v3/package/productList",
        method: "get",
        params: data,
    });
}
function updateUncUsed(data) {
    // 更新不常用字段
    return axios({
        url: "/api/commodities/v3/periods/updateUncUsed",
        method: "post",
        data,
    });
}
function syncFictitious(data) {
    // 同步仓库
    return axios({
        url: "/api/commodities/v3/fictitious/syncFictitious",
        method: "post",
        data,
    });
}
function praise(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/periods/praise",
        method: "post",
        data,
    });
}
function copyPeriod(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/periods/copyPeriod",
        method: "get",
        params: data,
    });
}
function createJson(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/period/createJson",
        method: "get",
        params: data,
    });
}
function copyWriterReview(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/review/copyWriterReview",
        method: "post",
        data,
    });
}
function supplierList(data) {
    // 供应商列表
    return axios({
        url: "/api/wiki/v3/supplier/filter",
        method: "get",
        params: data,
    });
}
function getPeriodsByShortCode(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/comment/getPeriodsByShortCode",
        method: "get",
        params: data,
    });
}
function getCommentByPeriodAndShortCode(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/comment/getCommentByPeriodAndShortCode",
        method: "get",
        params: data,
    });
}
function getCommentPoolList(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/comment/list",
        method: "get",
        params: data,
    });
}
function upcomment(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/comment/upcomment",
        method: "post",
        data,
    });
}
function rabbitDetail(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/rabbit/detail",
        method: "get",
        params: data,
    });
}
function rabbitUpdate(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/rabbit/update",
        method: "post",
        data,
    });
}
function rabbitUpdateInfo(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/cross/updateBuyerInfo",
        method: "post",
        data,
    });
}
function rabbitAdd(data, type = 1) {
    // 测试
    return axios({
        url:
            type == 1
                ? "/api/commodities/v3/rabbit/create"
                : "/api/commodities/v3/rabbit/update",
        method: "post",
        data,
    });
}
function rabbitCouponAdd(data) {
    // 新增兔头商品
    return axios({
        url: "/api/commodities/v3/rabbitCoupon/create",
        method: "post",
        data,
    });
}
function rabbitCouponUpdate(data) {
    // 更新兔头商品
    return axios({
        url: "/api/commodities/v3/rabbitCoupon/update",
        method: "post",
        data,
    });
}
function getCoupon(data) {
    // 测试
    return axios({
        url: "/api/coupon/v3/coupon/lists",
        method: "get",
        params: data,
    });
}
function getRabbitCouponDetail(data) {
    // 测试
    return axios({
        url: "/api/commodities/v3/rabbitCoupon/detail",
        method: "get",
        params: data,
    });
}
function queryaddition(data) {
    // 获取指令列表
    return axios({
        url: "/api/wiki/v3/product/queryaddition",
        method: "get",
        params: data,
    });
}
function createComment(data) {
    // 商品评论新增
    return axios({
        url: "/api/commodities/v3/comment/createComment",
        method: "post",
        data,
    });
}
function updatevestuser(data) {
    // 更新马甲用户
    return axios({
        url: "/api/user/v3/vestuser/update",
        method: "post",
        data,
    });
}
function getVestList() {
    // 我的马甲列表
    return axios({
        url: "/api/user/v3/vestuser/get",
        method: "get",
    });
}
function getTopicList(data) {
    // 话题列表
    return axios({
        url: "/api/community/v3/topic/index",
        method: "get",
        params: data,
    });
}
function getCommentList(data) {
    // 评论列表
    return axios({
        url: "/api/commodities/v3/comment/getCommentList",
        method: "get",
        params: data,
    });
}
function getlabelList(data) {
    return axios({
        url: "/api/user/v3/label/list",
        method: "get",
        params: data,
    });
}
function expressList(data) {
    // 评论列表
    return axios({
        url: "/api/orders/v3/express/webList",
        method: "get",
        params: data,
    });
}
function setCommentStatus(data) {
    // 评论列表
    return axios({
        url: "/api/contentaudit/v3/comment/changeStatusBySource",
        method: "post",
        data,
    });
}
function getMerchantList(data) {
    // 商家列表
    return axios({
        url: "/api/vmall/v3/merchant/list",
        method: "get",
        params: data,
    });
}
//
function createWineEvaluation(data) {
    // 添加酒评
    return axios({
        url: "/api/community/v3/wineEvaluation/create",
        method: "post",
        data,
    });
}
//获取酒类参数
function getAscategorylist(data) {
    return axios({
        url: "/api/wiki/v3/wineparam",
        method: "get",
        params: data,
    });
}
//获取酒云网小程序无限量二维码
function getMinappQrcode(data) {
    return axios({
        url: "/api/wechat/v3/minapp/qrcode/unlimit",
        method: "post",
        data,
    });
}

function importInvoiceFile(data) {
    return axios({
        url: "/api/commodities/v3/periods/importByExcel",
        method: "post",
        data,
    });
}
function importCrossByExcel(data) {
    return axios({
        url: "/api/commodities/v3/periods/importCrossByExcel",
        method: "post",
        data,
    });
}

//验证跨境库存信息
function verifyCrossStock(data) {
    return axios({
        url: "/api/orders/v3/cross/verifyCrossStock",
        method: "post",
        data,
    });
}
///v3/data/goods_exposure/goods获取商品曝光数据
function getGoodsExposure(data) {
    return axios({
        url: "/api/data/v3/exposure/goods",
        method: "get",
        params: data,
    });
}
// 商品折线图统计
function getPageviewsByDays(data) {
    return axios({
        url: "/api/commodities/v3/periods/getPageviewsByDays",
        method: "get",
        params: data,
    });
}
//期数浏览量下单统计
function getPeriodPageviewsByHours(data) {
    return axios({
        url: "/api/commodities/v3/periods/getPeriodPageviewsByHours",
        method: "get",
        params: data,
    });
}

//获取商品池列表
function goodsPoolList(data) {
    return axios({
        url: "/api/go-recommend/v3/second/goodsPoolList",
        method: "get",
        params: data,
    });
}
//添加商品到商品池
function addGoodsPool(data) {
    return axios({
        url: "/api/go-recommend/v3/second/addGoodsPool",
        method: "post",
        data,
    });
}
//编辑商品到商品池
function updateGoodsPool(data) {
    return axios({
        url: "/api/go-recommend/v3/second/updateGoodsPool",
        method: "post",
        data,
    });
}
//秒发瀑布流配置列表
function confList(data) {
    return axios({
        url: "/api/go-recommend/v3/conf/confList",
        method: "get",
        params: data,
    });
}
//秒发瀑布流配置更新
function confUpdate(data) {
    return axios({
        url: "/api/go-recommend/v3/conf/confUpdate",
        method: "post",
        data,
    });
}

//更新期数成本
function updateCost(data) {
    return axios({
        url: "/api/commodities/v3/vmall/updateCost",
        method: "post",
        data,
    });
}

function updatePayeeMerchant(data) {
    return axios({
        url: "/api/commodities/v3/periods/updatePayeeMerchant",
        method: "post",
        data,
    });
}

function getRegionList() {
    return axios({
        url: "/api/user/v3/regional/getAllData",
    });
}

function getHealthInspectStatus(params) {
    return axios({
        url: "/api/commodities/v3/periods/getCustomsOrderHealthInspect",
        params,
    });
}
//获取搜索预处理列表
function searchKeywordsList(data) {
    return axios({
        url: "/api/go-ai-search/v3/search/list",
        method: "get",
        params: data,
    });
}

function changeSearchKeywords(data) {
    return axios({
        url: "/api/go-ai-search/v3/search/change",
        method: "post",
        data,
    });
}
// 添加关键词
function addSearchKeywords(data) {
    return axios({
        url: "/api/go-ai-search/v3/search/add",
        method: "post",
        data,
    });
}

// 编辑关键词
function eidtSearchKeywords(data) {
    return axios({
        url: "/api/go-ai-search/v3/search/update",
        method: "post",
        data,
    });
}

function judgeMostwords(data) {
    return axios({
        url: "/api/badwords/v3/vinehoo.mostwords/badwords/findall",
        method: "post",
        data,
    });
}
// 查询待支付
function queryPendingPayment(data) {
    return axios({
        url: "/api/commodities/v3/inventory/query/prompt",
        method: "get",
        params: data,
    });
}

// 转尾货
function synchronizeTailGoods(data) {
    return axios({
        url: "/api/commodities/v3/periods/syncLeftover",
        method: "post",
        data,
    });
}

// 查询待支付
function getOperationRecordList(data) {
    return axios({
        url: "/api/commodities/v3/periods/OperationRecordList",
        method: "get",
        params: data,
    });
}
function getunpushorder(data) {
    return axios({
        url: "/api/commodities/v3/period/getunpushorder",
        method: "get",
        params: data,
    });
}
function getParametersChannelList(data) {
    return axios({
        url: "/api/commodities/v3/parameters/channellist",
        method: "get",
        params: data,
    });
}
function getPeriodsBuyerList(data) {
    return axios({
        url: "/api/commodities/v3/periodsBuyer/list",
        method: "get",
        params: data,
    });
}

function periodsBuyerSave(data) {
    return axios({
        url: "/api/commodities/v3/periodsBuyer/save",
        method: "post",
        data,
    });
}

// 根据简码批量查询产品信息
function getProductInfoByShortCode(data) {
    return axios({
        url: "/api/wiki/v3/product/batchQueryByShortCode",
        method: "get",
        params: data,
    });
}

// 添加简码到库存订阅
function addInventorySubscription(data) {
    return axios({
        url: "/api/wiki/v3/product/addInventorySubscription",
        method: "post",
        data,
    });
}
// 期数查询同时在售的期数
function getSamePeriods(data) {
    return axios({
        url: "/api/commodities/v3/periods/getSamePeriods",
        method: "get",
        params: data,
    });
}

// 取消渠道
function cancelChannel(data) {
    return axios({
        url: "/api/commodities/v3/periods/cancelChannel",
        method: "post",
        data,
    });
}
// 文案数据统计
function getSopywritingDataStatistics(data) {
    return axios({
        url: "/api/commodities/v3/periods/showingText",
        method: "get",
        params: data,
    });
}
export default {
    createComment,
    getunpushorder,
    importInvoiceFile,
    setCommentStatus,
    expressList,
    queryaddition,
    getRabbitCouponDetail,
    getVestList,
    getCoupon,
    rabbitCouponAdd,
    rabbitDetail,
    rabbitUpdate,
    rabbitCouponUpdate,
    rabbitUpdateInfo,
    getCommentList,
    rabbitAdd,
    upcomment,
    getCommentPoolList,
    getCommentByPeriodAndShortCode,
    getPeriodsByShortCode,
    supplierList,
    copyWriterReview,
    createJson,
    copyPeriod,
    syncFictitious,
    praise,
    updateUncUsed,
    articleList,
    updateStatus,
    updateStatus1,
    getLeftoverDetail,
    getCrossDetail,
    getSecondDetail,
    getFlashDetail,
    getProducts,
    leftoverAdd,
    secondAdd,
    crossAdd,
    flashAdd,
    articalDelete,
    updateBuyerInfo,
    packageAdd,
    goodsUpdate,
    updateOnsale,
    updateoffSale,
    remarkList,
    createRemark,
    createStock,
    purchaseList,
    getReviewLog,
    inventoryList,
    updateInventory,
    updateInventory1,
    getWarehouse,
    warehouseList,
    updateWarehouse,
    addWarehouse,
    getAddress,
    updatePurchaseInfo,
    vestStop,
    vestAdd,
    vestList,
    getSoldPurchasedOrderList,
    getConfigList,
    getOrderDetail,
    getLog,
    packList,
    productList,
    updateGoodsTimes,
    getVestLists,
    getVestProgress,
    createVestOp,
    stopVestOp,
    isPreView,
    getMerchantList,
    createWineEvaluation,
    getAscategorylist,
    getTopicList,
    getMinappQrcode,
    verifyCrossStock,
    importCrossByExcel,
    getGoodsExposure,
    getPageviewsByDays,
    getPeriodPageviewsByHours,
    goodsPoolList,
    addGoodsPool,
    updateGoodsPool,
    confList,
    confUpdate,
    updateCost,
    updatePayeeMerchant,
    getRegionList,
    getHealthInspectStatus,
    searchKeywordsList,
    getVestLabelList,
    changeSearchKeywords,
    addSearchKeywords,
    eidtSearchKeywords,
    remarkUserList,
    judgeMostwords,
    addFlashVestOp,
    getFlashVestLists,
    flashStopVestOp,
    queryPendingPayment,
    synchronizeTailGoods,
    getOperationRecordList,
    getParametersChannelList,
    getPeriodsBuyerList,
    periodsBuyerSave,
    getlabelList,
    getProductInfoByShortCode,
    addInventorySubscription,
    updatevestuser,
    getSamePeriods,
    cancelChannel,
    getSopywritingDataStatistics,
};
