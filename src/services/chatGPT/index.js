import axios from "axios";

//获取用户指令
function instructsByUser(data) {
    return axios({
        url: "/api/chatGpt/v3/instruct/instructsByUser",
        method: "get",
        params: data,
    });
}
//创建用户指令
function createInstructs(data) {
    return axios({
        url: "/api/chatGpt/v3/instruct/create",
        method: "post",
        data,
    });
}
//编辑用户指令
function updateInstructs(data) {
    return axios({
        url: "/api/chatGpt/v3/instruct/update",
        method: "post",
        data,
    });
}
//删除用户指令
function deleteInstructs(data) {
    return axios({
        url: "/api/chatGpt/v3/instruct/delete",
        method: "post",
        data,
    });
}
//获取历史聊天室
function roomsByUser(data) {
    return axios({
        url: "/api/chatGpt/v3/room/roomsByUser",
        method: "get",
        params: data,
    });
}
//聊天
function chatGptSearch(data) {
    return axios({
        // url: "/api/chatGpt/v3/chat/search",
        url: "/aaa/event",
        method: "post",
        data,
    });
}
//获取聊天室内容
function logByRoom(data) {
    return axios({
        url: "/api/chatGpt/v3/chat/logByRoom",
        method: "get",
        params: data,
    });
}
//删除聊天室
function deleteRoom(data) {
    return axios({
        url: "/api/chatGpt/v3/room/delete",
        method: "post",
        data,
    });
}
//统计文本内容数量
function tokensCount(data) {
    return axios({
        url: "/api/ai/chatgpt/v3/tokens/count",
        method: "post",
        data,
    });
}
//数据解密
function decrypt(data) {
    return axios({
        url: "/api/work/v3/GdCommon/decrypt",
        method: "post",
        data,
    });
}
//获取全局剩余聊天次数
function searchCnt(data) {
    return axios({
        url: "/api/chatGpt/v3/chat/searchCnt",
        method: "get",
        params: data,
    });
}
export default {
    instructsByUser,
    createInstructs,
    updateInstructs,
    deleteInstructs,
    roomsByUser,
    chatGptSearch,
    logByRoom,
    deleteRoom,
    tokensCount,
    decrypt,
    searchCnt,
};
