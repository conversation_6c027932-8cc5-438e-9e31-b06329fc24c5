import axios from "axios";
function getCommoditiesDetail(data) {
    // 获取目录
    return axios({
        url: "/api/commodities/v3/periods/list",
        method: "get",
        params: data,
    });
}
// /openapi/v3/package/detail
function getPackageDetail(data) {
    // 获取目录
    return axios({
        url: "/api/commodities/v3/package/list",
        method: "get",
        params: data,
    });
}
// /coupon/v3/coupon/detail
function getCouponDetail(data) {
    // 获取目录
    return axios({
        url: "/api/coupon/v3/coupon/detail",
        method: "get",
        params: data,
    });
}
// /commodities/v3/ap/addPeriods
function addPeriods(data) {
    // 获取目录
    return axios({
        url: "/api/commodities/v3/ap/addPeriods",
        method: "post",
        data,
    });
}
function getAddPurchase(data) {
    return axios({
        url: "/api/commodities/v3/ap/getAddPurchase",
        method: "get",
        params: data,
    });
}

// /commodities/v3/ap/updateAP
function updateAP(data) {
    return axios({
        url: "/api/commodities/v3/ap/updateAP",
        method: "post",
        data,
    });
}

function updateSort(data) {
    return axios({
        url: "/api/commodities/v3/ap/updateSort",
        method: "post",
        data,
    });
}
export default {
    getCommoditiesDetail,
    getPackageDetail,
    getCouponDetail,
    addPeriods,
    getAddPurchase,
    updateAP,
    updateSort,
};
