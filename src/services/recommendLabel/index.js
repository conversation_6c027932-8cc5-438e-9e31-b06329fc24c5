import axios from "axios";

// 推荐标签列表
const labelList = (params) => {
    return axios({
        url: "/api/commodities/v3/label/labelList",
        method: "get",
        params,
    });
};

// 添加标签
const labelAdd = (data) => {
    return axios({
        url: "/api/commodities/v3/label/labelAdd",
        method: "post",
        data,
    });
};

// 修改推荐标签
const labelEdit = (data) => {
    return axios({
        url: "/api/commodities/v3/label/labelEdit",
        method: "post",
        data,
    });
};

const goodLabelUpdate = (data) => {
    return axios({
        url: "/api/commodities/v3/periods/updateLabel",
        method: "post",
        data,
    });
};

const batchGoodLabelUpdate = (data) => {
    return axios({
        url: "/api/commodities/v3/periods/batchUpdateLabel",
        method: "post",
        data,
    });
};
//产区
function getRegionList(params) {
    return axios({
        url: "/api/wiki/v3/regions",
        method: "get",
        params,
    });
}
//酒庄
function getWineryList(params) {
    return axios({
        url: "/api/wiki/v3/winery",
        method: "get",
        params,
    });
}
//国家
function getCountryList(params) {
    return axios({
        url: "/api/wiki/v3/country",
        method: "get",
        params,
    });
}
//葡萄品种
function getGrapeList(params) {
    return axios({
        url: "/api/wiki/v3/grape",
        method: "get",
        params,
    });
}

function getProductCategory(params) {
    return axios({
        url: "/api/wiki/v3/productcategory/type",
        method: "get",
        params,
    });
}
export default {
    labelList,
    labelAdd,
    labelEdit,
    goodLabelUpdate,
    batchGoodLabelUpdate,
    getRegionList,
    getWineryList,
    getCountryList,
    getGrapeList,
    getProductCategory,
};
