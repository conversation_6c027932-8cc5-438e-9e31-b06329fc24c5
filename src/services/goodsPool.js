import axios from "axios";

const goodsPoolApi = {};

goodsPoolApi.searchGoodsPoolList = (params) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/list",
        params,
    });
};

goodsPoolApi.deleteGoodsPoolItem = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/del",
        method: "POST",
        data,
    });
};

goodsPoolApi.submitPurchaseInfo = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/purchaseSubmit",
        method: "POST",
        data,
    });
};

goodsPoolApi.saveGoodsInfo = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/editor",
        method: "POST",
        data,
    });
};

goodsPoolApi.submitGoodsInfo = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/editorSubmit",
        method: "POST",
        data,
    });
};

goodsPoolApi.updateGoodsInfo = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/editorUpdate",
        method: "POST",
        data,
    });
};

goodsPoolApi.allocCopyWriter = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/documentDistribution",
        method: "POST",
        data,
    });
};

goodsPoolApi.allocDesign = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/pictureDistribution",
        method: "POST",
        data,
    });
};

goodsPoolApi.allocDesignUpdate = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/uiPersonnel",
        method: "POST",
        data,
    });
};

goodsPoolApi.saveCertificate = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/qualification",
        method: "POST",
        data,
    });
};

goodsPoolApi.submitCertificate = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/qualificationSubmit",
        method: "POST",
        data,
    });
};

goodsPoolApi.purchaseResubmit = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/resend",
        method: "POST",
        data,
    });
};

goodsPoolApi.saveDesignInfo = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/uiPictureDistribution",
        method: "POST",
        data,
    });
};

goodsPoolApi.designSubmit = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/pictureSubmit",
        method: "POST",
        data,
    });
};

goodsPoolApi.checkGoodsInfo = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/document",
        method: "POST",
        data,
    });
};

goodsPoolApi.checkCertificate = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/purchaseExec",
        method: "POST",
        data,
    });
};

goodsPoolApi.purchaseCheckGoodsInfo = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/auditPurchase",
        method: "POST",
        data,
    });
};

goodsPoolApi.purchaseCheck = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/auditLastPurchase",
        method: "POST",
        data,
    });
};

goodsPoolApi.operationCheck = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/operation",
        method: "POST",
        data,
    });
};

goodsPoolApi.getSupplierProductCategories = () => {
    return axios({
        url: "/api/supplier-product/v3/supplierproduct/productCategory",
    });
};

goodsPoolApi.findGoods = (params) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/findPeriods",
        params,
    });
};

goodsPoolApi.getEventRecords = (params) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/logList",
        params,
    });
};

goodsPoolApi.getAdminInfo = (params) => {
    return axios({
        url: "/api/authority/v3/admin/info",
        params,
    });
};

goodsPoolApi.setCopyWriter = (data) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/documentPersonnel",
        method: "POST",
        data,
    });
};

goodsPoolApi.getPendingNumber = () => {
    return axios({
        url: "/api/commodities/v3/periodsPool/backlogQuantity",
        isCloseLoading: true,
    });
};

goodsPoolApi.searchProductList = (params) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/supplierProductList",
        params,
    });
};

goodsPoolApi.searchCorpsByProductId = (params) => {
    return axios({
        url: "/api/commodities/v3/periodsPool/getCompanyByProducts",
        params,
    });
};

export default goodsPoolApi;
