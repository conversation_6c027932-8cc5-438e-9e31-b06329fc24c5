import axios from "axios";

const purchaseExecManageApi = {};

purchaseExecManageApi.searchPurchaseOrderList = (params) => {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/list",
        params,
    });
};

purchaseExecManageApi.searchProductList = (params) => {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/getItems",
        params,
    });
};

purchaseExecManageApi.updatePurchaseOrder = (data) => {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/edit",
        method: "POST",
        data,
    });
};

purchaseExecManageApi.delayPurchaseOrder = (data) => {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/cancel",
        method: "POST",
        data,
    });
};

purchaseExecManageApi.agreePurchaseOrder = (data) => {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/approved",
        method: "POST",
        data,
    });
};
purchaseExecManageApi.autoAgreePurchaseOrder = (data) => {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/retrial",
        method: "POST",
        data,
    });
};

purchaseExecManageApi.rejectPurchaseOrder = (data) => {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/reject",
        method: "POST",
        data,
    });
};
// 备货列表
purchaseExecManageApi.getStockList = (params) => {
    return axios({
        url: "/api/purchase/v3/PreparePurchase/list",
        params,
    });
};

purchaseExecManageApi.updateStockNumber = (data) => {
    return axios({
        url: "/api/purchase/v3/PreparePurchase/update",
        method: "POST",
        data,
    });
};
purchaseExecManageApi.addStockNumber = (data) => {
    return axios({
        url: "/api/purchase/v3/PreparePurchase/add",
        method: "POST",
        data,
    });
};
purchaseExecManageApi.preparePurchaseExport = (params) => {
    return axios({
        url: "/api/purchase/v3/PreparePurchase/export",
        params,
    });
};
purchaseExecManageApi.preparePurchaseImportExcel = (data) => {
    return axios({
        url: "/api/purchase/v3/PreparePurchase/importExcel",
        method: "POST",
        data,
    });
};
// 日志记录
purchaseExecManageApi.logRecordList = (params) => {
    return axios({
        url: "/api/purchase/v3/PreparePurchase/logs",
        params,
    });
};
purchaseExecManageApi.uploadExpressNumber = (data) => {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/uploadWaybill",
        method: "POST",
        data,
    });
};
purchaseExecManageApi.retractPurchaseOrder = (data) => {
    return axios({
        url: "/api/commodities/v3/purchaseOrderno/retract",
        method: "POST",
        data,
    });
};
purchaseExecManageApi.getLogistics = (params) => {
    return axios({
        url: "/api/logistics/mapTrack/v3/track",
        params,
    });
};
export default purchaseExecManageApi;
