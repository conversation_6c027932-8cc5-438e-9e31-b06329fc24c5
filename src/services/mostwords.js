import axios from "axios";

function getWordList(data) {
    // 极限词列表
    return axios({
        url: "/api/user/v3/sensitivewords/lists",
        method: "get",
        params: data,
    });
}
function addWord(data) {
    // 新增极限词列表
    return axios({
        url: "/api/user/v3/sensitivewords/add",
        method: "post",
        data,
    });
}
function editWord(data) {
    // 编辑极限词列表
    return axios({
        url: "/api/user/v3/sensitivewords/edit",
        method: "post",
        data,
    });
}
function deleteWord(data) {
    // 删除极限词
    return axios({
        url: "/api/user/v3/sensitivewords/del",
        method: "post",
        data,
    });
}

export default {
    addWord,
    editWord,
    getWordList,
    deleteWord,
};
