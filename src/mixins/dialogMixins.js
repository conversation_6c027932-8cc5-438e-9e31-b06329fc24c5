export default {
    name: "dialogMixin",
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        rowData: {
            type: Object,
            default: {},
        },
        isAdd: {
            type: Number,
            default: 0,
        },
        title: {
            type: String,
            default: "新增",
        },
    },
    methods: {
        closeDialog() {
            this.ruleForm = this.$options.data().ruleForm;
            this.$refs.ruleForm.resetFields();
            this.$emit("update:visible", false);
            this.$emit("getList");
        },
    },
};
