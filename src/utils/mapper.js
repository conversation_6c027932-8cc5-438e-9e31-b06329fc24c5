import * as mapperModel from "./mapperModel";

export const MGoodsTypeText = Object.freeze([
    {
        value: mapperModel.MGoodsType.SG,
        label: "闪购",
    },
    {
        value: mapperModel.MGoodsType.MF,
        label: "秒发",
    },
    {
        value: mapperModel.MGoodsType.KJ,
        label: "跨境",
    },
    {
        value: mapperModel.MGoodsType.WH,
        label: "尾货",
    },
]);

export const MImportTypeText = Object.freeze([
    {
        value: mapperModel.MImportType.Land,
        label: "地采",
    },
    {
        value: mapperModel.MImportType.Self,
        label: "自进口",
    },
    {
        value: mapperModel.MImportType.KJ,
        label: "跨境",
    },
]);

export const MPayeeCompanyText = Object.freeze([
    {
        value: mapperModel.MPayeeCompany.Yunjiu,
        label: "重庆云酒佰酿电子商务有限公司",
    },
    {
        value: mapperModel.MPayeeCompany.Ke<PERSON>,
        label: "佰酿云酒（重庆）科技有限公司",
    },
    {
        value: mapperModel.MPayeeCompany.Weixun,
        label: "渝中区微醺酒业商行",
    },
    {
        value: mapperModel.MPayeeCompany.Yihuayisijie,
        label: "海南一花一世界科技有限公司",
    },
]);

export const MIsGiftBoxText = Object.freeze([
    {
        value: mapperModel.MIsGiftBox.YES,
        label: "带",
    },
    {
        value: mapperModel.MIsGiftBox.NO,
        label: "不带",
    },
]);

export const MIsGiftBoxText2 = Object.freeze([
    {
        value: mapperModel.MIsGiftBox.YES,
        label: "是",
    },
    {
        value: mapperModel.MIsGiftBox.NO,
        label: "否",
    },
]);

export const MIsSupplierDeliveryText = Object.freeze([
    {
        value: mapperModel.MIsSupplierDelivery.YES,
        label: "代发",
    },
    {
        value: mapperModel.MIsSupplierDelivery.NO,
        label: "非代发",
    },
]);

export const MIsSupplierDeliveryText2 = Object.freeze([
    {
        value: mapperModel.MIsSupplierDelivery.YES,
        label: "是",
    },
    {
        value: mapperModel.MIsSupplierDelivery.NO,
        label: "否",
    },
]);

export const MIsPresellText = Object.freeze([
    {
        value: mapperModel.MIsPresell.YES,
        label: "预售",
    },
    {
        value: mapperModel.MIsPresell.NO,
        label: "非预售",
    },
]);

export const MShippingConditionsText = Object.freeze([
    {
        value: mapperModel.MShippingConditions.Normal,
        label: "常温",
    },
    {
        value: mapperModel.MShippingConditions.ColdChain,
        label: "温控包裹",
    },
]);

export const MStorageConditionsText = Object.freeze([
    {
        value: mapperModel.MStorageConditions.Normal,
        label: "常温",
    },
    {
        value: mapperModel.MStorageConditions.Frozen,
        label: "冰冻",
    },
]);

export const MDeliveryTimeLimitText = Object.freeze([
    {
        value: mapperModel.MDeliveryTimeLimit.Hour24,
        label: "24小时",
    },
    {
        value: mapperModel.MDeliveryTimeLimit.Hour72,
        label: "72小时",
    },
    {
        value: mapperModel.MDeliveryTimeLimit.Hour72Above,
        label: "72小时以上",
    },
]);

export const MCreatorTypeText = Object.freeze([
    {
        value: mapperModel.MCreatorType.Add,
        label: "新增文案",
    },
    {
        value: mapperModel.MCreatorType.Copy,
        label: "复制文案",
    },
]);

export const MIsDesignText = Object.freeze([
    {
        value: mapperModel.MIsDesign.YES,
        label: "是",
    },
    {
        value: mapperModel.MIsDesign.NO,
        label: "否",
    },
]);

export const MDemandGradeText = Object.freeze([
    {
        value: mapperModel.MDemandGrade.Low,
        label: "低",
    },
    {
        value: mapperModel.MDemandGrade.Middle,
        label: "中",
    },
    {
        value: mapperModel.MDemandGrade.High,
        label: "高",
    },
]);
