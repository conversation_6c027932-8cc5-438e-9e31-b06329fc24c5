import * as mappers from "./mapper";

export function toText(input, type) {
    const mapper = mappers[type];
    if (!mapper) return "";

    const result = mapper.find((obj) => obj.value === input);
    return result && (result.text || result.label);
}

const filters = {
    toText,
};

export function registerFilters(Vue) {
    Object.keys(filters).forEach((key) => {
        Vue.filter(key, filters[key]);
    });
}
