import axios from "axios";
import Vue from "vue";

Vue.prototype.$viewPcGoods = async (id) => {
    const open = (id) => {
        window.open(
            Vue.prototype.$BASE.PCDomain +
                `/pages/goods-detail/goods-detail?id=${id}`
        );
    };
    await axios({
        url: "/api/commodities/v3/periods/getChannelEncryptId",
        params: { id },
    })
        .then((res) => {
            if (res.data.error_code === 0) {
                open(res.data.data.encrypt_id);
            } else {
                open(id);
            }
        })
        .catch(() => {
            open(id);
        });
};
