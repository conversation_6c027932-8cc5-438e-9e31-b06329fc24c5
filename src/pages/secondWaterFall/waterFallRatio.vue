<template>
    <div>
        <div class="f_box">
            <el-card shadow="never" class="box-card" style="width: 45%">
                <div slot="header" class="clearfix">
                    <span>产品板块（{{ leftNum }}）</span>
                </div>
                <div>
                    <div
                        class="f_box m-t-10"
                        v-for="(item, index) in leftData"
                        :key="index + 'left'"
                    >
                        <div>
                            <el-button type="primary" style="width: 150px">{{
                                item.desc.split("_")[1]
                            }}</el-button>
                        </div>
                        <div>
                            <el-input-number
                                v-model="item.val"
                                :min="0"
                                label=""
                                @change="
                                    changeVal(
                                        item.val,
                                        index,
                                        'leftData',
                                        'leftDataClone'
                                    )
                                "
                            ></el-input-number>
                        </div>
                    </div>
                </div>
            </el-card>
            <el-card shadow="never" class="box-card" style="width: 45%">
                <div slot="header" class="clearfix">
                    <span>内容板块（{{ rightNum }}）</span>
                </div>
                <div>
                    <div
                        class="f_box m-t-10"
                        v-for="(item, index) in rightData"
                        :key="index + 'right'"
                    >
                        <div>
                            <el-button type="primary" style="width: 150px">{{
                                item.desc.split("_")[1]
                            }}</el-button>
                        </div>
                        <div>
                            <el-input-number
                                v-model="item.val"
                                label=""
                                :min="0"
                                @change="
                                    changeVal(
                                        item.val,
                                        index,
                                        'rightData',
                                        'rightDataClone'
                                    )
                                "
                            ></el-input-number>
                        </div>
                    </div>
                </div>
            </el-card>
        </div>
        <div class="f_box_c">
            <el-button type="success" :disabled="isdisabled" @click="submit"
                >立即生效 ({{ leftNum + rightNum }})</el-button
            >
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            leftData: [],
            rightData: [],
            leftDataClone: [],
            rightDataClone: [],
            allData: {},
            isdisabled: true,
        };
    },
    computed: {
        leftNum({ leftData }) {
            let num = 0;
            leftData.map((item) => {
                num = num + item.val;
            });
            return num;
        },
        rightNum({ rightData }) {
            let num = 0;
            rightData.map((item) => {
                num = num + item.val;
            });
            return num;
        },
    },
    mounted() {
        this.confList();
    },
    methods: {
        async confList() {
            let res = await this.$request.article.confList();
            if (res.data.error_code == 0) {
                this.allData = res.data.data?.list[0] || {};
                this.leftData = [];
                this.rightData = [];
                const arr = res.data.data?.list[0]?.config || [];
                arr.map((item) => {
                    if (item.desc.indexOf("left") != -1) {
                        this.leftData.push(item);
                    } else if (item.desc.indexOf("right") != -1) {
                        this.rightData.push(item);
                    }
                });
                this.leftDataClone = JSON.parse(JSON.stringify(this.leftData));
                this.rightDataClone = JSON.parse(
                    JSON.stringify(this.rightData)
                );
                this.isdisabled = true;
            }
        },
        //比较修改后的数据是否和之前的数据相同
        changeVal(currentValue, index, data, dataClone) {
            console.log("---", currentValue);
            if (!currentValue) this[data][index].val = 0;
            for (let i = 0; i < this[dataClone].length; i++) {
                if (!(this[dataClone][i].val == this[data][i].val)) {
                    this.isdisabled = false;
                    return;
                } else {
                    this.isdisabled = true;
                }
            }
        },
        async submit() {
            const { code, genre, desc } = this.allData;
            let data = {
                code,
                genre,
                desc,
                config: [...this.leftData, ...this.rightData],
            };
            let res = await this.$request.article.confUpdate(data);
            if (res.data.error_code == 0) {
                this.$message.success("更新成功");
                this.confList();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.f_box {
    display: flex;
    justify-content: space-around;
}
.f_box_c {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}
</style>
