<template>
    <div>
        <!-- <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane
                :label="item.label"
                :name="item.value"
                v-for="(item, index) in tabList"
                :key="index"
            >
                <List :activeName="activeName" />
            </el-tab-pane>
        </el-tabs> -->
        <List :activeName="activeName" />
    </div>
</template>

<script>
import List from "./list.vue";
export default {
    components: {
        List,
    },
    data() {
        return {
            tabList: [
                // {
                //     value: "1",
                //     label: "新品推荐",
                // },
                {
                    value: "2",
                    label: "推荐产品广告位",
                },
            ],
            activeName: "2",
        };
    },
    methods: {
        handleClick() {},
    },
};
</script>

<style></style>
