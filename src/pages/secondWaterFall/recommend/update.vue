<template>
    <el-dialog
        :title="title"
        :visible="visible"
        width="50%"
        :before-close="closeDialog"
    >
        <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            label-width="130px"
            class="demo-ruleForm"
            size="mini"
        >
            <el-form-item label="期数" prop="period_id">
                <el-input
                    v-model="ruleForm.period_id"
                    class="w-large m-r-10"
                    placeholder="请输入期数"
                ></el-input>
                <el-button type="success" @click="search(1)">查询</el-button>
            </el-form-item>
            <div v-if="showDetail">
                <el-form-item label="商品标题" prop="title">
                    <el-input
                        type="textarea"
                        :rows="3"
                        v-model="ruleForm.title"
                        maxlength="30"
                        show-word-limit
                        class="w-large m-r-10"
                        placeholder="请输入商品标题"
                    ></el-input>
                </el-form-item>
                <el-form-item label="商品价格" prop="price">
                    <el-input
                        v-model="ruleForm.price"
                        class="w-large m-r-10"
                        placeholder="请输入商品价格"
                        disabled
                    ></el-input>
                </el-form-item>
            </div>
            <el-form-item label="推荐开始时间" prop="start_time">
                <el-date-picker
                    v-model="ruleForm.start_time"
                    type="datetime"
                    placeholder="选择推荐开始时间"
                >
                    <!-- value-format="yyyy-MM-dd HH:mm:ss" -->
                </el-date-picker>
            </el-form-item>
            <el-form-item label="推荐结束时间" prop="end_time">
                <el-date-picker
                    v-model="ruleForm.end_time"
                    type="datetime"
                    placeholder="选择推荐结束时间"
                >
                    <!-- value-format="yyyy-MM-dd HH:mm:ss" -->
                </el-date-picker>
            </el-form-item>

            <el-form-item>
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确定</el-button
                >
            </el-form-item>
        </el-form>
    </el-dialog>
</template>

<script>
import dialogMixins from "../../../mixins/dialogMixins";
export default {
    props: ["activeName"],
    mixins: [dialogMixins],
    data() {
        const validateTitle = (rule, value, callback) => {
            if (!value) {
                return callback(new Error("请输入商品标题"));
            } else if (value.length > 30) {
                return callback(new Error("商品标题不可超过30个字"));
            } else {
                callback();
            }
        };
        return {
            showDetail: false,
            ruleForm: {
                period_id: "", //期数
                title: "", //自定义标题
                price: "",
                // genre: 2, //类型(1新品池,2运营池)
                period_type: "", //频道
                status: 1, //1启用,2禁用
                start_time: "", //推荐开始时间(时间戳)
                end_time: "", //推荐结束时间
            },
            rules: {
                period_id: [
                    {
                        required: true,
                        message: "请输入期数",
                        trigger: "blur",
                    },
                ],
                title: [
                    {
                        required: true,
                        validator: validateTitle,
                        trigger: "blur",
                    },
                ],
                price: [
                    {
                        required: true,
                        message: "请输入商品价格",
                        trigger: "blur",
                    },
                ],
                start_time: [
                    {
                        required: true,
                        message: "请选择推荐开始时间",
                        trigger: "blur",
                    },
                ],
                end_time: [
                    {
                        required: true,
                        message: "请选择推荐结束时间",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    watch: {
        visible(val) {
            if (val) {
                this.showDetail = false;
                if (this.isAdd) {
                    this.ruleForm.period_id = this.rowData.period_id;
                    this.ruleForm.status = this.rowData.status;
                    this.ruleForm.start_time = this.rowData.start_time;
                    this.ruleForm.end_time = this.rowData.end_time;
                    this.search();
                }
                console.log("---visible", this.rowData, this.isAdd, this.title);
            }
        },
    },
    methods: {
        async search(type = 0) {
            if (!this.ruleForm.period_id) {
                this.$message.warning("请输入期数");
                return;
            }
            let res = await this.$request.article.articleList({
                periods: this.ruleForm.period_id,
            });
            if (res.data.error_code == 0) {
                this.ruleForm.title =
                    this.isAdd && !type
                        ? this.rowData.title
                        : res.data.data?.list[0]?.title;
                this.ruleForm.price = res.data.data?.list[0]?.price || "";
                this.ruleForm.period_type =
                    res.data.data?.list[0]?.periods_type;
                this.showDetail = true;
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        ...this.ruleForm,
                        genre: Number(this.activeName),
                    };
                    data.period_id = Number(this.ruleForm.period_id);
                    data.start_time =
                        new Date(this.ruleForm.start_time).getTime() / 1000;
                    data.end_time =
                        new Date(this.ruleForm.end_time).getTime() / 1000;
                    if (this.isAdd) {
                        data.id = this.rowData.id;
                    }
                    this.$request.article[
                        this.isAdd ? "updateGoodsPool" : "addGoodsPool"
                    ](data).then((res) => {
                        if (res.data.error_code == 0) {
                            this.$message.success(
                                `${this.isAdd ? "编辑" : "新增"}成功`
                            );
                            this.closeDialog();
                        }
                    });
                } else {
                    return false;
                }
            });
        },
    },
};
</script>

<style></style>
