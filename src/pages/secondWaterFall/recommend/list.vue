<template>
    <div>
        <el-card shadow="never">
            <el-input
                v-model="query.period_id"
                placeholder="期数"
                size="mini"
                class="w-normal m-r-10"
                clearable
            ></el-input>
            <el-input
                v-model="query.title"
                placeholder="标题"
                size="mini"
                class="w-large m-r-10"
                clearable
            ></el-input>
            <el-select
                v-model="query.status"
                placeholder="请选择"
                size="mini"
                class="m-r-10"
                clearable
            >
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
            <el-button size="mini" type="primary" @click="search"
                >查询</el-button
            >
            <el-button size="mini" type="success" @click="add">新增</el-button>
        </el-card>
        <el-card shadow="never" class="m-t-20">
            <el-table
                :data="tableData"
                border
                size="small"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="标题" prop="title" min-width="100">
                    <template slot-scope="scope">
                        <el-link
                            @click="$viewPcGoods(scope.row.period_id)"
                            type="primary"
                            >{{ scope.row.title }}</el-link
                        >
                    </template>
                </el-table-column>
                <el-table-column label="期数" prop="period_id" width="200">
                </el-table-column>
                <el-table-column label="时间" prop="create_time" width="230">
                    <template slot-scope="scope">
                        <div>创建：{{ scope.row.create_time }}</div>
                        <div>开始：{{ scope.row.start_time }}</div>
                        <div>结束：{{ scope.row.end_time }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="200">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            @click="edit(scope.row)"
                            class="m-r-10"
                            >编辑</el-button
                        >
                        <el-popconfirm
                            title="确认修改该条数据的状态吗？"
                            @confirm="
                                updateStatus(
                                    scope.row,
                                    scope.row.status == 1 ? 2 : 1
                                )
                            "
                        >
                            <el-button
                                type="text"
                                size="mini"
                                slot="reference"
                                v-if="scope.row.status != 2"
                                style="color: #f56c6c"
                                >禁用</el-button
                            >
                            <el-button
                                type="text"
                                size="mini"
                                slot="reference"
                                v-if="scope.row.status != 1"
                                style="color: #67c23a"
                                >开启</el-button
                            >
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="text-align: center">
            <el-pagination
                background
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <Update
            :visible.sync="dialogVisible"
            :rowData="rowData"
            :isAdd="isAdd"
            :title="title"
            :activeName="activeName"
            @getList="goodsPoolList"
        />
    </div>
</template>

<script>
import Update from "./update.vue";
export default {
    components: {
        Update,
    },
    props: ["activeName"],
    data() {
        return {
            isAdd: 0,
            title: "新增推荐产品",
            dialogVisible: false,
            rowData: {},
            query: {
                page: 1,
                limit: 10,
                period_id: "",
                title: "",
                status: "",
            },
            total: 0,
            options: [
                {
                    value: 2,
                    label: "禁用",
                },
                {
                    value: 1,
                    label: "开启",
                },
            ],
            tableData: [],
        };
    },
    watch: {
        activeName: {
            handler(newVal, oldVal) {
                console.log("genre", newVal, oldVal);
                this.goodsPoolList();
            },
            immediate: true,
        },
    },
    // mounted() {
    //     this.goodsPoolList();
    // },
    methods: {
        open(row) {
            window.open(
                `${this.$BASE.PCDomain}/pages/goods-detail/goods-detail?id=${row.period_id}`
            );
        },
        search() {
            this.query.page = 1;
            this.goodsPoolList();
        },
        async goodsPoolList() {
            let data = {
                ...this.query,
                genre: this.activeName,
            };
            let res = await this.$request.article.goodsPoolList(data);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        async updateStatus(row, status) {
            let data = {
                id: row.id,
                period_id: row.period_id, //期数
                title: row.title, //自定义标题
                genre: row.genre, //类型(1新品池,2运营池)
                period_type: row.period_type, //频道
                status, //1启用,2禁用
                start_time: new Date(row.start_time).getTime() / 1000, //推荐开始时间(时间戳)
                end_time: new Date(row.end_time).getTime() / 1000, //推荐结束时间
            };
            let res = await this.$request.article.updateGoodsPool(data);
            if (res.data.error_code == 0) {
                this.$message.success("更新成功");
                this.goodsPoolList();
            }
        },
        add() {
            this.isAdd = 0;
            this.title = "新增推荐产品";
            this.dialogVisible = true;
        },
        edit(row) {
            this.rowData = row;
            this.isAdd = 1;
            this.title = "编辑推荐产品";
            this.dialogVisible = true;
        },
        handleSizeChange(limit) {
            this.query.limit = limit;
            this.query.page = 1;
            this.goodsPoolList();
        },
        handleCurrentChange(page) {
            this.query.page = page;
            this.goodsPoolList();
        },
    },
};
</script>

<style></style>
