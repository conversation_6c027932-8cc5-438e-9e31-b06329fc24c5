<template>
    <div>
        <el-button type="primary" @click="onAdd">新增</el-button>
        <el-table border :data="list" style="margin-top: 20px">
            <el-table-column label="code" prop="code" align="center">
            </el-table-column>
            <el-table-column label="名称" prop="name" align="center">
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button
                        type="primary"
                        size="small"
                        @click="onEdit(scope.row)"
                        >编辑</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-row type="flex" justify="center" style="margin-top: 20px">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </el-row>

        <el-dialog :visible.sync="dialogVisible" fullscreen class="flow-dialog">
            <el-form slot="title" size="small">
                <el-form-item label="名称">
                    <el-row>
                        <el-col :span="6">
                            <el-input v-model="flow.name"></el-input>
                        </el-col>
                    </el-row>
                </el-form-item>
            </el-form>
            <div class="flow-dialog__body">
                <ApprovalFlow :node="flow.node"></ApprovalFlow>
            </div>
            <div slot="footer">
                <el-button type="primary" @click="onSave">保存</el-button>
            </div>
        </el-dialog>
        <!-- <ApprovalFlow :node="flow.node"></ApprovalFlow>
        <el-button type="primary" @click="onSave">保存</el-button> -->
    </div>
</template>

<script>
import ApprovalFlow from "@/components/approvalFlow";
import approvalFlowApi from "../services/approvalFlow";
export default {
    components: {
        ApprovalFlow,
    },
    data: () => ({
        query: {
            page: 1,
            limit: 10,
        },
        list: [],
        total: 0,
        dialogVisible: false,
        flow: {
            name: "",
            node: {
                id: `${Date.now()}`,
                name: "办理人",
                next: null,
                children: null,
                condition: null,
                mode: "Handle",
                approval_mode: 1,
                process_info: [],
                participant: [],
                approval_notice: [],
                denied_notice: [],
                process_node_ids: [],
                participant_node_ids: [],
            },
        },
    }),
    methods: {
        load() {
            approvalFlowApi.searchFlowList(this.query).then((res) => {
                if (res.data.error_code == 0) {
                    this.list = res.data.data.list || [];
                    this.total = res.data.data.total;
                }
            });
        },
        onEdit(row) {
            this.flow = { ...row };
            this.dialogVisible = true;
        },
        onAdd() {
            this.flow = this.$options.data().flow;
            this.dialogVisible = true;
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
        onSave() {
            approvalFlowApi[this.flow.code ? "updateFlow" : "createFlow"](
                this.flow
            ).then((res) => {
                if (res.data.error_code == 0) {
                    this.$message.success("操作成功");
                    this.dialogVisible = false;
                    this.load();
                }
            });
        },
    },
    created() {
        this.load();
    },
};
</script>

<style lang="scss" scoped>
.flow-dialog {
    /deep/ .el-dialog {
        background: #f8f8f8;
        &__body {
            padding: 0;
            display: flex;
            justify-content: center;
        }

        &__footer {
            padding: 10px;
        }
    }

    &__body {
        max-width: 100%;
        overflow: scroll;
    }
}
</style>
