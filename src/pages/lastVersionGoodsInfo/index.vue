<template>
    <div>
        <el-input
            class="w-normal"
            placeholder="请输入期数"
            v-model="periods"
            @keyup.enter.native="search"
            clearable
        ></el-input>
        <el-button
            class="m-l-10"
            :disabled="!periods"
            @click="search"
            type="primary"
            >查询</el-button
        >
        <hr />
        <div v-if="list.length">
            <el-card
                class="box-card"
                v-for="(item, index) in list"
                :key="index"
            >
                <div slot="header" class="clearfix">
                    <span>{{ item.name }}</span>
                </div>
                <div class="means">
                    <!-- <div>
                    <b>类别</b><span></span>
                </div> -->
                    <div>
                        <b>类型：</b
                        ><span>{{
                            item.type_name ? item.type_name : "-"
                        }}</span>
                    </div>
                    <div>
                        <b>酒庄：</b
                        ><span>{{
                            item.wine_name ? item.wine_name : "-"
                        }}</span>
                    </div>
                    <div>
                        <b>产区：</b
                        ><span>{{
                            item.area_name ? item.area_name : "-"
                        }}</span>
                    </div>
                    <div>
                        <b>国家：</b
                        ><span
                            >{{
                                item.country_ename ? item.country_ename : "-"
                            }}/{{
                                item.country_name ? item.country_name : "-"
                            }}</span
                        >
                    </div>
                </div>
                <div class="means">
                    <div>
                        <b>葡萄：</b
                        ><span>{{
                            item.grape_name ? item.grape_name : "-"
                        }}</span>
                    </div>
                    <div>
                        <b>残糖：</b
                        ><span>{{
                            item.residual_sugar ? item.residual_sugar : "-"
                        }}</span>
                    </div>
                    <div>
                        <b>酒精度：</b
                        ><span>{{
                            item.alcohol_content ? item.alcohol_content : "-"
                        }}</span>
                    </div>
                    <div>
                        <b>容量：</b
                        ><span>{{ item.capacity ? item.capacity : "-" }}</span>
                    </div>
                </div>
                <div class="means">
                    <div>
                        <b>关键词：</b
                        ><span>{{ item.keywords ? item.keywords : "-" }}</span>
                    </div>
                </div>
                <div class="means">
                    <div>
                        <b>酿造工艺：</b><br /><span>{{
                            item.brewing ? item.brewing : "-"
                        }}</span>
                    </div>
                </div>

                <div class="means">
                    <div>
                        <b>TastingNotes：</b><br /><span>{{
                            item.tasting_notes ? item.tasting_notes : "-"
                        }}</span>
                    </div>
                </div>
                <div class="means">
                    <div>
                        <b>评分：</b><br /><span>{{
                            item.score ? item.score : "-"
                        }}</span>
                    </div>
                </div>
                <div class="means">
                    <div>
                        <b>获奖：</b><br /><span>{{
                            item.prize ? item.prize : "-"
                        }}</span>
                    </div>
                </div>
                <div class="means">
                    <div>
                        <b>饮用建议：</b><br /><span>{{
                            item.drinking_suggestion
                                ? item.drinking_suggestion
                                : "-"
                        }}</span>
                    </div>
                </div>
            </el-card>
        </div>
        <el-empty v-else description="暂无数据"></el-empty>
    </div>
</template>

<script>
export default {
    data() {
        return {
            periods: "",
            list: [],
        };
    },
    methods: {
        async search() {
            if (!this.periods) {
                this.$message.warning("请输入正确的期数后查询");
                return;
            }
            this.list = [];
            const data = {
                period: this.periods,
            };
            const res = await this.$request.purchase.getLastVersionGoodsInfo(
                data
            );

            if (res.data.error_code === 0) {
                this.list = res.data.data;
                console.log(res.data);
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.box-card {
    // display: inline;
    margin-bottom: 10px;
    .clearfix {
        span {
            font-size: 18px;
        }
    }
    .means {
        margin-bottom: 20px;
        display: flex;
        & > div {
            margin-right: 20px;
            b {
                margin-right: 4px;
                font-weight: bold;
                font-size: 16px;
            }
            span {
                font-size: 14px;
            }
        }
    }
}
</style>
