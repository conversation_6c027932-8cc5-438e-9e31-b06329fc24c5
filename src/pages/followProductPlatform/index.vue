<template>
    <div>
        <el-form inline>
            <el-form-item>
                <el-input
                    v-model="query.short_code"
                    placeholder="请输入简码"
                    clearable
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.period"
                    placeholder="请输入期数"
                    clearable
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="operation_id"
                    placeholder="请选择运营"
                    clearable
                    multiple
                >
                    <el-option
                        v-for="item in operationOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="buyer_id"
                    placeholder="请选择采购"
                    clearable
                    multiple
                >
                    <el-option
                        v-for="item in purchaseList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="query.supplier_id"
                    placeholder="请输入供应商"
                    clearable
                    filterable
                    remote
                    :remote-method="supplierRemoteMethod"
                    :loading="supplierRemoteMethodLoading"
                >
                    <el-option
                        v-for="item in supplierOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="query.chateau_id"
                    placeholder="请输入酒庄"
                    clearable
                    filterable
                    remote
                    :remote-method="chateauRemoteMethod"
                    :loading="chateauRemoteMethodLoading"
                >
                    <el-option
                        v-for="item in chateauOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="query.is_sale"
                    placeholder="是否在售"
                    clearable
                >
                    <el-option
                        v-for="item in [
                            { label: '在售', value: 1 },
                            { label: '下架', value: 0 },
                        ]"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="query.is_slippage"
                    placeholder="是否已逾期"
                    clearable
                >
                    <el-option
                        v-for="item in [
                            { label: '未逾期', value: 0 },
                            { label: '已逾期', value: 1 },
                        ]"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-date-picker
                    v-model="completionTime"
                    type="daterange"
                    start-placeholder="计划期限-开始时间"
                    end-placeholder="计划期限-结束时间"
                    value-format="yyyy-MM-dd"
                    clearable
                ></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-date-picker
                    v-model="createTime"
                    type="daterange"
                    start-placeholder="创建时间-开始时间"
                    end-placeholder="创建时间-结束时间"
                    value-format="yyyy-MM-dd"
                    clearable
                ></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="success" @click="reload">查询</el-button>
                <el-button type="primary" @click="onAdd">添加</el-button>
                <el-button type="warning" @click="importDialogVisible = true"
                    >导入</el-button
                >
                <el-button type="primary" @click="onExport">导出</el-button>
            </el-form-item>
        </el-form>

        <el-table border :data="list" @sort-change="onSortChange">
            <el-table-column type="expand">
                <template slot-scope="scope">
                    <el-form
                        label-position="left"
                        inline
                        class="demo-table-expand"
                    >
                        <el-form-item label="国家&类型">
                            <span> {{ scope.row.country_name_cn || "-" }}</span>
                            <span>
                                {{ scope.row.product_type_name || "-" }}</span
                            >
                        </el-form-item>
                        <el-form-item label="酒庄">
                            <span> {{ scope.row.winery_name_cn || "-" }}</span>
                        </el-form-item>
                        <el-form-item label="供应商">
                            <span> {{ scope.row.supplier || "-" }}</span>
                        </el-form-item>
                        <el-form-item label="创建时间">
                            <span>
                                {{ scope.row.created_time }}
                            </span>
                        </el-form-item>
                        <el-form-item label="往期期数">
                            <span
                                v-for="item in scope.row.period"
                                :key="item.period"
                                class="mgr-10"
                            >
                                <el-tag
                                    size="mini"
                                    :type="
                                        item.$onsaleStatusText === '在售'
                                            ? 'danger'
                                            : 'info'
                                    "
                                    effect="dark"
                                    @click="onCopy(item.period)"
                                >
                                    {{ item.period }}
                                    {{ item.is_channel ? "| 渠道" : "" }}
                                </el-tag>
                            </span>
                        </el-form-item>
                    </el-form>
                </template>
            </el-table-column>
            <el-table-column align="center" label="简码" width="110">
                <template slot-scope="scope">
                    <el-link
                        :underline="false"
                        @click="onCopy(scope.row.short_code)"
                    >
                        {{ scope.row.short_code }}
                    </el-link>
                    <br />
                    <el-tag :type="scope.row.$importTypeBtnType" size="mini">{{
                        scope.row.$importTypeText
                    }}</el-tag>

                    <el-tag
                        type="danger"
                        size="mini"
                        v-if="scope.row.period[0].$onsaleStatusText === '在售'"
                    >
                        {{ scope.row.period[0].$onsaleStatusText || "" }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column align="center" label="运营" width="100">
                <template slot-scope="scope">
                    <div>采购：{{ scope.row.buyer_name || "-" }}</div>
                    <div>运营：{{ scope.row.operation_name || "-" }}</div>
                </template>
            </el-table-column>
            <el-table-column align="left" label="品名" min-width="300">
                <template slot-scope="scope">
                    <div>
                        {{ scope.row.en_product_name || "-" }}
                    </div>
                    <div>
                        {{ scope.row.cn_product_name || "-" }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                align="right"
                label="成本"
                width="80"
                prop="costprice"
            >
                <template slot-scope="scope">
                    ￥{{ formatNumber(Number(scope.row.costprice)) }}
                </template>
            </el-table-column>
            <el-table-column align="center" label="售价" width="80">
                <template slot-scope="scope">
                    <div
                        v-for="(item, index) in scope.row.package"
                        :key="index"
                    >
                        {{ formatCny(Number(item.price)) }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="近3天销售"
                width="100"
                prop="three_day_sale_num"
                sortable="custom"
            >
            </el-table-column>
            <el-table-column
                align="center"
                label="近3天销售"
                width="100"
                prop="three_day_sale_num"
                sortable="custom"
            >
            </el-table-column>
            <el-table-column
                align="center"
                label="计划完成"
                sortable="custom"
                prop="planned_completion_time"
                width="106"
            >
                <template slot-scope="scope">
                    <div :style="scope.row.$plannedCompletionTimeStyle">
                        <div>{{ scope.row.planned_completion_time }}</div>
                        <div>{{ scope.row.$plannedCompletionTimeText }}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                align="right"
                label="进度"
                prop="planned_progress"
                sortable="custom"
                width="70"
            >
                <template slot-scope="scope">
                    {{ scope.row.planned_progress }}%
                </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="入库时间"
                width="90"
                sortable="custom"
                prop="storage_time"
            >
                <template slot-scope="scope">
                    {{ formatDate(scope.row.storage_time) }}
                </template>
            </el-table-column>
            <el-table-column
                align="right"
                label="采购量"
                prop="purchase_nums"
                sortable="custom"
                width="78"
            >
                <template slot-scope="scope">
                    {{ formatNumber(Number(scope.row.purchase_nums)) }}
                </template>
            </el-table-column>
            <el-table-column
                align="right"
                label="已售"
                prop="real_sale_num"
                sortable="custom"
                width="60"
            >
                <template slot-scope="scope">
                    {{ formatNumber(Number(scope.row.real_sale_num)) }}
                </template>
            </el-table-column>
            <el-table-column
                align="right"
                label="库存"
                prop="my_inventory"
                sortable="custom"
                width="60"
            >
                <template slot-scope="scope">
                    {{ formatNumber(Number(scope.row.my_inventory)) }}
                </template>
            </el-table-column>
            <el-table-column
                align="right"
                label="库存金额"
                prop="inventory_amount"
                sortable="custom"
                width="90"
            >
                <template slot-scope="scope">
                    ￥{{ formatNumber(Number(scope.row.inventory_amount)) }}
                </template>
            </el-table-column>
            <el-table-column align="left" label="备注" min-width="300">
                <template slot-scope="scope">
                    <div
                        v-for="(item, index) in scope.row.remark"
                        :key="item.id"
                        style="text-align: left"
                    >
                        {{ index + 1 }}、{{ item.remark }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                fixed="right"
                align="center"
                label="操作"
                width="90"
                class="btn-box"
            >
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        @click="onEdit(scope.row)"
                        >编辑</el-button
                    >
                    <el-button
                        size="mini"
                        type="text"
                        @click="onRemove(scope.row)"
                        >移除</el-button
                    >
                    <el-button
                        size="mini"
                        type="text"
                        @click="onLookRemark(scope.row)"
                        >备注</el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <el-row style="margin-top: 20px" type="flex" justify="center">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </el-row>

        <ImportDialog
            v-if="importDialogVisible"
            :visible.sync="importDialogVisible"
            @load="load"
        ></ImportDialog>
        <FollowProductDetailDialog
            v-if="followProductDetailDialogVisible"
            :visible.sync="followProductDetailDialogVisible"
            :followProductDetail="followProductDetail"
            @load="load"
        >
        </FollowProductDetailDialog>
        <RemarkListDialog
            v-if="remarkListDialogVisible"
            :visible.sync="remarkListDialogVisible"
            :id="id"
            @load="load"
        >
        </RemarkListDialog>
    </div>
</template>

<script>
import fileDownload from "js-file-download";
import FollowProductDetailDialog from "@/components/followProductPlatform/FollowProductDetailDialog";
import ImportDialog from "@/components/followProductPlatform/ImportDialog";
import RemarkListDialog from "@/components/followProductPlatform/RemarkListDialog";
import followProductPlatformApi from "@/services/followProductPlatform";
import copy from "copy-to-clipboard";

export default {
    components: {
        FollowProductDetailDialog,
        ImportDialog,
        RemarkListDialog,
    },
    data: () => ({
        query: {
            page: 1,
            limit: 10,
            short_code: "",
            period: "",
            supplier_id: "",
            is_sale: "",
            chateau_id: "",
            is_slippage: "",
            start_completion_time: "",
            start_created_time: "",
            end_created_time: "",
            end_completion_time: "",
            end_created_time: "",
            start_created_time: "",
            sort_field: "",
            sort_order: "",
            operation_id:"",
            buyer_id:"",
        },
        list: [],
        total: 0,
        operationOptions: [],
        supplierOptions: [],
        supplierRemoteMethodLoading: false,
        chateauOptions: [],
        chateauRemoteMethodLoading: false,
        followProductDetailDialogVisible: false,
        followProductDetail: null,
        importDialogVisible: false,
        remarkListDialogVisible: false,
        id: 0,
        buyer_id:[],
        operation_id: [],
        purchaseList:[],
    }),
    computed: {
        createTime: {
            get() {
                if (
                    !this.query.start_created_time ||
                    !this.query.end_created_time
                ) {
                    return "";
                }
                return [
                    this.query.start_created_time,
                    this.query.end_created_time,
                ];
            },
            set(range) {
                const [stime = "", etime = ""] = range || [];
                this.query.start_created_time = stime;
                this.query.end_created_time = etime;
            },
        },
        completionTime: {
            get() {
                if (
                    !this.query.start_completion_time ||
                    !this.query.end_completion_time
                ) {
                    return "";
                }
                return [
                    this.query.start_completion_time,
                    this.query.end_completion_time,
                ];
            },
            set(range) {
                const [stime = "", etime = ""] = range || [];
                this.query.start_completion_time = stime;
                this.query.end_completion_time = etime;
            },
        },
    },
    created() {
        this.initOperationOptions();
        this.initPurchaseOptions();
        this.load();
    },
    methods: {
        load() {
            this.query.operation_id = this.operation_id.join(",");
            this.query.buyer_id = this.buyer_id.join(",");
            
            followProductPlatformApi
                .searchFollowList(this.query)
                .then((res) => {
                    if (res.data.error_code == 0) {
                        const { list, total } = res.data.data;
                        list.forEach((item) => {
                            item.$importTypeBtnType =
                                { 0: "primary", 1: "success", 2: "warning" }[
                                    item.import_type
                                ] || "";
                            item.$importTypeText =
                                { 0: "自采", 1: "地采", 2: "跨境" }[
                                    item.import_type
                                ] || "";
                            item.period?.forEach((item) => {
                                item.$periodTypeText =
                                    {
                                        0: "闪购",
                                        1: "秒发",
                                        2: "跨境",
                                        3: "尾货",
                                    }[item.periods_type] || "";
                                item.$onsaleStatusBtnType =
                                    item.onsale_status === 2
                                        ? "danger"
                                        : "info";
                                item.$onsaleStatusText =
                                    { 2: "在售" }[item.onsale_status] || "下架";
                            });
                            item.$plannedCompletionTimeText = "";
                            item.$plannedCompletionTimeStyle = {};
                            switch (item.status) {
                                case 0:
                                case 1:
                                    item.$plannedCompletionTimeText = `（剩余${item.days}天）`;
                                    item.$plannedCompletionTimeStyle = {
                                        color: "rgb(0, 0, 0)",
                                    };
                                    break;
                                case 2:
                                    item.$plannedCompletionTimeText = `（逾期${item.days}天）`;
                                    item.$plannedCompletionTimeStyle = {
                                        color: "rgb(189, 49, 36)",
                                    };
                                    break;
                                case 3:
                                    item.$plannedCompletionTimeText = `（逾期${item.days}天）`;
                                    item.$plannedCompletionTimeStyle = {
                                        color: "rgb(129, 179, 55)",
                                    };
                                    break;
                            }
                        });
                        this.list = list;
                        this.total = total;
                    }
                });
        },
        reload() {
            this.query.page = 1;
            this.load();
        },
        formatDate(date) {
            if (!date) {
                return "-";
            }
            let y = new Date(date).getFullYear();
            let m = new Date(date).getMonth() + 1;
            let d = new Date(date).getDate();

            return `${y}-${m}-${d}`;
        },
        formatCny(value) {
            const config = {
                style: "currency",
                currency: "CNY",
            };
            return value.toLocaleString("zh-CN", config);
        },
        formatNumber(value) {
            return value.toLocaleString();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
        onSortChange(e) {
            console.log("onSortChange", e);
            const { prop, order } = e;
            let sort_field = "";
            let sort_order = "";
            switch (order) {
                case "ascending":
                    sort_field = prop;
                    sort_order = "asc";
                    break;
                case "descending":
                    sort_field = prop;
                    sort_order = "desc";
                    break;
            }
            this.query = Object.assign({}, this.query, {
                sort_field,
                sort_order,
            });
            this.reload();
        },
        onAdd() {
            this.followProductDetail = this.$options.data().followProductDetail;
            this.followProductDetailDialogVisible = true;
        },
        onEdit(row) {
            const {
                id,
                short_code,
                purchase_nums,
                operation_id,
                operation_name,
                planned_completion_time,
            } = row;
            this.followProductDetail = {
                id,
                short_code,
                purchase_nums,
                operation_id,
                operation_name,
                planned_completion_time,
            };
            this.followProductDetailDialogVisible = true;
        },
        onRemove(row) {
            this.$confirm(
                `您是否确认要取消跟进此产品“${row.short_code}”吗？`,
                "提示",
                {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }
            )
                .then(() => {
                    followProductPlatformApi
                        .removeFollowItem({ id: row.id })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("移除成功");
                                this.load();
                            }
                        });
                })
                .catch(() => {});
        },
        onLookRemark(row) {
            this.id = row.id;
            this.remarkListDialogVisible = true;
        },
        initOperationOptions() {
            this.$request.article
                .purchaseList({
                    type: 14,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        const { list = [] } = res.data.data;
                        this.operationOptions = list.map(
                            ({ id, realname }) => ({
                                value: id,
                                label: realname,
                            })
                        );
                    }
                });
        },
        initPurchaseOptions() {
            this.$request.article
                .purchaseList({
                    type: 2,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        const { list = [] } = res.data.data;
                        this.purchaseList = list.map(
                            ({ id, realname }) => ({
                                value: id,
                                label: realname,
                            })
                        );
                    }
                });
        },
        supplierRemoteMethod(query) {
            if (query !== "") {
                this.supplierRemoteMethodLoading = true;
                this.$request.article
                    .supplierList({ page: 1, limit: 10, keyword: query })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.supplierOptions = res.data.data.list.map(
                                (item) => ({
                                    value: item.id,
                                    label: item.supplier_name,
                                })
                            );
                            this.supplierRemoteMethodLoading = false;
                        }
                    });
            } else {
                this.supplierOptions = [];
            }
        },
        chateauRemoteMethod(query) {
            if (query !== "") {
                this.chateauOptions = true;
                this.$request.product
                    .getWineryList({ page: 1, limit: 10, keyword: query })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.chateauOptions = res.data.data.list.map(
                                (item) => ({
                                    value: item.id,
                                    label: item.winery_name_cn,
                                })
                            );
                            this.chateauRemoteMethodLoading = false;
                        }
                    });
            } else {
                this.chateauOptions = [];
            }
        },
        onExport() {
            followProductPlatformApi
                .exportSalesDashboardFrom(this.query)
                .then((res) => {
                    console.log(res.data);
                    this.$message.success("导出成功");
                    fileDownload(res.data, "产品销售跟进关注看板.xlsx");
                });
        },
        onCopy(data) {
            copy(data);
            this.$message.success("复制成功");
        },
    },
};
</script>

<style lang="scss" scoped>
/deep/ .el-table .el-table__cell {
    padding: 0 !important;
}

/deep/ .el-table {
    font-size: 11px !important;
}

/deep/ .el-link {
    font-size: 12px !important;
}

/deep/ .demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    min-width: 20%;
    margin-left: 10px;
}

/deep/ .demo-table-expand label {
    color: #99a9bf;
}
/deep/ .el-table .caret-wrapper {
    width: 14px !important;
}
</style>
