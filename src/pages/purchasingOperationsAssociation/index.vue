<template>
  <div class="purchasing-operations">
    <!-- <h2>采购运营关联配置管理</h2> -->
    
    <div class="content-wrapper">
      <!-- 左侧运营人员列表 -->
      <div class="operator-list">
        <div class="list-header">
          <h3>运营人员列表</h3>
          <el-button type="primary" size="small" @click="handleAddOperator">添加运营人员</el-button>
        </div>
        <div class="list-content">
          <template v-if="operatorList.length">
            <div
              v-for="item in operatorList"
              :key="item.id"
              class="operator-item"
              :class="{ active: selectedOperator && selectedOperator.id === item.id }"
            >
              <div class="operator-info" @click="handleSelectOperator(item)">
                <span>{{ item.operation_review_name }}</span>
                <span class="buyer-count">{{ (item.buyer && item.buyer.length) || 0 }}</span>
              </div>
              <el-button 
                type="danger" 
                size="mini"
                plain
                class="delete-btn"
                @click="handleDeleteOperator(item)"
              >
                删除
              </el-button>
            </div>
          </template>
          <div v-else class="empty-tip">暂无运营人员</div>
        </div>
      </div>

      <!-- 右侧关联信息 -->
      <div class="relation-info">
        <!-- 关联的采购人员 -->
        <div class="buyer-list">
          <div class="list-header">
            <h3>关联的采购人员</h3>
            <el-button type="primary" size="small" @click="handleAddBuyer">添加采购人员</el-button>
          </div>
          <div v-if="!selectedOperator" class="empty-tip">请先选择左侧的运营人员</div>
          <el-table  height="400" v-else :data="selectedOperator.buyer || []" border>
            <el-table-column prop="buyer_name" label="姓名" />
            <el-table-column prop="created_time" label="创建时间" />
            <el-table-column label="操作" width="120">
              <template slot-scope="{ row }">
                <el-button 
                  type="danger"
                  size="mini"
                  @click="handleUnlinkBuyer(row)"
                >
                  解除关联
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 关联变更记录 -->
        <div class="relation-log">
          <h3>关联变更记录</h3>
          <div v-if="!selectedOperator" class="empty-tip">请先选择左侧的运营人员</div>
          <el-table  height="600" v-else :data="selectedOperator.log || []" border>
            <el-table-column prop="buyer_name" label="采购人员" />
            <el-table-column label="操作类型">
              <template slot-scope="{ row }">
                {{ row.type === 1 ? '添加关联' : '解除关联' }}
              </template>
            </el-table-column>
            <el-table-column prop="created_time" label="操作时间" />
            <el-table-column prop="vh_vos_name" label="操作人" />
          </el-table>
        </div>
      </div>
    </div>

    <!-- 添加运营人员弹窗 -->
    <el-dialog
      title="添加运营人员"
      :visible.sync="addOperatorDialogVisible"
      width="400px"
      @close="handleAddOperatorDialogClose"
    >
      <el-form :model="addOperatorForm" ref="addOperatorForm" label-width="100px">
        <el-form-item label="运营人员" prop="operation_review_id">
          <el-select
            v-model="addOperatorForm.operation_review_id"
            placeholder="请选择运营人员"
            style="width: 100%"
            @change="handleOperatorChange"
          >
            <el-option
              v-for="item in operationOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addOperatorDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitAddOperator">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 添加采购人员弹窗 -->
    <el-dialog
      title="添加采购人员"
      :visible.sync="addBuyerDialogVisible"
      width="500px"
      @close="handleAddBuyerDialogClose"
    >
      <div class="buyer-dialog-content">
        <h4>采购人员列表</h4>
        <div class="buyer-list-container">
          <el-checkbox-group v-model="selectedBuyers">
            <el-checkbox
              v-for="item in options"
              :key="item.id"
              :label="item.id"
            >
              {{ item.realname }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="buyer-tip">勾选要关联的采购人员</div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addBuyerDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAddBuyer">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'PurchasingOperationsAssociation',
  
  data() {
    return {
      operatorList: [],
      selectedOperator: null,
      currentPage: 1,
      pageSize: 100,
      total: 0,
      operationOptions:[],
      addOperatorDialogVisible: false,
      addOperatorForm: {
        operation_review_id: '',
        operation_review_name: ''
      },
      options:[],
      addBuyerDialogVisible: false,
      selectedBuyers: [],
    }
  },

  created() {
    this.fetchOperatorList();
    this.initOperationOptions();
  },

  methods: {
    // 获取运营人员列表并保持选中状态
    async fetchOperatorList(keepSelected = false) {
      try {
        const res = await this.$request.product.getOperateRelatedList({
          page: this.currentPage,
          limit: this.pageSize
        })
        if (res.data.error_code === 0) {
          this.operatorList = res.data.data.list
          this.total = res.data.data.total
          
          // 如果需要保持选中状态且之前有选中的运营人员
          if (keepSelected && this.selectedOperator) {
            // 在新数据中查找之前选中的运营人员
            const updatedOperator = this.operatorList.find(
              item => item.id === this.selectedOperator.id
            )
            // 如果找到则更新选中状态
            if (updatedOperator) {
              this.selectedOperator = updatedOperator
            }
          }
        }
      } catch (error) {
        console.error(error)
      }
    },
    initOperationOptions() {
            this.$request.article
                .purchaseList({
                    type: 14,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        const { list = [] } = res.data.data;
                        this.operationOptions = list.map(
                            ({ id, realname }) => ({
                                value: id,
                                label: realname,
                            })
                        );
                    }
                });
        },
        getPurchase() {
            this.$request.article
                .purchaseList({
                    type: 2,
                })
                .then((res) => {
                    // 获取所有采购人员列表
                    const allBuyers = res.data.data.list;
                    
                    // 获取所有运营人员已关联的采购人员ID列表
                    const linkedBuyerIds = this.operatorList.reduce((ids, operator) => {
                        if (operator.buyer && operator.buyer.length) {
                            operator.buyer.forEach(buyer => {
                                ids.push(buyer.buyer_id);
                            });
                        }
                        return ids;
                    }, []);
                    
                    // 过滤掉所有已关联的采购人员
                    this.options = allBuyers.filter(buyer => !linkedBuyerIds.includes(buyer.id));
                });
        },
    // 选择运营人员
    handleSelectOperator(operator) {
      this.selectedOperator = operator
    },

    // 打开添加运营人员弹窗
    handleAddOperator() {
      this.addOperatorDialogVisible = true
    },

    // 关闭弹窗时重置表单
    handleAddOperatorDialogClose() {
      this.addOperatorForm = {
        operation_review_id: '',
        operation_review_name: ''
      }
    },

    // 选择运营人员时设置名称
    handleOperatorChange(value) {
      const selected = this.operationOptions.find(item => item.value === value)
      if (selected) {
        this.addOperatorForm.operation_review_name = selected.label
      }
    },

    // 提交添加运营人员
    async submitAddOperator() {
      try {
        const res = await this.$request.product.addOperateRelated(this.addOperatorForm)
        if (res.data.error_code === 0) {
          this.$message.success('添加成功')
          this.addOperatorDialogVisible = false
          await this.fetchOperatorList()
        } 
      } catch (error) {
        
        console.error(error)
      }
    },

    // 打开添加采购人员弹窗
    handleAddBuyer() {
      if (!this.selectedOperator) {
        this.$message.warning('请先选择运营人员')
        return
      }
      this.getPurchase()
      this.addBuyerDialogVisible = true
    },

    // 关闭弹窗时重置选择
    handleAddBuyerDialogClose() {
      this.selectedBuyers = []
    },

    // 提交添加采购人员
    async submitAddBuyer() {
      if (this.selectedBuyers.length === 0) {
        this.$message.warning('请选择要添加的采购人员')
        return
      }

      const params = {
        operation_review_id: this.selectedOperator.operation_review_id,
        operation_review_name: this.selectedOperator.operation_review_name,
        buyer: this.selectedBuyers.map(id => {
          const buyer = this.options.find(item => item.id === id)
          return {
            buyer_id: buyer.id,
            buyer_name: buyer.realname
          }
        })
      }

      try {
        const res = await this.$request.product.addBuyerRelated(params)
        if (res.data.error_code === 0) {
          this.$message.success('添加成功')
          this.addBuyerDialogVisible = false
          await this.fetchOperatorList(true)
        } 
      } catch (error) {
        
        console.error(error)
      }
    },

    // 解除采购人员关联
    handleUnlinkBuyer(buyer) {
      this.$confirm(
        `确定要解除与采购人员 "${buyer.buyer_name}" 的关联吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          const res = await this.$request.product.delBuyerRelatedLog({
            id: buyer.id
          })
          if (res.data.error_code === 0) {
            this.$message.success('解除关联成功')
            await this.fetchOperatorList(true)
          }
        } catch (error) {
          console.error(error)
        }
      }).catch(() => {})
    },

    // 删除运营人员
    handleDeleteOperator(operator) {
      this.$confirm(
        `确定要删除运营人员 "${operator.operation_review_name}" 吗？相关的采购人员关联和历史记录也将被删除。`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          const res = await this.$request.product.deleteOperateRelated({
            id: operator.id
          })
          if (res.data.error_code === 0) {
            this.$message.success('删除成功')
            // 如果删除的是当前选中的运营人员，清空选中状态
            if (this.selectedOperator && this.selectedOperator.id === operator.id) {
              this.selectedOperator = null
            }
            await this.fetchOperatorList()
          }
        } catch (error) {
          console.error(error)
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.purchasing-operations {
  padding: 20px;

  .content-wrapper {
    display: flex;
    gap: 20px;
    margin-top: 20px;
  }

  .operator-list {
    width: 300px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;

    .list-header {
      padding: 12px;
      border-bottom: 1px solid #dcdfe6;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
      }
    }

    .operator-item {
      padding: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      border-bottom: 1px solid #f0f0f0;

      &:hover {
        background-color: #f5f7fa;
      }

      &.active {
        background-color: #e6f1fc;
      }

      .operator-info {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 10px;
        cursor: pointer;

        .buyer-count {
          background-color:#1C94FA;
          color: white;
          padding: 2px 8px;
          border-radius: 10px;
          font-size: 12px;
        }
      }

      .delete-btn {
        height: 28px;
        padding: 0 10px;
      }
    }
  }

  .relation-info {
    flex: 1;

    .buyer-list {
      margin-bottom: 20px;
      
      .list-header {
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
        }
      }
    }

    .relation-log {
      h3 {
        margin-bottom: 16px;
      }
     
    }
  }
}

.buyer-dialog-content {
  padding: 0 20px;

  h4 {
    margin: 0 0 15px;
    font-size: 14px;
  }

  .buyer-list-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    padding: 10px;

    .el-checkbox {
      display: block;
      margin-right: 0;
      padding: 8px 0;
      border-bottom: 1px solid #EBEEF5;

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .buyer-tip {
    margin-top: 10px;
    color: #909399;
    font-size: 12px;
  }
}

.danger-text {
  color: #F56C6C;
  
  &:hover {
    color: #f78989;
  }
}

.empty-tip {
  padding: 30px;
  text-align: center;
  color: #909399;
  font-size: 14px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
</style>

