export default [
    {
        label: "事项类型",
        prop: "current_node_name",
        minWidth: "140",
        isCustom: true,
    },
    {
        label: "期数",
        prop: "id",
        minWidth: "100",
    },
    {
        label: "SKU_ID",
        prop: "$item.sku_id",
        minWidth: "100",
    },
    {
        label: "酒款名(英文+中文)",
        prop: "$item.en_product_name",
        minWidth: "500",
        isCustom: true,
    },
    {
        label: "频道",
        prop: "purchase.periods_type",
        minWidth: "100",
        isToText: true,
        toTextMapper: "MGoodsTypeText",
    },
    {
        label: "进口类型",
        prop: "purchase.import_type",
        minWidth: "100",
        isToText: true,
        toTextMapper: "MImportTypeText",
    },
    {
        label: "简码",
        prop: "$item.short_code",
        minWidth: "150",
    },
    {
        label: "条码",
        prop: "$item.bar_code",
        minWidth: "200",
    },
    {
        label: "类型",
        prop: "$item.product_type_name",
        minWidth: "150",
    },
    {
        label: "进口商",
        prop: "$item.supplier",
        minWidth: "250",
    },
    {
        label: "国家",
        prop: "$item.country",
        minWidth: "100",
    },
    {
        label: "产区",
        prop: "$item.producing_area_name",
        minWidth: "250",
    },
    {
        label: "收款公司",
        prop: "purchase.payee_merchant_name",
        minWidth: "250",
    },
    {
        label: "规格",
        prop: "$item.capacity",
        minWidth: "100",
    },
    {
        label: "是否带礼盒发货",
        prop: "purchase.is_gift_box",
        minWidth: "100",
        isToText: true,
        toTextMapper: "MIsGiftBoxText2",
    },
    {
        label: "成本",
        prop: "$item.costprice",
        minWidth: "100",
    },
    {
        label: "售卖价格",
        prop: "$item.price",
        minWidth: "100",
    },
    {
        label: "售卖数量",
        prop: "$item.num",
        minWidth: "100",
    },
    {
        label: "小计",
        prop: "$item.$subtotal",
        minWidth: "100",
    },
    {
        label: "利润率",
        prop: "$item.$profitRate",
        minWidth: "100",
    },
    {
        label: "实际库存",
        prop: "$item.actual_num",
        minWidth: "100",
    },
    {
        label: "采购",
        prop: "purchase.purchase_name",
        minWidth: "100",
    },
    {
        label: "文案",
        prop: "document_distribution.creator_name",
        minWidth: "100",
    },
    {
        label: "是否已经上传关单卫检",
        prop: "$item.has_attachment",
        minWidth: "100",
        isCustom: true,
    },
    {
        label: "发货条件",
        prop: "purchase.shipping_conditions",
        minWidth: "100",
        isToText: true,
        toTextMapper: "MShippingConditionsText",
    },
    {
        label: "存储条件",
        prop: "purchase.storage_conditions",
        minWidth: "100",
        isToText: true,
        toTextMapper: "MStorageConditionsText",
    },
    {
        label: "是否代发",
        prop: "purchase.is_supplier_delivery",
        minWidth: "100",
        isToText: true,
        toTextMapper: "MIsSupplierDeliveryText2",
    },
    {
        label: "发货时效",
        prop: "purchase.delivery_time_limit",
        minWidth: "100",
        isToText: true,
        toTextMapper: "MDeliveryTimeLimitText",
    },
    {
        label: "生产日期",
        prop: "$item.produce_date",
        minWidth: "100",
    },
    {
        label: "保质期",
        prop: "$item.shelf_life",
        minWidth: "100",
        isCustom: true,
    },
    {
        label: "采购备注",
        prop: "purchase.purchase_remake",
        minWidth: "100",
    },
    {
        label: "卖点介绍",
        prop: "purchase.selling_point",
        minWidth: "100",
    },
    {
        label: "是否作图",
        prop: "purchase.is_design",
        minWidth: "100",
        isToText: true,
        toTextMapper: "MIsDesignText",
    },
    {
        label: "期待上架日期",
        prop: "purchase.expect_time_text",
        minWidth: "200",
    },
];
