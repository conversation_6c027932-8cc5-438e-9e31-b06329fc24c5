<template>
    <div>
        <GoodsPoolQuery :query="query" @reload="reload">
            <TableHeaderConfPopover
                :list.sync="colConfigList"
                class="mgl-10"
            ></TableHeaderConfPopover>
        </GoodsPoolQuery>
        <el-table
            :data="list"
            border
            :span-method="arraySpanMethod"
            class="goods-pool-table"
        >
            <template
                v-for="(item, index) in colConfigList.filter(
                    (item) => item.switch
                )"
            >
                <template v-if="item.isCustom || item.isToText">
                    <el-table-column
                        :key="`${item.prop}-${index}`"
                        :label="item.label"
                        :prop="item.prop"
                        :min-width="item.minWidth"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <template v-if="item.isToText">
                                {{
                                    getToTextValue(scope.row, item.prop)
                                        | toText(item.toTextMapper)
                                }}
                            </template>
                            <template
                                v-else-if="item.prop === 'current_node_name'"
                            >
                                <el-button
                                    type="text"
                                    style="white-space: normal"
                                    @click="viewEventRecords(scope.row)"
                                    >{{
                                        scope.row.current_node_name
                                    }}</el-button
                                >
                                <br />
                                <el-tag
                                    v-if="scope.row.$isReject"
                                    type="danger"
                                    size="mini"
                                    >已驳回</el-tag
                                >
                            </template>
                            <template
                                v-else-if="
                                    item.prop === '$item.en_product_name'
                                "
                            >
                                <div>{{ scope.row.$item.en_product_name }}</div>
                                <div>{{ scope.row.$item.product_name }}</div>
                            </template>
                            <template
                                v-else-if="item.prop === '$item.has_attachment'"
                            >
                                {{
                                    scope.row.$item.has_attachment ? "是" : "否"
                                }}
                            </template>
                            <template
                                v-else-if="item.prop === '$item.shelf_life'"
                            >
                                {{ scope.row.$item.shelf_life
                                }}{{ scope.row.$item.shelf_life ? "天" : "" }}
                            </template>
                        </template>
                    </el-table-column>
                </template>
                <el-table-column
                    v-else
                    :key="`${item.prop}-${index}`"
                    :label="item.label"
                    :prop="item.prop"
                    :min-width="item.minWidth"
                    align="center"
                ></el-table-column>
            </template>
            <el-table-column
                label="操作"
                align="center"
                min-width="100"
                prop="$lastCol"
                fixed="right"
            >
                <template slot-scope="scope">
                    <!-- <el-button type="text" @click="remove(scope.row)"
                        >删除</el-button
                    > -->
                    <div
                        v-for="(item, index) in getPermissionList(scope.row)"
                        :key="index"
                    >
                        <el-button
                            type="text"
                            @click="handleBtnClick(scope.row, item)"
                            >{{ item.text }}</el-button
                        >
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-row type="flex" justify="center" class="mgt-20">
            <el-pagination
                background
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </el-row>

        <PurchaseInfoDialog
            :visible.sync="purchaseInfoDialogVisible"
            :row="currRow"
            @load="load"
        />
        <CopyWriterAllocDialog
            :visible.sync="copyWriterAllocDialogVisible"
            :row="currRow"
            @load="load"
        />
        <GoodsInfoDialog
            ref="goodsInfoDialogRef"
            :visible.sync="goodsInfoDialogVisible"
            :row="currRow"
            @load="load"
        />
        <DesignAllocDialog
            ref="designAllocDialogRef"
            :visible.sync="designAllocDialogVisible"
            :row="currRow"
            @load="load"
        />
        <DesignAllocUpdateDialog
            :visible.sync="designAllocUpdateDialogVisible"
            :row="currRow"
            @load="load"
        />
        <GoodsInfoPreviewDialog
            ref="goodsInfoPreviewDialogRef"
            :visible.sync="goodsInfoPreviewDialogVisible"
            :row="currRow"
            @load="load"
        />
        <CertificateDialog
            ref="certificateDialogRef"
            :visible.sync="certificateDialogVisible"
            :row="currRow"
            @load="load"
        />
        <PurchaseCheckDialog
            :visible.sync="purchaseCheckDialogVisible"
            :row="currRow"
            @load="load"
        ></PurchaseCheckDialog>
        <OperationCheckDialog
            :visible.sync="operationCheckDialogVisible"
            :row="currRow"
            @load="load"
        ></OperationCheckDialog>
        <ResubmitDialog
            :visible.sync="resubmitDialogVisible"
            :row="currRow"
            @load="load"
        ></ResubmitDialog>
        <EventRecordsDialog
            :visible.sync="eventRecordsDialogVisible"
            :row="currRow"
        ></EventRecordsDialog>
    </div>
</template>

<script>
import { mapActions } from "vuex";
import GoodsPoolQuery from "@/components/goodsPool/GoodsPoolQuery";
import PurchaseInfoDialog from "@/components/goodsPool/PurchaseInfoDialog";
import CopyWriterAllocDialog from "@/components/goodsPool/CopyWriterAllocDialog";
import GoodsInfoDialog from "@/components/goodsPool/GoodsInfoDialog";
import DesignAllocDialog from "@/components/goodsPool/DesignAllocDialog";
import DesignAllocUpdateDialog from "@/components/goodsPool/DesignAllocUpdateDialog";
import GoodsInfoPreviewDialog from "@/components/goodsPool/GoodsInfoPreviewDialog";
import CertificateDialog from "@/components/goodsPool/CertificateDialog.vue";
import PurchaseCheckDialog from "@/components/goodsPool/PurchaseCheckDialog.vue";
import OperationCheckDialog from "@/components/goodsPool/OperationCheckDialog.vue";
import ResubmitDialog from "@/components/goodsPool/ResubmitDialog.vue";
import EventRecordsDialog from "@/components/goodsPool/EventRecordsDialog.vue";
import TableHeaderConfPopover from "@/components/goodsPool/TableHeaderConfPopover.vue";
import goodsPoolApi from "@/services/goodsPool";
import * as computedUtils from "@/components/goodsPool/computedUtils";
import colConfigList from "./colConfigList";
import { MGoodsType } from "@/utils/mapperModel";

const isDev = process.env.NODE_ENV == "development";
const COPY_WRITER_LEADER_ROLE_ID = isDev ? 38 : 77;
const DESIGN_LEADER_ROLE_ID = isDev ? 46 : 80;

export default {
    components: {
        GoodsPoolQuery,
        PurchaseInfoDialog,
        CopyWriterAllocDialog,
        GoodsInfoDialog,
        DesignAllocDialog,
        DesignAllocUpdateDialog,
        GoodsInfoPreviewDialog,
        CertificateDialog,
        PurchaseCheckDialog,
        OperationCheckDialog,
        ResubmitDialog,
        EventRecordsDialog,
        TableHeaderConfPopover,
    },
    data: () => ({
        query: {
            page: 1,
            limit: 10,
            id: "",
            sku_id: "",
            short_code: "",
            product_name: "",
            creator_uid: "",
            purchase_uid: "",
            periods_types: "",
            transaction_state: "",
        },
        list: [],
        total: 0,
        currRow: null,
        purchaseInfoDialogVisible: false,
        copyWriterAllocDialogVisible: false,
        goodsInfoDialogVisible: false,
        designAllocDialogVisible: false,
        designAllocUpdateDialogVisible: false,
        goodsInfoPreviewDialogVisible: false,
        certificateDialogVisible: false,
        purchaseCheckDialogVisible: false,
        operationCheckDialogVisible: false,
        resubmitDialogVisible: false,
        eventRecordsDialogVisible: false,
        colConfigList: [],
        weComId: 0,
        adminId: 0,
        roleIds: [],
    }),
    methods: {
        ...mapActions([
            "initCopyWriterOptions",
            "initPurchaseOptions",
            "initDesignOptions",
        ]),
        getToTextValue(obj, prop) {
            const props = prop.split(".");
            while (true) {
                const key = props.shift();
                if (!props.length) {
                    return obj[key];
                }
                obj = obj[key];
            }
        },
        load() {
            goodsPoolApi.searchGoodsPoolList(this.query).then((res) => {
                if (res.data.error_code == 0) {
                    const { list = [], total = 0 } = res.data.data;
                    const flatList = [];
                    list.forEach((listItem) => {
                        listItem.current_node_info.reverse();
                        listItem.current_node_info_list =
                            listItem.current_node_info;
                        const [
                            mainProcessNode = {},
                            certificate_current_node_info = {},
                        ] = listItem.current_node_info_list;
                        // if (
                        //     mainProcessNode.customer_id === "采购主管审核中" &&
                        //     Object.keys(certificate_current_node_info).length
                        // ) {
                        //     listItem.current_node_info =
                        //         certificate_current_node_info;
                        // } else {
                        //     listItem.current_node_info = mainProcessNode;
                        // }
                        listItem.current_node_info = mainProcessNode;
                        const {
                            current_node_info,
                            operation,
                            purchase,
                            picture_distribution,
                        } = listItem;
                        const current_node_id = current_node_info.id;
                        const current_node_name = current_node_info.name;
                        const current_node_customer_id =
                            current_node_info.customer_id;
                        listItem.current_node_id = current_node_id;
                        listItem.current_node_name = current_node_name;
                        listItem.current_node_customer_id =
                            current_node_customer_id;
                        const $apiBasicParams = {
                            instance_id: current_node_info.instance_id,
                            current_node_id,
                            current_node_name,
                            current_node_customer_id,
                        };
                        const $certificateApiBasicParams = {
                            instance_id:
                                certificate_current_node_info.instance_id,
                            current_node_id: certificate_current_node_info.id,
                            current_node_name:
                                certificate_current_node_info.name,
                            current_node_customer_id:
                                certificate_current_node_info.customer_id,
                        };
                        if (!current_node_customer_id) {
                            listItem.current_node_name = operation.status;
                            listItem.current_node_customer_id =
                                operation.status;
                        }
                        if (
                            purchase &&
                            !purchase.copy_periods_id &&
                            purchase.periods_type !== MGoodsType.MF &&
                            current_node_customer_id === "文案分配中"
                        ) {
                            const {
                                process_info = [],
                                participant = [],
                                is_denied = false,
                            } = current_node_info;
                            const isSomeProcessInfo =
                                process_info.length &&
                                process_info.some(
                                    (item) => item.we_com_id === this.weComId
                                );
                            const isSomeParticipant =
                                participant.length &&
                                participant.some(
                                    (item) => item.we_com_id === this.weComId
                                );
                            const {
                                design_title = "",
                                design_cn_highlights = "",
                                design_score = "",
                            } = picture_distribution || {};
                            const isAllocated =
                                design_title &&
                                design_cn_highlights &&
                                design_score;
                            if (isSomeParticipant && isSomeProcessInfo) {
                                if (isAllocated) {
                                    if (
                                        participant.some(
                                            (item) =>
                                                item.status === "Processing"
                                        ) &&
                                        !is_denied
                                    ) {
                                        listItem.$current_node_customer_id =
                                            "文案设计创作中";
                                    } else {
                                        listItem.$current_node_customer_id =
                                            "文案设计创作中(可提交、可设计提交)";
                                    }
                                } else {
                                    listItem.$current_node_customer_id =
                                        "文案分配中";
                                }
                            } else if (isSomeProcessInfo) {
                                if (isAllocated) {
                                    if (
                                        participant.some(
                                            (item) =>
                                                item.status === "Processing"
                                        ) &&
                                        !is_denied
                                    ) {
                                        listItem.$current_node_customer_id =
                                            "文案创作中";
                                    } else {
                                        listItem.$current_node_customer_id =
                                            "文案创作中(可提交)";
                                    }
                                } else {
                                    listItem.$current_node_customer_id =
                                        "文案分配中";
                                }
                            } else if (isSomeParticipant) {
                                if (isAllocated) {
                                    if (
                                        participant.some(
                                            (item) =>
                                                item.status === "Processing"
                                        ) &&
                                        !is_denied
                                    ) {
                                        listItem.$current_node_customer_id =
                                            "设计创作中";
                                    } else {
                                        listItem.$current_node_customer_id =
                                            "设计创作完成";
                                    }
                                } else {
                                    listItem.$current_node_customer_id =
                                        "设计分配中";
                                }
                            }
                        }
                        listItem.$isReject = current_node_info?.is_denied;
                        const itemsLength = listItem.items.length;
                        listItem.items.forEach((item, index) => {
                            item.$subtotal = computedUtils.getSubtotal(item);
                            item.$profitRate =
                                computedUtils.getProfitRate(item);
                            const targetItem = {
                                $item: item,
                                $apiBasicParams,
                                $certificateApiBasicParams,
                            };
                            if (itemsLength > 1) {
                                targetItem.columnSpan = index ? 0 : itemsLength;
                            }
                            flatList.push(
                                Object.assign({}, listItem, targetItem)
                            );
                        });
                    });
                    this.list = flatList;
                    this.total = total;
                }
            });
        },
        reload() {
            this.query.page = 1;
            this.load();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
        arraySpanMethod({ row, column, rowIndex, columnIndex }) {
            const colProp = column.property;
            const props = [
                "current_node_name",
                "id",
                "purchase.periods_type",
                "purchase.import_type",
                "purchase.payee_merchant_name",
                "purchase.is_gift_box",
                "purchase.purchase_name",
                "document_distribution.creator_name",
                "purchase.shipping_conditions",
                "purchase.storage_conditions",
                "purchase.is_supplier_delivery",
                "purchase.delivery_time_limit",
                "purchase.purchase_remake",
                "purchase.selling_point",
                "purchase.is_design",
                "purchase.expect_time",
                "$lastCol",
            ];
            if (props.includes(colProp)) {
                if (row.columnSpan !== undefined) {
                    if (row.columnSpan) {
                        return [row.columnSpan, 1];
                    } else {
                        return [0, 0];
                    }
                } else {
                    return [1, 1];
                }
            }
        },
        getPermissionList(row) {
            const {
                current_node_id = "",
                current_node_customer_id = "",
                current_node_info = {},
                purchase = {},
            } = row;
            const { process_info = [], participant = [] } =
                current_node_info || {};
            const {
                process_info: cerificate_process_info = [],
                participant: cerificate_participant = [],
            } = row.current_node_info_list[1] || {};
            if (!current_node_customer_id) return [];
            const mainProcessPermissionFlag =
                [...process_info, ...participant]
                    .filter((item) => item.genre === 1)
                    .some((item) => item.we_com_id === this.weComId) ||
                [...process_info, ...participant]
                    .filter((item) => item.genre === 2)
                    .some((item) => this.roleIds.includes(+item.role_id));
            const certificateProcessPermissionFlag =
                [...cerificate_process_info, ...cerificate_participant]
                    .filter((item) => item.genre === 1)
                    .some((item) => item.we_com_id === this.weComId) ||
                [...cerificate_process_info, ...cerificate_participant]
                    .filter((item) => item.genre === 2)
                    .some((item) => this.roleIds.includes(+item.role_id));
            const copyWriterLeaderFlag =
                ["文案分配中", "文案创作中"].includes(
                    current_node_customer_id
                ) && this.roleIds.includes(COPY_WRITER_LEADER_ROLE_ID);
            const { picture_distribution } = row;
            const {
                design_title = "",
                design_cn_highlights = "",
                design_score = "",
            } = picture_distribution || {};
            const isAllocated =
                design_title && design_cn_highlights && design_score;
            const designLeaderFlag =
                (!purchase.copy_periods_id &&
                    purchase.periods_type !== MGoodsType.MF &&
                    current_node_customer_id === "文案分配中" &&
                    isAllocated &&
                    ([...process_info]
                        .filter((item) => item.genre === 1)
                        .some((item) => item.we_com_id === this.weComId) ||
                        [...process_info]
                            .filter((item) => item.genre === 2)
                            .some((item) =>
                                this.roleIds.includes(+item.role_id)
                            ))) ||
                (!purchase.copy_periods_id &&
                    purchase.periods_type === MGoodsType.MF &&
                    current_node_customer_id === "设计创作中" &&
                    this.roleIds.includes(DESIGN_LEADER_ROLE_ID));
            if (
                current_node_id &&
                !mainProcessPermissionFlag &&
                !certificateProcessPermissionFlag &&
                !copyWriterLeaderFlag &&
                !designLeaderFlag
            )
                return this.getGeneralPermissionList(row);
            switch (current_node_customer_id) {
                case "未提交":
                    return [
                        {
                            text: "提交",
                            dialogVisibleKey: "purchaseInfoDialogVisible",
                        },
                        {
                            text: "删除",
                            func: () => {
                                this.remove(row);
                            },
                        },
                    ];
            }
            let permissionList = [];
            if (mainProcessPermissionFlag) {
                if (purchase.copy_periods_id) {
                    const list = this.getCopyPermissionList(row);
                    if (list.length) permissionList = list;
                } else if (purchase.periods_type === MGoodsType.MF) {
                    const list = this.getMFPermissionList(row);
                    if (list.length) permissionList = list;
                } else {
                    const list = this.getNotMFPermissionList(row);
                    if (list.length) permissionList = list;
                }
            }
            if (!purchase.copy_periods_id && copyWriterLeaderFlag) {
                permissionList.push({
                    text: "修改文案员",
                    dialogVisibleKey: "copyWriterAllocDialogVisible",
                });
            }
            if (designLeaderFlag) {
                permissionList.push({
                    text: "修改设计师",
                    dialogVisibleKey: "designAllocUpdateDialogVisible",
                });
            }
            let certificateList = [];
            if (certificateProcessPermissionFlag) {
                certificateList = this.getCertificatePermissionList(row);
            }
            permissionList.push(...certificateList);
            switch (current_node_customer_id) {
                case "运营确认上架中":
                    const OPERATION_ROLE_ID = 14;
                    if (this.roleIds.includes(OPERATION_ROLE_ID)) {
                        permissionList = [
                            {
                                text: "审核",
                                dialogVisibleKey: "operationCheckDialogVisible",
                            },
                        ];
                    }
                    break;
                case "运营通过":
                    permissionList = [];
                    break;
                case "运营未排期":
                    if (this.adminId === row.purchase.purchase_uid) {
                        permissionList = [
                            {
                                text: "重新提交",
                                dialogVisibleKey: "resubmitDialogVisible",
                            },
                        ];
                    }
                    break;
                case "运营延期":
                    if (this.adminId === row.purchase.purchase_uid) {
                        permissionList = [
                            {
                                text: "重新提交",
                                dialogVisibleKey: "resubmitDialogVisible",
                            },
                        ];
                    }
                    break;
            }
            const generalList = this.getGeneralPermissionList(row);
            permissionList.push(...generalList);
            return permissionList;
        },
        getCopyPermissionList(row) {
            const { current_node_customer_id } = row;
            switch (current_node_customer_id) {
                case "采购主管审核中":
                    return [
                        {
                            text: "预览",
                            func: () => {
                                this.$refs.goodsInfoPreviewDialogRef.currStatus = 0;
                                this.goodsInfoPreviewDialogVisible = true;
                            },
                        },
                        {
                            text: "审核",
                            dialogVisibleKey: "purchaseCheckDialogVisible",
                        },
                    ];
                default:
                    return [];
            }
        },
        getMFPermissionList(row) {
            const { current_node_customer_id, current_node_info } = row;
            switch (current_node_customer_id) {
                case "文案主管分配中":
                    return [
                        {
                            text: "分发",
                            dialogVisibleKey: "copyWriterAllocDialogVisible",
                        },
                    ];
                case "文案创作中":
                    return [
                        {
                            text: "编辑",
                            func: () => {
                                this.$refs.goodsInfoDialogRef.currStatus = 1;
                                this.goodsInfoDialogVisible = true;
                            },
                        },
                    ];
                case "文案主管审核中":
                    return [
                        {
                            text: "预览",
                            func: () => {
                                this.$refs.goodsInfoPreviewDialogRef.currStatus = 0;
                                this.goodsInfoPreviewDialogVisible = true;
                            },
                        },
                        {
                            text: "审核",
                            func: () => {
                                this.$refs.goodsInfoPreviewDialogRef.currStatus = 1;
                                this.goodsInfoPreviewDialogVisible = true;
                            },
                        },
                    ];
                case "采购审核中":
                    return [
                        {
                            text: "预览",
                            func: () => {
                                this.$refs.goodsInfoPreviewDialogRef.currStatus = 0;
                                this.goodsInfoPreviewDialogVisible = true;
                            },
                        },
                        {
                            text: "审核",
                            func: () => {
                                this.$refs.goodsInfoPreviewDialogRef.currStatus = 2;
                                this.goodsInfoPreviewDialogVisible = true;
                            },
                        },
                    ];
                case "设计创作中":
                    return [
                        {
                            text: "提交",
                            func: () => {
                                this.$refs.designAllocDialogRef.currStatus = 1;
                                this.designAllocDialogVisible = true;
                            },
                        },
                    ];
                case "采购主管审核中":
                    return [
                        {
                            text: "预览",
                            func: () => {
                                this.$refs.goodsInfoPreviewDialogRef.currStatus = 0;
                                this.goodsInfoPreviewDialogVisible = true;
                            },
                        },
                        {
                            text: "审核",
                            dialogVisibleKey: "purchaseCheckDialogVisible",
                        },
                    ];
                default:
                    return [];
            }
        },
        getNotMFPermissionList(row) {
            const { current_node_customer_id, $current_node_customer_id } = row;
            switch ($current_node_customer_id || current_node_customer_id) {
                case "文案主管分配中":
                    return [
                        {
                            text: "分发",
                            dialogVisibleKey: "copyWriterAllocDialogVisible",
                        },
                    ];
                case "文案分配中":
                    return [
                        {
                            text: "分发",
                            func: () => {
                                this.$refs.designAllocDialogRef.currStatus = 0;
                                this.designAllocDialogVisible = true;
                            },
                        },
                        {
                            text: "编辑",
                            func: () => {
                                this.$refs.goodsInfoDialogRef.currStatus = 0;
                                this.goodsInfoDialogVisible = true;
                            },
                        },
                    ];
                case "文案创作中":
                    return [
                        {
                            text: "编辑",
                            func: () => {
                                this.$refs.goodsInfoDialogRef.currStatus = 0;
                                this.goodsInfoDialogVisible = true;
                            },
                        },
                    ];
                case "文案创作中(可提交)":
                    return [
                        {
                            text: "编辑",
                            func: () => {
                                this.$refs.goodsInfoDialogRef.currStatus = 2;
                                this.goodsInfoDialogVisible = true;
                            },
                        },
                    ];
                case "设计创作中":
                    return [
                        {
                            text: "设计提交",
                            func: () => {
                                this.$refs.designAllocDialogRef.currStatus = 1;
                                this.designAllocDialogVisible = true;
                            },
                        },
                    ];
                case "设计创作完成":
                    return [
                        {
                            text: "查看设计",
                            func: () => {
                                this.$refs.designAllocDialogRef.currStatus = 2;
                                this.designAllocDialogVisible = true;
                            },
                        },
                    ];
                case "文案设计创作中":
                    return [
                        {
                            text: "编辑",
                            func: () => {
                                this.$refs.goodsInfoDialogRef.currStatus = 0;
                                this.goodsInfoDialogVisible = true;
                            },
                        },
                        {
                            text: "设计提交",
                            func: () => {
                                this.$refs.designAllocDialogRef.currStatus = 1;
                                this.designAllocDialogVisible = true;
                            },
                        },
                    ];
                case "文案设计创作中(可提交、可设计提交)":
                    return [
                        {
                            text: "编辑",
                            func: () => {
                                this.$refs.goodsInfoDialogRef.currStatus = 2;
                                this.goodsInfoDialogVisible = true;
                            },
                        },
                        {
                            text: "查看设计",
                            func: () => {
                                this.$refs.designAllocDialogRef.currStatus = 2;
                                this.designAllocDialogVisible = true;
                            },
                        },
                    ];
                case "文案主管审核中":
                    return [
                        {
                            text: "预览",
                            func: () => {
                                this.$refs.goodsInfoPreviewDialogRef.currStatus = 0;
                                this.goodsInfoPreviewDialogVisible = true;
                            },
                        },
                        {
                            text: "审核",
                            func: () => {
                                this.$refs.goodsInfoPreviewDialogRef.currStatus = 1;
                                this.goodsInfoPreviewDialogVisible = true;
                            },
                        },
                    ];
                case "采购审核中":
                    return [
                        {
                            text: "预览",
                            func: () => {
                                this.$refs.goodsInfoPreviewDialogRef.currStatus = 0;
                                this.goodsInfoPreviewDialogVisible = true;
                            },
                        },
                        {
                            text: "审核",
                            func: () => {
                                this.$refs.goodsInfoPreviewDialogRef.currStatus = 2;
                                this.goodsInfoPreviewDialogVisible = true;
                            },
                        },
                    ];
                case "采购主管审核中":
                    return [
                        {
                            text: "预览",
                            func: () => {
                                this.$refs.goodsInfoPreviewDialogRef.currStatus = 0;
                                this.goodsInfoPreviewDialogVisible = true;
                            },
                        },
                        {
                            text: "审核",
                            dialogVisibleKey: "purchaseCheckDialogVisible",
                        },
                    ];
                default:
                    return [];
            }
        },
        getGeneralPermissionList(row) {
            const list = [];
            if (row.purchase) {
                if (
                    (row.audit?.document?.status === 3 &&
                        row.audit?.purchase?.status === 3) ||
                    row.audit?.purchase_last?.status === 3
                ) {
                    list.push({
                        text: "更新文案",
                        func: () => {
                            this.$refs.goodsInfoDialogRef.currStatus = 3;
                            this.goodsInfoDialogVisible = true;
                        },
                    });
                }
                if (row?.audit?.purchase_exec?.status === 3) {
                    list.push({
                        text: "查看资质",
                        func: () => {
                            this.$refs.certificateDialogRef.currStatus = 2;
                            this.certificateDialogVisible = true;
                        },
                    });
                }
            }
            return list;
        },
        getCertificatePermissionList(row) {
            const { $certificateApiBasicParams } = row;
            const { current_node_customer_id } = $certificateApiBasicParams;
            switch (current_node_customer_id) {
                case "上传资质中":
                    return [
                        {
                            text: "上传",
                            func: () => {
                                this.$refs.certificateDialogRef.currStatus = 0;
                                this.certificateDialogVisible = true;
                            },
                        },
                    ];
                case "审核资质中":
                    return [
                        {
                            text: "审核资质",
                            func: () => {
                                this.$refs.certificateDialogRef.currStatus = 1;
                                this.certificateDialogVisible = true;
                            },
                        },
                    ];
                default:
                    return [];
            }
        },
        handleBtnClick(row, item) {
            this.currRow = row;
            const { dialogVisibleKey = "", func = "" } = item;
            if (dialogVisibleKey) {
                this[dialogVisibleKey] = true;
            } else if (func) {
                func();
            }
        },
        remove(row) {
            this.$confirm("删除后无法恢复，请再次确认", "删除确认", {
                confirmButtonText: "确定删除",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    goodsPoolApi
                        .deleteGoodsPoolItem({ id: row.id })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.load();
                                this.$message.success("删除成功");
                            }
                        });
                })
                .catch(() => {});
        },
        viewEventRecords(row) {
            this.currRow = row;
            this.eventRecordsDialogVisible = true;
        },
        getAdminInfo() {
            const userinfo = JSON.parse(
                localStorage.getItem("userinfo") || "{}"
            );
            const { dt_userid } = userinfo;
            if (!dt_userid) return;
            return goodsPoolApi
                .getAdminInfo({ admin_id: dt_userid, field: "id,roles" })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        const data = res.data.data;
                        const key = Object.keys(data)[0];
                        if (key) {
                            const { id, roles } = data[key];
                            this.weComId = dt_userid;
                            this.adminId = id;
                            this.roleIds = roles.map(({ id }) => id);
                        }
                    }
                });
        },
        initColConfigList() {
            let ccList = [];
            const goodsPoolTableColConf = JSON.parse(
                localStorage.getItem("goodsPoolTableColConf") || "[]"
            );
            if (goodsPoolTableColConf.length) {
                const list = colConfigList.map((item) => {
                    const findIndex = goodsPoolTableColConf.findIndex(
                        ({ label }) => item.label === label
                    );
                    if (findIndex !== -1) {
                        const findItem = goodsPoolTableColConf[findIndex];
                        return {
                            ...item,
                            switch: findItem.switch,
                        };
                    }
                    return { ...item, switch: true };
                });
                ccList = list;
            } else {
                ccList = colConfigList.map((item) => ({
                    ...item,
                    switch: true,
                }));
            }
            const ROLE_ID_LIST =
                process.env.NODE_ENV == "development"
                    ? [2, 39, 14, 1]
                    : [2, 78, 14, 1];
            this.colConfigList = ccList.filter((item) => {
                if (item.prop === "$item.costprice") {
                    return this.roleIds.some((roleId) =>
                        ROLE_ID_LIST.includes(roleId)
                    );
                } else {
                    return true;
                }
            });
        },
    },
    created() {
        Promise.all([this.getAdminInfo()]).finally(() => {
            this.initColConfigList();
            this.load();
        });
        this.initCopyWriterOptions();
        this.initPurchaseOptions();
        this.initDesignOptions();
    },
};
</script>

<style lang="scss" scoped>
.goods-pool-table {
    /deep/ .el-table__cell {
        padding: 5px 0;
    }
}
</style>
