<template>
    <div>
        <el-form inline>
            <el-form-item>
                <el-input
                    v-model="query.orderno"
                    placeholder="采购订单"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.supplier"
                    placeholder="供应商"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.short_code"
                    placeholder="简码"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.operator_name"
                    placeholder="采购员"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-date-picker
                    v-model="createdTime"
                    type="datetimerange"
                    start-placeholder="单据开始时间"
                    end-placeholder="单据结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.created_name"
                    placeholder="制单人"
                    clearable
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="query.payee_merchant_id"
                    placeholder="收款公司"
                    clearable
                >
                    <el-option
                        v-for="item in payeeMerchantOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="reload">查询</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="success" @click="createOrder">创建</el-button>
            </el-form-item>
        </el-form>
        <el-tabs v-model="query.query_type" @tab-click="reload">
            <el-tab-pane
                v-for="item in tabList"
                :key="item.name"
                :label="`${item.label}${statsInfo[item.key] || 0}`"
                :name="item.name"
            ></el-tab-pane>
        </el-tabs>
        <el-card
            v-for="item in list"
            :key="item.id"
            style="margin-bottom: 20px"
            v-show="loading"
        >
            <el-table :data="[{}]" border size="small">
                <el-table-column
                    align="center"
                    label="采购单号"
                    min-width="160"
                >
                    {{ item.orderno }}
                </el-table-column>
                <el-table-column align="center" label="单据日期" width="100">
                    {{ item.bill_date }}
                </el-table-column>
                <el-table-column align="center" label="供应商" min-width="160">
                    {{ item.supplier }}
                </el-table-column>
                <el-table-column align="center" label="入库仓" min-width="180">
                    {{ item.warehouse }}
                </el-table-column>
                <el-table-column align="center" label="采购员" width="60">
                    {{ item.operator_name }}
                </el-table-column>
                <el-table-column align="center" label="采购部门" width="80">
                    {{ item.department }}
                </el-table-column>
                <el-table-column
                    align="center"
                    label="收款公司"
                    min-width="160"
                >
                    {{ item.payee_merchant_name }}
                </el-table-column>
                <el-table-column align="center" label="付款方式" width="80">
                    {{ item.setttlement }}
                </el-table-column>
                <el-table-column
                    align="center"
                    label="下单备注"
                    min-width="140"
                >
                    {{ item.remark }}
                </el-table-column>
                <el-table-column
                    v-if="query.query_type == '6'"
                    align="center"
                    label="制单人"
                    width="80"
                >
                    {{ item.created_name }}
                </el-table-column>
            </el-table>
            <el-table
                :data="item.items"
                border
                size="small"
                :span-method="getArraySpanMethod(item.items.length)"
                :cell-style="columnStyle"
                :row-class-name="tableRowClassName"
            >
                <el-table-column label="下单明细">
                    <el-table-column
                        align="center"
                        prop="short_code"
                        label="简码"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="number"
                        label="下单数量"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="sale_nums"
                        label="售卖数量"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="purchased_nums"
                        label="已采数量"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="unshipped_num"
                        label="未发货数量"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="my_inventory"
                        label="萌牙库存（在库/在途）"
                    >
                        <template slot-scope="scope">
                            <div>
                                {{ scope.row.my_inventory }}/{{
                                    scope.row.transit_count
                                }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="period" label="期数">
                        <template slot-scope="scope">
                            <el-button
                                v-for="p in scope.row.period.split(',')"
                                :key="p"
                                type="text"
                                @click="
                                    () => {
                                        period = +p;
                                        goodsRemarkDialogVisible = true;
                                    }
                                "
                            >
                                {{ p }}
                            </el-button>
                            <span>{{
                                scope.row.period_info[0].onsale_status === 2
                                    ? " 在售"
                                    : " 非在售"
                            }}</span>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column
                        align="center"
                        prop="period_info"
                        label=""
                    > -->
                </el-table-column>
                <el-table-column align="center">
                    <el-row
                        v-if="query.query_type === '1'"
                        type="flex"
                        justify="center"
                        align="middle"
                    >
                        <div style="margin-right: 10px">
                            操作人: {{ item.created_name }}
                        </div>
                        <el-button
                            type="warning"
                            size="small"
                            @click="onConfirm(item, false)"
                            >查看详情</el-button
                        >
                    </el-row>
                    <template v-else>
                        <el-button
                            type="danger"
                            size="small"
                            @click="onDelay(item)"
                            >延期下单</el-button
                        >
                        <el-button
                            type="primary"
                            size="small"
                            @click="onConfirm(item)"
                            >确认下单</el-button
                        >
                        <el-button
                            v-if="query.query_type == '4'"
                            type="danger"
                            size="small"
                            @click="onCancle(item)"
                            >作废</el-button
                        >
                    </template>
                </el-table-column>
                <el-table-column></el-table-column>
                <el-table-column></el-table-column>
            </el-table>
            <div
                v-if="query.query_type === '4' && item.push_erp_refuse_reason"
                style="margin-top: 10px; color: red"
                v-html="
                    splitByAsterisk(
                        '拒绝理由：***' + item.push_erp_refuse_reason
                    )
                "
            ></div>
            <div
                v-if="query.query_type === '0' && item.push_erp_fail_reason"
                style="margin-top: 10px; color: red"
                v-html="splitByAsterisk(item.push_erp_fail_reason)"
            ></div>
        </el-card>
        <el-row type="flex" justify="center">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </el-row>

        <CreatePurchaseOrderDialog
            ref="createPurchaseOrderDialogRef"
            :visible.sync="createPurchaseOrderDialogVisible"
            :items="createPurchaseOrderItems"
            :annex="annex"
            @reload="load"
        ></CreatePurchaseOrderDialog>

        <GoodsRemarkDialog
            :visible.sync="goodsRemarkDialogVisible"
            :period="period"
        ></GoodsRemarkDialog>

        <DelayPurchaseOrderDialog
            :visible.sync="delayPurchaseOrderDialogVisible"
            :id="id"
        ></DelayPurchaseOrderDialog>
        <todayCaiGouCreateDialog
            ref="todayCaiGouCreateDialogRef"
            :visible.sync="todayCaiGouCreateDialogDialogVisible"
            @reload="load"
        ></todayCaiGouCreateDialog>
    </div>
</template>

<script>
import purchaseExecManageApi from "@/services/purchaseExecManage";
import CreatePurchaseOrderDialog from "@/pages/caiGouPerform/CreatePurchaseOrderDialog";
import GoodsRemarkDialog from "@/components/purchaseExecManage/GoodsRemarkDialog";
import DelayPurchaseOrderDialog from "@/components/purchaseExecManage/DelayPurchaseOrderDialog";
import todayCaiGouCreateDialog from "@/pages/caiGouPerform/todayCaiGouCreateDialog";
export default {
    components: {
        CreatePurchaseOrderDialog,
        GoodsRemarkDialog,
        DelayPurchaseOrderDialog,
        todayCaiGouCreateDialog,
    },

    data: () => ({
        loading: false,
        tabList: [
            {
                label: "下单待确认",
                name: "0",
                key: "unconfirmed_nums",
            },
            {
                label: "下单已确认",
                name: "1",
                key: "confirm_nums",
            },
            {
                label: "已拒绝订单",
                name: "4",
                key: "refuse_nums",
            },
            {
                label: "预下单采购单",
                name: "6",
                key: "preorder_count",
            },
        ],
        query: {
            page: 1,
            limit: 10,
            query_type: 0,
            orderno: "",
            supplier: "",
            short_code: "",
            created_name: "",
            payee_merchant_id: "",
            operator_name: "",
            start_bill_date: "",
            end_bill_date: "",
        },
        list: [],
        total: 0,
        statsInfo: {},
        createPurchaseOrderDialogVisible: false,
        createPurchaseOrderItems: [],
        goodsRemarkDialogVisible: false,
        period: 0,
        delayPurchaseOrderDialogVisible: false,
        id: 0,
        payeeMerchantOptions: [],
        annex: [],
        todayCaiGouCreateDialogDialogVisible: false,
    }),
    computed: {
        createdTime: {
            get() {
                if (!this.query.start_bill_date || !this.query.end_bill_date) {
                    return "";
                }
                return [this.query.start_bill_date, this.query.end_bill_date];
            },
            set(range) {
                const [stime = "", etime = ""] = range || [];
                this.query.start_bill_date = stime;
                this.query.end_bill_date = etime;
            },
        },
    },
    created() {
        this.load();
        this.$request.caiGouPerform
            .caiGouPerformpPyeeMerchantList()
            .then((res) => {
                if (res.data.error_code == 0) {
                    this.payeeMerchantOptions = res.data.data;
                }
            });
    },
    methods: {
        load() {
            purchaseExecManageApi
                .searchPurchaseOrderList(this.query)
                .then((res) => {
                    if (res.data.error_code == 0) {
                        const {
                            list,
                            total,
                            unconfirmed_nums = 0,
                            confirm_nums = 0,
                            refuse_nums = 0,
                            preorder_count = 0,
                        } = res.data.data;

                        this.list = list;
                        this.total = total;
                        this.statsInfo = {
                            unconfirmed_nums,
                            confirm_nums,
                            refuse_nums,
                            preorder_count,
                        };
                        this.loading = true;
                    }
                });
        },
        reload() {
            this.loading = false;
            this.query.page = 1;
            this.load();
        },
        createOrder() {
            this.todayCaiGouCreateDialogDialogVisible = true;
        },
        tableRowClassName({ row, rowIndex, columnIndex }) {
            console.log(row, rowIndex, columnIndex);
            if (row.period_info[0].is_yesterday) {
                return "success-row";
            }
            return "";
        },
        setClass({ row, column, rowIndex, columnIndex }) {
            //    console.log('-00--', columnIndex);
            if (row.period_info[0].is_yesterday && columnIndex != 9) {
                return "success-row";
            }
            return "";
        },
        columnStyle({ row, column, rowIndex, columnIndex }) {
            // 第一列的背景色
            if (columnIndex === 7) {
                return "background:#ffffff !important;";
            }
        },

        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
        splitByAsterisk(value) {
            if (!value) return "";
            return value.split("***").join("<br>");
        },
        getArraySpanMethod(cols) {
            return ({ row, column, rowIndex, columnIndex }) => {
                if (7 === columnIndex) {
                    return [cols, 3];
                } else if (8 === columnIndex) {
                    return [0, 0];
                }
            };
        },
        onDelay(row) {
            this.id = row.id;
            this.delayPurchaseOrderDialogVisible = true;
        },
        onCancle(row) {
            this.$confirm("确定作废?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    purchaseExecManageApi
                        .delayPurchaseOrder({ id: row.id })
                        .then((res) => {
                            if (res.data.error_code === 0) {
                                this.$message.success("操作成功");
                                this.load();
                            }
                        });
                })
                .catch(() => {
                    //几点取消的提示
                });
        },
        onConfirm(row, isUpdate = true) {
            const createPurchaseOrderDialogRef =
                this.$refs.createPurchaseOrderDialogRef;
            const {
                id,
                period,
                orderno,
                bill_date,
                warehouse,
                warehouse_code,
                supplier,
                supplier_code,
                department,
                department_code,
                operator_name,
                operator_code,
                setttlement,
                setttlement_code,
                remark,
            } = row;
            createPurchaseOrderDialogRef.supplierOptions = [
                { value: supplier_code, label: supplier },
            ];
            createPurchaseOrderDialogRef.departmentOptions = [
                { value: department_code, label: department },
            ];
            createPurchaseOrderDialogRef.model = Object.assign(
                {},
                createPurchaseOrderDialogRef.$options.data().model,
                {
                    id,
                    period,
                    orderno,
                    bill_date,
                    warehouse,
                    warehouse_code,
                    supplier,
                    supplier_code,
                    department,
                    department_code,
                    operator_name,
                    operator_code,
                    setttlement,
                    setttlement_code,
                    remark,
                    items: [],
                    statusMap: {
                        operate_status: row.operate_status,
                        status: row.status,
                        isUpdate,
                    },
                    // 添加收款公司信息
                    payee_merchant_id: row.payee_merchant_id,
                    payee_merchant_name: row.payee_merchant_name,
                }
            );
            // model 数据现在通过 modelData prop 传递，不需要手动设置
            // this.$nextTick(() => {
            //     createPurchaseOrderDialogRef.$refs.fileList = row.annex;
            // });
            this.$refs.createPurchaseOrderDialogRef.fileList = row.annex;
            this.createPurchaseOrderItems = row.items.map((item) => ({
                ...item,
            }));
            // this.annex = row.annex;
            // console.log('++++++++++', this.annex);
            createPurchaseOrderDialogRef.loadApproveList(row.corp_code);
            this.createPurchaseOrderDialogVisible = true;
        },
    },
};
</script>
<style>
.el-table .success-row {
    background: #ffa5a0;
    /* color:#fff; */
}
</style>
<style lang="scss" scoped></style>
