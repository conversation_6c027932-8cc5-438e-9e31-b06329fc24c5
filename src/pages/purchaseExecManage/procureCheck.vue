<template>
    <div>
        <el-form inline>
            <el-form-item>
                <el-input
                    v-model="query.orderno"
                    placeholder="采购订单"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.supplier"
                    placeholder="供应商"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.short_code"
                    placeholder="简码"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.operator_name"
                    placeholder="采购员"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-date-picker
                    v-model="createdTime"
                    type="datetimerange"
                    start-placeholder="单据开始时间"
                    end-placeholder="单据结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.created_name"
                    placeholder="制单人"
                    clearable
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="query.payee_merchant_id"
                    placeholder="收款公司"
                    clearable
                >
                    <el-option
                        v-for="item in payeeMerchantOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-checkbox v-model="isHidenDetail">隐藏明细</el-checkbox>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="reload">查询</el-button>
            </el-form-item>
        </el-form>
        <el-card
            v-for="item in list"
            :key="item.id"
            style="margin-bottom: 20px"
        >
            <el-table :data="[{}]" border size="small">
                <el-table-column
                    align="center"
                    label="采购单号"
                    min-width="160"
                >
                    {{ item.orderno }}
                </el-table-column>
                <el-table-column align="center" label="单据日期" width="100">
                    {{ item.bill_date }}
                </el-table-column>
                <el-table-column align="center" label="供应商" min-width="160">
                    {{ item.supplier }}
                </el-table-column>
                <el-table-column
                    align="center"
                    label="合同到期日期"
                    width="100"
                >
                    {{ item.contract_end }}
                </el-table-column>
                <el-table-column
                    align="center"
                    label="入库仓库"
                    min-width="180"
                >
                    {{ item.warehouse }}
                </el-table-column>
                <el-table-column align="center" label="采购员" width="60">
                    {{ item.operator_name }}
                </el-table-column>
                <el-table-column align="center" label="采购部门" width="80">
                    {{ item.department }}
                </el-table-column>
                <el-table-column
                    align="center"
                    label="收款公司"
                    min-width="160"
                >
                    {{ item.payee_merchant_name }}
                </el-table-column>
                <el-table-column align="center" label="结算方式" width="80">
                    {{ item.setttlement }}
                </el-table-column>
                <el-table-column
                    align="center"
                    label="下单备注"
                    min-width="140"
                >
                    {{ item.remark }}
                </el-table-column>
            </el-table>
            <el-table
                :data="item.items"
                border
                size="small"
                :span-method="getArraySpanMethod(item.items.length)"
                show-summary
                :summary-method="getSummaries"
                v-if="!isHidenDetail"
            >
                <el-table-column label="订单明细">
                    <el-table-column
                        align="center"
                        prop="billing_name"
                        label="中文名"
                        min-width="160"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="en_product_name"
                        label="英文名"
                        min-width="160"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="short_code"
                        label="简码"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="number"
                        label="下单数量"
                        width="50"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="sale_nums"
                        label="已售数量"
                        width="50"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="purchased_nums"
                        label="已采数量"
                        width="50"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="unshipped_num"
                        label="未发货数量"
                        width="60"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="remark"
                        label="备注信息"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="萌牙库存（在库/在途）"
                        width="100"
                    >
                        <template slot-scope="scope">
                            {{ scope.row.my_inventory }}/{{
                                scope.row.transit_count
                            }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="period"
                        label="期数"
                        width="80"
                    >
                        <template slot-scope="scope">
                            <el-button
                                v-for="p in scope.row.period.split(',')"
                                :key="p"
                                type="text"
                                @click="
                                    () => {
                                        period = +p;
                                        goodsRemarkDialogVisible = true;
                                    }
                                "
                            >
                                {{ p }}
                            </el-button>
                            <span>{{
                                scope.row.period_info[0].onsale_status === 2
                                    ? " 在售"
                                    : " 非在售"
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="price"
                        label="含税单价"
                        width="70"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="tax_rate"
                        label="税率"
                        width="70"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="total"
                        label="含税合计"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="tax_rate"
                        min-width="150"
                        label="毛利"
                    >
                        <template slot-scope="scope">
                            <div
                                v-for="(gm, gmIndex) in scope.row.gross_margin"
                                :key="gmIndex"
                            >
                                {{ gm.package_name }}：{{ gm.gross_margin }}%
                            </div>
                        </template>
                    </el-table-column>
                </el-table-column>
                <!-- <el-table-column align="center" label="附件">
                  
                </el-table-column> -->
                <el-table-column align="center">
                    <el-button
                        type="primary"
                        size="small"
                        @click="showFujian(item)"
                        >附件{{ item.annex.length }}</el-button
                    >
                    <el-button
                        type="primary"
                        size="small"
                        @click="onAgree(item)"
                        >通过审核</el-button
                    >
                    <el-button
                        type="danger"
                        size="small"
                        @click="
                            () => {
                                rejectParams = {
                                    id: item.id,
                                    reason: '',
                                    source: 0,
                                };
                                dialogVisible = true;
                            }
                        "
                        >驳回</el-button
                    >
                </el-table-column>
                <el-table-column></el-table-column>
            </el-table>
            <div
                v-if="item.push_erp_fail_reason"
                style="margin-top: 10px; color: red"
                v-html="splitByAsterisk(item.push_erp_fail_reason)"
            ></div>
        </el-card>
        <el-row type="flex" justify="center">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </el-row>

        <el-dialog title="驳回理由" :visible.sync="dialogVisible" width="30%">
            <el-input
                v-model="rejectParams.reason"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="请输入"
            ></el-input>
            <div slot="footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="onReject">确 定</el-button>
            </div>
        </el-dialog>
        <GoodsRemarkDialog
            :visible.sync="goodsRemarkDialogVisible"
            :period="period"
        ></GoodsRemarkDialog>

        <attachmentDialog
            ref="sattachmentDialogRef"
            :visible.sync="attachmentDialogShow"
            :id="id"
            @reload="load"
        ></attachmentDialog>
    </div>
</template>

<script>
import purchaseExecManageApi from "@/services/purchaseExecManage";
import GoodsRemarkDialog from "@/components/purchaseExecManage/GoodsRemarkDialog";
import attachmentDialog from "../caiGouPerform/attachmentDialog";
export default {
    components: {
        attachmentDialog,
        GoodsRemarkDialog,
    },
    data: () => ({
        query: {
            page: 1,
            limit: 10,
            query_type: 5,
            orderno: "",
            supplier: "",
            short_code: "",
            created_name: "",
            payee_merchant_id: "",
            operator_name: "",
            start_bill_date: "",
            end_bill_date: "",
        },
        list: [],
        total: 0,
        dialogVisible: false,
        rejectParams: {
            id: 0,
            reason: "",
            source: 0,
        },
        goodsRemarkDialogVisible: false,
        period: 0,
        payeeMerchantOptions: [],
        isHidenDetail: false,
        attachmentDialogShow: false,
        id: 0,
    }),
    computed: {
        createdTime: {
            get() {
                if (!this.query.start_bill_date || !this.query.end_bill_date) {
                    return "";
                }
                return [this.query.start_bill_date, this.query.end_bill_date];
            },
            set(range) {
                const [stime = "", etime = ""] = range || [];
                this.query.start_bill_date = stime;
                this.query.end_bill_date = etime;
            },
        },
    },
    created() {
        this.load();
        this.$request.caiGouPerform
            .caiGouPerformpPyeeMerchantList()
            .then((res) => {
                if (res.data.error_code == 0) {
                    this.payeeMerchantOptions = res.data.data;
                }
            });
    },
    methods: {
        load() {
            purchaseExecManageApi
                .searchPurchaseOrderList(this.query)
                .then((res) => {
                    if (res.data.error_code == 0) {
                        const { list, total } = res.data.data;
                        this.list = list;
                        this.total = total;
                    }
                });
        },
        reload() {
            this.query.page = 1;
            this.load();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = "总计";
                    return;
                }
                //column.property == 'zeroTotalPrice'只要换上你想汇总列的prop属性值就可以实现某一行汇总
                if (column.property == "number" || column.property == "total") {
                    const values = data.map((item) =>
                        Number(item[column.property])
                    );
                    if (!values.every((value) => isNaN(value))) {
                        sums[index] = values.reduce((prev, curr) => {
                            const value = Number(curr);
                            if (!isNaN(value)) {
                                return (Number(prev) + curr).toFixed(2);
                            } else {
                                return prev;
                            }
                        }, 0);
                    }
                }
            });
            return sums;
        },

        getArraySpanMethod(cols) {
            return ({ row, column, rowIndex, columnIndex }) => {
                if (columnIndex >= 14) {
                    return !rowIndex && columnIndex === 14 ? [cols, 2] : [0, 0];
                }
            };
        },
        onAgree(item) {
            this.$confirm("确定通过审核吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    purchaseExecManageApi
                        .agreePurchaseOrder({ id: item.id, source: 0 })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("操作成功");
                                this.load();
                            }
                        });
                })
                .catch(() => {});
        },
        showFujian(item) {
            // console.log(item.annex);
            this.$refs.sattachmentDialogRef.fileList = Array.from(item.annex);
            this.id = item.id;
            this.attachmentDialogShow = true;
        },
        onReject() {
            if (!this.rejectParams.reason) {
                this.$message.error("请输入驳回理由");
                return;
            }
            purchaseExecManageApi
                .rejectPurchaseOrder(this.rejectParams)
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功");
                        this.load();
                        this.dialogVisible = false;
                    }
                });
        },
        splitByAsterisk(value) {
            if (!value) return "";
            return value.split("***").join("<br>");
        },
    },
};
</script>

<style lang="scss" scoped></style>
