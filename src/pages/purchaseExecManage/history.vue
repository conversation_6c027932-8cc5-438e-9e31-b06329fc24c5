<template>
    <div>
        <el-form inline>
            <el-form-item>
                <el-input
                    v-model="query.orderno"
                    placeholder="采购订单"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.supplier"
                    placeholder="供应商"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.short_code"
                    placeholder="简码"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="query.status"
                    placeholder="ERP审核状态"
                    clearable
                >
                    <el-option
                        v-for="item in [
                            { value: 0, text: '自由(缺省)' },
                            { value: 1, text: '未用' },
                            { value: 2, text: '正在审批' },
                            { value: 3, text: '审批通过' },
                            { value: 4, text: '审批未通过' },
                            { value: 5, text: '输出' },
                            { value: 6, text: '冻结' },
                            { value: 7, text: '执行完毕' },
                        ]"
                        :key="item.value"
                        :label="item.text"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="query.push_erp_status"
                    placeholder="中台审核状态"
                    clearable
                >
                    <el-option
                        v-for="item in [
                            { value: 0, text: '待确认' },
                            { value: 1, text: '下单已确认' },
                            { value: 2, text: '下单失败' },
                            { value: 3, text: '已驳回' },
                            { value: 4, text: '采购审核中' },
                            { value: 5, text: '财务审核中' },
                            { value: 6, text: '预下单' },
                            { value: 7, text: '自动下单' },
                            { value: 8, text: '延期下单' },
                            { value: 9, text: '作废' },
                            { value: 10, text: '财务主管审核' },
                        ]"
                        :key="item.value"
                        :label="item.text"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.warehouse"
                    placeholder="入库仓库"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-date-picker
                    v-model="createdTime"
                    type="datetimerange"
                    start-placeholder="创建开始时间"
                    end-placeholder="创建结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.created_name"
                    placeholder="制单人"
                    clearable
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.waybill_no"
                    placeholder="运单号"
                    clearable
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="query.payee_merchant_id"
                    placeholder="收款公司"
                    clearable
                >
                    <el-option
                        v-for="item in payeeMerchantOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="reload">查询</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="list" border>
            <el-table-column align="center" prop="orderno" label="采购订单">
            </el-table-column>
            <el-table-column align="center" label="ERP审核状态">
                <template slot-scope="scope">
                    {{ scope.row.status | toErpStatusText }}
                </template>
            </el-table-column>
            <el-table-column align="center" label="中台审核状态">
                <template slot-scope="scope">
                    {{ scope.row.push_erp_status | toZhongTaiStatusText }}
                </template>
            </el-table-column>
            <el-table-column align="center" prop="supplier" label="供应商">
            </el-table-column>
            <el-table-column
                align="center"
                prop="created_time"
                label="创建时间"
            >
            </el-table-column>
            <el-table-column align="center" prop="created_name" label="制单人">
            </el-table-column>
            <el-table-column align="center" prop="operator_name" label="申请人">
            </el-table-column>
            <el-table-column align="center" prop="waybill" label="运单号">
                <template slot-scope="scope">
                    <el-link
                        type="primary"
                        @click="searchLogistics(item)"
                        v-for="(item, index) in scope.row.waybill"
                        :key="index"
                        >{{ item.waybill_no }}</el-link
                    >
                </template>
            </el-table-column>
            <el-table-column
                align="center"
                prop="my_entry_status"
                label="萌牙入库状态"
            >
            </el-table-column>
            <el-table-column align="center" prop="prop" label="操作">
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        @click="onViewPurchaseOrder(scope.row)"
                        >详情</el-button
                    >
                    <el-button
                        type="text"
                        v-if="scope.row.push_erp_status == 1"
                        @click="uploadTrackingNumber(scope.row)"
                        >上传运单号</el-button
                    >
                    <el-button
                        type="text"
                        v-show="
                            scope.row.push_erp_status == 1 &&
                            scope.row.corp_code == '032'
                        "
                        @click="showPaymentDetail(scope.row)"
                        >付款</el-button
                    >
                    <el-button
                        type="text"
                        v-show="
                            scope.row.push_erp_status == 1 &&
                            scope.row.corp_code == '032'
                        "
                        @click="pushWms(scope.row)"
                        >推送萌牙</el-button
                    >
                    <el-button
                        type="text"
                        v-if="
                            scope.row.push_erp_status == 4 ||
                            scope.row.push_erp_status == 5
                        "
                        @click="takeBack(scope.row)"
                        >收回</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-row type="flex" justify="center" style="margin-top: 20px">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </el-row>

        <CreatePurchaseOrderDialog
            ref="createPurchaseOrderDialogRef"
            :visible.sync="createPurchaseOrderDialogVisible"
            :items="createPurchaseOrderItems"
            :approve="createPurchaseOrderApprove"
            @reload="load"
        ></CreatePurchaseOrderDialog>

        <el-dialog
            title="上传运单号"
            :visible.sync="uploadDialogVisible"
            width="30%"
            :before-close="closeUploadDialog"
            destroy-on-close
        >
            <div class="mgb-10">{{ rowData.orderno }}</div>
            <div class="mgb-10">{{ rowData.supplier }}</div>
            <el-select
                v-model="numberData.express_type"
                placeholder="请选择"
                clearable
                class="mgb-10"
            >
                <el-option
                    v-for="item in [
                        { value: 2, text: '顺丰' },
                        { value: 3, text: '顺丰温控包裹' },
                        { value: 5, text: '京东' },
                        { value: 52, text: '圆通' },
                    ]"
                    :key="item.value"
                    :label="item.text"
                    :value="item.value"
                />
            </el-select>
            <el-input
                v-model="numberData.waybill_no"
                type="textarea"
                :rows="2"
                placeholder="运单号(多个单号用英文逗号隔开)"
            />
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeUploadDialog">取 消</el-button>
                <el-button type="primary" @click="sureBtnClick()"
                    >确 定</el-button
                >
            </span>
        </el-dialog>

        <el-dialog
            top="2vh"
            :visible.sync="routerHistoryStatus"
            width="50%"
            :close-on-click-modal="false"
        >
            <el-timeline :reverse="false">
                <el-timeline-item
                    style="font-size: 14px; font-weight: bold; color: #333"
                    v-for="(activity, index) in routerHistoryList"
                    :key="index"
                    :timestamp="activity.AcceptTime"
                >
                    <div>
                        <el-tag
                            style="margin-right: 8px"
                            size="mini"
                            :type="activity.tag ? 'danger' : ''"
                            effect="dark"
                        >
                            {{ activity.Location }} </el-tag
                        >{{ activity.AcceptStation }}
                    </div>
                </el-timeline-item>
            </el-timeline>
        </el-dialog>

        <PayDetail :visible.sync="payDetailVisible" :orderId="currentOrderId" />
    </div>
</template>

<script>
import purchaseExecManageApi from "@/services/purchaseExecManage";
import CreatePurchaseOrderDialog from "@/pages/caiGouPerform/CreatePurchaseOrderDialog";
import PayDetail from "./payDetail.vue";

export default {
    components: {
        CreatePurchaseOrderDialog,
        PayDetail,
    },
    data: () => ({
        payeeMerchantOptions: [],
        query: {
            page: 1,
            limit: 10,
            query_type: 2,
            orderno: "",
            supplier: "",
            short_code: "",
            status: "",
            warehouse: "",
            start_created_time: "",
            end_created_time: "",
            created_name: "",
            payee_merchant_id: "",
            push_erp_status: "",
            waybill_no: "",
        },
        numberData: {
            waybill_no: "",
            id: "",
            express_type: 2,
        },
        list: [],
        total: 0,
        createPurchaseOrderDialogVisible: false,
        createPurchaseOrderItems: [],
        createPurchaseOrderApprove: [],
        uploadDialogVisible: false,
        rowData: {},
        routerHistoryStatus: false,
        routerHistoryList: [],
        payDetailVisible: false,
        currentOrderId: null,
    }),
    computed: {
        createdTime: {
            get() {
                if (
                    !this.query.start_created_time ||
                    !this.query.end_created_time
                ) {
                    return "";
                }
                return [
                    this.query.start_created_time,
                    this.query.end_created_time,
                ];
            },
            set(range) {
                const [stime = "", etime = ""] = range || [];
                this.query.start_created_time = stime;
                this.query.end_created_time = etime;
            },
        },
    },
    filters: {
        toErpStatusText(input) {
            return (
                {
                    0: "自由(缺省)",
                    1: "未用",
                    2: "正在审批",
                    3: "审批通过",
                    4: "审批未通过",
                    5: "输出",
                    6: "冻结",
                    7: "执行完毕",
                }[input] || ""
            );
        },
        toZhongTaiStatusText(input) {
            return (
                {
                    0: "待确认",
                    1: "下单已确认",
                    2: "下单失败",
                    3: "已驳回",
                    4: "采购审核中",
                    5: "财务审核中",
                    6: "预下单",
                    7: "自动下单",
                    8: "延期下单",
                    9: "作废",
                }[input] || ""
            );
        },
    },
    created() {
        this.load();
        this.$request.caiGouPerform
            .caiGouPerformpPyeeMerchantList()
            .then((res) => {
                if (res.data.error_code == 0) {
                    this.payeeMerchantOptions = res.data.data;
                }
            });
    },
    methods: {
        load() {
            purchaseExecManageApi
                .searchPurchaseOrderList(this.query)
                .then((res) => {
                    if (res.data.error_code == 0) {
                        const { list, total } = res.data.data;
                        this.list = list;
                        this.total = total;
                    }
                });
        },
        reload() {
            this.query.page = 1;
            this.load();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
        uploadTrackingNumber(row) {
            this.rowData = row;
            this.uploadDialogVisible = true;
        },
        closeUploadDialog() {
            this.uploadDialogVisible = false;
            this.numberData = this.$options.data().numberData;
        },
        pushWms(row) {
            this.$confirm("确认推送萌牙吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.$request.caiGouPerform
                        .pushPurchaseOrderToWMS({ id: row.id })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message({
                                    type: "success",
                                    message: "推送成功!",
                                });
                                this.load();
                            }
                        });
                })
                .catch(() => {});
        },
        takeBack(row) {
            this.$confirm("确认收回这条采购订单吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    purchaseExecManageApi
                        .retractPurchaseOrder({ id: row.id })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message({
                                    type: "success",
                                    message: "操作成功!",
                                });
                                this.load();
                            }
                        });
                })
                .catch(() => {});
        },
        sureBtnClick() {
            this.numberData.id = this.rowData.id;
            purchaseExecManageApi
                .uploadExpressNumber(this.numberData)
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message({
                            type: "success",
                            message: "上传成功!",
                        });
                        this.closeUploadDialog();
                        this.load();
                    }
                });
        },
        searchLogistics(item) {
            console.log("item", item);
            purchaseExecManageApi
                .getLogistics({
                    logisticCode: item.waybill_no,
                    expressType: item.express_type,
                    phone: "17723885472",
                    address:
                        "江苏省南通市通州区希望大道与文昌路交叉口佰酿云酒南通中心仓",
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        let traces = [];
                        res.data.data.traces
                            ? res.data.data.traces.map((item) => {
                                  let obj = {
                                      Location: "物流轨迹",
                                      AcceptStation: item.context,
                                      AcceptTime: item.ftime,
                                  };
                                  traces.push(obj);
                              })
                            : false;
                        this.routerHistoryList = traces;

                        if (this.routerHistoryList.length != 0) {
                            this.routerHistoryStatus = true;
                        } else {
                            this.$message.warning("暂无物流信息");
                        }
                    }
                });
        },
        onViewPurchaseOrder(row) {
            const createPurchaseOrderDialogRef =
                this.$refs.createPurchaseOrderDialogRef;
            const {
                id,
                period,
                orderno,
                bill_date,
                warehouse,
                warehouse_code,
                supplier,
                supplier_code,
                department,
                department_code,
                operator_name,
                operator_code,
                setttlement,
                setttlement_code,
                remark,
            } = row;
            createPurchaseOrderDialogRef.supplierOptions = [
                { value: supplier_code, label: supplier },
            ];
            createPurchaseOrderDialogRef.departmentOptions = [
                { value: department_code, label: department },
            ];
            createPurchaseOrderDialogRef.model = Object.assign(
                {},
                createPurchaseOrderDialogRef.$options.data().model,
                {
                    id,
                    period,
                    orderno,
                    bill_date,
                    warehouse,
                    warehouse_code,
                    supplier,
                    supplier_code,
                    department,
                    department_code,
                    operator_name,
                    operator_code,
                    setttlement,
                    setttlement_code,
                    remark,
                    items: [],
                    statusMap: {
                        operate_status: row.operate_status,
                        status: row.status,
                    },
                }
            );

            this.$refs.createPurchaseOrderDialogRef.fileList = row.annex;
            this.createPurchaseOrderItems = row.items.map((item) => ({
                ...item,
            }));
            this.createPurchaseOrderApprove = row.approve.map((item) => ({
                ...item,
            }));
            createPurchaseOrderDialogRef.loadApproveList(row.corp_code);
            this.createPurchaseOrderDialogVisible = true;
            this.$nextTick(() => {
                createPurchaseOrderDialogRef.$refs.purchaseOrderPrintDialogRef.model =
                    row;
            });
        },
        showPaymentDetail(row) {
            this.currentOrderId = row.id;
            this.payDetailVisible = true;
        },
    },
};
</script>

<style lang="scss" scoped></style>
