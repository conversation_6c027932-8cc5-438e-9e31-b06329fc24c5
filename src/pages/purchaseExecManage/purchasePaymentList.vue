<template>
    <div class="payment-layout">
        <div>
            <el-form inline>
                <el-form-item>
                    <el-input
                        v-model="query.code"
                        placeholder="付款单号"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="query.creator"
                        placeholder="录入人"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="query.supplier_name"
                        placeholder="供应商"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="query.corp"
                        placeholder="公司主体"
                        filterable
                        clearable
                        style="width: 260px"
                    >
                        <el-option
                            v-for="item in cropCodeList"
                            :key="item.vh_code"
                            :label="item.corp_name"
                            :value="item.erp_code"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-date-picker
                        v-model="createdTime"
                        type="datetimerange"
                        start-placeholder="单据开始时间"
                        end-placeholder="单据结束时间"
                        :default-time="['00:00:00', '23:59:59']"
                        value-format="yyyy-MM-dd HH:mm:ss"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="reload">查询</el-button>
                    <el-button
                        type="warning"
                        @click="handleBatchPrint"
                        :disabled="!selectedRows.length"
                        >批量打印</el-button
                    >
                </el-form-item>
            </el-form>
        </div>
        <div>
            <!-- 表格 -->
            <el-table
                :data="tableData"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="55" align="center">
                </el-table-column>
                <el-table-column
                    prop="code"
                    label="付款单号"
                    width="120"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="bill_date"
                    label="单据日期"
                    width="110"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="supplier_name"
                    label="客商名称"
                    min-width="200"
                    align="left"
                >
                </el-table-column>
                <el-table-column
                    prop="remark"
                    label="备注"
                    min-width="150"
                    align="left"
                >
                </el-table-column>
                <el-table-column
                    prop="department"
                    label="部门"
                    width="110"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="applicant"
                    label="业务员"
                    width="80"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="payment_bank"
                    label="付款银行"
                    width="120"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="amount"
                    label="原币金额"
                    width="100"
                    align="right"
                >
                </el-table-column>
                <el-table-column
                    prop="status"
                    label="单据状态"
                    width="80"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="creator"
                    label="录入人"
                    width="90"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="auditor"
                    label="审核人"
                    width="90"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="is_prepay"
                    label="是否预收付"
                    width="90"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="print_count"
                    label="打印次数"
                    width="80"
                    align="center"
                >
                </el-table-column>
                <el-table-column label="操作" width="100" align="center">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            @click="handlePrint(scope.row)"
                        >
                            打印
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <div class="pagination-block">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="query.page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="query.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
            <!-- 打印弹窗 -->
            <el-dialog
                title=""
                :visible.sync="printDialogVisible"
                fullscreen
                :before-close="handleClosePrintDialog"
            >
                <div class="print-content">
                    <div class="print-layout">
                        <!-- 左侧打印内容 -->
                        <div class="print-main">
                            <div class="payment-form" v-if="currentPrintData">
                                <!-- 标题 -->
                                <h1 class="form-title">
                                    {{ currentPrintData.company }}采购付款单
                                </h1>

                                <!-- 基本信息表格 -->
                                <div class="basic-info">
                                    <table class="info-table">
                                        <tr>
                                            <td>单号：</td>
                                            <td>{{ currentPrintData.code }}</td>
                                            <td>采购单号：</td>
                                            <td>
                                                {{ currentPrintData.po_code }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>申请人：</td>
                                            <td>
                                                {{ currentPrintData.applicant }}
                                            </td>
                                            <td>申请日期：</td>
                                            <td>
                                                {{ currentPrintData.bill_date }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>币种：</td>
                                            <td>
                                                {{ currentPrintData.currency }}
                                            </td>
                                            <td>是否预付款：</td>
                                            <td>
                                                {{ currentPrintData.is_prepay }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>单位名称：</td>
                                            <td>
                                                {{
                                                    currentPrintData.supplier_name
                                                }}
                                            </td>
                                            <td>联系人：</td>
                                            <td>
                                                {{
                                                    currentPrintData.supplier_contact
                                                }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>开户银行：</td>
                                            <td>
                                                {{
                                                    currentPrintData.supplier_bank
                                                }}
                                            </td>
                                            <td>开户账号：</td>
                                            <td>
                                                {{
                                                    currentPrintData.supplier_account
                                                }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>备注：</td>
                                            <td colspan="3">
                                                {{ currentPrintData.remark }}
                                            </td>
                                        </tr>
                                    </table>
                                </div>

                                <!-- 付款信息表格 -->
                                <div class="payment-info">
                                    <table class="detail-table">
                                        <thead>
                                            <tr>
                                                <th>序号</th>
                                                <th>结算方式</th>
                                                <th>账号名称</th>
                                                <th>付款金额</th>
                                                <th>付款金额（本币）</th>
                                                <th>备注</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr
                                                v-for="(
                                                    item, index
                                                ) in currentPrintData.payment_info ||
                                                []"
                                                :key="index"
                                            >
                                                <td>{{ item.serial_no }}</td>
                                                <td>
                                                    {{ item.settlement_method }}
                                                </td>
                                                <td>{{ item.account_name }}</td>
                                                <td>
                                                    {{ item.payment_amount }}
                                                </td>
                                                <td>
                                                    {{
                                                        item.payment_local_amount
                                                    }}
                                                </td>
                                                <td>{{ item.summary }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 核销明细表格 -->
                                <div class="verification-info">
                                    <table class="detail-table">
                                        <thead>
                                            <tr>
                                                <th>序号</th>
                                                <th>业务类型</th>
                                                <th>单据日期</th>
                                                <th>单据编码</th>
                                                <th>金额</th>
                                                <th>金额(本币)</th>
                                                <th>结算金额</th>
                                                <th>结算金额(本币)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr
                                                v-for="(
                                                    item, index
                                                ) in currentPrintData.verify_info ||
                                                []"
                                                :key="index"
                                            >
                                                <td>{{ item.serial_no }}</td>
                                                <td>
                                                    {{ item.business_type }}
                                                </td>
                                                <td>
                                                    {{ item.verify_bill_date }}
                                                </td>
                                                <td>
                                                    {{ item.verify_bill_code }}
                                                </td>
                                                <td>
                                                    {{ item.verify_amount }}
                                                </td>
                                                <td>
                                                    {{
                                                        item.verify_local_amount
                                                    }}
                                                </td>
                                                <td>
                                                    {{ item.settlement_amount }}
                                                </td>
                                                <td>
                                                    {{
                                                        item.settlement_local_amount
                                                    }}
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <h3
                                    style="
                                        text-align: center;
                                        margin: 40px 0 20px 0;
                                    "
                                >
                                    审批情况
                                </h3>
                                <!-- 审批信息表格 -->
                                <div class="basic-info">
                                    <table class="info-table">
                                        <tr>
                                            <td>单据类型：</td>
                                            <td>采购付款单</td>
                                            <td>单据号：</td>
                                            <td>{{ currentPrintData.code }}</td>
                                        </tr>
                                        <tr>
                                            <td>业务流程：</td>
                                            <td>收付通用流程</td>
                                            <td>币种：</td>
                                            <td>人民币</td>
                                        </tr>
                                    </table>
                                </div>
                                <!-- 审批情况表格 -->
                                <div class="approval-info">
                                    <table class="detail-table">
                                        <thead>
                                            <tr>
                                                <th style="width: 100px">
                                                    提交人
                                                </th>
                                                <th style="width: 220px">
                                                    发送日期
                                                </th>
                                                <th style="width: 100px">
                                                    审批人
                                                </th>
                                                <th style="width: 100px">
                                                    审批意见
                                                </th>
                                                <th style="width: 220px">
                                                    处理日期
                                                </th>
                                                <th style="width: 260px">
                                                    批语
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr
                                                v-for="(
                                                    item, index
                                                ) in (currentPrintData.approval &&
                                                    currentPrintData.approval
                                                        .list) ||
                                                []"
                                                :key="index"
                                            >
                                                <td>{{ item.submitter }}</td>
                                                <td>{{ item.submit_date }}</td>
                                                <td>{{ item.approver }}</td>
                                                <td>
                                                    {{ item.approve_result }}
                                                </td>
                                                <td>{{ item.approve_date }}</td>
                                                <td>{{ item.approve_note }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 底部签名信息 -->
                                <div class="footer-info">
                                    <div>
                                        制表人：{{
                                            currentPrintData.table_maker
                                        }}
                                    </div>
                                    <div>
                                        制表日期：{{
                                            currentPrintData.table_date
                                        }}
                                    </div>
                                </div>
                            </div>
                            <div v-else class="loading-placeholder">
                                <el-empty description="暂无打印数据"></el-empty>
                            </div>
                        </div>
                        <!-- 右侧单号列表 -->
                        <div class="print-list" v-if="printList.length > 0">
                            <div class="print-list-title">待打印单据</div>
                            <div class="print-list-content">
                                <div
                                    v-for="item in printList"
                                    :key="item.id"
                                    class="print-list-item"
                                    :class="{
                                        active:
                                            currentPrintItem &&
                                            currentPrintItem.id === item.id,
                                    }"
                                    @click="switchPrintItem(item)"
                                >
                                    <span>{{ item.code }}</span>
                                    <el-tag
                                        size="mini"
                                        v-if="item.printed"
                                        type="success"
                                        >已打印</el-tag
                                    >
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <span
                    slot="footer"
                    class="dialog-footer"
                    v-show="isDisableFooter"
                >
                    <el-button @click="handleClosePrintDialog">取 消</el-button>
                    <el-button
                        type="primary"
                        @click="confirmBatchPrint"
                        :loading="printing"
                    >
                        {{ printing ? "打印中" : "确认打印" }}
                    </el-button>
                </span>
            </el-dialog>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            query: {
                page: 1,
                limit: 10,
                code: "",
                creator: "",
                supplier_name: "",
                date_end: "",
                date_start: "",
                corp: "",
            },
            selectedRows: [],
            createdTime: [],
            id: "",
            isDisableFooter: true,
            tableData: [],
            total: 0,
            printDialogVisible: false,
            currentPrintData: null,
            printList: [], // 待打印列表
            currentPrintItem: null, // 当前选中的打印项
            printing: false, // 是否正在打印
            cropCodeList: [],
        };
    },
    watch: {
        createdTime(val) {
            if (val) {
                this.query.date_start = val[0];
                this.query.date_end = val[1];
            } else {
                this.query.date_start = "";
                this.query.date_end = "";
            }
        },
    },
    mounted() {
        this.getCropCode();
        this.getList();
    },
    methods: {
        // 获取列表数据
        getList() {
            // 这里添加您的接口调用
            const data = this.query;
            this.$request.purchase.getPaymentList(data).then((res) => {
                if (res.data.error_code === 0) {
                    this.tableData = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        getCropCode() {
            this.$request.caiGouPerform.getCorpMappingList({}).then((res) => {
                if (res.data.error_code === 0) {
                    this.cropCodeList = res.data.data;
                }
            });
        },
        // 重新加载数据
        reload() {
            this.query.page = 1;
            this.getList();
        },
        // 处理分页
        handleCurrentChange(page) {
            this.query.page = page;
            this.getList();
        },
        // 处理每页显示数量变化
        handleSizeChange(size) {
            this.query.limit = size;
            this.query.page = 1;
            this.getList();
        },
        // 处理表格选择变化
        handleSelectionChange(selection) {
            this.selectedRows = selection;
        },
        // 处理打印按钮点击
        async handlePrint(row) {
            this.printList = [
                {
                    ...row,
                    printed: false,
                },
            ];
            this.currentPrintItem = this.printList[0];
            await this.loadPrintData(this.currentPrintItem);
            this.printDialogVisible = true;
        },
        // 处理批量打印
        async handleBatchPrint() {
            if (this.selectedRows.length === 0) {
                this.$message.warning("请至少选择一条记录");
                return;
            }
            this.printList = this.selectedRows.map((row) => ({
                ...row,
                printed: false,
            }));
            this.currentPrintItem = this.printList[0];
            await this.loadPrintData(this.currentPrintItem);
            this.printDialogVisible = true;
        },
        // 切换打印项
        async switchPrintItem(item) {
            this.currentPrintItem = item;
            await this.loadPrintData(item);
        },
        // 加载打印数据
        async loadPrintData(item) {
            const data = {
                id: item.id,
            };
            const res = await this.$request.purchase.getPrintInfo(data);
            if (res.data.error_code === 0) {
                this.currentPrintData = res.data.data.list[0];
            } else {
                this.$message.error("获取打印数据失败");
            }
        },
        // 确认批量打印
        async confirmBatchPrint() {
            this.printing = true;
            try {
                for (const item of this.printList) {
                    if (!item.printed) {
                        await this.loadPrintData(item);
                        await this.$request.purchase.getPrintCount({
                            id: item.id,
                        });
                        this.isDisableFooter = false;
                        await this.$nextTick();
                        window.print();
                        item.printed = true;
                    }
                }
                this.$message.success("打印完成");
                this.handleClosePrintDialog();
                this.getList();
            } catch (error) {
                this.$message.error("打印过程中出现错误");
            } finally {
                this.printing = false;
            }
        },
        // 处理关闭打印弹窗
        handleClosePrintDialog() {
            this.printDialogVisible = false;
            this.currentPrintData = null;
            this.printList = [];
            this.isDisableFooter = true;
            this.currentPrintItem = null;
            this.printing = false;
        },
    },
};
</script>
<style lang="scss" scoped>
.pagination-block {
    text-align: center;
    display: flex;
    margin-top: 20px;
    justify-content: center;
}

.loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
}

.print-content {
    padding: 20px;
    background: white;
    min-height: 500px;
}

.payment-form {
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
    font-size: 14px;

    .form-title {
        text-align: center;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
    }

    .info-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;

        td {
            padding: 8px;
            border: 1px solid #000;
            font-size: 18px;
            height: 32px;
            line-height: 32px;

            &:nth-child(odd) {
                width: 140px;
                text-align: right;
                background-color: #f5f7fa;
            }

            &:nth-child(even) {
                width: 350px;
                text-align: left;
                padding-left: 10px;
            }
        }
    }

    .detail-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;

        th,
        td {
            padding: 8px;
            border: 1px solid #000;
            text-align: center;
            font-size: 18px;
            height: 32px;
            line-height: 32px;
        }

        th {
            background-color: #f5f7fa;
            font-weight: bold;
        }
    }

    .payment-info,
    .verification-info,
    .approval-info {
        margin-top: 20px;
    }

    .footer-info {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
        font-size: 18px;
        padding: 0 50px;
    }
}

@media print {
    .el-dialog__header,
    .el-dialog__footer {
        display: none !important;
    }

    .print-content {
        padding: 0;
    }

    .payment-form {
        font-size: 18px;
    }
}
.print-content {
    font-weight: bold;
    color: #000 !important;
}

.print-layout {
    display: flex;
    height: 100%;

    .print-main {
        flex: 1;
        padding-right: 20px;
        overflow-y: auto;
    }

    .print-list {
        width: 250px;
        border-left: 1px solid #dcdfe6;

        .print-list-title {
            padding: 10px;
            font-weight: bold;
            background-color: #f5f7fa;
            border-bottom: 1px solid #dcdfe6;
        }

        .print-list-content {
            overflow-y: auto;
            height: calc(100% - 41px);
        }

        .print-list-item {
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            justify-content: space-between;
            align-items: center;

            &:hover {
                background-color: #f5f7fa;
            }

            &.active {
                background-color: #ecf5ff;
                color: #409eff;
            }
        }
    }
}

@media print {
    .print-list {
        display: none !important;
    }
}
.payment-layout {
    font-size: 18px !important;
}
</style>
