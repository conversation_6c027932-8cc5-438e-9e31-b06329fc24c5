<template>
    <div>
        <el-form inline>
            <el-form-item>
                <el-input
                    v-model="query.short_code"
                    clearable
                    placeholder="简码"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.customer_name"
                    clearable
                    placeholder="供应商"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="reload">查询</el-button>
                <el-button type="success" @click="addDialogVisible = true">新增</el-button>
                <el-button type="warning" @click="importDialogVisible = true">导入</el-button>
                <el-button type="danger" @click="onExport">导出</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="list" border>
            <el-table-column align="center" prop="customer_name" label="供应商"  min-width="120">
            </el-table-column>
            <el-table-column align="center" prop="short_code" label="简码"  min-width="100">
               
            </el-table-column>
            
            <el-table-column align="center" prop="number" label="数量"  min-width="60">
            </el-table-column>
            <el-table-column
                    label="操作"
                    fixed="right"
                    align="center"
                    min-width="100"
                >
                    <template slot-scope="scope">
                        <el-button
                            size="mini"
                            @click="editData(scope.row)"
                            type="text"
                        >
                            编辑
                        </el-button>
                        <el-button
                            size="mini"
                            @click="checkLog(scope.row)"
                            type="text"
                        >
                            日志
                        </el-button>
                    </template>
                </el-table-column>
        </el-table>
        <el-row type="flex" justify="center" style="margin-top: 20px">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </el-row>
        
        <el-dialog
            :visible.sync="editdialogVisible"
            title="修改"
            width="50%"
            @close="editCloseClick"
        >
            <div v-if="editdialogVisible">
                {{rowData.short_code}}
                <el-radio v-model="radio" :label="1">增</el-radio>
                <el-radio v-model="radio" :label="2">减</el-radio>
                <el-input-number
                    v-model="number"
                    placeholder="请输入"
                    class="w-normal m-r-10"
                    :controls="false"
                    :min="0"
                ></el-input-number>
                <el-button type="primary" @click="sureClick">确定</el-button>
            </div>
            
        </el-dialog>

        <el-dialog
            :visible.sync="addDialogVisible"
            title="新增"
            width="50%"
            @close="addCloseClick"
        >
        <el-form :rules="addRules" ref="addref" :model="addData" label-width="80px">
            <el-form-item label="供应商" prop="customer_name">
                <el-select
                v-model="addData.customer_name"
                filterable
                remote
                reserve-keyword
                placeholder="请输入供应商"
                :remote-method="remoteMethod"
            >
                <el-option
                    v-for="item in supplierOpration"
                    :key="item.id"
                    :label="item.supplier_name"
                    :value="item.supplier_name"
                >
                </el-option>
            </el-select>
            </el-form-item>
            
            <el-form-item label="简码" prop="short_code">
                <el-input
                            v-model="addData.short_code"
                            placeholder="请输入"
                            class="w-normal"
                        ></el-input>
            </el-form-item>
            <el-form-item label="数量" prop="number">
                <el-input-number
                            v-model="addData.number"
                            placeholder="请输入"
                            class="w-normal"
                            :controls="false"
                            :min="0"
                        ></el-input-number>
        </el-form-item>
        <el-form-item >
            <el-button type="primary" @click="addSureClick('addref')">确定</el-button>
        </el-form-item>
        </el-form>
        </el-dialog>

        <ImportDialog
            v-if="importDialogVisible"
            :visible.sync="importDialogVisible"
            @load="reload"
        ></ImportDialog>

        <el-dialog
            :visible.sync="logRecorddialogVisible"
            title="日志记录"
            width="60%"
          
        >
            <div >
                <el-table
                :data="tableData"
                style="width: 100%">
                <el-table-column
                    prop="create_time"
                    label="时间"
                    >
                </el-table-column>
                <el-table-column
                    prop="operator_name"
                    label="操作员"
                   >
                </el-table-column>
                <el-table-column
                    prop="remark"
                    label="记录">
                </el-table-column>
                </el-table>
            </div>
            
        </el-dialog>
        
    </div>
</template>

<script>
import purchaseExecManageApi from "@/services/purchaseExecManage";
import CreatePurchaseOrderDialog from "@/pages/caiGouPerform/CreatePurchaseOrderDialog";
import ImportDialog from "@/components/purchaseExecManage/UploadFileDialog";
export default {
    components: {
        CreatePurchaseOrderDialog,
        ImportDialog
    },
    data: () => ({
       
        query: {
            page: 1,
            limit: 10,
            customer_name: "",
            short_code: "",
        },
        addData:{
            customer_name:"",
            short_code:"",
            number:0,
        },
        list: [],
        total: 0,
        rowData:{},
        radio:1,
        number:"",
        editdialogVisible:false,
        tableData:[],
        logRecorddialogVisible:false,
        addDialogVisible:false,
        importDialogVisible:false,
        supplierOpration:[],
        addRules: {
            customer_name: [
                    {
                        required: true,
                        message: "请输入供应商",
                        trigger: "blur",
                    },
                ],
                short_code: [
                    {
                        required: true,
                        message: "请输入简码",
                        trigger: "blur",
                    },
                ],
                number: [
                    {
                        required: true,
                        message: "请输入数量",
                        trigger: "blur",
                    },
                ],
            },
    }),
   
    created() {
        this.load();
        
    },
    methods: {
        load() {
            purchaseExecManageApi
                .getStockList(this.query)
                .then((res) => {
                    if (res.data.error_code == 0) {
                        const { list, total } = res.data.data;
                        this.list = list;
                        this.total = total;
                    }
                });
        },
        reload() {
            this.query.page = 1;
            this.load();
        },
        editData(row){
            this.rowData = row;
            this.editdialogVisible = true;
        },
        checkLog(row){
           
            purchaseExecManageApi
                .logRecordList({id:row.id})
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.tableData = res.data.data.list;
                        this.logRecorddialogVisible =true;
                    }
                });
        },
        onExport() {
            window.open(
                `https://callback.vinehoo.com/purchase-management/purchase/v3/PreparePurchase/export?short_code=${this.query.short_code}&customer_name=${this.query.customer_name}`
            );
            // purchaseExecManageApi
            //     .preparePurchaseExport({short_code:this.query.short_code,customer_name:this.query.customer_name})
            //     .then((res) => {
            //         if (res.data.error_code == 0) {
            //             this.$message.success(res.data.error_msg);
            //         }
            //     });
        },
          //供应商查询
          remoteMethod(query) {
            if (query !== "") {
                
                this.$request.article
                    .supplierList({
                        page: 1,
                        limit: 10,
                        keyword: query,
                    })
                    .then((res) => {
                       
                        this.supplierOpration = res.data.data.list;
                    });
            } else {
                this.supplierOpration = [];
            }
        },
        sureClick(){
            var number;
            if(this.radio !==1) {
                number = -Number(this.number)
            } else {
                number = Number(this.number)
            }
            purchaseExecManageApi
                .updateStockNumber({id:this.rowData.id,number:number})
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.editCloseClick();
                        this.load();
                    }
                });
            
        },
        addSureClick(formName){
            this.$refs[formName].validate((valid) => {
                if (valid) {
                   
                    purchaseExecManageApi
                .addStockNumber({...this.addData
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.addCloseClick();
                        this.load();
                    }
                });
                   
                } else {
                    return false;
                }
            });
            
        },
        editCloseClick(){
            this.editdialogVisible = false;
            this.radio=1;
            this.number="";
        },
        addCloseClick(){
            this.addDialogVisible = false;
            // this.radio=1;
            // this.number="";
            this.addData = this.$options.data().addData
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
       
    },
};
</script>

<style lang="scss" scoped></style>
