<template>
    <div>
        <el-tabs v-model="activeTab" type="card">
            <el-tab-pane label="修改成本" name="cost">
                <el-form
                    :model="ruleForm"
                    :rules="rules"
                    ref="ruleForm"
                    label-width="100px"
                    class="demo-ruleForm"
                >
                    <el-form-item label="期数" prop="period">
                        <el-input
                            v-model.trim="ruleForm.period"
                            class="w-large"
                            placeholder="请输入期数"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="简码" prop="short_code">
                        <el-input
                            v-model.trim="ruleForm.short_code"
                            class="w-large"
                            placeholder="请输入简码"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="成本价" prop="costprice">
                        <el-input-number
                            v-model="ruleForm.costprice"
                            :min="0"
                            :precision="2"
                            :controls="false"
                            label="请输入成本价"
                        ></el-input-number>
                    </el-form-item>
                    <el-form-item>
                        <el-button
                            type="primary"
                            @click="submitForm('ruleForm')"
                            >确定</el-button
                        >
                    </el-form-item>
                </el-form>
            </el-tab-pane>

            <el-tab-pane label="同时上架" name="channel">
                <el-form :inline="true" class="demo-form-inline">
                    <el-form-item label="期数">
                        <el-input
                            v-model="queryPeriod"
                            placeholder="请输入期数"
                            class="w-large"
                        ></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="searchSamePeriods"
                            >查询</el-button
                        >
                    </el-form-item>
                </el-form>

                <el-table
                    :data="samePeriodsData"
                    v-if="samePeriodsData.length > 0"
                >
                    <el-table-column
                        prop="type_name"
                        label="频道"
                    ></el-table-column>
                    <el-table-column prop="id" label="期数"></el-table-column>
                    <el-table-column prop="is_channel" label="是否渠道">
                        <template slot-scope="scope">
                            <el-tag
                                :type="scope.row.is_channel ? 'danger' : 'info'"
                                size="mini"
                            >
                                {{ scope.row.is_channel ? "是" : "否" }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="title"
                        label="商品名称"
                        min-width="400"
                    ></el-table-column>
                </el-table>

                <div
                    v-if="samePeriodsData.length > 0"
                    style="
                        margin-top: 20px;
                        display: flex;
                        justify-content: center;
                    "
                >
                    <el-button type="danger" @click="cancelChannel"
                        >同时上架</el-button
                    >
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
export default {
    data() {
        return {
            activeTab: "cost",
            queryPeriod: "",
            samePeriodsData: [],
            ruleForm: {
                period: "",
                short_code: "",
                costprice: "",
            },
            rules: {
                period: [
                    {
                        required: true,
                        message: "请输入期数",
                        trigger: "blur",
                    },
                ],
                short_code: [
                    {
                        required: true,
                        message: "请输入简码",
                        trigger: "blur",
                    },
                ],
                costprice: [
                    {
                        required: true,
                        message: "请输入成本价",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.$request.article
                        .updateCost(this.ruleForm)
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("更新成功");
                                this.$refs[formName].resetFields();
                            }
                        });
                } else {
                    return false;
                }
            });
        },
        searchSamePeriods() {
            if (!this.queryPeriod) {
                this.$message.error("请输入期数");
                return;
            }
            this.$request.article
                .getSamePeriods({ period: this.queryPeriod })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.samePeriodsData = res.data.data.list;
                        if (this.samePeriodsData.length === 0) {
                            this.$message.info("未找到在售的期数");
                        }
                    }
                });
        },
        cancelChannel() {
            this.$confirm("确认同时上架吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.$request.article
                        .cancelChannel({ period: this.queryPeriod })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("同时上架成功");
                                this.samePeriodsData = [];
                                this.queryPeriod = "";
                            }
                        });
                })
                .catch(() => {});
        },
    },
};
</script>

<style></style>
