<template>
  <div>
    <div>
      <span>搜索范围</span
      ><el-button
        style="margin-left: 10px"
        circle
        v-on:click="$emit('operate-list', 1, addId)"
      >
        + </el-button
      ><el-button circle v-on:click="$emit('operate-list', 0, addId)">
        -
      </el-button>
    </div>
    <el-row>
      <el-col :span="12">
        <el-radio v-model="type" label="country">国家 </el-radio>
        <el-input
          class="w-normal"
          v-model="configs[0].inputStr"
          @keyup.enter.native="handleInputConfirm(0)"
          @blur="handleInputConfirm(0)"
          placeholder="请输入（支持多个）"
        >
        </el-input>

        <el-tag
          :key="tag"
          v-for="tag in configs[0].values"
          closable
          :disable-transitions="false"
          @close="handleClose(tag, 0)"
          type="info"
        >
          {{ tag }}
        </el-tag>
      </el-col>
      <el-col :span="12">
        <el-radio v-model="type" label="regions">产区 </el-radio>
        <el-input
          class="w-normal"
          v-model="configs[1].inputStr"
          @keyup.enter.native="handleInputConfirm(1)"
          @blur="handleInputConfirm(1)"
          placeholder="请输入（支持多个）"
        >
        </el-input>

        <el-tag
          :key="tag"
          v-for="tag in configs[1].values"
          closable
          :disable-transitions="false"
          @close="handleClose(tag, 1)"
          type="info"
        >
          {{ tag }}
        </el-tag>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-radio v-model="type" label="price">价格 </el-radio>
        <el-select class="w-mini" v-model="configs[2].operation" size="mini">
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-input
          oninput="value=value.replace(/[^0-9.]/g,'')"
          :disabled="configs[2].operation ? false : true"
          v-model="configs[2].values[0]"
          v-show="configs[2].operation === 'between' ? false : true"
          class="w-normal"
          placeholder="请输入"
        >
        </el-input>
        <span v-show="configs[2].operation === 'between' ? true : false"
          ><el-input
            oninput="value=value.replace(/[^0-9.]/g,'')"
            class="w-mini"
            v-model="configs[2].values[0]"
            placeholder="请输入"
          >
          </el-input
          >-<el-input
            oninput="value=value.replace(/[^0-9.]/g,'')"
            class="w-mini"
            v-model="configs[2].values[1]"
            placeholder="请输入"
          >
          </el-input
        ></span>
      </el-col>
      <el-col :span="12">
        <el-radio v-model="type" label="capacity_f">容量 </el-radio>
        <el-select v-model="configs[3].operation" size="mini" class="w-mini">
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-input
          oninput="value=value.replace(/[^0-9.]/g,'')"
          :disabled="configs[3].operation ? false : true"
          v-model="configs[3].values[0]"
          v-show="configs[3].operation === 'between' ? false : true"
          class="w-normal"
          placeholder="请输入"
        >
        </el-input>
        <span v-show="configs[3].operation === 'between' ? true : false"
          ><el-input
            oninput="value=value.replace(/[^0-9.]/g,'')"
            class="w-mini"
            v-model="configs[3].values[0]"
            placeholder="请输入"
          >
          </el-input
          >-<el-input
            oninput="value=value.replace(/[^0-9.]/g,'')"
            class="w-mini"
            v-model="configs[3].values[1]"
            placeholder="请输入"
          >
          </el-input>
        </span>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-radio v-model="type" label="grape">葡萄 </el-radio>
        <el-input
          class="w-normal"
          v-model="configs[4].inputStr"
          @keyup.enter.native="handleInputConfirm(4)"
          @blur="handleInputConfirm(4)"
          placeholder="请输入（支持多个）"
        >
        </el-input>

        <el-tag
          :key="tag"
          v-for="tag in configs[4].values"
          closable
          :disable-transitions="false"
          @close="handleClose(tag, 4)"
          type="info"
        >
          {{ tag }}
        </el-tag>
      </el-col>
      <el-col :span="12">
        <el-radio v-model="type" label="product_category">类型 </el-radio>
        <el-input
          class="w-normal"
          v-model="configs[5].inputStr"
          @keyup.enter.native="handleInputConfirm(5)"
          @blur="handleInputConfirm(5)"
          placeholder="请输入（支持多个）"
        >
        </el-input>

        <el-tag
          :key="tag"
          v-for="tag in configs[5].values"
          closable
          :disable-transitions="false"
          @close="handleClose(tag, 5)"
          type="info"
        >
          {{ tag }}
        </el-tag>
      </el-col>
    </el-row>

    <!-- <el-radio v-model="radio" label="2">备选项</el-radio> -->
  </div>
</template>
  
  <script>
export default {
  props: ["addId"],
  data() {
    return {
      type: "",
      configs: [
        {
          type: "country",
          mode: "string",
          operation: "=",
          values: [],
          inputStr: "",
        },
        {
          type: "regions",
          mode: "string",
          operation: "=",
          values: [],
          inputStr: "",
        },
        {
          type: "price",
          mode: "number",
          operation: "",
          values: [],
          inputStr: "",
        },
        {
          type: "capacity_f",
          mode: "number",
          operation: "",
          values: [],
          inputStr: "",
        },
        {
          type: "grape",
          mode: "string",
          operation: "=",
          values: [],
          inputStr: "",
        },
        {
          type: "product_category",
          mode: "string",
          operation: "=",
          values: [],
          inputStr: "",
        },
      ],
      options: [
        {
          id: 1,
          value: ">",
          label: "大于",
        },
        {
          id: 2,
          value: "<",
          label: "小于",
        },
        {
          id: 3,
          value: "=",
          label: "等于",
        },
        {
          id: 4,
          value: "<=",
          label: "小于等于",
        },
        {
          id: 5,
          value: ">=",
          label: "大于等于",
        },
        {
          id: 6,
          value: "between",
          label: "介于",
        },
      ],
    };
  },

  mounted() {},
  methods: {
    handleClose(tag, index) {
      this.configs[index].values.splice(
        this.configs[index].values.indexOf(tag),
        1
      );
    },
    handleInputConfirm(index) {
      console.log("--------" + index);
      var inputValue;
      inputValue = this.configs[index].inputStr;
      if (inputValue) {
        this.configs[index].values.push(inputValue);
      }
      this.configs[index].inputStr = "";
    },
  },
};
</script>
  
 <style>
</style>
  