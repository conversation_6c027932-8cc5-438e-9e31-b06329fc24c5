<template>
  <el-dialog
    :title="title"
    :visible="visible"
    width="70%"
    :before-close="closeDialog"
    :close-on-press-escape="false"
  >
    <el-form :model="ruleForm" class="demo-ruleForm" size="mini">
      <el-form-item label="" prop="">
        <el-input
          v-model="ruleForm.word"
          class="w-large m-r-10"
          placeholder="请输入关键词"
          :disabled="isAdd === 1 ? true : false"
          ref="firstFocus"
        ></el-input>
      </el-form-item>
      <el-form-item label="" prop="end_time">
        <el-radio :value="type" @click.native.prevent="clickitemdataType('replace')" label="replace"
          >替换词</el-radio
        >
        <div>
          <el-input
            class="input-new-tag"
            v-if="replaceInputVisible"
            v-model="replaceInputValue"
            ref="saveTagInput"
            size="small"
            @keyup.enter.native="handleInputConfirm"
            @blur="handleInputConfirm"
          >
          </el-input>
          <el-button
            v-else
            class="button-new-tag"
            size="small"
            @click="showInput"
            >请输入替换词（支持多个）</el-button
          >
          <el-tag
            :key="tag"
            v-for="tag in replace_words"
            closable
            :disable-transitions="false"
            @close="handleClose(tag)"
            type="info"
          >
            {{ tag }}
          </el-tag>
        </div>
      </el-form-item>
      <el-form-item label="" prop="end_time">
        <el-radio :value="type" @click.native.prevent="clickitemdataType('extend')" label="extend"
          >扩展词</el-radio
        >
        <div>
          <el-input
            class="input-new-tag"
            v-if="extInputVisible"
            v-model="extInputValue"
            ref="saveTagInput"
            size="small"
            @keyup.enter.native="handleInputConfirm"
            @blur="handleInputConfirm"
          >
          </el-input>
          <el-button
            v-else
            class="button-new-tag"
            size="small"
            @click="showExtInput"
            >请输入扩展词（支持多个）</el-button
          >
          <el-tag
            :key="tag"
            v-for="tag in extend_words"
            closable
            :disable-transitions="false"
            @close="handleExtClose(tag)"
            type="info"
          >
            {{ tag }}
          </el-tag>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div v-show="searchList.length ? false : true">
          <span>搜索范围</span>
          <el-button style="margin-left: 10px;" circle @click="addSearch"> + </el-button>
        </div>
        <SearchScope
          v-on:operate-list="onOperateList"
          ref="scope"
          :addId="searchList[index]"
          v-for="(item, index) in searchList"
          :key="item"
        >
        </SearchScope>
      </el-form-item>
    </el-form>
    <span slot="footer" style="display: flex; justify-content: center">
        <el-button type="primary" @click="addKeywords()">保存</el-button>
     </span>
  </el-dialog>
</template>

<script>
import dialogMixins from "../../mixins/dialogMixins";
import SearchScope from "./searchScope.vue";
export default {
  components: {
    SearchScope,
  },
  //   props: ["activeName"],
  mixins: [dialogMixins],
  data() {
    return {
      type: "",
      showDetail: true,
      ruleForm: {
        word: "",
      },
      searchList: [],
      extend_words: [],
      replace_words: [],
      replaceInputVisible: false,
      replaceInputValue: "",
      extInputVisible: false,
      extInputValue: "",
    };
  },

  mounted() {
    console.log("---visible", this.rowData, this.isAdd, this.title);
    
    if (this.isAdd) {
      var type = "";
      if (this.rowData.replace_words.length) {
        type = "replace";
      } else if (this.rowData.extend_words.length) {
        type = "extend";
      }
      this.type = type;
      this.ruleForm.word = this.rowData.word;
      this.replace_words = this.rowData.replace_words;
      this.extend_words = this.rowData.extend_words;
      for (let i = 0; i < this.rowData.range_configs.length; i++) {
        this.searchList.push(i + 1);
      }
      this.$nextTick((_) => {
        console.log(this.$refs.scope);
        for (let i = 0; i < this.rowData.range_configs.length; i++) {
        for (let j = 0; j < this.$refs.scope[i].configs.length; j++) {
          if(this.rowData.range_configs[i].type === this.$refs.scope[i].configs[j].type) {
            this.$refs.scope[i].configs[j].values = this.rowData.range_configs[i].values;
            this.$refs.scope[i].configs[j].operation = this.rowData.range_configs[i].operation;
            this.$refs.scope[i].type =  this.rowData.range_configs[i].type;
            break;
          }
        }
        console.log("fsdfsdfsdaf",this.rowData);
      }
      });
     
     
    } else {
      console.log("-0-------");
      this.$nextTick((_) => {
        this.$refs.firstFocus.$refs.input.focus();
      });
      
    }
  },

  methods: {
    onOperateList(option, addId) {
      if (option) {
        this.addSearch();
      } else {
        console.log(this.$refs.scope);
        this.deleteSearch(addId);
      }
    },
    addSearch() {
      if (this.type === "replace") {
        this.$message.error("请先取消替换词");
        return;
      }
      this.searchList.push(Date.now());
    },
    deleteSearch(value) {
      const index = this.searchList.indexOf(value);
      this.searchList.splice(index, 1);
    },
    handleClose(tag) {
      this.replace_words.splice(this.replace_words.indexOf(tag), 1);
    },
    handleExtClose(tag) {
      this.extend_words.splice(this.extend_words.indexOf(tag), 1);
    },

    showInput() {
      if (this.extend_words.length || this.searchList.length) {
        this.$message.error("变更到“替换词”前，请先删除扩展词及搜索范围。");
        return;
      }
      
      this.type = "replace";
      if (this.type === "replace") {
        this.replaceInputVisible = true;
      } else {
        this.extInputVisible = true;
      }

      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    showExtInput() {
    
      if (this.replace_words.length) {
        this.$message.error("请先删除替换词");
        return;
      }
      this.type = "extend";
      this.extInputVisible = true;

      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    clickitemdataType (e) { // e为radio的label值
      e === this.type ? this.type = '' : this.type = e
    },
    handleInputConfirm() {
      // console.log(this.rowData);
      var inputValue;
      if (this.type === "replace") {
        inputValue = this.replaceInputValue;
        if (inputValue) {
          this.replace_words.push(inputValue);
        }
        this.replaceInputVisible = false;
        this.replaceInputValue = "";
      } else {
        inputValue = this.extInputValue;
        if (inputValue) {
          this.extend_words.push(inputValue);
        }
        this.extInputVisible = false;
        this.extInputValue = "";
      }
    },
    closeDialog() {
      this.$emit("update:visible", false);
      this.$emit("getList");
    },
    async addKeywords() {
      console.log(this.$refs.scope);
      if (!this.ruleForm.word.length) {
        this.$message.error("请输入关键词");
        return;
      }
      if (!this.type.length && !this.searchList.length) {
        this.$message.error("请输入");
        return;
      }
      if (this.type === "replace" && !this.replace_words.length) {
        this.$message.error("请输入替换词");
        return;
      }
      if (this.type === "extend" && !this.extend_words.length) {
        this.$message.error("请输入扩展词");
        return;
      }

      var range_configs = [];

      if (this.$refs.scope) {
        for (let i = 0; i < this.$refs.scope.length; i++) {
          if (this.$refs.scope[i].type === "") {
            this.$message.error("请完整填写搜索范围");
            return;
          }
        }
        for (let i = 0; i < this.$refs.scope.length; i++) {
          for (let element of this.$refs.scope[i].configs) {
            if (element.type === this.$refs.scope[i].type) {
              range_configs.push(element);
              break;
            }
          }
        }
      }

      let data = {
        word: this.ruleForm.word,
        replace_words: this.type==="replace" ? this.replace_words : [],
        extend_words: this.type==="extend" ? this.extend_words : [],
        range_configs: range_configs,
      };
      console.log(data);
      var res; 
      if(this.isAdd) {
        res = await this.$request.article.eidtSearchKeywords(data);
      } else {
        res = await this.$request.article.addSearchKeywords(data);
      }
      
      if (res.data.error_code == 0) {
        this.closeDialog();
        this.$message.success("已保存");
      }
    },
  },
};
</script>

<style>
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  width: 180px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 180px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
