<template>
  <div>
    <el-card shadow="never">
      <el-input
        v-model="query.word"
        placeholder="请输入关键词（支持模糊查询）"
        size="mini"
        class="w-large m-r-10"
        clearable
      ></el-input>
      <el-select
        v-model="query.is_disabled"
        size="mini"
        class="m-r-10"
        clearable
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <el-button size="mini" type="primary" @click="search">查询</el-button>
      <el-button size="mini" type="success" @click="add">新增</el-button>
    </el-card>
    <el-card shadow="never" class="m-t-20">
      <el-table
        :data="tableNewData"
        border
        size="small"
        :header-cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column label="关键词" prop="word" width="200">
        </el-table-column>
        <el-table-column
          label="状态"
          prop="statusStr"
          align="center"
          width="100"
        >
        </el-table-column>
        <el-table-column label="预处理配置" prop="optionStr" min-width="200">
          <template slot-scope="scope">
            <!-- <div v-html="scope.row.optionStr">
                    
                </div> -->
            <div style="white-space: pre">{{ scope.row.optionStr }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="200" align="center">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="edit(scope.row)"
              class="m-r-10"
              >编辑</el-button
            >
            <el-popconfirm
              title="确认修改该条数据的状态吗？"
              @confirm="
                updateStatus(scope.row, scope.row.is_disabled == 0 ? 1 : 0)
              "
            >
              <el-button
                type="text"
                size="mini"
                slot="reference"
                class="m-r-10"
                v-if="scope.row.is_disabled != 1"
                style="color: #f56c6c"
                >禁用</el-button
              >
              <el-button
                type="text"
                size="mini"
                slot="reference"
                class="m-r-10"
                v-if="scope.row.is_disabled != 0"
                style="color: #67c23a"
                >启用</el-button
              >
            </el-popconfirm>
            <el-popconfirm
              title="确认删除吗？"
              @confirm="updateStatus(scope.row, 2)"
            >
              <el-button
                type="text"
                size="mini"
                slot="reference"
                style="color: #f56c6c"
                >删除</el-button
              >
            </el-popconfirm>
          </template>
        </el-table-column>
        <el-table-column
          label="添加人"
          prop="operation_name"
          align="center"
          width="100"
        >
        </el-table-column>
        <el-table-column
          label="最后修改时间"
          prop="update_time"
          align="center"
          width="150"
        >
        </el-table-column>
      </el-table>
    </el-card>
    <div style="text-align: center">
      <el-pagination
        background
        style="margin-top: 10px"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        :page-size="query.limit"
        :current-page="query.page"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <Update
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      :rowData="rowData"
      :isAdd="isAdd"
      :title="title"
      :activeName="activeName"
      @getList="searchKeywordsList"
    />
  </div>
</template>

<script>
import Update from "./update.vue";
export default {
  components: {
    Update,
  },
  props: ["activeName"],
  data() {
    return {
      isAdd: 0,
      title: "添加编辑关键词",
      dialogVisible: false,
      rowData: {},
      query: {
        page: 1,
        limit: 10,
        is_disabled: 99,
        word: "",
      },
      total: 0,
      options: [
        {
          value: 99,
          label: "所有状态",
        },
        {
          value: 0,
          label: "已启用",
        },
        {
          value: 1,
          label: "未启用",
        },
      ],
      tableData: [],
      tableNewData: [],
    };
  },
  // watch: {
  //     activeName: {
  //         handler(newVal, oldVal) {
  //             console.log("genre", newVal, oldVal);
  //             this.goodsPoolList();
  //         },
  //         immediate: true,
  //     },
  // },
  // mounted() {
  //     this.goodsPoolList();
  // },
  mounted() {
    this.query.page = 1;
    this.searchKeywordsList();
  },
  methods: {
    search() {
      this.query.page = 1;
      this.searchKeywordsList();
    },
    async searchKeywordsList() {
      let data = {
        ...this.query,
      };
      let res = await this.$request.article.searchKeywordsList(data);
      if (res.data.error_code == 0) {
        this.tableData = res.data.data.list;
        this.total = res.data.data.total;
        this.tableNewData = this.processList(this.tableData);
        // console.log(this.tableData);
        
        // console.log(this.tableNewData);
        console.log(res.data.data.list);
      }
    },
    async updateStatus(row, status) {
      console.log(row, status);
      let data = {
        word: row.word,
        status: status,
      };
      let res = await this.$request.article.changeSearchKeywords(data);
      if (res.data.error_code == 0) {
        this.$message.success("更新成功");
        this.searchKeywordsList();
      }
    },
    add() {
      this.isAdd = 0;
      this.title = "新增关键词";
      this.dialogVisible = true;
    },
    edit(row) {
      const index = this.tableNewData.indexOf(row);
      // console.log(this.tableData[index]);
      this.rowData = this.tableData[index];
      this.isAdd = 1;
      this.title = "编辑关键词";
      this.dialogVisible = true;
    },
    handleSizeChange(limit) {
      this.query.limit = limit;
      this.query.page = 1;
      this.searchKeywordsList();
    },
    handleCurrentChange(page) {
      this.query.page = page;
      this.searchKeywordsList();
    },
    processList(list) {
      //  新数组用于存储结果
      const processedList = [];
      //  遍历每个元素进行处理
      list.forEach((item) => {
        //  新的对象，包含所需的字段和准备添加的  optionStr  数组
        const newItem = {
          word: item.word,
          operation_name: item.operation_name,
          update_time: item.update_time,
          is_disabled: item.is_disabled,
          statusStr: item.is_disabled ? "未启用" : "已启用",
          optionStr: "",
        };
        const strList = [];
        if (item.replace_words.length) {
          strList.push(
            `${strList.length + 1}.替换词-${item.replace_words.join("，")}`
          );
        }
        if (item.extend_words.length) {
          strList.push(
            `${strList.length + 1}.扩展词-${item.extend_words.join("，")}`
          );
        }

        //  处理范围配置  range_configs
        item.range_configs.forEach((config) => {
          // (country国家,regions产区,product_category类型,grape葡萄,price价格,capacity_f容量
          const typeMap = {
            country: "国家",
            regions: "产区",
            product_category: "类型",
            grape: "葡萄",
            price: "价格",
            capacity_f: "容量",
          };
          const typeLabel = typeMap[config.type] || config.type; //  如果类型未在映射中找到，则使用原始值
          var valuesStr;
          var opt = "";
          if (config.operation === "between") {
            valuesStr = config.values.join("-");
            config.operation = "";
          } else {
            valuesStr = config.values.join("，");
          }
          if (config.type === "price") {
            valuesStr += "元";
            opt = config.operation;
          } else if (config.type === "capacity_f") {
            valuesStr += "ml";
            opt = config.operation;
          } else if (config.type === "country") {
            // config.operation = "";
          } else if (config.type === "regions") {
            // config.operation = "";
          } else if (config.type === "grape") {
            // config.operation = "";
          } else if (config.type === "product_category") {
            // config.operation = "";
          }
          strList.push(
            `${strList.length + 1}.搜索范围-${typeLabel}-${
              opt
            }${valuesStr}`
          );
        });
        newItem.optionStr = strList.join("\n");
        //  将处理完的新元素添加到结果数组
        processedList.push(newItem);
      });
      return processedList;
    },
  },
};
</script>

<style></style>

