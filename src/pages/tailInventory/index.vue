<template>
    <div>
        <el-card shadow="hover">
            <el-input
                size="mini"
                @keyup.enter.native="search"
                class="m-r-10"
                style="width: 180px"
                v-model="query.short_code"
                placeholder="简码"
                clearable
            />
            <el-checkbox
             class="m-r-10"
                v-model="query.not_sale"
                :true-label="1"
                :false-label="0"
                >未售</el-checkbox
            >
                                   
            <el-button type="success" @click="search" size="mini"
                >查询
            </el-button>
          
        </el-card>
        <el-card style="margin-top: 10px" shadow="hover">
            <el-table
                size="mini"
                border
                stripe
                :data="DataList"
                fit
                highlight-current-row
                style="width: 100%"
            >
                <el-table-column
                    width="200"
                    label="中文名"
                    align="left"
                >
                    <template slot-scope="scope">
                        <el-tag
                            size="mini"
                            :type="scope.row.period ? 'success' : 'info'"
                            class="m-r-5"
                        >
                            {{ scope.row.period ? '在售' : '未售' }}
                        </el-tag>
                        {{ scope.row.cn_goods_name }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="英文名"
                    align="left"
                    prop="en_goods_name"
                    show-overflow-tooltip
                    min-width="220"
                />
                <el-table-column
                    label="简码"
                    prop="short_code"
                    align="center"
                    width="120"
                >
                </el-table-column>
                <el-table-column
                    label="萌牙库存"
                    prop="short_code"
                    align="center"
                    width="280"
                >
                    <template slot-scope="row">
                        <div v-for="(item, index) in row.row.inventory" :key="index" class="inventory-item">
                            <div class="inventory-content">
                                <span class="store-name">{{ item.fictitious_name }}</span>
                                <span class="inventory-count">{{ `库存：${item.goods_count}` }}</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="在售期数"
                    align="center"
                    width="100"
                >
                <template slot-scope="scope">
                        <div v-if="scope.row.period">
                            <div style="display: flex; flex-wrap: wrap;">
                                <span v-for="(item, index) in scope.row.remaining_sale_inventory" :key="index" style="margin-right: 8px; min-width: 60px;">
                                    {{ item.period }}
                                </span>
                            </div>
                        </div>
                        <div v-else>
                            <div v-if="scope.row.period_info.length">
                                <div>上次开售时间: {{ scope.row.period_info[0].sell_time }}</div>
                            <div>供应商: {{ scope.row.period_info[0].supplier }}</div>
                            <div>采购: {{ scope.row.period_info[0].buyer_name }}</div>
                            <div>成本: {{ scope.row.period_info[0].costprice }}</div>
                            <div>
                                售价: <span v-for="(item, index) in scope.row.period_info[0].package" :key="index">{{ `${item.package_name}${item.price}` }};</span>
                            </div>
                            </div>   
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="剩余售卖库存"
                    align="center"
                    width="100"
                >
                    <template slot-scope="scope">
                        <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                            <span v-for="(item, index) in scope.row.remaining_sale_inventory" :key="index" style="min-width: 40px;">
                                {{ item.inventory }}
                            </span>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 分页 -->
        <div class="pagination-block">
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
       
    </div>
</template>

<script>

export default {
    
    data() {
        return {
            detail: {},
            editDialogVisible: false,
            dialogVisible: false,
            DataList: [],
            query: {
                page: 1,
                limit: 10,
                short_code: "",
                not_sale:0
            },

            total: 0,
        };
    },
    mounted() {
        this.getRealWareHouseList();
    },
    methods: {
        handleClick(row) {
            this.detail = row;
            this.editDialogVisible = true;
        },
        getRealWareHouseList() {
            let data = {
                ...this.query,
            };
            this.$request.product.getTailInventoryList(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.DataList = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        close() {
            this.editDialogVisible = false;
            this.dialogVisible = false;
            this.getRealWareHouseList();
        },
        search() {
            this.query.page = 1;
            this.getRealWareHouseList();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getRealWareHouseList();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getRealWareHouseList();
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.article-main {
    margin: 10px 0;

    /deep/ .el-table .warning-row {
        background: oldlace;
    }

    /deep/ .el-table .danger-row {
        background: oldlace;
    }

    /deep/ .el-table .success-row {
        background: #f0f9eb;
    }
}

.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}

.article-form {
    & > div {
        display: inline-block;
    }
}

.inventory-item {
    margin-bottom: 4px;
    &:last-child {
        margin-bottom: 0;
    }
    .inventory-content {
        background: #f5f7fa;
        border-radius: 4px;
        padding: 4px 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .store-name {
        color: #606266;
        font-weight: 500;
    }
    .inventory-count {
        color: #409EFF;
    }
}
</style>
