<template>
    <div>
        <el-form>
            <el-form-item label="选择直播间">
                <el-select
                    v-model="query.room_id"
                    placeholder="请选择"
                    style="width: 60%"
                    @change="onRoomIdChange"
                >
                    <el-option
                        v-for="item in liveList"
                        :key="item.roomid"
                        :label="`${item.start_time}${item.name}`"
                        :value="item.roomid"
                    />
                </el-select>
            </el-form-item>
        </el-form>
        <template v-if="query.room_id">
            <div style="font-size: 50px; color: red; text-align: center">
                {{ sellAmount }}
            </div>
            <div style="font-size: 24px; text-align: center">销售额</div>

            <el-tabs v-model="activeName" @tab-click="onTabClick">
                <el-tab-pane label="商品列表" name="goodsList">
                    <el-row type="flex" justify="space-between">
                        <div>
                            <el-button
                                type="primary"
                                :disabled="batchPutawatBtnDisabled"
                                @click="onBatchPutaway"
                                >批量上架</el-button
                            >
                            <el-button type="danger" @click="onUpdateGoodsSort"
                                >调整排序</el-button
                            >
                        </div>
                        <el-button type="success" @click="onAddGoods"
                            >添加商品</el-button
                        >
                    </el-row>
                    <el-form>
                        <el-form-item label="状态">
                            <el-checkbox-group
                                v-model="query.filter_status"
                                @change="getGoodsList()"
                            >
                                <el-checkbox
                                    v-for="item in goodsStatusText"
                                    :key="item.value"
                                    :label="item.value"
                                    >{{ item.label }}
                                </el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                    </el-form>
                    <el-table :data="goodsList" border style="margin-top: 20px">
                        <el-table-column
                            label="序号"
                            align="center"
                            prop="goods_id"
                        >
                            <template slot-scope="scope">
                                <el-checkbox
                                    v-model="scope.row.$checked"
                                    :disabled="
                                        ![2, 4].includes(scope.row.status)
                                    "
                                />
                                <span style="margin-left: 5px">{{
                                    scope.row.sort
                                }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="商品信息"
                            align="center"
                            min-width="300"
                        >
                            <template slot-scope="scope">
                                <el-row type="flex">
                                    <div>
                                        <img
                                            :src="scope.row.img_url"
                                            style="width: 50px; height: 50px"
                                        />
                                        <div
                                            v-if="
                                                scope.row.status === 3 &&
                                                scope.row.sort >
                                                    firstPutawaySort
                                            "
                                            style="margin-top: 10px"
                                        >
                                            <el-button
                                                type="info"
                                                size="mini"
                                                @click="onTopGoods(scope.row)"
                                                >置顶</el-button
                                            >
                                        </div>
                                    </div>
                                    <div
                                        style="flex-grow: 1; margin-left: 10px"
                                    >
                                        <el-row
                                            type="flex"
                                            justify="space-between"
                                        >
                                            <span
                                                >{{ scope.row.period_id }} -
                                                {{ scope.row.title }}</span
                                            >
                                            <span
                                                style="
                                                    flex-shrink: 0;
                                                    color: red;
                                                "
                                            >
                                                <span>{{
                                                    scope.row.price
                                                }}</span>
                                                <span v-if="scope.row.price2"
                                                    >-{{
                                                        scope.row.price2
                                                    }}</span
                                                >
                                                <span>元</span>
                                            </span>
                                        </el-row>
                                        <div style="text-align: left">
                                            <div
                                                v-for="item in scope.row
                                                    .packages"
                                                :key="item.package_id"
                                            >
                                                <span>{{
                                                    item.package_name
                                                }}</span>
                                                <span>-</span>
                                                <span>{{ item.price }}元</span>
                                                <span
                                                    style="
                                                        text-decoration: line-through;
                                                        color: #999;
                                                    "
                                                    >{{ " "
                                                    }}{{
                                                        item.market_price
                                                    }}</span
                                                >
                                            </div>
                                        </div>
                                    </div>
                                </el-row>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="状态"
                            align="center"
                            prop="status"
                        >
                            <template slot-scope="scope">
                                {{ scope.row.status | toGoodsStatusText }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="已售"
                            align="center"
                            prop="sell_count"
                        ></el-table-column>
                        <el-table-column label="售罄" align="center">
                            <template slot-scope="scope">
                                {{ scope.row.sell_out ? "是" : "否" }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="备注"
                            align="center"
                            min-width="250"
                        >
                            <template slot-scope="scope">
                                <div style="margin-bottom: 10px">
                                    <el-input
                                        v-if="scope.row.$isEditRemark"
                                        type="textarea"
                                        :autosize="{ minRows: 2 }"
                                        placeholder="请输入备注"
                                        v-model="scope.row.$remark"
                                        :disabled="!scope.row.$isEditRemark"
                                        style="margin-bottom: 10px"
                                    />
                                    <div
                                        v-else
                                        style="text-align: left"
                                        v-html="scope.row.$remark"
                                    ></div>
                                </div>
                                <div
                                    v-if="scope.row.$isEditRemark"
                                    style="text-align: left"
                                >
                                    <el-button
                                        size="mini"
                                        @click="
                                            onCancelUpdateGoodsRemark(scope.row)
                                        "
                                        >取消</el-button
                                    >
                                    <el-button
                                        type="primary"
                                        size="mini"
                                        @click="
                                            onConfirmUpdateGoodsRemark(
                                                scope.row
                                            )
                                        "
                                        >确定</el-button
                                    >
                                </div>
                                <div v-else style="text-align: left">
                                    <el-button
                                        type="primary"
                                        size="mini"
                                        @click="scope.row.$isEditRemark = true"
                                        >编辑</el-button
                                    >
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="操作"
                            align="center"
                            min-width="200"
                        >
                            <template slot-scope="scope">
                                <template v-if="scope.row.status === 2">
                                    <el-button
                                        size="mini"
                                        @click="
                                            onUpdateGoodsStatus(scope.row, 1)
                                        "
                                        >上架</el-button
                                    >
                                    <el-button
                                        size="mini"
                                        @click="
                                            onUpdateGoodsStatus(scope.row, 2)
                                        "
                                        >上架推送</el-button
                                    >
                                </template>
                                <template v-else-if="scope.row.status === 3">
                                    <el-button
                                        size="mini"
                                        @click="
                                            onUpdateGoodsStatus(scope.row, 0)
                                        "
                                        >下架</el-button
                                    >
                                    <el-button
                                        size="mini"
                                        @click="
                                            onUpdateGoodsStatus(scope.row, 3)
                                        "
                                        >推送</el-button
                                    >
                                </template>
                                <template v-else-if="scope.row.status === 4">
                                    <el-button
                                        size="mini"
                                        @click="
                                            onUpdateGoodsStatus(scope.row, 1)
                                        "
                                        >上架</el-button
                                    >
                                </template>
                                <el-button
                                    size="mini"
                                    @click="onDeleteGoods(scope.row)"
                                    >删除</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                <el-tab-pane label="SKU库存" name="skuList">
                    <el-form inline>
                        <el-form-item>
                            <el-input
                                v-model="query.seqnum"
                                placeholder="序号"
                                @keyup.enter.native="getSkuList"
                            ></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-input
                                v-model="query.period_id"
                                placeholder="期数"
                                @keyup.enter.native="getSkuList"
                            ></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-input
                                v-model="query.key_word"
                                placeholder="输入关键词（套餐名称，中文名称，英文名称，简码）"
                                style="width: 400px"
                                @keyup.enter.native="getSkuList"
                            ></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="getSkuList()"
                                >搜索</el-button
                            >
                        </el-form-item>
                    </el-form>
                    <el-table
                        :data="skuList"
                        border
                        @sort-change="onSkuSortChange"
                    >
                        <el-table-column
                            label="序号"
                            align="center"
                            prop="sort"
                        >
                        </el-table-column>
                        <el-table-column
                            label="期数"
                            align="center"
                            prop="period_id"
                        ></el-table-column>
                        <el-table-column
                            label="套餐"
                            align="center"
                            prop="package_name"
                        ></el-table-column>
                        <el-table-column
                            label="中文名称"
                            align="center"
                            prop="product_name"
                        ></el-table-column>
                        <el-table-column
                            label="英文名称"
                            align="center"
                            prop="en_product_name"
                        ></el-table-column>
                        <el-table-column
                            label="简码"
                            align="center"
                            prop="short_code"
                        ></el-table-column>
                        <el-table-column
                            label="实时库存"
                            align="center"
                            prop="inventory"
                            sortable="custom"
                        ></el-table-column>
                        <el-table-column
                            label="成本"
                            align="center"
                            prop="costprice"
                        ></el-table-column>
                        <el-table-column label="价格" align="center">
                            <template slot-scope="scope">
                                <div>直播价：{{ scope.row.price }}</div>
                                <div>原价格：{{ scope.row.market_price }}</div>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
            </el-tabs>

            <el-dialog :visible.sync="addGoodsDialogVisible" title="添加商品">
                <el-form label-width="80px">
                    <el-form-item label="期数">
                        <el-row type="flex">
                            <el-input
                                v-model="goodsParams.periodIdInput"
                                placeholder="请输入期数"
                                style="margin-right: 10px; width: 200px"
                                @keyup.enter.native="onSearchVhGoods"
                            ></el-input>
                            <el-button
                                type="primary"
                                :disabled="!goodsParams.periodIdInput"
                                @click="onSearchVhGoods"
                                >查询</el-button
                            >
                        </el-row>
                    </el-form-item>
                    <el-form-item label="标题">
                        <el-input
                            v-model="goodsParams.title"
                            placeholder="请输入标题"
                            :maxlength="titleMaxLength"
                            show-word-limit
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="价格">
                        <span>{{ goodsParams.price }}</span>
                        <span v-if="goodsParams.price2"
                            >-{{ goodsParams.price2 }}</span
                        >
                    </el-form-item>
                    <el-form-item label="商品封面">
                        <vos-oss
                            v-if="addGoodsDialogVisible"
                            ref="vosOssRef"
                            list-type="picture-card"
                            :showFileList="true"
                            dir="vinehoo/goods-images/"
                            :file-list="goodsParams.imgUrlFileList"
                            :limit="1"
                        >
                            <i class="el-icon-plus" />
                        </vos-oss>
                    </el-form-item>
                    <el-form-item label="备注">
                        <el-input
                            type="textarea"
                            :autosize="{ minRows: 2 }"
                            placeholder="请输入备注"
                            v-model="goodsParams.remarks"
                        />
                    </el-form-item>
                </el-form>
                <div slot="footer">
                    <el-button type="success" @click="onConfirmAddGoods"
                        >确认并提交审核</el-button
                    >
                </div>
            </el-dialog>

            <el-dialog :visible.sync="sortGoodsDialogVisible" title="调整排序">
                <VueDraggable v-model="draggableGoodsList">
                    <el-row
                        v-for="(item, index) in draggableGoodsList"
                        :key="index"
                        type="flex"
                        justify="space-between"
                        style="cursor: pointer"
                    >
                        <el-row type="flex" align="middle">
                            <img
                                src="@/assets/img/draggable_icon.png"
                                style="width: 20px; height: 20px"
                            />
                            <span
                                style="
                                    margin-left: 5px;
                                    font-size: 14px;
                                    color: #606266;
                                    line-height: 40px;
                                "
                            >
                                {{ item.period_id }} - {{ item.title }}
                            </span>
                        </el-row>
                    </el-row>
                </VueDraggable>
                <div slot="footer">
                    <el-button @click="sortGoodsDialogVisible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="onConfirmUpdateGoodsSort"
                        >保存</el-button
                    >
                </div>
            </el-dialog>
        </template>
    </div>
</template>

<script>
import VosOss from "vos-oss";
import VueDraggable from "vuedraggable";
import liveApi from "@/services/live";
const OSS_DOMAIN =
    process.env.NODE_ENV == "development"
        ? "https://images.wineyun.com"
        : "https://images.vinehoo.com";

const goodsStatusText = Object.freeze([
    {
        label: "审核中",
        value: 1,
    },
    {
        label: "待上架",
        value: 2,
    },
    {
        label: "已上架",
        value: 3,
    },
    {
        label: "已下架",
        value: 4,
    },
    {
        label: "审核未通过",
        value: 5,
    },
]);

export default {
    components: {
        VosOss,
        VueDraggable,
    },
    data: () => ({
        goodsStatusText,
        liveList: [],
        activeName: "goodsList",
        query: {
            room_id: "",
            filter_status: [],
            seqnum: "",
            period_id: "",
            key_word: "",
            skuSortMode: "",
        },
        goodsList: [],
        sellAmount: 0,
        skuList: [],
        goodsParams: {
            room_id: "",
            period_id: "",
            periodIdInput: "",
            period_type: "",
            title: "",
            price_type: 1,
            price: "",
            price2: "",
            img_url: "",
            imgUrlFileList: [],
            media_id: "",
            remarks: "",
            mini_url: "",
        },
        addGoodsDialogVisible: false,
        sortGoodsDialogVisible: false,
        draggableGoodsList: [],
        interval: null,
        titleMaxLength: 16,
    }),
    computed: {
        batchPutawatBtnDisabled({ goodsList }) {
            return !goodsList.filter(({ $checked }) => $checked).length;
        },
        firstPutawaySort({ goodsList }) {
            return (
                goodsList
                    .filter((item) => item.status === 3)
                    .map(({ sort }) => sort)[0] || 0
            );
        },
        startTime({ query, liveList }) {
            return (
                liveList.find((item) => item.roomid === query.room_id)
                    ?.start_time || ""
            );
        },
    },
    filters: {
        toGoodsStatusText(status) {
            return (
                goodsStatusText.find((item) => item.value === status)?.label ||
                ""
            );
        },
    },
    created() {
        this.initLiveList();
        this.interval && clearInterval(this.interval);
        this.interval = setInterval(() => {
            this.getGoodsList();
            this.getSkuList();
        }, 10 * 1000);
    },
    beforeDestroy() {
        console.log("beforeDestroy123");
        this.interval && clearInterval(this.interval);
    },
    methods: {
        initLiveList() {
            liveApi.getLiveList().then((res) => {
                if (res.data.error_code === 0) {
                    this.liveList = res.data.data.list;
                }
            });
        },
        onRoomIdChange() {
            this.getGoodsList();
            this.getSkuList();
        },
        getGoodsList() {
            const { room_id, filter_status } = this.query;
            if (!room_id) return;
            const query = {
                room_id,
                filter_status,
                room_start_time: this.startTime,
            };
            liveApi.getGoodsList(query).then((res) => {
                if (res.data.error_code === 0) {
                    const { list, sell_amount } = res.data.data;
                    const checkedList = this.goodsList.filter(
                        (item) => item.$checked
                    );
                    console.log("checkedList", checkedList);
                    const editRemarkList = this.goodsList.filter(
                        (item) => item.$isEditRemark
                    );
                    list.forEach((item) => {
                        item.$checked = checkedList.some(
                            ({ goods_id }) => item.goods_id === goods_id
                        );
                        if (
                            !editRemarkList.some(
                                ({ goods_id }) => item.goods_id === goods_id
                            )
                        ) {
                            item.$remark = item.remarks;
                            item.$isEditRemark = false;
                        }
                    });
                    this.goodsList = list;
                    this.sellAmount = sell_amount;
                }
            });
        },
        onBatchPutaway() {
            this.$confirm("是否确认批量上架？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    const goods_ids = this.goodsList
                        .filter(({ $checked }) => $checked)
                        .map(({ goods_id }) => goods_id);
                    liveApi.batchPutawayGoods({ goods_ids }).then((res) => {
                        if (res.data.error_code === 0) {
                            this.$message.success("操作成功");
                            this.goodsList.forEach((item) => {
                                item.$checked = false;
                            });
                            this.getGoodsList();
                        }
                    });
                })
                .catch(() => {});
        },
        getAllGoodsListQueryParams() {
            return {
                room_id: this.query.room_id,
                filter_status: [],
                room_start_time: this.startTime,
            };
        },
        onUpdateGoodsSort() {
            if (!this.query.room_id) return;
            liveApi
                .getGoodsList(this.getAllGoodsListQueryParams())
                .then((res) => {
                    if (res.data.error_code === 0) {
                        const { list } = res.data.data;
                        this.pendSortGoodsList = list
                            .filter((item) => item.status !== 3)
                            .map(({ goods_id, title }) => ({
                                goods_id,
                                title,
                            }));
                        this.draggableGoodsList = list
                            .filter((item) => item.status === 3)
                            .map(({ goods_id, title, period_id }) => ({
                                goods_id,
                                title,
                                period_id,
                            }));
                        this.sortGoodsDialogVisible = true;
                    }
                });
        },
        onConfirmUpdateGoodsSort() {
            const params = {
                room_id: this.query.room_id,
                goods_ids: [
                    ...this.draggableGoodsList,
                    ...this.pendSortGoodsList,
                ].map(({ goods_id }) => goods_id),
            };
            liveApi.updateGoodsSort(params).then((res) => {
                if (res.data.error_code === 0) {
                    this.$message.success("操作成功");
                    this.getGoodsList();
                    this.sortGoodsDialogVisible = false;
                }
            });
        },
        async onTopGoods(goods) {
            let goodsList = this.goodsList;
            if (this.query.filter_status.length) {
                const goodsListRes = await liveApi.getGoodsList(
                    this.getAllGoodsListQueryParams()
                );
                if (goodsListRes.data.error_code === 0) {
                    goodsList = goodsListRes.data.data.list;
                }
            }
            const topGoodsId = goods.goods_id;
            const goods_ids = [
                topGoodsId,
                ...goodsList
                    .map(({ goods_id }) => goods_id)
                    .filter((item) => topGoodsId !== item),
            ];
            const params = {
                room_id: this.query.room_id,
                goods_ids,
            };
            liveApi.updateGoodsSort(params).then((res) => {
                if (res.data.error_code === 0) {
                    this.$message.success("操作成功");
                    this.getGoodsList();
                }
            });
        },
        onCancelUpdateGoodsRemark(goods) {
            goods.$remark = goods.remarks;
            goods.$isEditRemark = false;
        },
        onConfirmUpdateGoodsRemark(goods) {
            const { goods_id, $remark } = goods;
            liveApi
                .updateGoodsRemark({ goods_id, remarks: $remark })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.$message.success("操作成功");
                        goods.$isEditRemark = false;
                        this.getGoodsList();
                    }
                });
        },
        onUpdateGoodsStatus(goods, status) {
            let desc = "确认修改商品状态吗？";
            switch (status) {
                case 0:
                    desc = "确认下架吗？";
                    break;
                case 1:
                    desc = "确认上架吗？";
                    break;
                case 2:
                    desc = "确认上架推送吗？";
                    break;
                case 3:
                    desc = "确认推送吗？";
                    break;
            }
            this.$confirm(desc, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    const { goods_id } = goods;
                    liveApi
                        .updateGoodsStatus({ goods_id, on_sale: status })
                        .then(async (res) => {
                            if (res.data.error_code === 0) {
                                this.$message.success("操作成功");
                                if ([1, 2].includes(status)) {
                                    let goodsList = this.goodsList;
                                    if (this.query.filter_status.length) {
                                        const goodsListRes =
                                            await liveApi.getGoodsList(
                                                this.getAllGoodsListQueryParams()
                                            );
                                        if (
                                            goodsListRes.data.error_code === 0
                                        ) {
                                            goodsList =
                                                goodsListRes.data.data.list;
                                        }
                                    }
                                    const goods_ids = [
                                        goods_id,
                                        ...goodsList
                                            .map((item) => item.goods_id)
                                            .filter(
                                                (item) => goods_id !== item
                                            ),
                                    ];
                                    const params = {
                                        room_id: this.query.room_id,
                                        goods_ids,
                                    };
                                    liveApi
                                        .updateGoodsSort(params)
                                        .then((res) => {
                                            if (res.data.error_code === 0) {
                                                this.getGoodsList();
                                            }
                                        });
                                } else {
                                    this.getGoodsList();
                                }
                            }
                        });
                })
                .catch(() => {});
        },
        onDeleteGoods(goods) {
            this.$confirm("确认删除吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    const { goods_id } = goods;
                    liveApi
                        .deleteGoods({ goods_ids: [goods_id] })
                        .then((res) => {
                            if (res.data.error_code === 0) {
                                this.$message.success("操作成功");
                                this.getGoodsList();
                            }
                        });
                })
                .catch(() => {});
        },
        getSkuList() {
            if (!this.query.room_id) return;
            const { room_id, skuSortMode, seqnum, period_id, key_word } =
                this.query;
            const query = { room_id };
            if (skuSortMode) {
                query.sort_mode = skuSortMode;
            }
            if (seqnum) {
                query.goods_id = seqnum.trim();
            }
            if (period_id) query.period_id = period_id.trim();
            if (key_word) query.key_word = key_word.trim();
            liveApi.getSkuList(query).then((res) => {
                if (res.data.error_code === 0) {
                    const { list } = res.data.data;
                    this.skuList = Object.freeze(list);
                }
            });
        },
        onSkuSortChange({ prop, order }) {
            if (prop === "inventory" && order === "descending") {
                this.query.skuSortMode = "inventory";
            }
            this.getSkuList();
        },
        onTabClick() {
            if (this.query.room_id) {
                switch (this.activeName) {
                    case "goodsList":
                        this.getGoodsList();
                        break;
                    case "skuList":
                        this.getSkuList();
                        break;
                    default:
                        break;
                }
            }
        },
        onAddGoods() {
            this.goodsParams = this.$options.data().goodsParams;
            this.addGoodsDialogVisible = true;
        },
        onSearchVhGoods() {
            liveApi
                .getVhGoods({ period: this.goodsParams.periodIdInput })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        const goods = res.data.data;
                        if (!goods) {
                            this.$message.error("该期数不存在");
                            return;
                        }
                        if (goods.id === this.goodsParams.period_id) return;
                        this.$refs.vosOssRef.viewFileList = [];
                        const {
                            id,
                            periods_type,
                            title,
                            package_prices,
                            product_img,
                        } = goods;
                        const priceList = package_prices
                            .split("/")
                            .map((item) => +item);
                        const priceListLength = priceList.length;
                        const price = priceList[0];
                        const price2 =
                            priceListLength > 1
                                ? priceList[priceListLength - 1]
                                : 0;
                        this.goodsParams = Object.assign({}, this.goodsParams, {
                            period_id: id,
                            period_type: periods_type,
                            title: title.slice(0, this.titleMaxLength),
                            price,
                            price2,
                            imgUrlFileList: [product_img],
                        });
                    }
                });
        },
        onConfirmAddGoods() {
            const [img_url] = this.goodsParams.imgUrlFileList.map(
                (item) => `${OSS_DOMAIN}${item}`
            );
            const [file_name] = img_url
                .split("/")
                .slice(-1)
                .map((item) => item.split("?")[0]);
            const params = {
                url: `${img_url}${
                    img_url.includes("?") ? "&" : "?"
                }x-oss-process=image/resize,w_300,h_300`,
                file_name,
            };
            liveApi.getImgMediaId(params).then((res) => {
                if (res.data.error_code === 0) {
                    const media_id = res.data.data.media_id;
                    const {
                        period_id,
                        period_type,
                        title,
                        price,
                        price2,
                        remarks,
                    } = this.goodsParams;
                    const params = {
                        room_id: this.query.room_id,
                        period_id,
                        period_type,
                        title,
                        price,
                        price_type: 1,
                        img_url,
                        media_id,
                        remarks,
                        mini_url: `pages/goods-detail/goods-detail?id=${period_id}`,
                    };
                    if (price2) {
                        params.price_type = 2;
                        params.price2 = price2;
                    }
                    liveApi.addGoods(params).then((res) => {
                        if (res.data.error_code === 0) {
                            this.$message.success("添加成功");
                            this.getGoodsList();
                            this.goodsParams = this.$options.data().goodsParams;
                            this.addGoodsDialogVisible = false;
                        }
                    });
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>
