<template>
    <div class="article-layout">
        <el-card>
            <div class="article-form">
                <div>
                    <el-select
                        v-model="query.type"
                        placeholder="请选择标签类型"
                        size="mini"
                        class="m-r-10"
                        @change="search"
                    >
                        <el-option
                            v-for="item in lableTypeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                    <el-input
                        v-model="query.name"
                        class="w-mini m-r-10"
                        placeholder="标签名"
                        @keyup.enter.native="search"
                        size="mini"
                        clearable
                    >
                    </el-input>
                </div>

                <div class="form-actions">
                    <div style="display: flex">
                        <el-button
                            class="m-r-10"
                            size="mini"
                            type="warning"
                            @click="search"
                            >查询</el-button
                        >
                        <el-popover
                            placement="bottom"
                            title="请选择要新增的标签类型"
                            trigger="click"
                        >
                            <el-button
                                size="mini"
                                type="warning"
                                @click="handleAdd(1)"
                                >搜索标签</el-button
                            >
                            <el-button
                                size="mini"
                                type="primary"
                                @click="handleAdd(2)"
                                >商品标签</el-button
                            >
                            <el-button
                                size="mini"
                                type="success"
                                slot="reference"
                                >新增</el-button
                            >
                        </el-popover>
                    </div>
                </div>
            </div>
        </el-card>
        <div class="article-main">
            <el-card>
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        prop="name"
                        :label="
                            query.type == 1 ? '搜索标签名称' : '商品标签名称'
                        "
                        show-overflow-tooltip
                        min-width="240"
                        align="center"
                    />
                    <el-table-column
                        prop="operator"
                        label="创建人"
                        show-overflow-tooltip
                        min-width="240"
                        align="center"
                    />

                    <el-table-column
                        prop="created_time"
                        label="创建时间"
                        min-width="170"
                        align="center"
                    />
                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        min-width="180"
                        align="center"
                    >
                        <template slot-scope="{ row }">
                            <el-button
                                size="mini"
                                type="primary"
                                @click="handleEdit(row)"
                            >
                                编辑
                            </el-button>

                            <el-button
                                size="mini"
                                type="danger"
                                @click="handleDel(row)"
                                >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <!-- 弹框 -->
        <el-dialog
            :visible.sync="diaVisible"
            :title="`${isEdit ? '编辑' : '新增'}${
                ruleForm.type == 1 ? '搜索' : '商品'
            }标签`"
            custom-class="dialogwid"
            @close="close"
        >
            <el-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleForm"
                size="mini"
                label-width="100px"
                class="demo-ruleForm"
            >
                <el-form-item
                    :label="ruleForm.type == 1 ? '搜索标签' : '商品标签'"
                    prop="name"
                >
                    <el-input
                        placeholder="请输入标签"
                        v-model="ruleForm.name"
                    ></el-input>
                </el-form-item>
                <AddGoodsChoose
                    v-if="ruleForm.type == 2"
                    :isEdit=isEdit
                    :add_method="ruleForm.add_method"
                    :auto_add_type="ruleForm.auto_add_type"
                    :auto_add_content="ruleForm.auto_add_content"
                    ref="AddGoodsChoose"
                ></AddGoodsChoose>
                <el-form-item>
                    <el-button plane @click="diaVisible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="submitForm('ruleForm')"
                        >确认</el-button
                    >
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
import AddGoodsChoose from "../../components/addGoodsChoose/addGoodsChoose.vue";

export default {
    components: {
        AddGoodsChoose
    },
    data() {
        return {
            diaVisible: false,
            isEdit: false,
            total: 0,
            tableData: [],
            lableTypeOptions: [
                {
                    value: 1,
                    label: "搜索标签",
                },
                {
                    value: 2,
                    label: "商品标签",
                },
            ],
            query: {
                page: 1,
                limit: 10,
                name: "",
                type: 2,
            },
            ruleForm: {
                name: "",
                type: "",
                add_method:0,
                auto_add_type:1,
                auto_add_content:[],
            },
            labelId: "",
            rules: {
                name: [
                    {
                        required: true,
                        message: "请输入标签名称",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    mounted() {
        this.getData();
    },
    methods: {
        getData() {
            if (!this.query.type) {
                this.$message.warning("请选择标签类型");
                return;
            }
            this.$request.recommendLabel.labelList(this.query).then((res) => {
                if (res.data.error_code == 0) {
                    this.tableData = res?.data?.data?.list || [];
                    this.total = res.data.data.total;
                }
            });
        },

        search() {
            this.query.page = 1;
            this.getData();
        },

        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },

        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },

        handleAdd(type) {
            this.ruleForm.type = type;
            this.isEdit = false;
            this.diaVisible = true;
        },

        handleEdit(item) {
            this.isEdit = true;
            this.ruleForm.name = item.name;
            this.ruleForm.type = item.type;
            this.ruleForm.add_method = item.add_method;
            this.ruleForm.auto_add_type = item.auto_add_type;
            this.ruleForm.auto_add_content = item.auto_add_content;
            this.labelId = item.id;
            this.diaVisible = true;
        },

        handleDel({ id }) {
            this.$confirm("确认删除该标签嘛?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.$request.recommendLabel
                        .labelEdit({ id, is_delete: 1 })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("删除成功");
                                this.getData();
                                this.diaVisible = false;
                            }
                        });
                })
                .catch(() => {
                    this.$message.info("已取消删除");
                });
        },

        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        name: this.ruleForm.name,
                    };
                    if(this.ruleForm.type == 2) {
                        const {
                            add_methodnew, auto_add_typeNew,  resultArr
                        } = this.$refs.AddGoodsChoose.getEditData(); //接受路径配置传过来的参数

                        // 检查是否有选择内容
                        let hasContent = false;
                        if (add_methodnew == 1 && resultArr && typeof resultArr === 'object') {
                            hasContent = Object.keys(resultArr).some(key =>
                                resultArr[key] && resultArr[key].length > 0
                            );
                        }

                        if(add_methodnew==1 && !hasContent){
                            this.$message.error("定义内容不能为空");
                            return;
                        }

                        data.add_method=add_methodnew;
                        if(add_methodnew == 1) {
                            data.auto_add_type=auto_add_typeNew;
                            data.auto_add_content=resultArr;
                        }
                    }

                    if (this.isEdit) {
                        data.id = this.labelId;
                    } else {
                        data.type = this.ruleForm.type;
                    }
                    this.$request.recommendLabel[
                        this.isEdit ? "labelEdit" : "labelAdd"
                    ](data).then((res) => {
                        if (res.data.error_code == 0) {
                            this.$message.success("操作成功！");
                            this.getData();
                            this.diaVisible = false;
                        }
                    });
                } else {
                    return false;
                }
            });
        },

        close() {
            const { isEdit, ruleForm, labelId } = this.$options.data();
            this.isEdit = isEdit;
            this.labelId = labelId;
            this.ruleForm = ruleForm;
            this.$refs.ruleForm.resetFields();
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.article-layout {
    .article-main {
        .el-button--mini,
        .el-button--mini.is-round {
            padding: 0;
            background-color: #ffffff;
            border-color: #ffffff;
            color: #66b1ff;
        }

        margin: 10px 0;

        /deep/ .el-table .warning-row {
            background: oldlace;
        }

        /deep/ .el-table .danger-row {
            background: oldlace;
        }

        /deep/ .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
</style>
