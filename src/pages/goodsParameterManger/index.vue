<template>
  <div class="goods-parameter-manager">
    <div class="operation-bar">
      <template v-if="!isEditing">
        <el-button type="primary" @click="startEdit">修改</el-button>
      </template>
      <template v-else>
        <el-button type="success" @click="handleSave">保存</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </template>
    </div>

    <div class="table-container">
      <div v-for="(group, index) in tableData" :key="index" class="table-group">
        <div class="group-title">{{ group.periods_type_name }}</div>
        <el-table :data="group.list" stripe>
          <!-- 参数名称 -->
          <el-table-column prop="name" label="参数名称" min-width="120" />

          <!-- 前端显示 -->
          <el-table-column label="前端显示" width="120" align="center">
            <template #default="scope">
              <el-checkbox
                v-model="scope.row.is_display"
                :true-label="1"
                :false-label="0"
                :disabled="!isEditing"
              />
            </template>
          </el-table-column>

          <!-- 默认值 -->
          <el-table-column label="默认值" width="120" align="center">
            <template #default="scope">
              <el-checkbox
                v-model="scope.row.is_default"
                :true-label="1"
                :false-label="0"
                :disabled="!isEditing"
              />
            </template>
          </el-table-column>

          <!-- 参数说明 -->
          <el-table-column prop="field_desc" label="参数说明" min-width="180" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GoodsParameterManager',
  data() {
    return {
      tableData: [],
      backupData: [], // 用于存储编辑前的数据
      isEditing: false
    }
  },
  mounted() {
    this.getParameterList()
  },
  methods: {
    // 获取参数列表
    async getParameterList() {
      this.$request.product.getGoodsparameters({}).then((res) => {
        if (res.data.error_code == 0) {
          this.tableData = res.data.data.list
          this.backupData = JSON.parse(JSON.stringify(res.data.data.list))
        }
      })
    },

    // 开始编辑
    startEdit() {
      this.isEditing = true
      // 备份当前数据
      this.backupData = JSON.parse(JSON.stringify(this.tableData))
    },

    // 处理保存
    async handleSave() {
      try {
        // 构造更新数据
        const updateData = []
        this.tableData.forEach(group => {
          group.list.forEach(item => {
            updateData.push({
              id: item.id,
              is_display: item.is_display,
              is_default: item.is_default
            })
          })
        })

        const res = await this.$request.product.updateGoodsParameters({param:updateData})
        if (res.data.error_code === 0) {
          this.$message.success('保存成功')
          this.isEditing = false
          this.getParameterList() // 重新获取数据
        } else {
          this.$message.error(res.data.msg || '保存失败')
        }
      } catch (error) {
        // this.$message.error('保存失败')
        console.error(error)
      }
    },

    // 处理取消
    handleCancel() {
      this.tableData = JSON.parse(JSON.stringify(this.backupData))
      this.isEditing = false
    }
  }
}
</script>

<style lang="scss" scoped>
.goods-parameter-manager {
  padding: 20px;
  
  .operation-bar {
    margin-bottom: 20px;
    
    .el-button {
      margin-right: 10px;
    }
  }
  
  .table-container {
    .table-group {
      margin-bottom: 20px;
      
      .group-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 10px;
        padding: 8px;
        background-color: #f5f7fa;
      }
    }
  }
}
</style>
