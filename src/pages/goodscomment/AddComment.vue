<template>
    <div>
        <el-form
            ref="form"
            :model="datas"
            label-width="100px"
            :rules="rules"
            label-position="top"
        >
            <el-form-item
                label="商品期数"
                prop="period"
                style="display: inline-block; width: 45%; margin-right: 20px"
            >
                <el-col :span="24">
                    <el-input
                        oninput="value=value.replace(/[^0-9]/g,'')"
                        v-model="datas.period"
                        placeholder="请输入期数"
                        :disabled="!!externalPeriod"
                    />
                </el-col>
            </el-form-item>
            <el-form-item
                label="马甲账号"
                prop="uid"
                style="display: inline-block; width: 45%"
            >
                <el-col :span="24">
                    <el-select
                        v-model="datas.uid"
                        clearable
                        filterable
                        @change="userChange"
                        style="width: 100%"
                        placeholder="请选择"
                        class="filter-item"
                    >
                        <el-option
                            v-for="item in uidOption"
                            :key="item.uid"
                            :label="
                                item.user_level +
                                '级 - ' +
                                item.nickname +
                                ' - ' +
                                item.label
                            "
                            :value="item.uid"
                        />
                    </el-select>
                </el-col>
            </el-form-item>

            <el-form-item
                label="是否购买"
                prop="vest_is_buy"
                style="display: inline-block; width: 45%; margin-right: 20px"
            >
                <el-radio-group v-model="datas.vest_is_buy">
                    <el-radio
                        v-for="(item, key) in vest_is_buyOptions"
                        :key="key"
                        :label="item.label"
                        >{{ item.value }}</el-radio
                    >
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="计划任务"
                style="display: inline-block; width: 45%"
            >
                <el-radio-group v-model="datas.automatic_task">
                    <el-radio
                        v-for="(item, key) in automatic_taskOptions"
                        :key="key"
                        :label="item.label"
                        >{{ item.value }}</el-radio
                    >
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="发送时间"
                prop="automatic_task_time"
                v-if="datas.automatic_task == 1"
            >
                <el-col :span="11">
                    <el-date-picker
                        value-format="yyyy-MM-dd HH:mm:ss"
                        v-model="datas.automatic_task_time"
                        type="datetime"
                        style="width: 100%"
                        placeholder="请选择发送时间"
                    >
                    </el-date-picker>
                </el-col>
            </el-form-item>
            <el-form-item label="评论提示词" v-if="selectedUser">
                <template slot="label">
                    <div
                        style="
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            width: 600px;
                        "
                    >
                        <span>评论提示词</span>
                        <el-button
                            type="primary"
                            size="small"
                            @click="updateCommentPrompts"
                            >更新提示词</el-button
                        >
                    </div>
                </template>
                <el-col :span="22">
                    <div>
                        <el-input
                            v-model="commentPrompts"
                            type="textarea"
                            :rows="2"
                            placeholder="请输入评论提示词"
                        />
                    </div>
                </el-col>
            </el-form-item>
            <el-form-item label="评论内容" prop="content">
                <el-col :span="22" style="display: flex">
                    <div style="flex: 1">
                        <el-input
                            v-model="datas.content"
                            placeholder="请输入回复内容"
                            type="textarea"
                            :autosize="{ minRows: 4 }"
                        />
                    </div>
                </el-col>
            </el-form-item>
            <el-form-item>
                <el-col :span="22">
                    <div
                        style="
                            display: flex;
                            align-items: center;
                            flex-wrap: wrap;
                        "
                    >
                        <div
                            style="
                                display: flex;
                                align-items: center;
                                width: 100%;
                                margin-bottom: 10px;
                            "
                        >
                            <!-- <el-checkbox 
                                v-model="useGpt" 
                                style="margin-bottom: 0px;"
                            >ChatGPT</el-checkbox>
                            <span v-if="useGpt" style="margin-left: 10px; font-size: 13px; color: #606266;">温度值</span>
                            <i v-if="useGpt" 
                               class="el-icon-question" 
                               style="margin-left: 4px; color: #909399; cursor: pointer;"
                               @click="showTemperatureInfo = true"></i>
                            <el-slider
                                v-if="useGpt"
                                v-model="gptTemperature"
                                :min="0.5"
                                :max="1"
                                :step="0.1"
                                style="flex: 1; margin-left: 12px;"
                            ></el-slider>
                            <span v-if="useGpt" style="margin-right: 8px; margin-left: 12px; font-size: 13px; color: #606266;">{{ gptTemperature }}</span> -->
                            <el-checkbox
                                v-model="useDs"
                                style="margin-bottom: 0px"
                                >DeepSeek-R1</el-checkbox
                            >
                            <span
                                v-if="useDs"
                                style="
                                    margin-left: 10px;
                                    font-size: 13px;
                                    color: #606266;
                                "
                                >温度值</span
                            >
                            <i
                                v-if="useDs"
                                class="el-icon-question"
                                style="
                                    margin-left: 4px;
                                    color: #909399;
                                    cursor: pointer;
                                "
                                @click="showTemperatureInfo = true"
                            ></i>
                            <el-slider
                                v-if="useDs"
                                v-model="dsTemperature"
                                :min="0.5"
                                :max="1"
                                :step="0.1"
                                style="flex: 1; margin-left: 12px"
                            ></el-slider>
                            <span
                                v-if="useDs"
                                style="
                                    margin-right: 8px;
                                    margin-left: 12px;
                                    font-size: 13px;
                                    color: #606266;
                                "
                                >{{ dsTemperature }}</span
                            >
                        </div>
                    </div>
                </el-col>
            </el-form-item>
            <el-form-item>
                <el-col :span="22">
                    <div style="display: flex; align-items: center">
                        <Face ref="face" @getEmoji="getEmoji" />
                        <div
                            style="
                                display: flex;
                                align-items: center;
                                flex: 1;
                                justify-content: space-between;
                            "
                        >
                            <div style="display: flex; align-items: center">
                                <el-button
                                    type="primary"
                                    size="small"
                                    @click="generateComment"
                                    style="margin-left: 20px"
                                    >生成评论</el-button
                                >
                                <el-button
                                    v-if="aiContent.gpt || aiContent.ds"
                                    type="primary"
                                    size="small"
                                    style="margin-left: 20px"
                                    @click="dialogVisible = true"
                                    >切换评论内容</el-button
                                >
                            </div>
                        </div>
                    </div>
                </el-col>
            </el-form-item>
        </el-form>
        <div class="dialog-footer">
            <el-button @click="$emit('close')">取 消</el-button>
            <el-button type="primary" @click="submits">确 定</el-button>
        </div>
        <el-dialog
            title="选择AI生成内容"
            :visible.sync="dialogVisible"
            width="900px"
            append-to-body
            :before-close="handleClose"
        >
            <div class="content-preview">
                <div class="content-container">
                    <div class="content-item left">
                        <h4>DeepSeek推理过程：</h4>
                        <div class="think-box">
                            <div class="think-content" ref="dsThinkContent">
                                {{ aiContent.ds_think }}
                            </div>
                        </div>
                        <h4>DeepSeek生成评论：</h4>
                        <div class="content-box" ref="dsContentBox">
                            {{
                                dsHistory.length > 0
                                    ? dsHistory[dsCurrentPage - 1]
                                    : aiContent.ds
                            }}
                        </div>
                        <div
                            style="
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                margin-top: 10px;
                            "
                        >
                            <div style="display: flex; align-items: center">
                                <el-button
                                    type="primary"
                                    size="small"
                                    @click="selectContent('ds')"
                                    :disabled="dsGenerating"
                                    >{{
                                        dsGenerating
                                            ? "生成中..."
                                            : "使用此评论"
                                    }}</el-button
                                >
                                <el-button
                                    icon="el-icon-refresh"
                                    size="small"
                                    @click="regenerateContent('ds')"
                                    style="margin-left: 10px"
                                    :disabled="dsGenerating"
                                    >重新生成</el-button
                                >
                            </div>
                            <div
                                v-if="dsHistory.length > 0"
                                style="display: flex; align-items: center"
                            >
                                <el-button
                                    icon="el-icon-arrow-left"
                                    size="mini"
                                    :disabled="dsCurrentPage === 1"
                                    @click="dsCurrentPage--"
                                ></el-button>
                                <span style="margin: 0 10px"
                                    >{{ dsCurrentPage }}/{{
                                        dsHistory.length
                                    }}</span
                                >
                                <el-button
                                    icon="el-icon-arrow-right"
                                    size="mini"
                                    :disabled="
                                        dsCurrentPage === dsHistory.length
                                    "
                                    @click="dsCurrentPage++"
                                ></el-button>
                            </div>
                        </div>
                        <div
                            style="
                                display: flex;
                                align-items: center;
                                margin-top: 10px;
                            "
                        >
                            <div
                                style="
                                    display: flex;
                                    align-items: center;
                                    margin-left: 15px;
                                "
                            >
                                <span
                                    style="
                                        font-size: 13px;
                                        color: #606266;
                                        margin-right: 8px;
                                    "
                                    >温度值:</span
                                >
                                <el-slider
                                    v-model="dsTemperature"
                                    :min="0.5"
                                    :max="1"
                                    :step="0.1"
                                    style="width: 100px; margin-right: 8px"
                                ></el-slider>
                                <span style="font-size: 13px; color: #606266">{{
                                    dsTemperature
                                }}</span>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="content-item right">
                        <h4>ChatGPT生成评论：</h4>
                        <div v-if="aiContent.gpt_think" class="think-box">
                            <div class="think-content" ref="gptThinkContent">
                                {{ aiContent.gpt_think }}
                            </div>
                        </div>
                        <div class="content-box" ref="gptContentBox">
                            {{
                                gptHistory.length > 0
                                    ? gptHistory[gptCurrentPage - 1]
                                    : aiContent.gpt
                            }}
                        </div>
                        <div
                            style="
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                margin-top: 10px;
                            "
                        >
                            <div style="display: flex; align-items: center">
                                <el-button
                                    type="primary"
                                    size="small"
                                    @click="selectContent('gpt')"
                                    :disabled="gptGenerating"
                                    >{{
                                        gptGenerating
                                            ? "生成中..."
                                            : "使用此评论"
                                    }}</el-button
                                >
                                <el-button
                                    icon="el-icon-refresh"
                                    size="small"
                                    @click="regenerateContent('gpt')"
                                    style="margin-left: 10px"
                                    :disabled="gptGenerating"
                                    >重新生成</el-button
                                >
                            </div>
                            <div
                                v-if="gptHistory.length > 0"
                                style="display: flex; align-items: center"
                            >
                                <el-button
                                    icon="el-icon-arrow-left"
                                    size="mini"
                                    :disabled="gptCurrentPage === 1"
                                    @click="gptCurrentPage--"
                                ></el-button>
                                <span style="margin: 0 10px"
                                    >{{ gptCurrentPage }}/{{
                                        gptHistory.length
                                    }}</span
                                >
                                <el-button
                                    icon="el-icon-arrow-right"
                                    size="mini"
                                    :disabled="
                                        gptCurrentPage === gptHistory.length
                                    "
                                    @click="gptCurrentPage++"
                                ></el-button>
                            </div>
                        </div>
                        <div
                            style="
                                display: flex;
                                align-items: center;
                                margin-top: 10px;
                            "
                        >
                            <div
                                style="
                                    display: flex;
                                    align-items: center;
                                    margin-left: 15px;
                                "
                            >
                                <span
                                    style="
                                        font-size: 13px;
                                        color: #606266;
                                        margin-right: 8px;
                                    "
                                    >温度值:</span
                                >
                                <el-slider
                                    v-model="gptTemperature"
                                    :min="0.5"
                                    :max="1"
                                    :step="0.1"
                                    style="width: 100px; margin-right: 8px"
                                ></el-slider>
                                <span style="font-size: 13px; color: #606266">{{
                                    gptTemperature
                                }}</span>
                            </div>
                        </div>
                    </div> -->
                </div>
            </div>
        </el-dialog>
        <el-dialog
            title="DeepSeek推理过程"
            :visible.sync="showThinkingDialog"
            width="600px"
            append-to-body
        >
            <div class="thinking-content-dialog">
                {{ aiContent.ds_think }}
            </div>
        </el-dialog>
        <el-dialog
            title="温度值（Temperature）"
            :visible.sync="showTemperatureInfo"
            width="500px"
            append-to-body
            custom-class="temperature-info-dialog"
        >
            <div class="temperature-info">
                <p>
                    大语言模型里的
                    <strong>temperature（温度）</strong>
                    参数，用来控制生成文本的随机性和创造性。简单来说：
                </p>

                <ul>
                    <li>
                        <strong>高温度</strong
                        >：更随机，更具创造性，但也可能出错。
                    </li>
                    <li>
                        <strong>低温度</strong
                        >：更稳定，更准确，但可能缺乏新意。
                    </li>
                </ul>

                <p><strong>使用方法：</strong></p>

                <ol>
                    <li>
                        <strong>理解平衡：</strong>
                        调整温度就是在随机性和准确性之间做选择。
                    </li>
                    <li>
                        <strong>设置数值：</strong> 一般在 0 到 1 之间，可以从
                        0.7 开始尝试。
                    </li>
                </ol>

                <p><strong>应用场景：</strong></p>

                <ul>
                    <li>
                        <strong>创意类任务：</strong>
                        比如写作、头脑风暴，用高温度（0.7 以上），鼓励模型发散。
                    </li>
                    <li>
                        <strong>精确类任务：</strong>
                        比如写代码、查资料，用低温度（0.3 以下），保证结果可靠。
                    </li>
                </ul>

                <p>
                    <strong>实验调整：</strong>
                    没有固定答案，根据你的需求多试试。
                </p>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import Face from "../../components/face/index.vue";
//import axios from "axios";
export default {
    components: {
        Face,
    },
    props: {
        externalPeriod: {
            type: [String, Number],
            default: "",
        },
    },
    data() {
        return {
            datas: {
                period: "",
                uid: "",
                uname: "",
                content: "",
                vest_is_buy: 0,
                automatic_task: 0,
                automatic_task_time: "",
            },
            commontList: [],
            automatic_taskOptions: [
                {
                    label: 1,
                    value: "是",
                },
                {
                    label: 0,
                    value: "否",
                },
            ],
            vest_is_buyOptions: [
                {
                    label: 1,
                    value: "是",
                },
                {
                    label: 0,
                    value: "否",
                },
            ],
            rules: {
                automatic_task_time: [
                    {
                        required: true,
                        message: "请选择发送时间",
                        trigger: "change",
                    },
                ],
                uid: [
                    {
                        required: true,
                        message: "请选择马甲账号",
                        trigger: "change",
                    },
                ],
                period: [
                    {
                        required: true,
                        message: "请输入商品ID",
                        trigger: "blur",
                    },
                ],
                content: [
                    {
                        required: true,
                        message: "请输入评论内容",
                        trigger: "blur",
                    },
                ],
            },
            uidOption: [],
            selectedUser: null,
            commentPrompts: "",
            currentPage: 1,
            pageSize: 10,
            selectedAI: "gpt",
            aiContent: {
                gpt: "",
                ds: "",
                ds_think: "",
                gpt_think: "",
            },
            dialogVisible: false,
            showThinkingDialog: false,
            useGpt: true,
            useDs: true,
            temperature: 0.7,
            gptTemperature: 0.7,
            dsTemperature: 0.7,
            showTemperatureInfo: false,
            dsGenerating: false,
            gptGenerating: false,
            dsHistory: [],
            gptHistory: [],
            dsCurrentPage: 1,
            gptCurrentPage: 1,
            lastDsContent: "",
            lastGptContent: "",
        };
    },
    mounted() {
        this.getVestList();
        if (this.externalPeriod) {
            this.datas.period = this.externalPeriod;
        }
    },
    methods: {
        userChange() {
            this.selectedUser = this.uidOption.find((item) => {
                return item.uid == this.datas.uid;
            });
            this.datas.uname = this.selectedUser.nickname;
            this.commentPrompts = this.selectedUser.comment_prompts
                ? this.selectedUser.comment_prompts.join(",")
                : "";
        },
        async getVestList() {
            const res = await this.$request.article.getVestList();
            if (res.data.error_code == 0) {
                this.uidOption = res.data.data.list;
            }
        },
        getEmoji(item) {
            this.datas.content = this.datas.content + item;
        },
        //提交
        async submits() {
            if (this.validateForm()) {
                this.datas.emoji_image = this.$refs.face.chooseFaceTitle;
                const data = {
                    ...this.datas,
                };
                console.log("表单", data);
                this.$request.article.createComment(data).then((res) => {
                    if (res.data.error_code == 0) {
                        this.$emit("close");
                        this.$message({
                            type: "success",
                            message: "操作成功",
                        });
                    }
                });
            }
        },
        validateForm() {
            let flag = null;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
        async updateCommentPrompts() {
            const data = {
                id: this.selectedUser.id,
                avatar_image: this.selectedUser.avatar_image,
                type: this.selectedUser.type,
                nickname: this.selectedUser.nickname,
                label: this.selectedUser.label,
                exps: this.selectedUser.exps,
                comment_prompts: this.commentPrompts
                    .split(",")
                    .filter((item) => item.trim()),
                admin_id: this.selectedUser.admin_id,
                admin_name: this.selectedUser.admin_name,
            };
            this.$request.article.updatevestuser(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.$message({
                        type: "success",
                        message: "提示词更新成功",
                    });
                    this.getVestList();
                }
            });
        },
        async getCommentList() {
            const data = {
                page: this.currentPage,
                limit: this.pageSize,
                uname: this.selectedUser.nickname,
            };
            const res = await this.$request.article.getCommentList(data);
            if (res.data.error_code === 0) {
                this.commontList = res.data.data.list;
            }
        },
        async generateComment() {
            if (!this.datas.period) {
                this.$message.error("请先填写商品期数");
                return;
            }
            try {
                // 获取商品信息
                const url = `https://images.vinehoo.com/vinehoo/client/commodities/periods/${this.datas.period}.json?t=1&isJson=true&id=${this.datas.period}`;
                const response = await fetch(url);
                const data = await response.json();
                const title = data.data.title;

                // 获取评论列表
                await this.getCommentList();

                // 拼接所有评论内容
                const allComments = this.commontList
                    .map((item) => item.content)
                    .join("\n");

                // 重置AI内容
                this.aiContent = {
                    gpt: "",
                    ds: "",
                    ds_think: "",
                    gpt_think: "",
                };
                this.lastDsContent = "";
                this.lastGptContent = "";

                // 立即显示选择弹窗
                this.dialogVisible = true;
                // 如果选择了DeepSeek
                if (this.useDs) {
                    this.dsGenerating = true;
                    const eventSource = new EventSource(
                        `https://chatgpt.vinehoo.com/emb/embedding/v3/completion/commentStream?use_ds=true&use_gpt=false&prompt=${encodeURIComponent(
                            this.commentPrompts
                        )}&title=${encodeURIComponent(
                            title
                        )}&use_ds=true&temperature=${
                            this.dsTemperature
                        }&wait_time=25&pid=${this.datas.period}`
                    );

                    eventSource.onmessage = (event) => {
                        const data = JSON.parse(event.data);
                        if (data.is_think) {
                            this.aiContent.ds_think += data.msg;
                        } else {
                            this.aiContent.ds += data.msg.trim();
                        }
                    };

                    eventSource.onerror = (error) => {
                        console.log("123---------", error);
                        this.dsGenerating = false;
                        eventSource.close();
                        if (!this.aiContent.ds) {
                            this.$message.error("DeepSeek生成失败");
                        }
                    };
                }

                // 如果选择了GPT
                // if (this.useGpt) {
                //     this.gptGenerating = true;
                //     const eventSource = new EventSource(
                //         `https://chatgpt.vinehoo.com/emb/embedding/v3/completion/commentStream?use_ds=false&use_gpt=true&prompt=${encodeURIComponent(
                //             this.commentPrompts
                //         )}&title=${encodeURIComponent(
                //             title
                //         )}&use_gpt=true&temperature=${this.gptTemperature}`
                //     );

                //     eventSource.onmessage = (event) => {
                //         const data = JSON.parse(event.data);
                //         if (data.is_think) {
                //             this.aiContent.gpt_think = data.msg;
                //         } else {
                //             this.aiContent.gpt += data.msg;
                //             if (data.msg && data.msg.includes("[DONE]")) {
                //                 this.gptGenerating = false;
                //             }
                //         }
                //     };

                //     eventSource.onerror = (error) => {
                //         this.gptGenerating = false;
                //         eventSource.close();
                //         if (!this.aiContent.gpt) {
                //             this.$message.error("ChatGPT生成失败");
                //         }
                //     };
                // }
            } catch (error) {
                console.error("Error:", error);
                this.$message.error("生成评论失败");
                this.dsGenerating = false;
                this.gptGenerating = false;
            }
        },
        switchAIContent(value) {
            this.datas.content = this.aiContent[value];
        },
        selectContent(type) {
            this.datas.content = this.aiContent[type];
            this.selectedAI = type;
            this.dialogVisible = false;
        },
        handleClose() {
            this.dialogVisible = false;
        },
        async regenerateContent(type) {
            // 清空对应AI的内容
            if (type === "ds") {
                this.aiContent.ds = "";
                this.aiContent.ds_think = "";
                this.dsGenerating = true;

                const url = `https://images.vinehoo.com/vinehoo/client/commodities/periods/${this.datas.period}.json?t=1&isJson=true&id=${this.datas.period}`;
                try {
                    const response = await fetch(url);
                    const data = await response.json();
                    const title = data.data.title;

                    const eventSource = new EventSource(
                        `https://chatgpt.vinehoo.com/emb/embedding/v3/completion/commentStream?use_ds=true&use_gpt=false&prompt=${encodeURIComponent(
                            this.commentPrompts
                        )}&title=${encodeURIComponent(
                            title
                        )}&use_ds=true&temperature=${
                            this.dsTemperature
                        }&wait_time=25&pid=${this.datas.period}`
                    );

                    eventSource.onmessage = (event) => {
                        const data = JSON.parse(event.data);
                        if (data.is_think) {
                            this.aiContent.ds_think += data.msg;
                        } else {
                            this.aiContent.ds += data.msg.trim();
                        }
                    };

                    eventSource.onerror = (error) => {
                        this.dsGenerating = false;
                        eventSource.close();
                        if (!this.aiContent.ds) {
                            this.$message.error("DeepSeek生成失败");
                        }
                    };
                } catch (error) {
                    console.error("Error:", error);
                    this.$message.error("生成评论失败");
                    this.dsGenerating = false;
                }
            } else if (type === "gpt") {
                this.aiContent.gpt = "";
                this.aiContent.gpt_think = "";
                this.gptGenerating = true;

                const url = `https://images.vinehoo.com/vinehoo/client/commodities/periods/${this.datas.period}.json?t=1&isJson=true&id=${this.datas.period}`;
                try {
                    const response = await fetch(url);
                    const data = await response.json();
                    const title = data.data.title;

                    const eventSource = new EventSource(
                        `https://chatgpt.vinehoo.com/emb/embedding/v3/completion/commentStream?use_ds=false&use_gpt=true&prompt=${encodeURIComponent(
                            this.commentPrompts
                        )}&title=${encodeURIComponent(
                            title
                        )}&use_gpt=true&temperature=${this.gptTemperature}`
                    );

                    eventSource.onmessage = (event) => {
                        const data = JSON.parse(event.data);
                        if (data.is_think) {
                            this.aiContent.gpt_think = data.msg;
                        } else {
                            this.aiContent.gpt += data.msg;
                            if (data.msg && data.msg.includes("[DONE]")) {
                                this.gptGenerating = false;
                            }
                        }
                    };

                    eventSource.onerror = (error) => {
                        this.gptGenerating = false;
                        eventSource.close();
                        if (!this.aiContent.gpt) {
                            this.$message.error("ChatGPT生成失败");
                        }
                    };
                } catch (error) {
                    console.error("Error:", error);
                    this.$message.error("生成评论失败");
                    this.gptGenerating = false;
                }
            }
        },
        addToHistory(type, content) {
            if (type === "ds") {
                if (content !== this.lastDsContent && content.trim()) {
                    this.dsHistory.push(content);
                    this.dsCurrentPage = this.dsHistory.length;
                    this.lastDsContent = content;
                }
            } else if (type === "gpt") {
                if (content !== this.lastGptContent && content.trim()) {
                    this.gptHistory.push(content);
                    this.gptCurrentPage = this.gptHistory.length;
                    this.lastGptContent = content;
                }
            }
        },
    },
    watch: {
        "aiContent.ds_think": {
            handler(newVal) {
                if (newVal) {
                    this.$nextTick(() => {
                        if (this.$refs.dsThinkContent) {
                            this.$refs.dsThinkContent.scrollTop =
                                this.$refs.dsThinkContent.scrollHeight;
                        }
                    });
                }
            },
            deep: true,
        },
        "aiContent.gpt_think": {
            handler(newVal) {
                if (newVal) {
                    this.$nextTick(() => {
                        if (this.$refs.gptThinkContent) {
                            this.$refs.gptThinkContent.scrollTop =
                                this.$refs.gptThinkContent.scrollHeight;
                        }
                    });
                }
            },
            deep: true,
        },
        "aiContent.ds": {
            handler(newVal) {
                if (newVal && !newVal.includes("[DONE]")) {
                    if (!this.dsGenerating) {
                        this.addToHistory("ds", newVal);
                    }
                    this.$nextTick(() => {
                        if (this.$refs.dsContentBox) {
                            this.$refs.dsContentBox.scrollTop =
                                this.$refs.dsContentBox.scrollHeight;
                        }
                    });
                }
            },
            deep: true,
        },
        "aiContent.gpt": {
            handler(newVal) {
                if (newVal && !newVal.includes("[DONE]")) {
                    if (!this.gptGenerating) {
                        this.addToHistory("gpt", newVal);
                    }
                    this.$nextTick(() => {
                        if (this.$refs.gptContentBox) {
                            this.$refs.gptContentBox.scrollTop =
                                this.$refs.gptContentBox.scrollHeight;
                        }
                    });
                }
            },
            deep: true,
        },
        dsGenerating(newVal) {
            if (!newVal && this.aiContent.ds) {
                this.addToHistory("ds", this.aiContent.ds);
            }
        },
        gptGenerating(newVal) {
            if (!newVal && this.aiContent.gpt) {
                this.addToHistory("gpt", this.aiContent.gpt);
            }
        },
    },
};
</script>
<style scoped lang="scss">
.dialog-footer {
    margin-top: 30px;
    display: flex;
    justify-content: center;
}
.el-upload-list .is-ready {
    display: none;
}
.el-checkbox {
    margin-right: 0px;
    width: fit-content;
    .el-checkbox__label {
        padding-left: 6px;
    }
}
.el-form-item {
    margin-bottom: 10px;
}
/deep/ .el-form--label-top .el-form-item__label {
    padding: 0;
}
.content-preview {
    padding: 10px;
    .content-container {
        display: flex;
        gap: 20px;
        .content-item {
            flex: 1;
            h4 {
                margin: 0 0 10px 0;
                color: #333;
                font-size: 16px;
                font-weight: bold;
            }
            h5 {
                margin: 0 0 8px 0;
                color: #666;
                font-size: 14px;
            }
            .think-box {
                margin-bottom: 15px;
                .think-content {
                    padding: 12px;
                    background: #f8f9fb;
                    border-radius: 4px;
                    font-size: 13px;
                    color: #666;
                    line-height: 1.5;
                    white-space: pre-wrap;
                    word-break: break-all;
                    max-height: 240px;
                    overflow-y: auto;
                    &::-webkit-scrollbar {
                        width: 6px;
                    }
                    &::-webkit-scrollbar-thumb {
                        background: #ddd;
                        border-radius: 3px;
                    }
                    &::-webkit-scrollbar-track {
                        background: #f5f5f5;
                        border-radius: 3px;
                    }
                }
            }
            .content-box {
                padding: 15px;
                background: #f5f7fa;
                border-radius: 4px;
                min-height: 100px;
                white-space: pre-wrap;
                word-break: break-all;
                line-height: 1.5;
                font-size: 14px;
                color: #666;
                max-height: 240px;
                overflow-y: auto;
                &::-webkit-scrollbar {
                    width: 6px;
                }
                &::-webkit-scrollbar-thumb {
                    background: #ddd;
                    border-radius: 3px;
                }
                &::-webkit-scrollbar-track {
                    background: #f5f5f5;
                    border-radius: 3px;
                }
            }
        }
    }
}
.thinking-content-dialog {
    padding: 15px;
    background: #f8f9fb;
    border-radius: 4px;
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 60vh;
    overflow-y: auto;
}
.temperature-info-dialog {
    .temperature-info {
        color: #606266;
        line-height: 1.6;

        p {
            margin: 12px 0;
        }

        strong {
            color: #303133;
        }

        ul,
        ol {
            margin: 12px 0;
            padding-left: 24px;

            li {
                margin: 8px 0;
            }
        }
    }
}
</style>
