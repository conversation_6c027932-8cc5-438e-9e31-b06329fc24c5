<template>
    <div>
        <el-card shadow="hover" style="margin: 0 0 10px; padding: 0 40px 0 0">
            <!-- 高级查询 -->
            <el-form :inline="true" size="small" class="demo-form-inline">
                <el-form-item>
                    <el-select
                        class="w-mini"
                        v-model="query.source"
                        filterable
                        size="mini"
                        placeholder="评论类型"
                        clearable
                    >
                        <el-option
                            v-for="(item, index) in [
                                { label: '商品评论', value: 1 },
                                { label: '酒评', value: 2 },
                            ]"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="query.content"
                        placeholder="请输入评论内容"
                        @keyup.enter.native="search"
                    />
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="query.uname"
                        @keyup.enter.native="search"
                        placeholder="请输入用户名称"
                    />
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="query.period"
                        @keyup.enter.native="search"
                        placeholder="请输入期数"
                    />
                </el-form-item>
                <el-form-item>
                    <el-select
                        class="w-mini"
                        v-model="query.label"
                        filterable
                        size="mini"
                        placeholder="马甲标签"
                        clearable
                    >
                        <el-option
                            v-for="(item, index) in labelOptions"
                            :key="index"
                            :label="item.label"
                            :value="item.label"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                    class="m-r-10 w-normal"
                    v-model="query.user_label_name"
                    filterable
                    size="mini"
                    placeholder="标签"
                    clearable
                >
                    <el-option
                        v-for="(item) in labelList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name"
                    >
                    </el-option>
                </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        class="w-mini"
                        v-model="query.is_show"
                        filterable
                        size="mini"
                        placeholder="是否显示"
                        clearable
                    >
                        <el-option
                            v-for="(item, index) in [
                                { label: '显示', value: 1 },
                                { label: '不显示', value: 0 },
                            ]"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="warning" size="mini" @click="search"
                        >查询</el-button
                    >
                    <el-button
                        type="primary"
                        size="mini"
                        @click="dialogVisible1 = true"
                        >添加评论</el-button
                    >
                    <el-button
                        type="primary"
                        size="mini"
                        @click="openAddWineComment"
                        >添加酒评</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover">
            <el-table
                stripe
                border
                size="mini"
                :data="DataList"
                cell-class-name="flo"
                fit
                highlight-current-row
                style="width: 100%"
            >
                <el-table-column label="ID" align="left" width="120">
                    <template slot-scope="row">
                        <div>评论ID：{{ row.row.id }}</div>
                        <div>回复ID：{{ row.row.pid ? row.row.pid : "-" }}</div>
                        <div>用户ID：{{ row.row.uid }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="类型" align="center" width="90">
                    <template slot-scope="row">
                        {{ sourceFormat(row.row.source) }}
                    </template>
                </el-table-column>

                <el-table-column
                    label="商品名称"
                    align="left"
                    prop="title"
                    min-width="200"
                >
                    <template slot-scope="row">
                        <span>{{ row.row.period }}期：</span>
                        <span
                            style="cursor: pointer"
                            @click="$viewPcGoods(row.row.period)"
                            >{{ row.row.title }}</span
                        >
                    </template>
                </el-table-column>

                <el-table-column label="用户信息" align="left" min-width="180">
                    <template
                        slot-scope="{
                            row: {
                                vest_is_buy,
                                label,
                                uname,
                                user_level,
                                is_vest_user,
                            },
                        }"
                    >
                        <div>
                            <span style="margin-right: 6px">{{ uname }}</span>
                        </div>
                        <div>
                            <span>{{ user_level }}级</span>
                            <span>
                                {{ vest_is_buy == 1 ? "已购" : "未购" }}
                            </span>
                        </div>
                        <div>
                            {{ label }} <span v-if="is_vest_user">[马甲]</span>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column
                    label="内容"
                    align="left"
                    prop="content"
                    min-width="300"
                >
                    <template slot-scope="{ row }">
                        <div>
                            {{ row.content }}
                        </div>
                        <div v-if="row.emoji_image">
                            {{ row.emoji_image }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="热度"
                    prop="hot_num"
                    width="80"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        <el-input
                            size="mini"
                            v-model="row.hot_num"
                            oninput="value=value.replace(/[^0-9]/g,'')"
                            @change="updateHotNum(row)"
                        />
                    </template>
                </el-table-column>
                <!-- 
                <el-table-column
                    label="点赞"
                    prop="likenums"
                    width="90"
                    align="center"
                /> -->

                <el-table-column
                    label="评论时间"
                    prop="created_time"
                    width="100"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        <div>
                            {{ timeFormat(row.created_time, "y") }}
                        </div>
                        <div>
                            {{ timeFormat(row.created_time, "s") }}
                        </div>
                        <div v-if="row.emoji_image">
                            {{ row.emoji_image }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        label="标签"
                        show-overflow-tooltip
                        prop="user_label_name"
                        min-width="130"
                    >
                    </el-table-column>
                <el-table-column
                    label="操作"
                    fixed="right"
                    align="center"
                    width="100"
                >
                    <template slot-scope="row">
                        <div>
                            <el-button
                                size="mini"
                                @click="setCommentStatus(row.row, 4)"
                                v-if="row.row.is_recommend == 1"
                                type="text"
                                >不显示</el-button
                            >
                            <el-button
                                size="mini"
                                @click="setCommentStatus(row.row, 1)"
                                v-if="row.row.is_recommend == 0"
                                type="text"
                                >显示</el-button
                            >
                            <el-button
                                size="mini"
                                @click="rejectComment(row.row, 2)"
                                v-if="row.row.audit_status != 2"
                                type="text"
                                >驳回</el-button
                            >
                        </div>

                        <div>
                            <el-button
                                size="mini"
                                @click="replyComment(row.row)"
                                type="text"
                            >
                                回复
                            </el-button>
                            <el-button
                                size="mini"
                                @click="changeStatus(row.row)"
                                type="text"
                            >
                                {{ row.row.vest_is_buy == 1 ? "未购" : "已购" }}
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 分页 -->
        <div class="page-center">
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="pageSize"
                :current-page="currentPage"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <el-dialog
            :visible.sync="dialogVisible1"
            :close-on-click-modal="false"
            title="添加评论"
            width="800px"
            custom-class="dialogwid"
        >
            <AddComment @close="close" v-if="dialogVisible1"></AddComment>
        </el-dialog>
        <el-dialog
            :visible.sync="replyDialogStatus"
            title="回复评论"
            width="630px"
            :close-on-click-modal="false"
            custom-class="dialogwid"
        >
            <reply
                @close="close"
                v-if="replyDialogStatus"
                :CommentDetail="CommentDetail"
            ></reply>
        </el-dialog>
        <el-dialog
            :close-on-click-modal="false"
            title="添加酒评"
            :visible.sync="addWineCommentVisible"
            width="70%"
            top="4vh"
        >
            <AddWineComment
                ref="addWineComment"
                @updateSuccess="updateSuccess"
                v-if="addWineCommentVisible"
            ></AddWineComment>
            <span slot="footer">
                <el-button @click="addWineCommentVisible = false"
                    >取消</el-button
                >
                <el-button type="primary" @click="comfirmAddWineComment"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import reply from "./replyComment.vue";
import AddComment from "./AddComment.vue";
import AddWineComment from "./AddWineComment.vue";
export default {
    components: {
        AddComment,
        AddWineComment,
        reply,
    },
    name: "OrderList",
    data() {
        return {
            currentPage: 1, // 当前页
            pageSize: 10, // 每页条数
            labelOptions: [],
            total: 0, // 总条数
            query: {
                content: "",
                is_show: "",
                label: "",
                source: "",
                uname: "",
                period: "",
                user_label_name:""
            },
            DataList: [],
            dialogVisible1: false,
            CommentDetail: {},
            replyDialogStatus: false,
            addWineCommentVisible: false,
            labelList:[]
        };
    },

    mounted() {
        this.getCommentList();
        this.getVestLabelList();
        this.getLabelListreq();
    },
    methods: {
        timeFormat(dateTimeString, type) {
            if (type == "y") {
                return dateTimeString.split(" ")[0];
            } else {
                return dateTimeString.split(" ")[1];
            }
        },
        sourceFormat(val) {
            switch (val) {
                case 1:
                    return "商品评论";
                case 2:
                    return "酒评";
                default:
                    return "";
            }
        },
        async getVestLabelList() {
            let data = {};
            let res = await this.$request.article.getVestLabelList(data);
            if (res.data.error_code === 0) {
                this.labelOptions = res.data.data;
            }
        },
        async getLabelListreq() {
            const res = await this.$request.article.getlabelList({});
                if (res.data.error_code === 0) {
                    this.labelList = res.data.data.list;
                }
        },
        updateSuccess() {
            this.addWineCommentVisible = false;
            this.getCommentList();
        },
        openAddWineComment() {
            this.addWineCommentVisible = true;
        },
        comfirmAddWineComment() {
            this.$refs.addWineComment.comfirmAddWineComment();
        },
        viewGoods(item) {
            window.open(
                this.$BASE.PCDomain +
                    "/pages/goods-detail/goods-detail?id=" +
                    (item.encrypt_id || item.period)
            );
        },
        async updateHotNum(row) {
            const data = {
                id: row.id,
                hot_num: row.hot_num,
            };
            const res = await this.$request.article.upcomment(data);
            if (res.data.error_code == 0) {
                this.$message.success("操作成功");
                this.getCommentList();
            }
        },
        rejectComment(row, status) {
            this.$prompt("请输入驳回原因", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(async ({ value }) => {
                if (!value) {
                    this.$message.warning("请输入驳回原因");
                    return;
                }
                const data = {
                    id: row.id,
                    status: status,
                    source: 1,
                    type: row.pid ? 2 : 1,
                    remark: value,
                };
                const res = await this.$request.article.setCommentStatus(data);
                if (res.data.error_code == 0) {
                    this.$message.success("操作成功");
                    this.getCommentList();
                }
            });
        },
        async setCommentStatus(row, status) {
            const data = {
                id: row.id,
                source: 1,
                type: row.pid ? 2 : 1,
                status,
            };
            const res = await this.$request.article.setCommentStatus(data);
            if (res.data.error_code == 0) {
                this.$message.success("操作成功");
                this.getCommentList();
            }
            console.log(data);
        },
        // 获取数据
        async getCommentList() {
            const data = {
                page: this.currentPage,
                limit: this.pageSize,
                ...this.query,
            };
            const res = await this.$request.article.getCommentList(data);
            if (res.data.error_code === 0) {
                this.DataList = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        search() {
            this.currentPage = 1;
            this.getCommentList();
        },
        replyComment(row) {
            this.replyDialogStatus = true;
            this.CommentDetail = row;
        },
        async changeStatus(row) {
            const data = {
                id: row.id,
                vest_is_buy: row.vest_is_buy == 1 ? 0 : 1,
            };
            const res = await this.$request.article.upcomment(data);
            if (res.data.error_code == 0) {
                this.$message.success("操作成功");
                this.getCommentList();
            }
        },
        close() {
            this.getCommentList();
            this.dialogVisible1 = false;
            this.replyDialogStatus = false;
        },
        // 改变每页条数
        handleSizeChange(val) {
            this.currentPage = 1;
            this.pageSize = val;
            this.getCommentList();
        },
        // 改变当前页
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getCommentList();
        },
    },
};
</script>

<style lang="scss" scoped>
/deep/ .el-input--mini .el-input__inner {
    height: 32px !important;
    line-height: 32px !important;
}
.page-center {
    text-align: center;
}
</style>
