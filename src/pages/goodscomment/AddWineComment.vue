<template>
    <div class="comment_container">
        <el-form
            :model="add_wine_comment"
            ref="commentform"
            :rules="rules"
            label-width="100px"
            :inline="false"
            size="normal"
        >
            <el-row>
                <el-col :span="12">
                    <el-form-item label="商品期数" prop="period">
                        <el-select
                            style="width: 220px"
                            v-model="add_wine_comment.period"
                            placeholder="请输入商品期数"
                            clearable
                            remote
                            filterable
                            :remote-method="searchGoods"
                            @change="selectGoods"
                        >
                            <el-option
                                v-for="item in GoodsOptions"
                                :key="item.id"
                                :label="item.title"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>

                        <div
                            style="color: rgba(232, 4, 4, 1)"
                            v-for="item in GoodsOptions"
                            :key="item.id"
                            v-show="
                                !item.product_main_category.includes('酒类')
                            "
                        >
                            该商品非酒类商品不可发布酒评
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="马甲账号" prop="uid">
                        <el-select
                            style="width: 220px"
                            v-model="add_wine_comment.uid"
                            placeholder="请选择马甲账号"
                            filterable
                        >
                            <el-option
                                v-for="item in vest_list"
                                :key="item.id"
                                :label="
                                    item.user_level +
                                    '级 - ' +
                                    item.nickname +
                                    ' - ' +
                                    item.label
                                "
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12" :offset="0">
                    <el-form-item label="关联话题" prop="topic_id">
                        <el-select
                            style="width: 220px"
                            v-model="add_wine_comment.topic_id"
                            placeholder="请选择关联话题"
                            filterable
                            multiple
                            :multiple-limit="3"
                        >
                            <el-option
                                v-for="item in topic_list"
                                :key="item.id"
                                :label="item.title"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12" :offset="0">
                    <!-- <el-form-item label="发布时间">
                        <el-date-picker
                            v-model="value1"
                            type="datetime"
                            placeholder="选择日期时间"
                        >
                        </el-date-picker>
                    </el-form-item> -->
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12" :offset="0">
                    <el-form-item label="评论内容" prop="wine_evaluation">
                        <el-input
                            v-model="add_wine_comment.wine_evaluation"
                            type="textarea"
                            placeholder="请输入评论内容"
                            clearable
                            :rows="6"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12" :offset="0">
                    <el-form-item label="评论图片" prop="type_data">
                        <vos-oss
                            list-type="picture-card"
                            :showFileList="true"
                            :dir="dir"
                            :file-list="fileList"
                            :multiple="true"
                            :limit="9"
                        >
                            <i slot="default" class="el-icon-plus"></i>
                        </vos-oss>
                        <div style="font-size: 12px; color: #909399">
                            最多选择9张图片
                        </div>
                    </el-form-item>
                </el-col>
            </el-row>
            <div v-if="isShowWineDetail">
                <el-row>
                    <el-col :span="24" :offset="0">
                        <el-form-item prop="wine_color" label="酒体颜色">
                            <el-form-item size="normal">
                                <el-select
                                    v-model="color_option_value"
                                    placeholder="请选择类型"
                                    @change="changeColorOption"
                                >
                                    <el-option
                                        v-for="item in this.wineAscategoryList
                                            .vision"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id"
                                    >
                                    </el-option>
                                </el-select>
                                <el-select
                                    style="margin-left: 20px"
                                    v-model="add_wine_comment.wine_color"
                                    placeholder="请选择酒体颜色"
                                    @change="changeWineColorOption"
                                    clearable
                                >
                                    <el-option
                                        v-for="item in color_option"
                                        :key="item.asid"
                                        :label="item.cate_name"
                                        :value="item.cate_name"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item> </el-form-item
                    ></el-col>
                </el-row>
                <el-row>
                    <el-col :span="24" :offset="0">
                        <el-form-item label="香气特征" prop="smell_aroma">
                            <el-form-item
                                v-for="i in this.wineAscategoryList.smell"
                                :key="i.id"
                                :label="i.name + ':'"
                            >
                                <el-checkbox-group
                                    v-model="add_wine_comment.smell_aroma"
                                >
                                    <el-checkbox
                                        v-for="j in i.list"
                                        :key="j.asid"
                                        :label="j.cate_name"
                                        >{{ j.cate_name }}</el-checkbox
                                    >
                                </el-checkbox-group>
                            </el-form-item>
                        </el-form-item></el-col
                    >
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24" :offset="0">
                        <el-form-item
                            prop="tasteid"
                            label="味道特征"
                            class="taste_container"
                        >
                            <el-form-item label="酒体" prop="wine_body">
                                <el-radio-group
                                    style="margin-top: 8px"
                                    v-model="add_wine_comment.wine_body"
                                    :max="1"
                                >
                                    <el-radio
                                        v-for="j in this.taste_option
                                            .wine_body_list"
                                        :key="j.asid"
                                        :label="j.score"
                                        >{{ j.cate_name }}</el-radio
                                    >
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="酸度" prop="acidity">
                                <el-radio-group
                                    style="margin-top: 8px"
                                    v-model="add_wine_comment.acidity"
                                    :max="1"
                                >
                                    <el-radio
                                        v-for="j in this.taste_option
                                            .acidity_list"
                                        :key="j.asid"
                                        :label="j.score"
                                        >{{ j.cate_name }}</el-radio
                                    >
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="甜度" prop="sweetness">
                                <el-radio-group
                                    style="margin-top: 8px"
                                    v-model="add_wine_comment.sweetness"
                                    :max="1"
                                >
                                    <el-radio
                                        v-for="j in this.taste_option
                                            .sweetness_list"
                                        :key="j.asid"
                                        :label="j.score"
                                        >{{ j.cate_name }}</el-radio
                                    >
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="余味" prop="aftertaste">
                                <el-radio-group
                                    style="margin-top: 8px"
                                    v-model="add_wine_comment.aftertaste"
                                    :max="1"
                                >
                                    <el-radio
                                        v-for="j in this.taste_option
                                            .aftertaste_list"
                                        :key="j.asid"
                                        :label="j.score"
                                        >{{ j.cate_name }}</el-radio
                                    >
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="单宁" prop="tannin_content">
                                <el-radio-group
                                    style="margin-top: 8px"
                                    v-model="add_wine_comment.tannin_content"
                                    :max="1"
                                >
                                    <el-radio
                                        v-for="j in this.taste_option
                                            .aftertaste_list"
                                        :key="j.asid"
                                        :label="j.score"
                                        >{{ j.cate_name }}</el-radio
                                    >
                                </el-radio-group>
                            </el-form-item>
                        </el-form-item></el-col
                    >
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="有无单宁" prop="is_tannin">
                            <el-radio-group
                                v-model="add_wine_comment.is_tannin"
                                style="margin-left: 20px; margin-top: 8px"
                            >
                                <el-radio :label="1">有 </el-radio>
                                <el-radio :label="0"> 无</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>
        </el-form>
    </div>
</template>

<script>
import VosOss from "vos-oss";
export default {
    name: "Vue2CommoditiesAddWineComment",
    components: {
        VosOss,
    },
    data() {
        const checkTypeData = (rule, value, callback) => {
            // if (this.fileList.length > 0) {
            //     callback();
            // } else {
            //     callback(new Error("请上传图片"));
            // }
            callback();
        };
        const checktaste = (rule, value, callback) => {
            if (value === "") {
                callback(new Error("请选择对应的味道特征"));
            } else {
                callback();
            }
        };
        const checktannin = (rule, value, callback) => {
            if (this.add_wine_comment.is_tannin === 1 && value === "") {
                callback(new Error("请选择对应的味道特征"));
            } else {
                callback();
            }
        };
        const checkTasteid = (rule, value, callback) => {
            callback();
        };
        return {
            add_wine_comment: {
                is_backstage: 1,
                uid: "",
                period: "",
                period_title: "",
                periods_type: "",
                wine_color: "",
                wine_color_image: "",
                smell_aroma: [],
                sweetness: "",
                acidity: "",
                is_tannin: "",
                tannin_content: "",
                aftertaste: "",
                wine_body: "",
                topic_id: [],
                wine_evaluation: "",
                type_data: "",
            },
            taste_option: {
                sweetness_list: [],
                tannin_content_list: [],
                acidity_list: [],
                wine_body_list: [],
                aftertaste_list: [],
            },
            color_option_value: "",
            color_option: [],
            rules: {},
            wineAscategoryList: {},
            GoodsOptions: [],
            dir: "vinehoo/vos/wineComment/",
            fileList: [],
            options: [],
            tasteid: new Array(5).fill([]),
            rules: {
                period: [
                    {
                        required: true,
                        message: "请选择商品",
                        trigger: "change",
                    },
                ],
                uid: [
                    {
                        required: true,
                        message: "请选择马甲账号",
                        trigger: "change",
                    },
                ],
                topic_id: [
                    {
                        required: false,
                        message: "请选择关联话题",
                        trigger: "change",
                    },
                ],
                wine_evaluation: [
                    {
                        required: true,
                        message: "请输入评论内容",
                        trigger: "blur",
                    },
                ],
                type_data: [
                    {
                        required: false,
                        validator: checkTypeData,
                    },
                ],
                wine_color: [
                    {
                        required: true,
                        message: "请选择酒体颜色",
                        trigger: "change",
                    },
                ],
                smell_aroma: [
                    {
                        required: true,
                        message: "请选择香气特征",
                        trigger: "change",
                    },
                ],
                tasteid: [
                    {
                        required: true,
                        validator: checkTasteid,
                    },
                ],
                is_tannin: [
                    {
                        required: true,
                        message: "请选择有无单宁",
                        trigger: "change",
                    },
                ],
                wine_body: [
                    {
                        validator: checktaste,
                        trigger: "change",
                    },
                ],
                acidity: [
                    {
                        validator: checktaste,
                        trigger: "change",
                    },
                ],
                sweetness: [
                    {
                        validator: checktaste,
                        trigger: "change",
                    },
                ],
                aftertaste: [
                    {
                        validator: checktaste,
                        trigger: "change",
                    },
                ],
                tannin_content: [
                    {
                        validator: checktannin,
                        trigger: "change",
                    },
                ],
            },
            vest_list: [],
            topic_list: [],
            wineStyle: [
                "红葡萄酒",
                "干红葡萄酒",
                "半干红葡萄酒",
                "半甜红葡萄酒",
                "甜红葡萄酒",
                "白葡萄酒",
                "干白葡萄酒",
                "半干白葡萄酒",
                "半甜白葡萄酒",
                "甜白葡萄酒",
                "桃红葡萄酒",
                "干桃红葡萄酒",
                "半干桃红葡萄酒",
                "半甜桃红葡萄酒",
                "甜桃红葡萄酒",
                "起泡酒",
                "香槟",
                "加强酒",
                "利口酒",
                "橙酒",
                "自然酒",
            ],
            isShowWineDetail: true,
        };
    },

    mounted() {
        this.getAscategorylist();
        this.getVestList();
        this.getTopicList();
    },

    methods: {
        comfirmAddWineComment() {
            this.$refs["commentform"].validate((valid) => {
                if (valid) {
                    let comment_type = 0;
                    for (let i in this.GoodsOptions) {
                        if (
                            !this.GoodsOptions[
                                i
                            ].product_main_category.includes("酒类")
                        ) {
                            this.$message.error("该商品非酒类商品不可发布酒评");
                            return;
                        } else {
                            comment_type = this.isShowWineDetail ? 1 : 2;
                        }
                    }
                    let submit_data = JSON.parse(
                        JSON.stringify(this.add_wine_comment)
                    );
                    this.vest_list.map((item) => {
                        if (item.id === this.add_wine_comment.uid) {
                            submit_data.uid = item.uid;
                        }
                    });
                    submit_data.type_data = this.fileList.join(",");
                    submit_data.smell_aroma = submit_data.smell_aroma.join(",");
                    submit_data.topic_id = submit_data.topic_id.join(",");
                    submit_data.comment_type = comment_type;
                    console.info(submit_data);
                    this.$request.article
                        .createWineEvaluation(submit_data)
                        .then((res) => {
                            if (res.data.error_code === 0) {
                                this.$message.success("发布成功");
                                this.$emit("updateSuccess");
                            }
                        });
                }
            });
        },
        selectGoods() {
            let current_item = {};
            this.GoodsOptions.map((item) => {
                if (item.id === this.add_wine_comment.period) {
                    this.add_wine_comment.period_title = item.title;
                    this.add_wine_comment.periods_type = item.periods_type;
                    current_item = item || [];
                }
            });
            let current_flag = 0;
            for (let i = 0; i < current_item.product_category.length; i++) {
                if (this.wineStyle.includes(current_item.product_category[i])) {
                    current_flag++;
                }
            }
            if (current_flag !== 0) {
                this.isShowWineDetail = true;
            } else {
                this.isShowWineDetail = false;
            }
        },
        searchGoods(periods) {
            this.$request.article
                .articleList({
                    periods: Number(periods),
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.GoodsOptions = res.data.data.list;
                    }
                });
        },
        getAscategorylist() {
            this.$request.article.getAscategorylist().then((res) => {
                if (res.data.error_code == 0) {
                    this.wineAscategoryList = res.data.data.asc;
                    this.wineBrewayList = res.data.data.tmp;
                    let taste_arr = [
                        { label: "甜度", value: "sweetness_list" },
                        { label: "酸度", value: "acidity_list" },
                        { label: "单宁", value: "tannin_content_list" },
                        { label: "酒体", value: "wine_body_list" },
                        { label: "余味", value: "aftertaste_list" },
                    ];
                    taste_arr.map((ta) => {
                        res.data.data.asc.taste.map((t) => {
                            if (ta.label === t.name) {
                                this.taste_option[ta.value] = t.list.map(
                                    (item, key) => {
                                        item.score = key * 25;
                                        return item;
                                    }
                                );
                            }
                        });
                    });
                }
            });
        },
        changeColorOption(value) {
            this.add_wine_comment.wine_color = "";
            this.wineAscategoryList.vision.map((item) => {
                if (item.id === value) {
                    this.color_option = item.list;
                }
            });
        },
        changeWineColorOption(params) {
            this.color_option.map((item) => {
                if (item.cate_name == params) {
                    this.add_wine_comment.wine_color_image = item.cate_image;
                }
            });
        },
        getVestList() {
            this.$request.article.getVestList().then((res) => {
                if (res.data.error_code === 0) {
                    this.vest_list = res.data.data.list;
                }
            });
        },
        getTopicList() {
            this.$request.article
                .getTopicList({
                    type: 0,
                    page: 1,
                    limit: 999,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.topic_list = res.data.data.list;
                    }
                });
        },
    },
};
</script>

<style lang="scss" scoped>
.comment_container {
    height: 700px;
    overflow-y: scroll;
    overflow-x: hidden;
}
/deep/ .taste_container {
    .el-form-item__error {
        top: 80%;
        padding-top: 0;
    }
}
</style>
