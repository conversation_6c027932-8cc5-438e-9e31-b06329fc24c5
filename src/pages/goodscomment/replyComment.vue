<template>
    <div>
        <el-form ref="form" :model="datas" label-width="100px" :rules="rules">
            <el-form-item label="商品期数" prop="period">
                <el-col :span="12">
                    <el-input
                        :disabled="true"
                        oninput="value=value.replace(/[^0-9]/g,'')"
                        v-model="datas.period"
                        placeholder="请输入期数"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="马甲账号" prop="uid">
                <el-col :span="12">
                    <el-select
                        v-model="datas.uid"
                        clearable
                        @change="userChange"
                        size="small"
                        placeholder="请选择"
                        filterable
                        class="filter-item"
                    >
                        <el-option
                            v-for="item in uidOption"
                            :key="item.uid"
                            :label="
                                item.user_level +
                                '级 - ' +
                                item.nickname +
                                ' - ' +
                                item.label
                            "
                            :value="item.uid"
                        />
                    </el-select>
                </el-col>
            </el-form-item>
            <el-form-item label="是否购买" prop="vest_is_buy">
                <el-col :span="12">
                    <el-radio-group v-model="datas.vest_is_buy">
                        <el-radio
                            v-for="(item, key) in vest_is_buyOptions"
                            :key="key"
                            :label="item.label"
                            >{{ item.value }}</el-radio
                        >
                    </el-radio-group>
                </el-col>
            </el-form-item>
            <el-form-item label="是否计划任务">
                <el-col :span="12">
                    <el-radio-group v-model="datas.automatic_task">
                        <el-radio
                            v-for="(item, key) in automatic_taskOptions"
                            :key="key"
                            :label="item.label"
                            >{{ item.value }}</el-radio
                        >
                    </el-radio-group>
                </el-col>
            </el-form-item>
            <el-form-item
                label="发送时间"
                prop="automatic_task_time"
                v-if="datas.automatic_task == 1"
            >
                <el-col :span="12">
                    <el-date-picker
                        value-format="yyyy-MM-dd HH:mm:ss"
                        v-model="datas.automatic_task_time"
                        type="datetime"
                        placeholder="请选择发送时间"
                    >
                    </el-date-picker>
                </el-col>
            </el-form-item>
            <el-form-item label="回复内容" prop="content">
                <el-col :span="12">
                    <el-input
                        v-model="datas.content"
                        placeholder="请输入回复内容"
                        type="textarea"
                        :autosize="{ minRows: 4 }"
                    />
                </el-col>
            </el-form-item>
            <div style="margin-left: 100px">
                <Face ref="face" @getEmoji="getEmoji" />
            </div>
        </el-form>
        <div class="dialog-footer">
            <el-button @click="$emit('close')">取 消</el-button>
            <el-button type="primary" @click="submits">确 定</el-button>
        </div>
    </div>
</template>
<script>
import Face from "../../components/face/index.vue";
export default {
    components: {
        Face,
    },
    props: ["CommentDetail"],
    data() {
        return {
            replyComment: {
                period: "",
                first_id: "",
                pid: 0,
                p_uid: "",
            },
            datas: {
                period: "",
                uid: "",
                uname: "",
                content: "",
                vest_is_buy: 0,
                automatic_task: 0,
                automatic_task_time: "",
            },
            automatic_taskOptions: [
                {
                    label: 1,
                    value: "是",
                },
                {
                    label: 0,
                    value: "否",
                },
            ],
            vest_is_buyOptions: [
                {
                    label: 1,
                    value: "是",
                },
                {
                    label: 0,
                    value: "否",
                },
            ],
            rules: {
                automatic_task_time: [
                    {
                        required: true,
                        message: "请选择发送时间",
                        trigger: "change",
                    },
                ],
                uid: [
                    {
                        required: true,
                        message: "请选择马甲账号",
                        trigger: "change",
                    },
                ],
                period: [
                    {
                        required: true,
                        message: "请输入商品ID",
                        trigger: "blur",
                    },
                ],
                content: [
                    {
                        required: true,
                        message: "请输入评论内容",
                        trigger: "blur",
                    },
                ],
            },
            uidOption: [],
        };
    },
    mounted() {
        this.replyComment = {
            period: this.CommentDetail.period,
            first_id: this.CommentDetail.first_id
                ? this.CommentDetail.first_id
                : this.CommentDetail.id,
            pid: this.CommentDetail.id,
            p_uid: this.CommentDetail.uid,
        };
        this.datas.period = this.CommentDetail.period;
        this.getVestList();
    },
    methods: {
        userChange() {
            this.datas.uname = this.uidOption.find((item) => {
                return item.uid == this.datas.uid;
            }).nickname;
        },
        async getVestList() {
            const res = await this.$request.article.getVestList();
            if (res.data.error_code == 0) {
                this.uidOption = res.data.data.list;
            }
        },
        getEmoji(item) {
            this.datas.content = this.datas.content + item;
        },
        //提交
        async submits() {
            if (this.validateForm()) {
                this.datas.emoji_image = this.$refs.face.chooseFaceTitle;
                const data = {
                    ...this.datas,
                    ...this.replyComment,
                };
                console.log("表单", data);
                this.$request.article.createComment(data).then((res) => {
                    if (res.data.error_code == 0) {
                        this.$emit("close");
                        this.$message({
                            type: "success",
                            message: "回复成功",
                        });
                    }
                });
            }
        },
        validateForm() {
            let flag = null;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
    },
};
</script>
<style scoped lang="scss">
.dialog-footer {
    margin-top: 30px;
    display: flex;
    justify-content: center;
}
.el-upload-list .is-ready {
    display: none;
}
</style>
