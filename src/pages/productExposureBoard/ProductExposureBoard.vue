<template>
    <div>
        <el-card shadow="hover">
            <div class="tip_content">
                <div>本看板每分钟自动更新一次</div>
                <div>统计规则：</div>
                <div>
                    用户端加载商品列表后．每一页列表中所有商品的曝光量会自动加1，列表包括（首页新品列表，首页Banner，首页卡片列表，秒发列表，闪购列表）
                </div>
            </div>
        </el-card>
        <el-date-picker
            v-model="date"
            type="date"
            size="medium"
            :clearable="false"
            placeholder="选择日期时间"
            value-format="yyyyMMdd"
            style="width: 200px; margin-top: 10px"
            @change="getGoodsExposure"
        >
        </el-date-picker>

        <el-card shadow="hover" style="margin-top: 10px">
            <el-table
                :header-cell-style="{ 'text-align': 'center' }"
                :data="tableData"
                border
                size="mini"
            >
                <el-table-column
                    prop="period"
                    label="期数"
                    width="100"
                    align="center"
                >
                </el-table-column>
                <el-table-column prop="title" label="商品名称" align="left">
                    <template slot-scope="scope">
                        <el-link
                            :href="`${$BASE.PCDomain}/pages/goods-detail/goods-detail?id=${scope.row.period}`"
                            target="_blank"
                            :underline="false"
                            style="text-decoration: none"
                            >{{ scope.row.title }}</el-link
                        >
                    </template>
                </el-table-column>
                <el-table-column
                    prop="score"
                    label="曝光量"
                    width="120"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="channel"
                    label="频道"
                    width="120"
                    align="center"
                >
                </el-table-column>
            </el-table>
        </el-card>
    </div>
</template>

<script>
import moment from "moment";
export default {
    data() {
        return {
            date: moment().format("YYYYMMDD"),
            tableData: [],
            timer: null,
        };
    },

    mounted() {
        // 获取商品曝光量且每分钟自动更新一次
        this.getGoodsExposure();
        this.timer = setInterval(() => {
            this.getGoodsExposure();
        }, 60000);
    },

    methods: {
        getGoodsExposure() {
            this.$request.article
                .getGoodsExposure({ date: this.date })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.tableData = res.data.data;
                    }
                });
        },
        numFormat(row, column, cellValue) {
            return numFormat(cellValue);
        },
        moneyFormat(row, column, cellValue) {
            return "￥" + numFormat(cellValue);
        },
    },
    beforeDestroy() {
        clearInterval(this.timer);
        this.timer = null;
    },
};
</script>

<style lang="scss">
.tip_content {
    font-size: 12px;
    color: #606266;
    line-height: 18px;
}
</style>
