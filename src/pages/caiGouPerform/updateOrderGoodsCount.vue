<template>
    <div>
        <!-- 订货量操作 -->
        <div>
            <div><b>订货量操作</b></div>
            <el-card shadow="never" class="m-t-5">
                <el-table
                    :data="tableData1"
                    :span-method="objectSpanMethod"
                    border
                    style="width: 100%"
                >
                    <el-table-column
                        prop="short_code"
                        label="简码"
                        width="120"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <span
                                v-if="
                                    scope.row.costprice >
                                    scope.row.min_costprice
                                "
                                style="color: red; cursor: pointer"
                                @click="onViewHistoryCostprice(scope.row)"
                                >{{ scope.row.short_code }}</span
                            >
                            <span v-else>{{ scope.row.short_code }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="order"
                        label="已订货量"
                        width="70"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="saled"
                        label="已售数量"
                        width="70"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="inventory"
                        label="剩余可售库存"
                        width="130"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="order"
                        label="修改订货量"
                        width="200"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <div>
                                <el-radio-group v-model="scope.row.order_type">
                                    <el-radio :label="1">增</el-radio>
                                    <el-radio
                                        :label="0"
                                        :disabled="scope.row.order == 0"
                                        >减</el-radio
                                    >
                                </el-radio-group>
                                <el-input
                                    v-model="scope.row.orderGoodsCount"
                                    placeholder=""
                                    size="mini"
                                    class="m-l-10"
                                    style="width: 60px"
                                ></el-input>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="当前发货时间"
                        width="250"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-date-picker
                            disabled
                                v-model="period_info.current_shipment_time"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetime"
                                size="mini"
                                placeholder="选择日期时间"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="超卖发货时间"
                        width="250"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-date-picker
                          
                                v-model="period_info.predict_shipment_time"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetime"
                                size="mini"
                                placeholder="选择日期时间"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="warehouse"
                        label="发货仓库"
                        min-width="150"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        fixed="right"
                        label="操作"
                        width="100"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-button
                                @click="updateInventoryOrder(scope.row)"
                                type="text"
                            >
                                确定
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <!-- 订货量记录 -->
        <div class="m-t-20">
            <div><b>订货量记录</b></div>
            <el-card shadow="never" class="m-t-5">
                <div
                    v-for="(item, index) in tableData2"
                    :key="index"
                    class="m-b-10"
                >
                    <span class="m-r-25">{{ item.created_time }}</span>
                    <span class="m-r-25">{{ item.operator }}</span>
                    <span class="m-r-25"
                        >{{ item.type == 1 ? "增加" : "减少" }}【{{
                            item.short_code
                        }}】订货量：<span style="color: red">{{
                            item.order
                        }}</span></span
                    >

                    <span class="m-r-25"
                        >超卖发货时间：{{ item.predict_shipment_time }}</span
                    >
                </div>
            </el-card>
        </div>
        <div class="footerBtn">
            <el-button type="primary" @click="close">关闭</el-button>
        </div>
        <el-dialog
            title="历史成本"
            :visible.sync="visible"
            width="30%"
            append-to-body
        >
            <el-table :data="list" border>
                <el-table-column
                    label="期数"
                    align="center"
                    prop="period"
                ></el-table-column>
                <el-table-column
                    label="成本"
                    align="center"
                    prop="costprice"
                ></el-table-column>
            </el-table>
        </el-dialog>
    </div>
</template>

<script>
export default {
    props: ["rowData"],
    data() {
        return {
            tableData1: [],
            tableData2: [],
            period_info: {},
            visible: false,
            list: [],
        };
    },
    mounted() {
        this.getInventoryOrderList();
    },
    methods: {
        async getInventoryOrderList() {
            let res = await this.$request.caiGouPerform.getInventoryOrderList({
                period: this.rowData.id,
            });
            if (res.data.error_code == 0) {
                this.tableData1 = res.data.data.list;
                this.tableData1.forEach((item) => {
                    this.$set(item, "orderGoodsCount", "");
                    this.$set(item, "order_type", 1);
                });
                this.period_info = res.data.data.period_info;
                this.tableData2 = res.data.data.order_log;
            }
        },
        //更新订货量
        async updateInventoryOrder(row) {
            let order_data = this.tableData1.map((item) => {
                return {
                    id: item.id,
                    period: item.period,
                    short_code: item.short_code,
                    type: item.order_type,
                    order: item.orderGoodsCount,
                    predict_shipment_time:
                        this.period_info.predict_shipment_time,
                    current_shipment_time:
                        this.period_info.current_shipment_time,
                    period: this.period_info.id,
                    periods_type: this.period_info.periods_type,
                };
            });
            console.log("确定", order_data);
            let res = await this.$request.caiGouPerform.updateInventoryOrder(
                order_data
            );
            if (res.data.error_code == 0) {
                this.$message.success("操作成功");
                this.getInventoryOrderList();
            }
        },
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            if (columnIndex === 5 || columnIndex === 7) {
                if (rowIndex % this.tableData1.length === 0) {
                    return {
                        rowspan: this.tableData1.length,
                        colspan: 1,
                    };
                } else {
                    return {
                        rowspan: 0,
                        colspan: 0,
                    };
                }
            }
        },
        close() {
            this.$emit("orderGoodshandleClose");
        },
        onViewHistoryCostprice(row) {
            const { id, product_id } = row;
            this.$request.caiGouPerform
                .getHistoryCostprice({
                    period: id,
                    product_id,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.list = res.data.data.list;
                        this.visible = true;
                    }
                });
        },
    },
};
</script>

<style lang="scss" scoped>
.footerBtn {
    margin-top: 15px;
    display: flex;
    justify-content: right;
}
</style>
