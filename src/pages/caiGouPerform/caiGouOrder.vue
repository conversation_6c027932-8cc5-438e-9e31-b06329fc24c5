<template>
    <div>
        <el-button type="primary" size="mini" @click="addCaiGouOrder"
            >添加采购单号</el-button
        >
        <div class="m-t-10">
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column prop="orderno" label="采购单" align="center">
                </el-table-column>
                <el-table-column
                    prop="created_name"
                    label="添加人"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="created_time"
                    label="时间"
                    align="center"
                >
                </el-table-column>
                <el-table-column label="ERP状态" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.status | toErpStatusText }}
                    </template>
                </el-table-column>
                <el-table-column label="中台审核状态" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.push_erp_status | toZhongTaiStatusText }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                    <template slot-scope="scope">
                        <el-popconfirm
                            title="确认要删除这个采购单吗？"
                            @confirm="del(scope.row)"
                        >
                            <el-button
                                v-if="!scope.row.source"
                                slot="reference"
                                type="text"
                            >
                                移除
                            </el-button>
                        </el-popconfirm>
                        <el-button
                            v-if="scope.row.source"
                            type="text"
                            @click="onCheckPurchaseOrder(scope.row)"
                            >查看</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="pagination-block m-t-10">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <div>
            <el-dialog
                title="添加采购单"
                :visible.sync="addCaiGouOrderDialogStatus"
                :before-close="addCaiGouOrderhandleClose"
                width="30%"
                append-to-body
            >
                <el-form
                    :model="ruleForm"
                    :rules="rules"
                    ref="ruleForm"
                    label-width="0px"
                    class="demo-ruleForm"
                >
                    <el-form-item label="" prop="orderno">
                        <el-input
                            v-model="ruleForm.orderno"
                            placeholder="请输入采购单号"
                        ></el-input>
                    </el-form-item>
                    <el-form-item class="pagination-block">
                        <el-button @click="addCaiGouOrderhandleClose()"
                            >取消</el-button
                        >
                        <el-button
                            type="primary"
                            @click="submitForm('ruleForm')"
                            >确定</el-button
                        >
                    </el-form-item>
                </el-form>
            </el-dialog>
        </div>
        <div class="footerBtn">
            <el-button type="primary" @click="close">关闭</el-button>
        </div>
        <CreatePurchaseOrderDialog
            ref="createPurchaseOrderDialogRef"
            :visible.sync="createPurchaseOrderDialogVisible"
            :items="createPurchaseOrderItems"
            @reload="getPurchaseOrdernoList"
        ></CreatePurchaseOrderDialog>
    </div>
</template>

<script>
import CreatePurchaseOrderDialog from "./CreatePurchaseOrderDialog";
import moment from "moment";

export default {
    props: ["rowData"],
    components: {
        CreatePurchaseOrderDialog,
    },
    data() {
        return {
            addCaiGouOrderDialogStatus: false,
            tableData: [],
            ruleForm: { orderno: "" },
            rules: {
                orderno: [
                    {
                        required: true,
                        message: "请输入采购单号",
                        trigger: "blur",
                    },
                ],
            },
            query: {
                page: 1,
                limit: 10,
            },
            total: 0,
            createPurchaseOrderDialogVisible: false,
            createPurchaseOrderItems: [],
        };
    },

    filters: {
        toErpStatusText(input) {
            return (
                {
                    0: "自由(缺省)",
                    1: "未用",
                    2: "正在审批",
                    3: "审批通过",
                    4: "审批未通过",
                    5: "输出",
                    6: "冻结",
                    7: "执行完毕",
                }[input] || ""
            );
        },
        toZhongTaiStatusText(input) {
            return (
                {
                    0: "待确认",
                    1: "下单已确认",
                    2: "下单失败",
                    3: "已驳回",
                    4: "采购审核中",
                    5: "财务审核中",
                    6: "预下单",
                    7: "自动下单",
                    8: "延期下单",
                    9: "作废",
                }[input] || ""
            );
        },
    },
    mounted() {
        this.getPurchaseOrdernoList();
    },
    methods: {
        //采购单列表
        async getPurchaseOrdernoList() {
            let data = {
                ...this.query,
            };
            data.period = this.rowData.id;
            let res = await this.$request.caiGouPerform.getPurchaseOrdernoList(
                data
            );
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        async del(row) {
            let res = await this.$request.caiGouPerform.delPurchaseOrderno({
                id: row.id,
            });
            if (res.data.error_code == 0) {
                this.$message.success("删除成功");
                this.getPurchaseOrdernoList();
            }
        },
        close() {
            this.$emit("caiGouOrderhandleClose");
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        ...this.ruleForm,
                    };
                    data.period = this.rowData.id;
                    this.$request.caiGouPerform
                        .addPurchaseOrderno(data)
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.addCaiGouOrderhandleClose();
                                this.$message.success("添加成功");
                            }
                        });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        addCaiGouOrder() {
            this.ruleForm.orderno = "";
            this.addCaiGouOrderDialogStatus = true;
        },
        addCaiGouOrderhandleClose() {
            this.addCaiGouOrderDialogStatus = false;
            this.getPurchaseOrdernoList();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getPurchaseOrdernoList();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getPurchaseOrdernoList();
        },
        onCheckPurchaseOrder(row) {
            const createPurchaseOrderDialogRef =
                this.$refs.createPurchaseOrderDialogRef;
            const {
                id,
                period,
                orderno,
                bill_date,
                warehouse,
                warehouse_code,
                supplier,
                supplier_code,
                department,
                department_code,
                operator_name,
                operator_code,
                setttlement,
                setttlement_code,
                remark,
            } = row;
            createPurchaseOrderDialogRef.supplierOptions = [
                { value: supplier_code, label: supplier },
            ];
            createPurchaseOrderDialogRef.departmentOptions = [
                { value: department_code, label: department },
            ];
            createPurchaseOrderDialogRef.model = Object.assign(
                {},
                createPurchaseOrderDialogRef.$options.data().model,
                {
                    id,
                    period,
                    orderno,
                    bill_date: moment(bill_date * 1000).format("YYYY-MM-DD"),
                    warehouse,
                    warehouse_code,
                    supplier,
                    supplier_code,
                    department,
                    department_code,
                    operator_name,
                    operator_code,
                    setttlement,
                    setttlement_code,
                    remark,
                    items: [],
                    statusMap: {
                        operate_status: row.operate_status,
                        status: row.status,
                    },
                    // 添加收款公司信息
                    payee_merchant_id: row.payee_merchant_id,
                    payee_merchant_name: row.payee_merchant_name,
                }
            );
            // model 数据现在通过 modelData prop 传递，不需要手动设置
            this.createPurchaseOrderItems = row.items.map((item) => ({
                ...item,
            }));
            createPurchaseOrderDialogRef.loadApproveList(row.corp_code);
            this.createPurchaseOrderDialogVisible = true;
        },
    },
};
</script>

<style lang="scss" scoped>
.footerBtn {
    margin-top: 15px;
    display: flex;
    justify-content: right;
}
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}
</style>
