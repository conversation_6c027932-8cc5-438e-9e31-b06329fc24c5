<template>
    <el-dialog
        :visible="visible"
        fullscreen
        append-to-body
        :before-close="closeDialog"
    >
        <el-row type="flex" justify="end">
            <el-button type="primary" @click="onPrint">打印</el-button>
        </el-row>
        <vue-easy-print tableShow ref="vueEasyPrintRef">
            <div class="title">{{ companyName }}</div>
            <div class="title">国内采购单</div>
            <el-form label-position="left">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="单号">{{
                            currentModel.orderno
                        }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="供应商">{{
                            currentModel.supplier
                        }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="申请人">{{
                            currentModel.operator_name
                        }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="申请时间">{{
                            currentModel.created_time
                        }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="仓库">{{
                            currentModel.warehouse
                        }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="预计到货时间"></el-form-item>
                    </el-col>
                </el-row>
                <el-table
                    :data="items"
                    border
                    show-summary
                    :summary-method="getSummaries"
                >
                    <el-table-column
                        label="简码"
                        align="center"
                        prop="short_code"
                    ></el-table-column>
                    <el-table-column
                        label="存货名称"
                        align="center"
                        prop="billing_name"
                    ></el-table-column>
                    <el-table-column
                        label="英文名"
                        align="center"
                        prop="en_product_name"
                    ></el-table-column>
                    <el-table-column label="年份" align="center" prop="years">
                        <template slot-scope="scope">
                            <div>
                                {{
                                    scope.row.years
                                        ? scope.row.years
                                        : scope.row.grape_picking_years
                                }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="税率"
                        align="center"
                        prop="tax_rate"
                    ></el-table-column>
                    <el-table-column
                        label="规格"
                        align="center"
                        prop="capacity"
                    ></el-table-column>
                    <el-table-column
                        label="主计量"
                        align="center"
                        prop="unit"
                    ></el-table-column>
                    <el-table-column
                        label="数量"
                        align="center"
                        prop="number"
                    ></el-table-column>
                    <el-table-column
                        label="含税单价"
                        align="center"
                        prop="price"
                    ></el-table-column>
                    <el-table-column
                        label="含税金额"
                        align="center"
                        prop="total"
                    ></el-table-column>
                </el-table>
                <div
                    style="
                        text-align: right;
                        font-size: 16px;
                        font-weight: bold;
                        margin-top: 10px;
                    "
                >
                    {{ total_cn }}
                </div>
                <el-form-item
                    label="收货地址和联系人"
                    label-width="130px"
                    style="margin-top: 10px"
                >
                    <el-input
                        v-model="address"
                        type="textarea"
                        :autosize="{ minRows: 2 }"
                        class="input"
                    ></el-input>
                    <div class="input-text">{{ address }}</div>
                </el-form-item>
                <el-form-item
                    label="收货质量"
                    label-width="130px"
                    style="margin-top: 10px"
                >
                    <el-input
                        v-model="quality"
                        type="textarea"
                        :autosize="{ minRows: 2 }"
                        class="input"
                    ></el-input>
                    <div class="input-text">{{ quality }}</div>
                </el-form-item>
                <div>
                    请在一个工作日内提供真实有效的运单号或者发货凭证，谢谢
                </div>
            </el-form>
        </vue-easy-print>
    </el-dialog>
</template>

<script>
import vueEasyPrint from "vue-easy-print";
export default {
    components: {
        vueEasyPrint,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        items: {
            type: Array,
            default: () => [],
        },
        modelData: {
            type: Object,
            default: () => ({}),
        },
    },
    data: () => ({
        model: {},
        total_cn: "",
        address:
            "江苏省南通市通州区金新街道希望大道与文昌路交叉口酒云网南通中心仓29A-4后门（中国供销集团东二门），王代密，13052888102",
        quality:
            "酒款一定要无漏液，顶塞，污标，并且有贴中文背标，否则，仓库拒收",
        // 收款公司映射
        payeeCompanyMap: {
            1: "重庆云酒佰酿电子商务有限公司",
            2: "佰酿云酒（重庆）科技有限公司",
            5: "渝中区微醺酒业商行",
            10: "海南一花一世界科技有限公司",
        },
    }),
    computed: {
        // 获取当前使用的model数据（优先使用prop传递的modelData）
        currentModel() {
            return Object.keys(this.modelData).length > 0
                ? this.modelData
                : this.model;
        },
        // 根据收款公司ID动态获取公司名称
        companyName() {
            const model = this.currentModel;
            if (model.payee_merchant_name) {
                console.log("1111111111");

                return model.payee_merchant_name;
            }
            if (
                model.payee_merchant_id &&
                this.payeeCompanyMap[model.payee_merchant_id]
            ) {
                console.log("2222222222222");
                return this.payeeCompanyMap[model.payee_merchant_id];
            }
            // 默认返回佰酿云酒科技有限公司
            return "佰酿云酒（重庆）科技有限公司";
        },
    },
    updated() {
        this.totalPriceFormat();
    },
    mounted() {},
    methods: {
        totalPriceFormat() {
            let total = 0;
            this.items.map((item) => {
                console.log(item);
                total = total + item.total;
            });
            console.log(total);
            this.total_cn = this.convertToChinese(total.toFixed(2));
        },
        convertToChinese(num) {
            // 定义汉字数字
            const cnNums = [
                "零",
                "壹",
                "贰",
                "叁",
                "肆",
                "伍",
                "陆",
                "柒",
                "捌",
                "玖",
            ];
            // 定义基本单位
            const cnIntRadice = ["", "拾", "佰", "仟"];
            // 定义大单位
            const cnIntUnits = ["", "万", "亿", "兆"];
            // 定义小数单位
            const cnDecUnits = ["角", "分", "毫", "厘"];
            // 整数金额单位
            const cnInteger = "整";
            // 整型完以后的单位
            const cnIntLast = "元";
            // 最大处理的数字
            const maxNum = 9999999999999999.99;

            let integerNum; // 金额整数部分
            let decimalNum; // 金额小数部分
            let chineseStr = ""; // 输出的中文金额字符串
            let parts; // 分离金额后用的数组，预定义

            if (num === "") {
                return "";
            }

            num = parseFloat(num);
            if (num >= maxNum) {
                return "";
            }
            if (num === 0) {
                chineseStr = cnNums[0] + cnIntLast + cnInteger;
                return chineseStr;
            }
            // 转换为整数和小数部分
            num = num.toString();
            if (num.indexOf(".") === -1) {
                integerNum = num;
                decimalNum = "";
            } else {
                parts = num.split(".");
                integerNum = parts[0];
                decimalNum = parts[1].substr(0, 4); // 只保留到厘位
            }
            // 处理整数部分
            if (parseInt(integerNum, 10) > 0) {
                let zeroCount = 0;
                let IntLen = integerNum.length;
                for (let i = 0; i < IntLen; i++) {
                    let n = integerNum.substr(i, 1);
                    let p = IntLen - i - 1;
                    let q = p / 4;
                    let m = p % 4;
                    if (n === "0") {
                        zeroCount++;
                    } else {
                        if (zeroCount > 0) {
                            chineseStr += cnNums[0];
                        }
                        zeroCount = 0;
                        chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
                    }
                    if (m === 0 && zeroCount < 4) {
                        chineseStr += cnIntUnits[parseInt(q)];
                    }
                }
                chineseStr += cnIntLast;
            }
            // 处理小数部分
            if (decimalNum !== "") {
                let decLen = decimalNum.length;
                for (let i = 0; i < decLen; i++) {
                    let n = decimalNum.substr(i, 1);
                    if (n !== "0") {
                        chineseStr += cnNums[Number(n)] + cnDecUnits[i];
                    }
                }
            }
            if (chineseStr === "") {
                chineseStr += cnNums[0] + cnIntLast + cnInteger;
            } else if (decimalNum === "") {
                chineseStr += cnInteger;
            }
            return chineseStr;
        },
        closeDialog() {
            this.$emit("update:visible", false);
        },
        onPrint() {
            this.$refs.vueEasyPrintRef.print();
        },
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = "总计";
                    return;
                }
                //column.property == 'zeroTotalPrice'只要换上你想汇总列的prop属性值就可以实现某一行汇总
                if (column.property == "number" || column.property == "total") {
                    const values = data.map((item) =>
                        Number(item[column.property])
                    );
                    if (!values.every((value) => isNaN(value))) {
                        sums[index] = values.reduce((prev, curr) => {
                            const value = Number(curr);
                            if (!isNaN(value)) {
                                return Number(prev) + Number(curr);
                            } else {
                                return Number(prev);
                            }
                        }, 0);
                    }
                    if (column.property == "total") {
                        sums[index] = sums[index].toFixed(2);
                    }
                }
            });
            return sums;
        },
    },
};
</script>

<style scoped lang="scss">
.title {
    font-weight: bold;
    font-size: 20px;
    color: #333;
    text-align: center;
}
.el-form-item {
    margin-bottom: 0;
}

.input-text {
    display: none;
}
/* 合计行整体样式修改开始 */
.el-table /deep/ .el-table__footer-wrapper tbody td {
    font-weight: bolder;
}

@media print {
    /deep/ .el-table {
        &__header,
        &__body,
        &__footer {
            width: 100% !important;
        }

        colgroup {
            display: none;
        }
    }

    .input {
        display: none;

        &-text {
            display: block;
        }
    }
}
</style>
