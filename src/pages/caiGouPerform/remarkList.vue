<template>
    <div>
        <el-button type="primary" size="mini" @click="addRemark"
            >添加备注</el-button
        >
        <div class="m-t-10">
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column
                    prop="remark"
                    label="备注内容"
                    min-width="180"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="operator_name"
                    label="备注人"
                    width="180"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="created_time"
                    label="时间"
                    width="200"
                    align="center"
                >
                </el-table-column>
            </el-table>
        </div>
        <div class="pagination-block m-t-10">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <div>
            <el-dialog
                title="添加备注"
                :visible.sync="addRemarkDialogStatus"
                :before-close="addRemarkhandleClose"
                width="30%"
                append-to-body
            >
                <el-form
                    :model="ruleForm"
                    :rules="rules"
                    ref="ruleForm"
                    label-width="0px"
                    @submit.native.prevent
                    class="demo-ruleForm"
                >
                    <el-form-item label="" prop="remark">
                        <el-input
                            v-model="ruleForm.remark"
                            placeholder="请输入商品备注"
                        ></el-input>
                    </el-form-item>
                    <el-form-item class="pagination-block">
                        <el-button @click="addRemarkhandleClose()"
                            >取消</el-button
                        >
                        <el-button
                            type="primary"
                            @click="submitForm('ruleForm')"
                            >确定</el-button
                        >
                    </el-form-item>
                </el-form>
            </el-dialog>
        </div>
        <div class="footerBtn">
            <el-button type="primary" @click="close">关闭</el-button>
        </div>
    </div>
</template>

<script>
export default {
    props: ["rowData"],
    data() {
        return {
            addRemarkDialogStatus: false,
            tableData: [],
            ruleForm: { remark: "" },
            rules: {
                remark: [
                    {
                        required: true,
                        message: "请输入商品备注",
                        trigger: "blur",
                    },
                ],
            },
            query: {
                page: 1,
                limit: 10,
            },
            total: 0,
        };
    },
    mounted() {
        this.remarkList();
    },
    methods: {
        async remarkList() {
            let data = {
                ...this.query,
            };
            data.period = this.rowData.id;
            let res = await this.$request.article.remarkList(data);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        ...this.ruleForm,
                    };
                    data.period = this.rowData.id;
                    data.periods_type = this.rowData.periods_type;
                    this.$request.article.createRemark(data).then((res) => {
                        if (res.data.error_code == 0) {
                            this.addRemarkhandleClose();
                            this.$message.success("添加成功");
                        }
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        addRemark() {
            this.addRemarkDialogStatus = true;
            this.ruleForm.remark = "";
        },
        addRemarkhandleClose() {
            this.addRemarkDialogStatus = false;
            this.remarkList();
        },
        close() {
            this.$emit("remarkhandleClose");
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.remarkList();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.remarkList();
        },
    },
};
</script>

<style lang="scss" scoped>
.footerBtn {
    margin-top: 15px;
    display: flex;
    justify-content: right;
}
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}
</style>
