<template>
    <div>
        <el-card shadow="never">
            <div class="header_search">
                <div>
                    <el-input
                        v-model="query.period_ids"
                        class="w-mini m-r-10"
                        placeholder="期数"
                        @keyup.enter.native="search"
                        size="mini"
                        clearable
                    >
                    </el-input>
                </div>
                <div>
                    <el-select
                        class="w-mini m-r-10"
                        v-model="query.import_type"
                        filterable
                        size="mini"
                        placeholder="采购类型"
                        clearable
                    >
                        <el-option
                            v-for="item in caiGouTypeOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-input
                        v-model="query.supplier"
                        class="w-normal m-r-10"
                        placeholder="供应商"
                        @keyup.enter.native="search"
                        clearable
                        size="mini"
                    >
                    </el-input>
                </div>
                <div>
                    <el-select
                        class="w-mini m-r-10"
                        v-model="query.periods_type"
                        filterable
                        size="mini"
                        placeholder="频道"
                        clearable
                    >
                        <el-option
                            v-for="item in periods_typeList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-select
                        class="w-large m-r-10"
                        v-model="query.payee_merchant_id"
                        filterable
                        size="mini"
                        placeholder="收款公司"
                        clearable
                    >
                        <el-option
                            v-for="item in payee_merchant_list"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-date-picker
                        v-model="time_sell"
                        type="datetimerange"
                        align="right"
                        size="mini"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="开售开始日期"
                        end-placeholder="开售结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                        @change="
                            changeTime(
                                time_sell,
                                'sell_time_start',
                                'sell_time_end'
                            )
                        "
                    >
                    </el-date-picker>
                </div>
                <div>
                    <el-date-picker
                        v-model="estimatePurchaseTime"
                        type="datetimerange"
                        align="right"
                        size="mini"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="预计采购开始日期"
                        end-placeholder="预计采购结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                        @change="
                            changeTime(
                                estimatePurchaseTime,
                                'estimate_purchase_start',
                                'estimate_purchase_end'
                            )
                        "
                    >
                    </el-date-picker>
                </div>
                <!-- <div>
                    <el-date-picker
                        v-model="time1"
                        type="datetimerange"
                        align="right"
                        size="mini"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="上架开始日期"
                        end-placeholder="上架结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                         @change="
                            changeTime(time1, 'onsale_time_start', 'onsale_time_end')
                        "
                    >
                    </el-date-picker>
                </div> -->
                <div>
                    <el-date-picker
                        v-model="time2"
                        type="datetimerange"
                        align="right"
                        size="mini"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="发货开始日期"
                        end-placeholder="发货结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                        @change="
                            changeTime(time2, 'shipment_start', 'shipment_end')
                        "
                    >
                    </el-date-picker>
                </div>
                <div>
                    <el-select
                        class="w-mini m-r-10"
                        v-model="query.is_inventory_order"
                        filterable
                        size="mini"
                        placeholder="是否订货"
                        clearable
                    >
                        <el-option
                            v-for="item in is_inventory_order_options"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-select
                        class="w-mini m-r-10"
                        v-model="query.sort"
                        filterable
                        size="mini"
                        placeholder="排序"
                        clearable
                    >
                        <el-option
                            v-for="(item, index) in sortList"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-select
                        class="w-mini m-r-10"
                        v-model="query.sales_type"
                        filterable
                        size="mini"
                        placeholder="销售类型"
                        clearable
                    >
                        <el-option
                            v-for="item in [
                                { label: '代发', value: 0 },
                                { label: '预售', value: 1 },
                                { label: '订金', value: 2 },
                            ]"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </div>
                <div class="m-r-10">
                    <el-checkbox v-model="placeOrderChecked"
                        >未下采购单</el-checkbox
                    >
                </div>
                <div class="m-r-10">
                    <el-checkbox v-model="salesChecked"
                        >有售</el-checkbox
                    >
                </div>
            </div>
            <div>
                <el-button type="warning" size="mini" @click="search"
                    >查询</el-button
                >
                <el-button type="primary" size="mini" @click="onAdd"
                    >添加</el-button
                >
                <el-button
                    type="danger"
                    size="mini"
                    :disabled="!checkIdList.length || !!currBatchCommand"
                    @click="onCreatePurchaseOrder"
                    >下单</el-button
                >
                <!-- <el-button type="primary" size="mini">批量移除</el-button> -->
                <el-dropdown
                    size="small"
                    trigger="click"
                    @command="onBatchCommand"
                >
                    <el-button
                        type="primary"
                        size="mini"
                        style="margin-left: 10px"
                        >批量操作</el-button
                    >
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                            v-if="!currBatchCommand"
                            command="batchRemove"
                            >批量移除</el-dropdown-item
                        >
                        <template v-if="currBatchCommand === 'batchRemove'">
                            <el-dropdown-item command="cancelBatchRemove"
                                >取消移除</el-dropdown-item
                            >
                            <el-dropdown-item
                                command="confirmBatchRemove"
                                :disabled="!removeCheckIdList.length"
                                >确认移除</el-dropdown-item
                            >
                        </template>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
        </el-card>
        <el-card shadow="never" class="m-t-10">
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column
                    v-if="currBatchCommand !== 'batchRemove'"
                    width="80"
                    align="center"
                    label="下单"
                >
                    <template slot-scope="scope">
                        <el-checkbox-group v-model="checkIdList">
                            <el-checkbox
                                :label="scope.row.id"
                                :disabled="
                                    currCheckPayeeAndSupplier
                                        ? currCheckPayeeAndSupplier !==
                                          `${scope.row.payee_merchant_id}-${scope.row.supplier_merchant_id}`
                                        : false
                                "
                                >{{ "" }}</el-checkbox
                            >
                        </el-checkbox-group>
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="currBatchCommand === 'batchRemove'"
                    width="80"
                    align="center"
                    label="移除"
                >
                    <template slot-scope="scope">
                        <el-checkbox-group v-model="removeCheckIdList">
                            <el-checkbox :label="scope.row.id">{{
                                ""
                            }}</el-checkbox>
                        </el-checkbox-group>
                    </template>
                </el-table-column>
                <!-- @selection-change="handleSelectionChange" -->
                <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
                <el-table-column
                    prop="id"
                    label="期数"
                    width="100"
                    align="center"
                >
                </el-table-column>
                <el-table-column label="时间" width="270" align="center">
                    <template slot-scope="scope">
                        <!-- <div>上架时间：{{ scope.row.onsale_time }}</div> -->
                        <div>开售时间：{{ scope.row.sell_time }}</div>
                        <div>结束时间：{{ scope.row.sold_out_time }}</div>
                        <div>
                            发货时间：{{ scope.row.predict_shipment_time }}
                        </div>
                        <div style="text-align: left;">
                            最近发货时间：{{ scope.row.last_delivery_time }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="payee_merchant_name"
                    label="收款公司"
                    width="150"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="supplier"
                    label="供应商"
                    width="150"
                    align="center"
                >
                </el-table-column>

                <el-table-column
                    prop="id"
                    label="预计采购时间"
                    width="250"
                    align="center"
                >
                    <template slot-scope="scope">
                        <div>
                            <!-- {{
                                moment(
                                    new Date(scope.row.onsale_time).valueOf()
                                ).format("yyyy-MM-DD HH:mm:ss")
                            }} -->
                            <el-date-picker
                                v-model="scope.row.estimate_purchase"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetime"
                                size="mini"
                                placeholder="选择日期时间"
                                @change="updateEstimatePurchase(scope.row)"
                            >
                            </el-date-picker>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="id"
                    label="采购单"
                    width="100"
                    align="center"
                >
                    <template slot-scope="scope">
                        <div @click="caiGou(scope.row)">
                            <el-link type="primary" :underline="false">{{
                                scope.row.orderno ? scope.row.orderno : "未填"
                            }}</el-link>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="title"
                    label="商品名称"
                    min-width="250"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="onsale_status"
                    label="商品状态"
                    width="100"
                    align="center"
                >
                    <template slot-scope="scope">
                        <div>
                            {{ statusTxt[scope.row.onsale_status] }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="short_code"
                    label="简码"
                    width="220"
                    align="center"
                >
                    <template slot-scope="scope">
                        <div v-if="scope.row.inventory_order">
                            <div
                                v-for="(item, index) in scope.row
                                    .inventory_order"
                                :key="index"
                            >
                                {{ item.short_code }} ({{ item.costprice }})
                            </div>
                        </div>
                        <div v-else>
                            <div
                                v-for="(item, index) in scope.row.short_code"
                                :key="index"
                            >
                                {{ item }}
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="short_code_saled"
                    label="已售(已售瓶数)"
                    width="180"
                    align="center"
                >
                    <template slot-scope="scope">
                        <div @click="kuCun(scope.row)">
                            <div
                                v-for="(item, i) in scope.row
                                    .$shortCodeSaledList"
                                :key="i"
                            >
                                <el-link type="primary" :underline="false"
                                    >{{ item.short_code }}({{
                                        item.num.toFixed(2)
                                    }})</el-link
                                >
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <!-- <el-table-column
                    prop="rec_order"
                    label="建议订货数量"
                    width="180"
                    align="center"
                >
                    <template slot-scope="scope">
                        <div>
                            <div
                                v-for="(val, key, i) in scope.row.rec_order"
                                :key="i"
                            >
                                <span>{{ key }}({{ val }})</span>
                            </div>
                        </div>
                    </template>
                </el-table-column> -->
                <el-table-column label="当前订货量" width="180" align="center">
                    <template slot-scope="scope">
                        <div
                            v-for="(item, index) in scope.row.inventory_order"
                            :key="index"
                        >
                            {{ item.short_code }}({{ item.order }})
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="本期售卖库存"
                    width="180"
                    align="center"
                >
                    <template slot-scope="scope">
                        <div
                            v-for="(item, index) in scope.row.inventory_order"
                            :key="index"
                        >
                            {{ item.short_code }}({{ item.inventory }})
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="periods_type"
                    label="频道"
                    width="100"
                    align="center"
                >
                    <template slot-scope="scope">
                        <div>
                            {{ periods_typeTxt[scope.row.periods_type] }}
                        </div>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="id"
                    label="自动延期"
                    width="100"
                    align="center"
                >
                    <template slot-scope="scope">
                        <el-checkbox
                            :true-label="1"
                            :false-label="0"
                            v-model="scope.row.is_postpone"
                            @change="changeIsPostpone(scope.row)"
                        ></el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="is_supplier_delivery"
                    label="代发"
                    width="100"
                    align="center"
                >
                    <template slot-scope="scope">
                        <span>{{
                            scope.row.is_supplier_delivery == 0 ? "否" : "是"
                        }}</span>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="id"
                    label="负责人"
                    width="200"
                    align="center"
                >
                    <template slot-scope="scope">
                        <div>运营：{{ scope.row.operation_name }}</div>
                        <div>采购：{{ scope.row.buyer_name }}</div>
                        <div>文案：{{ scope.row.creator_name }}</div>
                        <div>审核：{{ scope.row.operation_review_name }}</div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="import_type"
                    label="采购类型"
                    width="100"
                    align="center"
                >
                    <template slot-scope="scope">
                        <div>
                            {{ scope.row.import_type | import_typeFormat }}
                        </div>
                    </template>
                </el-table-column>

                <el-table-column
                    fixed="right"
                    label="操作"
                    width="120"
                    align="center"
                >
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            @click="onCheckWmsStock(scope.row)"
                            >萌牙库存</el-button
                        >
                        <br />
                        <el-button @click="orderGoods(scope.row)" type="text">
                            修改订货量
                        </el-button>
                        <br />
                        <el-button @click="remark(scope.row)" type="text">
                            备注
                        </el-button>
                        <br />
                        <el-popconfirm
                            title="确认要移除这个期数吗？移除后无法再加入采购看板"
                            @confirm="deleteRow(scope.row)"
                        >
                            <el-button slot="reference" type="text">
                                移除
                            </el-button>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div class="pagination-block m-t-10">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <!-- 修改订货量 -->
        <div>
            <el-dialog
                title="修改订货量"
                :visible.sync="orderGoodsDialogStatus"
                :before-close="orderGoodshandleClose"
                width="70%"
            >
                <UpdateOrderGoodsCount
                    v-if="orderGoodsDialogStatus"
                    :rowData="rowData"
                    @orderGoodshandleClose="orderGoodshandleClose"
                />
            </el-dialog>
        </div>
        <!-- 商品备注 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="商品备注"
                :visible.sync="remarkDialogStatus"
                width="50%"
                :before-close="remarkhandleClose"
            >
                <RemarkList
                    v-if="remarkDialogStatus"
                    :rowData="rowData"
                    @remarkhandleClose="remarkhandleClose"
                />
            </el-dialog>
        </div>
        <!-- 库存信息 -->
        <div>
            <el-dialog
                title="库存信息"
                :visible.sync="kuCunDialogStatus"
                :before-close="kuCunhandleClose"
                width="60%"
            >
                <kuCunInfo
                    v-if="kuCunDialogStatus"
                    :rowData="rowData"
                    @kuCunhandleClose="kuCunhandleClose"
                />
            </el-dialog>
        </div>
        <!-- 采购单 -->
        <div>
            <el-dialog
                title="采购单"
                :visible.sync="caiGouOrderDialogStatus"
                :before-close="caiGouOrderhandleClose"
                width="50%"
            >
                <CaiGouOrder
                    v-if="caiGouOrderDialogStatus"
                    :rowData="rowData"
                    @caiGouOrderhandleClose="caiGouOrderhandleClose"
                />
            </el-dialog>
        </div>

        <el-dialog :visible.sync="addDialogVisible">
            <el-form @submit.native.prevent>
                <el-form-item>
                    <el-input
                        v-model="vhGoodsId"
                        placeholder="请输入期数"
                        style="margin-right: 10px; width: 200px"
                        @keyup.enter.native="onSearchVhGoods"
                    ></el-input>
                    <el-button
                        type="primary"
                        :disabled="!vhGoodsId"
                        @click="onSearchVhGoods"
                        >查询</el-button
                    >
                </el-form-item>
                <el-form-item v-if="vhGoods" label="商品状态">{{
                    statusTxt[vhGoods.onsale_status]
                }}</el-form-item>
            </el-form>
            <div slot="footer">
                <el-button type="primary" @click="onConfirmAdd"
                    >确认添加</el-button
                >
            </div>
        </el-dialog>
        <WmsStockDialog
            :visible.sync="wmsStockDialogVisible"
            :period="checkWmsStockPeriod"
        ></WmsStockDialog>
        <CreatePurchaseOrderDialog
            ref="createPurchaseOrderDialogRef"
            :visible.sync="createPurchaseOrderDialogVisible"
            :items="createPurchaseOrderItems"
            @reload="caiGouPerformList"
        ></CreatePurchaseOrderDialog>
    </div>
</template>

<script>
import UpdateOrderGoodsCount from "./updateOrderGoodsCount.vue";
import RemarkList from "./remarkList.vue";
import kuCunInfo from "./kuCunInfo.vue";
import CaiGouOrder from "./caiGouOrder.vue";
import liveApi from "@/services/live";
import WmsStockDialog from "./WmsStockDialog";
import CreatePurchaseOrderDialog from "./CreatePurchaseOrderDialog";

export default {
    components: {
        UpdateOrderGoodsCount,
        RemarkList,
        kuCunInfo,
        CaiGouOrder,
        WmsStockDialog,
        CreatePurchaseOrderDialog,
    },
    data() {
        return {
            orderGoodsDialogStatus: false,
            remarkDialogStatus: false,
            kuCunDialogStatus: false,
            caiGouOrderDialogStatus: false,
            time_sell: "",
            rowData: {},
            caiGouTypeOptions: [
                {
                    name: "跨境",
                    id: 2,
                },
                {
                    name: "自进口",
                    id: 0,
                },
                {
                    name: "地采",
                    id: 1,
                },
            ],
            is_inventory_order_options: [
                {
                    id: 0,
                    name: "初次订货",
                },
                {
                    id: 1,
                    name: "订货",
                },
            ],
            periods_typeTxt: {
                0: "闪购",
                1: "秒发",
                2: "跨境",
                3: "尾货",
                4: "兔头",
                9: "商家秒发",
            },
            statusTxt: {
                0: "待上架",
                1: "待售中",
                2: "在售中",
                3: "已下架",
                4: "已售罄",
            },
            tableData: [],
            multipleSelection: [],
            query: {
                period_ids: "",
                periods_type: "",
                is_inventory_order: "",
                import_type: "",
                supplier: "",
                onsale_time_start: "",
                onsale_time_end: "",
                sell_time_start: "",
                sell_time_end: "",
                shipment_start: "",
                shipment_end: "",
                // estimate_purchase_start: "",
                // estimate_purchase_end: "",
                sort: "",
                page: 1,
                limit: 10,
                sales_type: "",
                payee_merchant_id:"",
               
            },
            total: 0,
            time1: "",
            estimatePurchaseTime: "",
            periods_typeList: [
                {
                    id: 0,
                    name: "闪购",
                },
                {
                    id: 1,
                    name: "秒发",
                },
                {
                    id: 2,
                    name: "跨境",
                },
                {
                    id: 3,
                    name: "尾货",
                },
            ],
            payee_merchant_list:[],
            time2: "",
            sortList: [
                {
                    label: "期数降序",
                    value: JSON.stringify({ id: "desc" }),
                },
                {
                    label: "期数升序",
                    value: JSON.stringify({ id: "asc" }),
                },
                {
                    label: "开售时间降序",
                    value: JSON.stringify({ sell_time: "desc" }),
                },
                {
                    label: "开售时间升序",
                    value: JSON.stringify({ sell_time: "asc" }),
                },
            ],
            addDialogVisible: false,
            vhGoodsId: "",
            vhGoods: null,
            checkIdList: [],
            wmsStockDialogVisible: false,
            checkWmsStockPeriod: "",
            createPurchaseOrderDialogVisible: false,
            createPurchaseOrderItems: [],
            placeOrderChecked: false,
            salesChecked: false,
            removeCheckIdList: [],
            currBatchCommand: "",
        };
    },
    computed: {
        currCheckPayeeAndSupplier({ checkIdList }) {
            if (!checkIdList.length) return "";
            const [id] = checkIdList;
            const findItem = this.tableData.find((item) => item.id === id);
            if (!findItem) return "";
            return `${findItem.payee_merchant_id}-${findItem.supplier_merchant_id}`;
        },
    },
    filters: {
        import_typeFormat(val) {
            switch (val) {
                case 0:
                    return "自进口";
                case 1:
                    return "地采";
                case 2:
                    return "跨境";
                default:
                    return "未知";
            }
        },
    },
    mounted() {
        this.payeeMerchantList();
        this.caiGouPerformList();
        
    },
    methods: {
        changeTime(time, s, e) {
            if (time && time.length != 0) {
                this.query[s] = time[0];
                this.query[e] = time[1];
            } else {
                this.query[s] = "";
                this.query[e] = "";
            }
        },
        async caiGouPerformList() {
            this.checkIdList = [];
            this.removeCheckIdList = [];
            let data = {
                ...this.query,
            };
            if (this.placeOrderChecked) data.place_order = 0;
            this.salesChecked ? data.is_sold = 1 : data.is_sold = 0;
            console.log(this.time1, this.time2);

            let res = await this.$request.caiGouPerform.caiGouPerformList(data);
            if (res.data.error_code == 0) {
                const tableData = res.data.data.list;
                tableData.forEach((item) => {
                    const { inventory_order, short_code, short_code_saled } =
                        item;
                    const shortCodeList = inventory_order
                        ? inventory_order.map(({ short_code }) => short_code)
                        : short_code;
                    item.$shortCodeSaledList = shortCodeList.map(
                        (short_code) => ({
                            short_code,
                            num: short_code_saled[short_code] || 0,
                        })
                    );
                });
                this.tableData = tableData;
                this.total = res.data.data.total;
            }
        },

        async payeeMerchantList() {
            const data= {};
            let res = await this.$request.caiGouPerform.caiGouPerformpPyeeMerchantList(data);
            if (res.data.error_code == 0) {
                this.payee_merchant_list = res.data.data;
            }
        },
        search() {
            this.query.page = 1;
            this.caiGouPerformList();
        },
        async deleteRow(row) {
            let res = await this.$request.caiGouPerform.delRecord({
                period: row.id,
            });
            if (res.data.error_code == 0) {
                this.$message.success("移除成功");
                this.caiGouPerformList();
            }
        },
        //更新自动延期字段
        async changeIsPostpone(row) {
            let data = {
                period: row.id,
                periods_type: row.periods_type,
                is_postpone: row.is_postpone,
            };
            let res = await this.$request.article.updateUncUsed(data);
            if (res.data.error_code == 0) {
                this.$message.success("更新成功");
                this.caiGouPerformList();
            }
        },
        //更新预计采购时间
        async updateEstimatePurchase(row) {
            let data = {
                period: row.id,
                estimate_purchase: row.estimate_purchase,
            };
            let res = await this.$request.caiGouPerform.updateEstimatePurchase(
                data
            );
            if (res.data.error_code == 0) {
                this.$message.success("更新成功");
                this.caiGouPerformList();
            }
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
            console.log("已选择", this.multipleSelection);
        },
        //修改订货量
        orderGoods(row) {
            this.rowData = row;
            this.orderGoodsDialogStatus = true;
        },
        orderGoodshandleClose() {
            this.orderGoodsDialogStatus = false;
            this.caiGouPerformList();
        },
        //备注
        remark(row) {
            this.rowData = row;
            this.remarkDialogStatus = true;
        },
        remarkhandleClose() {
            this.remarkDialogStatus = false;
            this.caiGouPerformList();
        },
        // 库存信息
        kuCun(row) {
            this.rowData = row;
            this.kuCunDialogStatus = true;
        },
        kuCunhandleClose() {
            this.kuCunDialogStatus = false;
            this.caiGouPerformList();
        },
        caiGou(row) {
            this.rowData = row;
            this.caiGouOrderDialogStatus = true;
        },
        caiGouOrderhandleClose() {
            this.caiGouOrderDialogStatus = false;
            this.caiGouPerformList();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.caiGouPerformList();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.caiGouPerformList();
        },
        onAdd() {
            this.addDialogVisible = true;
            const { vhGoodsId, vhGoods } = this.$options.data();
            this.vhGoodsId = vhGoodsId;
            this.vhGoods = vhGoods;
        },
        onSearchVhGoods() {
            liveApi.getVhGoods({ period: this.vhGoodsId }).then((res) => {
                if (res.data.error_code === 0) {
                    const goods = res.data.data;
                    if (!goods) {
                        this.$message.error("该期数不存在");
                        return;
                    }
                    this.vhGoods = goods;
                }
            });
        },
        onConfirmAdd() {
            if (+this.vhGoodsId !== this.vhGoods.id) {
                this.$message.error("期数不一致");
                return;
            }
            this.$request.caiGouPerform
                .addPurchaseRecord({
                    period: this.vhGoodsId,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.$message.success("操作成功");
                        this.caiGouPerformList();
                        this.addDialogVisible = false;
                    }
                });
        },
        onCheckWmsStock(row) {
            this.checkWmsStockPeriod = row.id;
            this.wmsStockDialogVisible = true;
        },
        onCreatePurchaseOrder() {
            console.log(this.rowData);
            const erpIdList = [];
            this.createPurchaseOrderItems = this.checkIdList
                .map((id) => this.tableData.find((item) => item.id === id))
                .map((item) =>
                    item.inventory_order.map((i) => ({
                        ...i,
                        number: item.short_code_saled[i.short_code] || 0,
                    }))
                )
                .reduce((prev, curr) => {
                    curr.forEach((item) => {
                        if (item.erp_id && !erpIdList.includes(item.erp_id))
                            erpIdList.push(item.erp_id);
                        const refItem = {
                            short_code: item.short_code,
                            billing_name: item.invoice_name,
                            en_product_name: item.en_product_name,
                            unit: item.unit,
                            capacity: item.capacity,
                            remark: `已售${item.number}`,
                            period: `${item.period}`,
                            number: item.number,
                            price: item.costprice,
                            tax_rate: item.tax_rate,
                            is_gift: 0,
                            total: item.number * item.costprice,
                            grape_picking_years: item.grape_picking_years,
                        };
                        const findItem = prev.find(
                            ({ short_code }) =>
                                refItem.short_code === short_code
                        );
                        if (findItem) {
                            findItem.number += refItem.number;
                            findItem.remark = `已售${findItem.number}`;
                            if (!findItem.period.includes(refItem.period)) {
                                findItem.period += `,${refItem.period}`;
                            }
                        } else {
                            prev.push(refItem);
                        }
                    });
                    return prev;
                }, [])
                .filter((item) => item.number);
            const findItem = this.tableData.find(
                (item) => item.id === this.checkIdList[0]
            );
            console.log('1123', findItem);
            const {
                supplier,
                supplier_code,
                buyer_dep_name,
                buyer_dep_code,
                supplier_buyer_name,
                buyer_code,
                supplier_remark,
                payee_merchant_id,
                payee_merchant_name
            } = findItem;
            const createPurchaseOrderDialogRef =
                this.$refs.createPurchaseOrderDialogRef;
            if (supplier_code)
                createPurchaseOrderDialogRef.supplierOptions = [
                    { value: supplier_code, label: supplier },
                ];
            if (buyer_dep_code)
                createPurchaseOrderDialogRef.departmentOptions = [
                    { value: buyer_dep_code, label: buyer_dep_name },
                ];
            createPurchaseOrderDialogRef.model = Object.assign(
                {},
                createPurchaseOrderDialogRef.$options.data().model,
                {
                    period: this.checkIdList.join(),
                    warehouse_code: erpIdList.length === 1 ? erpIdList[0] : "",
                    supplier,
                    supplier_code,
                    department: buyer_dep_name,
                    department_code: buyer_dep_code,
                    operator_name: supplier_buyer_name,
                    operator_code: buyer_code,
                    remark: supplier_remark,
                    payee_merchant_id:payee_merchant_id,
                    payee_merchant_name:payee_merchant_name
                }
            );
            this.createPurchaseOrderDialogVisible = true;
        },
        onBatchCommand(command) {
            switch (command) {
                case "batchRemove":
                    this.currBatchCommand = command;
                    break;
                case "cancelBatchRemove":
                    this.removeCheckIdList = [];
                    this.currBatchCommand = "";
                    break;
                case "confirmBatchRemove":
                    this.$request.caiGouPerform
                        .delRecord({
                            period: this.removeCheckIdList.join(),
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.checkIdList = [];
                                this.removeCheckIdList = [];
                                this.currBatchCommand = "";
                                this.$message.success("移除成功");
                                const timer = setTimeout(() => {
                                    timer && clearTimeout(timer);
                                    this.caiGouPerformList();
                                }, 300);
                            }
                        });
                    break;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.header_search {
    display: flex;
    justify-content: left;
    flex-wrap: wrap;
    div {
        margin-right: 5px;
    }
}
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}
</style>
