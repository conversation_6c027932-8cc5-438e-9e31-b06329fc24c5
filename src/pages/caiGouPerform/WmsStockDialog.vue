<template>
    <el-dialog
        :visible="visible"
        title="商品库存信息"
        :before-close="closeDialog"
    >
        <div v-for="(item, index) in list" :key="index">
            <div>简码：{{ item.barCode }}</div>
            <div class="mgt-10">
                <el-tag type="primary"
                    >总良品数：{{ item.totalGoodsCount }}</el-tag
                >
                <el-tag type="danger"
                    >总次品数：{{ item.totalFakeCount }}</el-tag
                >
            </div>
            <div
                v-for="(stockItem, stockIndex) in item.stockList"
                :key="`${index}-${stockIndex}`"
                class="mgt-10"
            >
                <el-tag type="info"
                    >虚拟仓名称：{{ stockItem.fictitious_name }}</el-tag
                >
                <el-tag type="primary"
                    >良品数：{{ stockItem.goods_count }}</el-tag
                >
                <el-tag type="danger"
                    >次品数：{{ stockItem.fake_count }}</el-tag
                >
            </div>
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        period: {
            required: true,
        },
    },
    data: () => ({
        list: [],
    }),
    watch: {
        visible(newVal) {
            if (newVal) {
                this.list = [];
                this.$request.caiGouPerform
                    .getWmsStockByPeriod({
                        period: this.period,
                    })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            const data = res.data.data;
                            this.list = Object.keys(data).map((key) => {
                                const stockList = data[key];
                                let totalGoodsCount = 0;
                                let totalFakeCount = 0;
                                stockList.forEach((item) => {
                                    totalGoodsCount += item.goods_count;
                                    totalFakeCount += item.fake_count;
                                });
                                return {
                                    barCode: key,
                                    totalGoodsCount,
                                    totalFakeCount,
                                    stockList,
                                };
                            });
                        }
                    });
            }
        },
    },
    methods: {
        closeDialog() {
            this.$emit("update:visible", false);
        },
    },
};
</script>

<style lang="scss" scoped>
.el-tag + .el-tag {
    margin-left: 10px;
}
</style>
