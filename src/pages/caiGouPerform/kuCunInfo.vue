<template>
    <div>
        <el-card shadow="never" class="m-t-5">
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column
                    prop="product_name"
                    label="产品名称"
                    min-width="220"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="short_code"
                    label="简码"
                    width="130"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="order"
                    label="已订货量"
                    width="100"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="saled"
                    label="已售数量"
                    width="100"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="inventory"
                    label="当前剩余库存"
                    width="130"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="inventory"
                    label="修改库存"
                    width="200"
                    align="center"
                >
                    <template slot-scope="scope">
                        <div>
                            <el-radio-group v-model="scope.row.action">
                                <el-radio label="inc">增</el-radio>
                                <el-radio label="dec">减</el-radio>
                            </el-radio-group>
                            <el-input
                                v-model="scope.row.nums"
                                placeholder=""
                                size="mini"
                                class="m-l-10"
                                style="width: 60px"
                            ></el-input>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="100"
                    align="center"
                >
                    <template slot-scope="scope">
                        <el-button @click="confirm(scope.row)" type="text">
                            确定
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
    </div>
</template>

<script>
export default {
    props: ["rowData"],
    data() {
        return {
            tableData: [],
            action: "inc",
            nums: "",
        };
    },
    mounted() {
        this.getInventoryOrderList();
    },
    methods: {
        async getInventoryOrderList() {
            let res = await this.$request.caiGouPerform.getInventoryOrderList({
                period: this.rowData.id,
            });
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.tableData.forEach((item) => {
                    this.$set(item, "nums", "");
                    this.$set(item, "action", "inc");
                });
            }
        },
        async confirm(row) {
            let data = {
                period_id: this.rowData.id,
                product_id: row.product_id,
                nums: parseInt(row.nums),
                action: row.action,
            };
            let res = await this.$request.article.updateInventory1(data);
            if (res.data.error_code == 0) {
                this.$message.success("操作成功！");
                this.getInventoryOrderList();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}
</style>
