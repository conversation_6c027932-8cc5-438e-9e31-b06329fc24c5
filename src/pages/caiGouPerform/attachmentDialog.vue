<template>
    <el-dialog
        :visible="visible"
        width="40%"
        append-to-body
        :before-close="closeDialog"
    >
    <div v-if="visible"> 
        <vos-oss
        list-type="text"
        :showFileList="true"
        :limit="999"
        :dir="dir"
        :file-list="fileList"
        filesType=""
        :is_download="true"
        :showName="true"
        :fileSize="10"
    >
        <el-button type="primary" size="default"
            >上传附件</el-button
        >
    </vos-oss>

    <span
        slot="footer"
        class="dialog-footer"
        style="display: flex; justify-content: flex-end"
    >
        
        <el-button type="primary" @click="saveClick"
            >保存</el-button
        >
    </span>
    </div>
   
    </el-dialog>
</template>

<script>

import vosOss from "vos-oss";
import { number } from 'echarts';
export default {
    // name:attachmentDialog,
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        attannex: {
            type: Array,
            default: () => [],
        },
        id: {
            type: Number,
            default: 0,
        },
    },
    components: {
        vosOss,
    },
  data(){
    return{
        fileList: [],
        dir: "vinehoo/goods-images/",
    }
  },
  watch: {
    fileList: {
    //   deep: true,
      handler(newVal, oldVal) {
        if(newVal.length){
            var oss_url;
        if (process.env.NODE_ENV == "development") {
                oss_url = "https://images.wineyun.com";
            } else {
                oss_url = "https://images.vinehoo.com";
            }
        newVal.forEach((v, i) => {
           
            if (!v.includes("http") ) {
                this.fileList[i]= oss_url+v;
            }
          });
          console.log(this.fileList, 99);
        }
       
      }
    }
  },
    created() {
        // this.fileList = this.attannex;
        // console.log('===-1', this.attannex)
        // console.log('===-', this.fileList)
        // 
        // this.fileList = this.annex.map(item => ({
        //     url:item,
        //     name:item.split("/").pop()
        // }));
      

    },
    methods: {
        closeDialog() {
            this.$emit("update:visible", false);
            // this.fileList=[];
        },
       
        showAddDialog(){
            this.addDialogVisible = true;
            this.inShortCode = "";
            this.addData = this.$options.data().addData;
        },
      
        
      saveClick(){
        //if(!this.fileList.length){
        //     this.$message.error("请先上传附件");
        //     return;
        // }
        this.$request.caiGouPerform
                .updateAnnex({
                    id:this.id,
                    annex: this.fileList,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        // const data = res.data.data;
                        this.$message.success("保存成功");
                        this.closeDialog();
                        this.$emit("reload");
                    }
                });
      },
    },
};
</script>

<style lang="scss" scoped></style>
