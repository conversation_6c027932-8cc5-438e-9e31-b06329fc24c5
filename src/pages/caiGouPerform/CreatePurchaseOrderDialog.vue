<template>
    <el-dialog
        :visible="visible"
        fullscreen
        append-to-body
        :before-close="closeDialog"
    >
        <template #title>
            <el-row type="flex" justify="end" style="padding-right: 50px">
                <el-button
                    type="primary"
                    v-if="query_type == 6"
                    @click="showAddDialog"
                    >新增</el-button
                >
                <el-button
                    type="primary"
                    v-if="query_type == 6 || query_type == 2"
                    @click="clickFujian"
                    >附件</el-button
                >
                <el-button
                    type="primary"
                    @click="purchaseOrderPrintDialogVisible = true"
                    >打印</el-button
                >
            </el-row>
        </template>
        <el-form inline>
            <el-form-item label="采购订单">
                <el-input v-model="model.orderno"></el-input>
            </el-form-item>
            <el-form-item label="单据日期">
                <el-date-picker
                    v-model="model.bill_date"
                    type="date"
                    placeholder="选择日期"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item label="入库仓库">
                <el-select
                    v-model="model.warehouse_code"
                    placeholder="入库仓库"
                    filterable
                    @change="onWarehouseCodeChange"
                >
                    <el-option
                        v-for="item in warehouseOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="供应商">
                <el-select
                    v-model="model.supplier_code"
                    filterable
                    remote
                    :remote-method="supplierRemoteMethod"
                    :loading="supplierRemoteMethodLoading"
                    @change="onSupplierCodeChange"
                >
                    <el-option
                        v-for="item in supplierOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="采购部门">
                <el-select
                    v-model="model.department_code"
                    filterable
                    remote
                    :remote-method="departmentRemoteMethod"
                    :loading="departmentRemoteMethodLoading"
                    @change="onDepartmentCodeChange"
                >
                    <el-option
                        v-for="item in departmentOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="采购员">
                <el-select
                    v-model="model.operator_code"
                    filterable
                    @change="onOperatorCodeChange"
                >
                    <el-option
                        v-for="item in employeesOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="付款方式">
                <el-select
                    v-model="model.setttlement_code"
                    filterable
                    placeholder="收款方式"
                    @change="onSetttlementCodeChange"
                >
                    <el-option
                        v-for="item in settlementMethodList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="备注">
                <el-input
                    v-model="model.remark"
                    type="textarea"
                    :autosize="{ minRows: 2 }"
                ></el-input>
            </el-form-item>
        </el-form>
        <el-table
            :data="items"
            border
            :row-class-name="tableRowClassName"
            :show-summary="true"
            :summary-method="getSummaries"
        >
            <el-table-column
                label="简码"
                prop="short_code"
                align="center"
                min-width="70"
            >
                <template slot-scope="scope">
                    <span
                        style="cursor: pointer; color: #409eff"
                        @click="showHistoryPrice(scope.row)"
                        >{{ scope.row.short_code }}</span
                    >
                </template>
            </el-table-column>
            <el-table-column
                label="开票名称"
                prop="billing_name"
                align="center"
            ></el-table-column>
            <el-table-column
                label="英文名"
                prop="en_product_name"
                align="center"
                min-width="130"
            ></el-table-column>
            <el-table-column
                label="单位"
                prop="unit"
                align="center"
                min-width="50"
            ></el-table-column>
            <el-table-column
                label="规格"
                prop="capacity"
                align="center"
                min-width="60"
            ></el-table-column>
            <el-table-column label="备注" prop="remark" align="center">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.remark"></el-input>
                </template>
            </el-table-column>
            <el-table-column
                label="期数"
                prop="period"
                align="center"
                min-width="60"
            ></el-table-column>
            <el-table-column
                label="数量"
                prop="number"
                align="center"
                min-width="60"
            >
                <template slot-scope="scope">
                    <el-input
                        v-model="scope.row.number"
                        @blur="onNumberInputBlur(scope.row)"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="含税单价" prop="price" align="center">
                <template slot-scope="scope">
                    <el-input
                        v-model="scope.row.price"
                        @blur="onPriceInputBlur(scope.row)"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column
                label="税率"
                prop="tax_rate"
                align="center"
                min-width="80"
            >
                <template slot-scope="scope">
                    <el-input v-model="scope.row.tax_rate"></el-input>
                </template>
            </el-table-column>
            <el-table-column label="价税合计" align="center" prop="total">
                <template slot-scope="scope">
                    <el-input
                        v-model="scope.row.total"
                        @blur="onTotalInputBlur(scope.row)"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button type="text" @click="onRemove(scope)"
                        >移除</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-row type="flex" justify="center" class="mgt-20">
            <template
                v-if="model.statusMap && model.statusMap.isUpdate !== undefined"
            >
                <el-button @click="closeDialog">取消</el-button>
                <el-button
                    v-if="model.statusMap.isUpdate && query_type == 6"
                    type="primary"
                    @click="onUpdateOrder(1)"
                    >下单</el-button
                >
                <el-button
                    v-if="query_type == 0 || query_type == 4"
                    type="primary"
                    @click="onUpdateOrder(0)"
                    >预下单</el-button
                >
            </template>
            <template v-else>
                <el-button
                    v-if="isShowSaveDraftBtn"
                    type="primary"
                    @click="onSave(0)"
                    >保存草稿</el-button
                >
                <el-button
                    v-if="isShowCreateOrderBtn"
                    type="primary"
                    @click="onSave(1)"
                    >下单</el-button
                >
            </template>
        </el-row>
        <el-tabs
            v-model="activeName"
            v-if="approvalList.length || approve.length"
        >
            <el-tab-pane label="中台审批" name="first">
                <el-row v-if="approve.length" style="mgt-20">
                    <el-col :span="18">
                        <div>审批状态：</div>
                        <el-table :data="approve" border class="mgt-10">
                            <el-table-column
                                label="审批类型"
                                prop="approve_type"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                label="发送人"
                                prop="sender"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                min-width="130"
                                label="发送时间"
                                prop="send_time"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                label="审批人"
                                prop="reviewer"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                label="审核日期"
                                min-width="130"
                                prop="reviewe_time"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                label="审批意见"
                                prop="status"
                                align="center"
                            >
                            </el-table-column>
                            <el-table-column
                                label="批语"
                                prop="comments"
                                align="center"
                            ></el-table-column>
                        </el-table>
                    </el-col>
                </el-row>
            </el-tab-pane>
            <el-tab-pane label="ERP审批" name="second">
                <el-row v-if="approvalList.length" style="mgt-20">
                    <el-col :span="18">
                        <div>审批状态：</div>
                        <el-table :data="approvalList" border class="mgt-10">
                            <el-table-column
                                label="发送人"
                                prop="senderman"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                label="发送时间"
                                prop="senddate"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                label="审批人"
                                prop="checkman"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                label="审核日期"
                                prop="dealdate"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                label="审批状况"
                                prop="duration"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                label="审批意见"
                                prop="approvestatus"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    {{
                                        scope.row.approvestatus
                                            | toApproveStatusText
                                    }}
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="批语"
                                prop="checknote"
                                align="center"
                            ></el-table-column>
                        </el-table>
                    </el-col>
                </el-row>
            </el-tab-pane>
        </el-tabs>

        <PurchaseOrderPrintDialog
            ref="purchaseOrderPrintDialogRef"
            :visible.sync="purchaseOrderPrintDialogVisible"
            :items="items"
            :modelData="model"
        ></PurchaseOrderPrintDialog>

        <attachmentDialog
            ref="attachmentDialogRef"
            :visible.sync="attachmentDialogShow"
            :attannex:="annex"
            :id="model.id"
            @reload="myload"
        ></attachmentDialog>
        <el-dialog :visible.sync="addDialogVisible" width="40%" append-to-body>
            <el-form
                ref="addData"
                :model="addData"
                label-width="100px"
                v-if="addDialogVisible"
                class="add_form"
            >
                <el-form-item label="简码:" prop="short_code">
                    <el-input
                        placeholder="请输入"
                        class="w-large"
                        v-model="addData.short_code"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="期数:" prop="period">
                    <el-input
                        placeholder="请输入"
                        class="w-large mgr-10"
                        v-model="addData.period"
                    >
                    </el-input>
                    <el-button type="primary" @click="searchClick"
                        >查询</el-button
                    >
                </el-form-item>
                <el-form-item label="开票名称:" prop="billing_name">
                    {{ addData.billing_name }}
                </el-form-item>
                <el-form-item label="英文名:" prop="en_product_name">
                    {{ addData.en_product_name }}
                </el-form-item>

                <el-form-item label="单位:" prop="unit">
                    {{ addData.unit }}
                </el-form-item>
                <el-form-item label="规格:" prop="capacity">
                    {{ addData.capacity }}
                </el-form-item>

                <el-form-item label="下单数量:" prop="number">
                    <el-input
                        v-model="addData.number"
                        type="number"
                        placeholder="请输入数量"
                        @blur="calculateTotal()"
                    />
                </el-form-item>
                <!-- <el-form-item label="含税单价:" prop="price">
                <el-input
                    v-model="addData.price"
                    type="number"
                    placeholder="请输入含税单价"
                    @blur="calculateTotal()"
                />
            </el-form-item> -->
                <el-form-item label="售卖数量:" prop="sale_nums">
                    {{ addData.sale_nums }}
                </el-form-item>
                <el-form-item label="已采数量:" prop="purchased_nums">
                    {{ addData.purchased_nums }}
                </el-form-item>
                <el-form-item label="未发货数量:" prop="unshipped_num">
                    {{ addData.unshipped_num }}
                </el-form-item>
                <el-form-item label-width="120px" label="萌牙库存/在途:">
                    {{ addData.my_inventory }}/{{ addData.transit_count }}
                </el-form-item>
                <!-- <el-form-item label="价税合计:" prop="total">
                <el-input
                    v-model="addData.total"
                    type="number"
                    placeholder="请输入价税合计"
                />
            </el-form-item> -->
            </el-form>
            <el-row type="flex" justify="center" class="mgt-20">
                <el-button @click="addDialogVisible = false">取消</el-button>
                <el-button
                    type="primary"
                    @click="saveAddData('addData')"
                    :disabled="canClick"
                    >确定</el-button
                >
            </el-row>
        </el-dialog>
        <el-dialog
            title="历史价格"
            :visible.sync="historyPriceDialogVisible"
            width="40%"
            append-to-body
        >
            <div class="history-price-content">
                <div class="history-price-header">
                    简码：{{ currentShortCode }}
                </div>
                <el-table :data="historyPriceList" border>
                    <el-table-column
                        label="订单号"
                        prop="orderno"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        label="供应商"
                        prop="supplier"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        label="价格"
                        prop="price"
                        align="center"
                    ></el-table-column>
                </el-table>
            </div>
        </el-dialog>
    </el-dialog>
</template>

<script>
import moment from "moment";
import PurchaseOrderPrintDialog from "./PurchaseOrderPrintDialog";
import purchaseExecManageApi from "@/services/purchaseExecManage";
import attachmentDialog from "./attachmentDialog";
// import { number } from 'echarts';

export default {
    // name:CreatePurchaseOrderDialog,
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        items: {
            type: Array,
            default: () => [],
        },
        approve: {
            type: Array,
            default: () => [],
        },
        annex: {
            type: Array,
            default: () => [],
        },
    },
    components: {
        PurchaseOrderPrintDialog,
        attachmentDialog,
    },
    data: () => ({
        rules: {
            short_code: [
                {
                    required: true,
                    message: "请输入简码",
                    trigger: "blur",
                },
            ],
            period: [
                {
                    required: true,
                    message: "请输入期数",
                    trigger: "blur",
                },
            ],
            number: [
                {
                    required: true,
                    message: "请输入数量",
                    trigger: "blur",
                },
            ],
        },
        addDialogVisible: false,
        warehouseOptions: [],
        supplierOptions: [],
        supplierRemoteMethodLoading: false,
        departmentOptions: [],
        departmentRemoteMethodLoading: false,
        attachmentDialogShow: false,
        employeesOptions: [],
        settlementMethodList: [
            { value: "01", label: "分期付款" },
            { value: "02", label: "月结" },
            { value: "03", label: "其他" },
        ],
        canClick: true,
        model: {
            period: "",
            orderno: "",
            bill_date: "",
            warehouse: "",
            warehouse_code: "",
            supplier: "",
            supplier_code: "",
            department: "",
            department_code: "",
            operator_name: "",
            operator_code: "",
            setttlement: "",
            setttlement_code: "",
            remark: "",
            payee_merchant_id: "",
            payee_merchant_name: "",
        },
        addData: {
            id: 0,
            short_code: "",
            billing_name: "",
            en_product_name: "",
            unit: "",
            capacity: "",
            remark: "",
            period: "",
            number: "",
            price: "",
            tax_rate: "",
            total: "",
            sale_nums: 0,
            purchased_nums: 0,
            unshipped_num: 0,
            my_inventory: 0,
            transit_count: 0,
            years: "",
        },
        query_type: 0,
        approvalList: [],
        purchaseOrderPrintDialogVisible: false,

        activeName: "first",
        fileList: [],
        historyPriceDialogVisible: false,
        historyPriceList: [],
        currentShortCode: "",
    }),
    computed: {
        isShowSaveDraftBtn({ model }) {
            return !model.statusMap || model.statusMap.operate_status === 0;
        },
        isShowCreateOrderBtn({ model }) {
            return (
                !model.statusMap ||
                model.statusMap.operate_status === 0 ||
                (model.statusMap.operate_status === 1 &&
                    [0, 1, 4].includes(model.statusMap.status))
            );
        },
    },

    watch: {
        visible(newVal) {
            if (newVal) {
                if (!this.model.id) {
                    this.$request.caiGouPerform.getPoNo().then((res) => {
                        if (res.data.error_code === 0) {
                            this.model = Object.assign(this.model, {
                                orderno: res.data.data.no,
                                bill_date: moment().format("YYYY-MM-DD"),
                            });
                        }
                    });
                }
                this.initWarhouseOptions();
                this.initEmployeesOptions();
                this.query_type = this.$parent.$data.query.query_type;
                console.log("dddd", this.$parent.$data.query.query_type);
            }
        },
        "addData.price": function (newVal, oldValue) {
            // this.total = number(this.addData.number) *this.addData.tax_rate*number(newVal);
            console.log(newVal, oldValue);
        },
        "addData.number": function (newVal, oldValue) {
            // this.total = number(this.addData.price)*this.addData.tax_rate*number(newVal);
            console.log(newVal, oldValue);
        },
    },
    filters: {
        toApproveStatusText(input) {
            return (
                {
                    0: "未处理",
                    1: "批准",
                    4: "驳回",
                }[input] || ""
            );
        },
    },
    mounted() {
        console.log("---++++00l", this.model);
    },
    methods: {
        closeDialog() {
            this.$emit("update:visible", false);
        },
        saveAddData(formName) {
            if (!this.addData.number.length) {
                this.$message.error("请输入下单数量");
                return;
            }
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.items.push(this.addData);
                    this.addDialogVisible = false;
                } else {
                    return false;
                }
            });
        },
        tableRowClassName({ row }) {
            if (row.period_info) {
                if (row.period_info[0].is_yesterday) {
                    return "success-row";
                }
            }

            return "";
        },
        showAddDialog() {
            this.addDialogVisible = true;
            // this.inShortCode = "";
            this.addData = this.$options.data().addData;
        },
        searchClick() {
            if (!this.addData.short_code.length) {
                this.$message.error("请输入简码");
                return;
            }
            if (!this.addData.period.length) {
                this.$message.error("请输入期数");
                return;
            }

            purchaseExecManageApi
                .searchProductList({
                    short_code: this.addData.short_code,
                    period: this.addData.period,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        const dict = res.data.data;
                        // const dict = res.data.data.list[0];
                        this.addData = this.$options.data().addData;
                        if (!res.data.data) {
                            this.$message.error("暂无数据");
                        }
                        res.data.data
                            ? (this.canClick = false)
                            : (this.canClick = true);
                        console.log("dict", dict);
                        this.addData.billing_name = dict.cn_product_name;
                        this.addData.short_code = dict.short_code;
                        this.addData.years = dict.years;
                        this.addData.en_product_name = dict.en_product_name;
                        this.addData.unit = dict.unit;
                        this.addData.capacity = dict.capacity;
                        this.addData.tax_rate = dict.tax_rate;
                        this.addData.price = dict.price;
                        this.addData.period = dict.period;
                        this.addData.sale_nums = dict.sale_nums;
                        this.addData.purchased_nums = dict.purchased_nums;
                        this.addData.unshipped_num = dict.unshipped_num;
                        this.addData.my_inventory = dict.my_inventory;
                        this.addData.transit_count = dict.transit_count;
                        this.addData.period_info = [
                            { is_yesterday: dict.is_yesterday },
                        ];
                        this.addData.remark = dict.remark;
                        // this.addData.number = '0';
                    }
                });
        },
        initWarhouseOptions() {
            this.$request.article.warehouseList().then((res) => {
                if (res.data.error_code === 0) {
                    this.warehouseOptions = res.data.data.map((item) => ({
                        value: item.erp_id,
                        label: item.fictitious_name,
                    }));
                }
            });
        },
        calculateTotal() {
            this.addData.total = +this.addData.price * +this.addData.number;
            console.log();
        },

        onWarehouseCodeChange() {
            this.model.warehouse =
                this.warehouseOptions.find(
                    (item) => item.value === this.model.warehouse_code
                )?.label || "";
        },
        myload() {
            this.$emit("reload");
            this.fileList = this.$refs.attachmentDialogRef.fileList;
        },
        clickFujian() {
            this.attachmentDialogShow = true;
            this.$refs.attachmentDialogRef.fileList = Array.from(this.fileList);
        },
        supplierRemoteMethod(query) {
            if (query !== "") {
                this.supplierRemoteMethodLoading = true;
                this.$request.caiGouPerform
                    .getPartnerentityList({ page: 1, limit: 10, name: query })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.supplierOptions = res.data.data.list.map(
                                (item) => ({
                                    value: item.code,
                                    label: item.name,
                                })
                            );
                            this.supplierRemoteMethodLoading = false;
                        }
                    });
            } else {
                this.supplierOptions = [];
            }
        },
        onSupplierCodeChange() {
            this.model.supplier =
                this.supplierOptions.find(
                    (item) => item.value === this.model.supplier_code
                )?.label || "";
        },
        departmentRemoteMethod(query) {
            if (query !== "") {
                this.departmentRemoteMethodLoading = true;
                this.$request.caiGouPerform
                    .getDepartmentList({ page: 1, limit: 10, name: query })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.departmentOptions = res.data.data.list.map(
                                (item) => ({
                                    value: item.dept_code,
                                    label: item.name,
                                })
                            );
                            this.departmentRemoteMethodLoading = false;
                        }
                    });
            } else {
                this.departmentOptions = [];
            }
        },
        onDepartmentCodeChange() {
            this.model.department =
                this.departmentOptions.find(
                    (item) => item.value === this.model.department_code
                )?.label || "";
        },
        initEmployeesOptions() {
            this.$request.caiGouPerform
                .getEmployeesList({ page: 1, limit: 9999 })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.employeesOptions = Object.freeze(
                            res.data.data.list.map((item) => ({
                                value: item.staff_code,
                                label: item.realname,
                            }))
                        );
                    }
                });
        },
        onOperatorCodeChange() {
            this.model.operator_name =
                this.employeesOptions.find(
                    (item) => item.value === this.model.operator_code
                )?.label || "";
        },
        onSetttlementCodeChange() {
            this.model.setttlement =
                this.settlementMethodList.find(
                    (item) => item.value === this.model.setttlement_code
                )?.label || "";
        },
        async onSave(operate_status) {
            this.onWarehouseCodeChange();
            this.onSupplierCodeChange();
            this.onDepartmentCodeChange();
            this.onOperatorCodeChange();
            const list = this.items.filter(
                (item) => !item.tax_rate || item.tax_rate === "0.0000"
            );
            if (operate_status && list.length) {
                const confirm = () => {
                    return new Promise((resolve) => {
                        this.$confirm(
                            "有产品税率为0，是否确认继续提交",
                            "提示",
                            {
                                confirmButtonText: "确认",
                                cancelButtonText: "取消",
                                type: "warning",
                                dangerouslyUseHTMLString: true,
                            }
                        )
                            .then(() => {
                                resolve();
                            })
                            .catch(() => {});
                    });
                };
                await confirm();
            }
            this.$request.caiGouPerform
                .createPurchaseOrder({
                    ...this.model,
                    operate_status,
                    items: this.items,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.$emit("reload");
                        this.closeDialog();
                    }
                });
        },
        loadApproveList(corp_code) {
            this.$request.caiGouPerform
                .queryPurchaseOrderApproveList({
                    corp_code,
                    bill_code: this.model.orderno,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        const data = res.data.data;
                        this.approvalList = Array.isArray(data)
                            ? Object.freeze(data)
                            : [];
                    }
                });
        },
        onNumberInputBlur(row) {
            this.totalComputedFun(row);
        },
        onPriceInputBlur(row) {
            this.totalComputedFun(row);
        },
        totalComputedFun(row) {
            row.total = parseFloat((row.number * row.price).toFixed(8));
        },
        onTotalInputBlur(row) {
            row.price = parseFloat((row.total / row.number).toFixed(8));
        },
        onRemove(scope) {
            this.items.splice(scope.$index, 1);
        },
        async onUpdateOrder(typeStatus) {
            this.onWarehouseCodeChange();
            this.onSupplierCodeChange();
            this.onDepartmentCodeChange();
            this.onOperatorCodeChange();
            const list = this.items.filter(
                (item) => !item.tax_rate || item.tax_rate === "0.0000"
            );
            if (list.length) {
                const confirm = () => {
                    return new Promise((resolve) => {
                        this.$confirm(
                            "有产品税率为0，是否确认继续提交",
                            "提示",
                            {
                                confirmButtonText: "确认",
                                cancelButtonText: "取消",
                                type: "warning",
                                dangerouslyUseHTMLString: true,
                            }
                        )
                            .then(() => {
                                resolve();
                            })
                            .catch(() => {});
                    });
                };
                await confirm();
            }
            console.log("items", this.items);
            this.$delete(this.model, "payee_merchant_id");
            this.$delete(this.model, "payee_merchant_name");
            purchaseExecManageApi
                .updatePurchaseOrder({
                    ...this.model,
                    operate_status: typeStatus,
                    items: this.items,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.$emit("reload");
                        this.closeDialog();
                    }
                });
        },
        showHistoryPrice(row) {
            this.currentShortCode = row.short_code;
            this.$request.caiGouPerform
                .getHistoryPrice({
                    short_code: row.short_code,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.historyPriceList = res.data.data.list;
                        this.historyPriceDialogVisible = true;
                    }
                });
        },
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = "合计";
                    return;
                }

                // 数量列合计（第8列，索引为7）
                if (column.property === "number") {
                    const values = data.map((item) => Number(item.number) || 0);
                    const sum = values.reduce((prev, curr) => {
                        const value = Number(curr);
                        if (!isNaN(value)) {
                            return prev + value;
                        } else {
                            return prev;
                        }
                    }, 0);
                    sums[index] = sum.toFixed(2);
                    return;
                }

                // 价税合计列合计（第10列）
                if (column.property === "total") {
                    const values = data.map((item) => Number(item.total) || 0);
                    const sum = values.reduce((prev, curr) => {
                        const value = Number(curr);
                        if (!isNaN(value)) {
                            return prev + value;
                        } else {
                            return prev;
                        }
                    }, 0);
                    sums[index] = sum.toFixed(2);
                    return;
                }

                // 其他列不显示合计
                sums[index] = "";
            });

            return sums;
        },
    },
};
</script>
<!-- <style >
 .el-table .success-row {
    background: #ffa5a0;
    /* color:#fff; */
  }
</style> -->
<style lang="scss" scoped>
.el-table .success-row {
    background: #ffa5a0;
    /* color:#fff; */
}
.add_form {
    .el-form-item {
        margin-bottom: 5px;
    }
}
.history-price-content {
    .history-price-header {
        margin-bottom: 15px;
        font-size: 14px;
        font-weight: bold;
    }
}

// 合计行样式
/deep/ .el-table__footer-wrapper .el-table__footer {
    .el-table__row {
        background-color: #f5f7fa;
        font-weight: bold;

        .cell {
            color: #303133;
            font-size: 14px;
        }
    }
}

// 合计行悬停效果
/deep/ .el-table__footer-wrapper .el-table__footer .el-table__row:hover {
    background-color: #f0f2f5 !important;
}
</style>
