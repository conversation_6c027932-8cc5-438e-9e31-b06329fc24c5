<template>
    <div>
        <!-- 搜索条件 -->
        <el-card :body-style="{ padding: '10px' }" shadow="hover">
            <el-form :inline="true" size="mini" :model="queryForm">
                <el-form-item label="开始时间:">
                    <el-date-picker
                        v-model="queryForm.start_time"
                        type="date"
                        placeholder="选择开始时间"
                        value-format="yyyy-MM-dd"
                        size="mini"
                        :clearable="false"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="结束时间:">
                    <el-date-picker
                        v-model="queryForm.end_time"
                        type="date"
                        placeholder="选择结束时间"
                        value-format="yyyy-MM-dd"
                        size="mini"
                        :clearable="false"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button
                        size="mini"
                        @click="getStatistics"
                        type="primary"
                        :loading="loading"
                    >
                        查询
                    </el-button>
                    <!-- <el-button
                        size="mini"
                        @click="exportData"
                        type="success"
                        :disabled="!statisticsList.length"
                    >
                        导出
                    </el-button> -->
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 统计表格 -->
        <el-card
            :body-style="{ padding: '10px' }"
            shadow="hover"
            style="margin-top: 10px"
        >
            <div class="card-title">文案数据统计</div>

            <el-table
                :data="tableData"
                border
                size="mini"
                :header-cell-style="{
                    'text-align': 'center',
                    'background-color': '#f5f7fa',
                }"
                :cell-style="getTableCellStyle"
                v-loading="loading"
                element-loading-text="数据加载中..."
                :row-class-name="getRowClassName"
            >
                <el-table-column
                    prop="operator_name"
                    label="采购"
                    width="120"
                    show-overflow-tooltip
                >
                </el-table-column>

                <el-table-column
                    prop="total_submit"
                    label="提交文案总数"
                    width="120"
                    sortable
                >
                    <template slot-scope="{ row }">
                        <span :class="getNumberClass(row)">{{
                            row.total_submit
                        }}</span>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="onsale_nums"
                    label="已上架"
                    width="100"
                    sortable
                >
                    <template slot-scope="{ row }">
                        <span :class="getNumberClass(row, 'success')">{{
                            row.onsale_nums
                        }}</span>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="not_onsale_nums"
                    label="未上架"
                    width="100"
                    sortable
                >
                    <template slot-scope="{ row }">
                        <span :class="getNumberClass(row, 'warning')">{{
                            row.not_onsale_nums
                        }}</span>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="not_onsale_ratio"
                    label="未上架占比"
                    width="120"
                    sortable
                >
                    <template slot-scope="{ row }">
                        <span
                            :class="
                                getRatioClass(
                                    row.not_onsale_ratio,
                                    row.operator_name === '合计'
                                )
                            "
                        >
                            {{ row.not_onsale_ratio }}%
                        </span>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 空数据提示 -->
            <div
                v-if="!loading && statisticsList.length === 0"
                class="empty-data"
            >
                <el-empty description="暂无数据"></el-empty>
            </div>
        </el-card>
    </div>
</template>

<script>
export default {
    name: "CopywritingDataStatistics",
    data() {
        // 获取当前日期
        const today = new Date();
        const endTime = this.formatDate(today);

        // 获取一个月前的日期
        const oneMonthAgo = new Date(today);
        oneMonthAgo.setMonth(today.getMonth() - 1);
        const startTime = this.formatDate(oneMonthAgo);

        return {
            loading: false,
            queryForm: {
                start_time: startTime,
                end_time: endTime,
            },
            statisticsList: [],
            summaryData: {
                operator_name: "合计",
                total_submit: 0,
                onsale_nums: 0,
                not_onsale_nums: 0,
                not_onsale_ratio: 0,
            },
        };
    },
    computed: {
        // 合并数据列表和合计行
        tableData() {
            if (this.statisticsList.length === 0) {
                return [];
            }
            return [...this.statisticsList, this.summaryData];
        },
    },
    mounted() {
        // 页面加载时自动查询数据
        this.getStatistics();
    },
    methods: {
        // 格式化日期为 yyyy-MM-dd 格式
        formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, "0");
            const day = String(date.getDate()).padStart(2, "0");
            return `${year}-${month}-${day}`;
        },

        // 获取统计数据
        async getStatistics() {
            if (!this.queryForm.start_time || !this.queryForm.end_time) {
                this.$message.warning("请选择开始时间和结束时间");
                return;
            }

            if (this.queryForm.start_time > this.queryForm.end_time) {
                this.$message.warning("开始时间不能大于结束时间");
                return;
            }

            this.loading = true;
            try {
                const params = {
                    start_time: this.queryForm.start_time,
                    end_time: this.queryForm.end_time,
                };

                const response =
                    await this.$request.article.getSopywritingDataStatistics(
                        params
                    );

                if (response.data.error_code === 0) {
                    this.statisticsList = response.data.data.list || [];
                    this.calculateSummary();
                    this.$message.success("数据查询成功");
                } else {
                    this.$message.error(response.data.error_msg || "查询失败");
                    this.statisticsList = [];
                }
            } catch (error) {
                console.error("查询文案数据统计失败:", error);
                this.$message.error("查询失败，请稍后重试");
                this.statisticsList = [];
            } finally {
                this.loading = false;
            }
        },

        // 计算合计数据
        calculateSummary() {
            if (this.statisticsList.length === 0) {
                this.summaryData = {
                    operator_name: "合计",
                    total_submit: 0,
                    onsale_nums: 0,
                    not_onsale_nums: 0,
                    not_onsale_ratio: 0,
                };
                return;
            }

            const totalSubmit = this.statisticsList.reduce(
                (sum, item) => sum + (item.total_submit || 0),
                0
            );
            const totalOnsale = this.statisticsList.reduce(
                (sum, item) => sum + (item.onsale_nums || 0),
                0
            );
            const totalNotOnsale = this.statisticsList.reduce(
                (sum, item) => sum + (item.not_onsale_nums || 0),
                0
            );

            // 计算总的未上架占比
            const totalRatio =
                totalSubmit > 0
                    ? ((totalNotOnsale / totalSubmit) * 100).toFixed(2)
                    : 0;

            this.summaryData = {
                operator_name: "合计",
                total_submit: totalSubmit,
                onsale_nums: totalOnsale,
                not_onsale_nums: totalNotOnsale,
                not_onsale_ratio: parseFloat(totalRatio),
            };
        },

        // 根据占比返回样式类名
        getRatioClass(ratio, isSummary = false) {
            let baseClass = "ratio-text";
            if (isSummary) {
                baseClass += " summary-ratio";
            }

            if (ratio >= 80) {
                return baseClass + " high-ratio";
            } else if (ratio >= 50) {
                return baseClass + " medium-ratio";
            } else {
                return baseClass + " low-ratio";
            }
        },

        // 获取数字样式类名
        getNumberClass(row, type = "") {
            let baseClass = "number-text";
            if (row.operator_name === "合计") {
                baseClass += " summary-number";
            }
            if (type) {
                baseClass += " " + type;
            }
            return baseClass;
        },

        // 获取表格行样式类名
        getRowClassName({ row }) {
            if (row.operator_name === "合计") {
                return "summary-row-style";
            }
            return "";
        },

        // 获取表格单元格样式
        getTableCellStyle({ row }) {
            let style = { "text-align": "center" };
            if (row.operator_name === "合计") {
                style["font-weight"] = "bold";
                style["background-color"] = "#f0f9ff";
            }
            return style;
        },

        // 导出数据
        exportData() {
            if (this.statisticsList.length === 0) {
                this.$message.warning("暂无数据可导出");
                return;
            }

            try {
                // 准备导出数据
                const exportData = [...this.statisticsList, this.summaryData];

                // 创建CSV内容
                const headers = [
                    "采购",
                    "提交文案总数",
                    "已上架",
                    "未上架",
                    "占比(%)",
                ];
                const csvContent = [
                    headers.join(","),
                    ...exportData.map((row) =>
                        [
                            row.operator_name,
                            row.total_submit,
                            row.onsale_nums,
                            row.not_onsale_nums,
                            row.not_onsale_ratio,
                        ].join(",")
                    ),
                ].join("\n");

                // 创建下载链接
                const blob = new Blob(["\ufeff" + csvContent], {
                    type: "text/csv;charset=utf-8;",
                });
                const link = document.createElement("a");
                const url = URL.createObjectURL(blob);
                link.setAttribute("href", url);
                link.setAttribute(
                    "download",
                    `文案数据统计_${this.queryForm.start_time}_${this.queryForm.end_time}.csv`
                );
                link.style.visibility = "hidden";
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                this.$message.success("导出成功");
            } catch (error) {
                console.error("导出失败:", error);
                this.$message.error("导出失败，请稍后重试");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.card-title {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 15px;
    color: #303133;
}

.number-text {
    font-weight: 500;

    &.success {
        color: #67c23a;
    }

    &.warning {
        color: #e6a23c;
    }
}

.ratio-text {
    font-weight: 600;

    &.low-ratio {
        color: #67c23a; // 绿色 - 低占比（好）
    }

    &.medium-ratio {
        color: #e6a23c; // 橙色 - 中等占比
    }

    &.high-ratio {
        color: #f56c6c; // 红色 - 高占比（需要关注）
    }
}

// 合计行样式
.summary-row-style {
    background-color: #f0f9ff !important;
}

.summary-number {
    font-size: 14px;
    font-weight: bold;
    color: #409eff;
}

.summary-ratio {
    font-size: 14px;
    font-weight: bold;
    color: #303133;
}

.empty-data {
    padding: 40px 0;
    text-align: center;
}

// 表格样式优化
/deep/ .el-table {
    .el-table__header-wrapper {
        th {
            background-color: #f5f7fa !important;
            color: #606266;
            font-weight: 600;
        }
    }

    .el-table__body-wrapper {
        tr:hover {
            background-color: #f5f7fa;
        }
    }
}

// 按钮样式
.el-button {
    margin-left: 8px;

    &:first-child {
        margin-left: 0;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .el-form--inline .el-form-item {
        display: block;
        margin-bottom: 10px;
    }

    .el-table {
        font-size: 12px;
    }

    .card-title {
        font-size: 14px;
    }
}
</style>
