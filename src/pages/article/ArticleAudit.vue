<template>
    <div class="article-layout">
        <el-card>
            <div class="article-form">
                <div>
                    <el-input
                        v-model="query.periods"
                        class="w-mini m-r-10"
                        placeholder="期数"
                        size="mini"
                        @keyup.enter.native="search"
                        clearable
                    >
                    </el-input>
                </div>
                <div>
                    <el-input
                        size="mini"
                        class="w-mini m-r-10"
                        v-model="query.short_code"
                        placeholder="简码"
                        @keyup.enter.native="search"
                        clearable
                    >
                    </el-input>
                </div>
                <div>
                    <el-input
                        v-model="query.title"
                        class="w-normal m-r-10"
                        placeholder="标题"
                        clearable
                        size="mini"
                        @keyup.enter.native="search"
                    >
                    </el-input>
                </div>
                <div>
                    <el-select
                        class="w-mini m-r-10"
                        v-model="query.creator_id"
                        filterable
                        size="mini"
                        placeholder="文案制作人"
                        clearable
                    >
                        <el-option
                            v-for="item in options"
                            :key="item.id"
                            :label="item.realname"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-select
                        class="w-mini m-r-10"
                        v-model="query.buyer_id"
                        filterable
                        size="mini"
                        placeholder="采购人"
                        clearable
                    >
                        <el-option
                            v-for="item in buyerOptions"
                            :key="item.id"
                            :label="item.realname"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-select
                        class="w-normal m-r-10"
                        v-model="query.buyer_review_status"
                        filterable
                        size="mini"
                        placeholder="审核状态"
                        :clearable="false"
                    >
                        <el-option
                            v-for="item in statusOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-select
                        class="w-mini m-r-10"
                        v-model="query.onsale_status"
                        filterable
                        size="mini"
                        clearable
                        placeholder="上架状态"
                    >
                        <el-option
                            v-for="item in onsale_statusOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>

                <div class="form-actions">
                    <el-button size="mini" type="warning" @click="search"
                        >查询</el-button
                    >
                    <!-- <el-button size="mini" type="success" @click="dialogVisible= true">新增</el-button> -->
                </div>
            </div>
        </el-card>
        <div class="article-main">
            <el-card>
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                    :row-class-name="tableRowClassName"
                >
                    <el-table-column
                        prop="id"
                        label="期数"
                        width="70"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="id"
                        show-overflow-tooltip
                        label="简码"
                        width="120"
                        align="center"
                    >
                        <template slot-scope="row">
                            {{ formatShortCode(row.row.short_code) }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="period"
                        label="频道"
                        width="70"
                        align="center"
                    >
                        <template slot-scope="{ row }">
                            {{ periods_typeTxt[row.periods_type] }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="title"
                        label="标题"
                        min-width="240"
                        show-overflow-tooltip
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="creator_name"
                        label="文案"
                        width="80"
                        align="center"
                    />
                    <el-table-column
                        prop="buyer_name"
                        label="采购"
                        width="80"
                        align="center"
                    />

                    <el-table-column
                        prop="address"
                        align="center"
                        label="审核状态"
                        width="110"
                    >
                        <template slot-scope="{ row }">
                            <span
                                slot="reference"
                                style="color: #409eff; cursor: pointer"
                                @click="handleStatusClick(row)"
                                >{{ statusTxt[row.buyer_review_status] }}</span
                            >
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="onsale_status"
                        label="上架状态"
                        width="90"
                        align="center"
                    >
                        <template slot-scope="row">
                            {{ onsale_statusFormat(row.row.onsale_status) }}
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="created_time"
                        label="创建时间"
                        width="150"
                        align="center"
                    />
                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        width="160"
                        align="center"
                    >
                        <template slot-scope="{ row }">
                            <el-button
                                type="primary"
                                size="mini"
                                v-if="row.buyer_review_status == 2"
                                @click="HandleAudit(row, 3, '')"
                                >审核</el-button
                            >

                            <el-button
                                size="mini"
                                type="primary"
                                @click="handleClick(row)"
                            >
                                <!-- v-if="
                                    row.buyer_review_status == 4 ||
                                    row.copywriting_review_status == 3 ||
                                    row.copywriting_review_status == 0
                                " -->
                                编辑
                            </el-button>
                            <!-- <el-button type="danger" size="mini" @click="HandleReject(row)" style="margin-left: 8px;">
                                驳回
                            </el-button> -->
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <el-dialog
            title="驳回信息"
            :visible.sync="dialogVisible"
            width="30%"
            :before-close="closeAuditDialog"
            destroy-on-close
        >
            <el-form
                v-if="dialogVisible"
                label-width="100px"
                ref="form"
                :model="entity"
                :rules="rules"
            >
                <el-form-item label="驳回理由" prop="reason">
                    <el-select
                        class="w-mini m-r-10"
                        v-model="entity.reason"
                        filterable
                        size="mini"
                        placeholder="状态"
                    >
                        <el-option
                            v-for="item in reasonOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-col :span="18">
                        <el-input
                            v-model="entity.remark"
                            type="textarea"
                            :rows="4"
                            placeholder="请填写驳回原因"
                        />
                    </el-col>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeAuditDialog">取 消</el-button>
                <el-button type="primary" @click="rejects()">确 定</el-button>
            </span>
        </el-dialog>
        <el-dialog
            :visible.sync="createArticleVisible"
            title="文案信息"
            :before-close="closeGoodsOpDialog"
            custom-class="dialogwid"
            fullscreen
            destroy-on-close
        >
            <createArticle
                ref="articleform"
                @closeGoodsOpDialog="closeGoodsOpDialog"
                v-if="createArticleVisible"
                :parentObj="this"
            ></createArticle>
        </el-dialog>
        <el-dialog
            :visible.sync="GoodsDetailVisible"
            title="商品详情浏览"
            :close-on-click-modal="false"
            custom-class="dialogwid"
            width="50%"
            destroy-on-close
        >
            <!-- <iframe v-if="GoodsDetailVisible" :src="ifreameurl" frameborder="0"
                style="width: 100%;height: 800px;"></iframe> -->
            <div>
                <goodsdetail
                    v-if="GoodsDetailVisible"
                    ref="goodsdetail"
                    :goodsinfo="goodsinfo"
                ></goodsdetail>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button
                    type="primary"
                    @click="passBtnClick(6)"
                    :disabled="auditTime > 0"
                >
                    时间紧迫而通过{{ auditTime > 0 ? auditTime : "" }}
                </el-button>
                <el-button
                    type="primary"
                    @click="passBtnClick(3)"
                    :disabled="auditTime > 0"
                >
                    勉强通过{{ auditTime > 0 ? auditTime : "" }}
                </el-button>
                <el-button
                    type="success"
                    @click="passBtnClick(5)"
                    :disabled="auditTime > 0"
                >
                    加分通过{{ auditTime > 0 ? auditTime : "" }}
                </el-button>
                <el-button type="primary" @click="HandleReject(rows)"
                    >驳回</el-button
                >
            </div>
        </el-dialog>
        <el-dialog
            :visible.sync="dialogVisible1"
            :close-on-click-modal="false"
            title="商品信息"
            custom-class="dialogwid"
            destroy-on-close
            :before-close="closeDialogVisible1Dialog"
            width="50%"
        >
            <div v-if="dialogVisible1">
                <div>
                    <div
                        v-for="(v, i) in goodsinfo.product_list"
                        :key="i"
                        style="
                            display: flex;
                            flex-wrap: wrap;
                            margin-bottom: 22px;
                        "
                    >
                        <div>{{ v.short_code }}</div>
                        <div style="margin-left: 15px">
                            {{ v.en_product_name }}
                        </div>
                        <div style="margin-left: 15px; color: red">
                            {{
                                v.corp
                                    .split(",")
                                    .map((corp) => corpToName[corp] || "未知")
                                    .join()
                            }}
                        </div>
                    </div>
                </div>
                <el-form
                    label-width="100px"
                    ref="form"
                    :model="goodsData"
                    :rules="rules"
                >
                    <el-form-item label="进口类型" prop="import_type">
                        <el-select
                            :value="goodsData.import_type"
                            size="small"
                            filterable
                            placeholder="进口类型"
                            @change="onImportTypeChange"
                        >
                            <el-option
                                v-for="(item, index) in entranceOptions"
                                :key="index"
                                :disabled="
                                    (item.value == 2 &&
                                        rows.periods_type != 2) ||
                                    (item.value != 2 && rows.periods_type == 2)
                                "
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="供应商" prop="suppliers">
                        <!--   <el-input v-model="goodsData.supplier" style="width: 120px;" placeholder="请输入供应商" size="mini">
                        </el-input> -->
                        <!--  <el-select  v-model="goodsData.suppliers" filterable size="mini" value-key="id"
                            placeholder="请选择供应商">
                            <el-option v-for="item in supplierOpration" :key="item.id" :label="item.supplier_name"
                                :value="item">
                            </el-option>
                        </el-select> -->
                        <el-select
                            :value="goodsData.suppliers"
                            @input="onSuppliersInput"
                            filterable
                            remote
                            reserve-keyword
                            placeholder="请输入供应商"
                            :remote-method="remoteMethod"
                            size="mini"
                            value-key="id"
                            :loading="loading"
                            :disabled="goodsData.import_type === ''"
                        >
                            <el-option
                                v-for="item in supplierOpration"
                                :key="item.id"
                                :label="item.supplier_name"
                                :value="item"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="收款账户">
                        {{ goodsData.payee_merchant_name || "无" }}
                    </el-form-item>
                    <el-form-item
                        label="是否预售"
                        v-if="rows.periods_type != 3 && rows.periods_type != 1"
                    >
                        <el-radio-group v-model="goodsData.is_presell">
                            <el-radio
                                v-for="(item, key) in operations"
                                :key="key"
                                :label="item.label"
                                >{{ item.value }}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item
                        label="是否代发"
                        v-if="rows.periods_type != 3 && rows.periods_type != 2"
                    >
                        <el-radio-group
                            v-model="goodsData.is_supplier_delivery"
                        >
                            <el-radio
                                v-for="(item, key) in operations"
                                :key="key"
                                :label="item.label"
                                >{{ item.value }}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item
                        v-if="goodsData.is_supplier_delivery === 1"
                        label="发货地"
                        prop="supplier_delivery_address"
                    >
                        <el-select
                            :value="
                                goodsData.supplier_delivery_address
                                    .split(',')
                                    .filter(Boolean)
                            "
                            @change="
                                goodsData.supplier_delivery_address =
                                    $event.join()
                            "
                            multiple
                            filterable
                        >
                            <el-option
                                v-for="item in regionList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.name"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button
                    type="primary"
                    @click="audit(rows, pendingReviewStatus, '')"
                >
                    确认
                </el-button>
            </div>
        </el-dialog>
        <remarkList ref="remarklist"></remarkList>
        <logList ref="logList"></logList>
        <el-dialog
            :visible.sync="corpSelectDialogVisible"
            title="选择收款商户"
            width="30%"
            append-to-body
            :close-on-click-modal="false"
        >
            <el-radio-group v-model="corpName">
                <div v-for="value in corpsObj" :key="value">
                    <el-radio :label="value">{{ value }}</el-radio>
                </div>
            </el-radio-group>
            <div slot="footer" class="dialog-footer">
                <el-button
                    type="primary"
                    :disabled="!corpName"
                    @click="onCorpNameConfirm"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>
<script>
import goodsdetail from "./GoodsDetail.vue";
import remarkList from "./remarkList.vue";
import logList from "../goods/components/LogList.vue";
import createArticle from "./CreateArticle.vue";
export default {
    components: {
        goodsdetail,
        createArticle,
        remarkList,
        logList,
    },
    data() {
        return {
            createArticleVisible: false,
            entranceOptions: [
                {
                    label: "自进口",
                    value: 0,
                },
                {
                    label: "地采",
                    value: 1,
                },
                { label: "跨境", value: 2 },
            ],
            dialogVisible: false,
            dialogVisible1: false,
            onsale_statusOptions: [
                {
                    label: "待上架",
                    value: 0,
                },
                {
                    label: "待售中",
                    value: 1,
                },
                {
                    label: "在售中",
                    value: 2,
                },
                {
                    label: "已下架",
                    value: 3,
                },
            ],
            GoodsDetailVisible: false,
            query: {
                page: 1,
                onsale_status: "",
                limit: 10,
                review: 1,
                short_code: "",
                buyer_id: "",
                buyer_review_status: 2,
                copywriting_review_status: 2,
                periods_type: "",
                periods: "",
                title: "",
            },
            buyerOptions: [],
            goodsinfo: {
                periods_type: 0,
            },
            supplierOpration: [],
            ifreameurl: "",
            tableData: [],
            entity: {
                reason: "",
                remark: "",
            },
            goodsData: {
                period: "",
                supplier: "",
                is_supplier_delivery: 0,
                is_presell: 0,
                import_type: "",
                reject_reason: "",
                supplier_delivery_address: "",
            },
            auditTime: 30,
            operations: [
                {
                    value: "是",
                    label: 1,
                },
                {
                    value: "否",
                    label: 0,
                },
            ],
            statusTxt: {
                2: "文案主管已通过",
                3: "采购已通过",
            },
            periods_typeTxt: {
                0: "闪购",
                1: "秒发",
                2: "跨境",
                3: "尾货",
                4: "兔头商品",
                5: "兔头优惠券",
                9: "商家秒发",
            },
            total: 0,
            importOpration: [
                {
                    label: "自采",
                    value: 0,
                },
                {
                    label: "地采",
                    value: 1,
                },
                {
                    label: "跨境",
                    value: 2,
                },
            ],
            statusOptions: [
                {
                    label: "文案主管已通过",
                    value: 2,
                },
                {
                    label: "采购已通过",
                    value: 3,
                },
            ],
            reasonOptions: [
                {
                    label: "酒瓶信息错误",
                    value: "酒瓶信息错误",
                },
                {
                    label: "年份错误",
                    value: "年份错误",
                },
                {
                    label: "详细资料错误",
                    value: "详细资料错误",
                },
                {
                    label: "采购人不符",
                    value: "采购人不符",
                },
                {
                    label: "其他",
                    value: "其他",
                },
            ],
            options: [],
            rules: {
                suppliers: [
                    {
                        required: true,
                        message: "请选择供应商",
                        trigger: "change",
                    },
                ],
                reason: [
                    {
                        required: true,
                        message: "请选择驳回原因",
                        trigger: "change",
                    },
                ],
                import_type: [
                    {
                        required: true,
                        message: "请选择进口类型",
                        trigger: "change",
                    },
                ],
                reject_reason: [
                    {
                        required: true,
                        message: "请选择驳回原因",
                        trigger: "change",
                    },
                ],
                supplier: [
                    {
                        required: true,
                        message: "请输入供应商",
                        trigger: "blur",
                    },
                ],
                supplier_delivery_address: [
                    {
                        required: true,
                        message: "请选择发货地",
                        trigger: "change",
                    },
                ],
            },
            rows: {},
            loading: false,
            payee_merchant_options: [
                { label: "重庆云酒佰酿电子商务有限公司", value: 1 },
                { label: "佰酿云酒（重庆）科技有限公司", value: 2 },
                { label: "渝中区微醺酒业商行", value: 5 },
                { label: "海南一花一世界科技有限公司", value: 10 },
            ],
            corpSelectDialogVisible: false,
            corpsObj: {},
            corpName: "",
            entitySuppliers: null,
            entityImportType: "",
            isSelfImportToPayee: false,
            disabledSupplierToPayee: false,
            corpToName: {
                "001": "佰酿云酒（重庆）科技有限公司",
                "002": "重庆云酒佰酿电子商务有限公司",
                "008": "渝中区微醺酒业商行",
                "032": "海南一花一世界科技有限公司",
            },
            regionList: [],
            pendingReviewStatus: 3, // 待确认的审核状态
        };
    },
    mounted() {
        this.getData();
        this.getPurchase();
        this.getRegionList();
    },
    methods: {
        remoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.article
                    .supplierList({
                        page: 1,
                        limit: 10,
                        keyword: query,
                    })
                    .then((res) => {
                        this.loading = false;
                        this.supplierOpration = res.data.data.list;
                    });
            } else {
                this.supplierOpration = [];
            }
        },
        async passBtnClick(reviewStatus) {
            this.pendingReviewStatus = reviewStatus;
            const mostwordsList = [
                { text: this.goodsinfo.title, label: "商品标题" },
                { text: this.goodsinfo.brief, label: "一句话介绍" },
                { text: this.goodsinfo.detail, label: "商品详情" },
            ];
            const judgeMostwords = async (text, label) => {
                try {
                    const res = await this.$request.article.judgeMostwords({
                        text,
                    });
                    const { found = [] } = res?.data || {};
                    return found.length
                        ? { label, desc: found.join("，") }
                        : null;
                } catch (e) {
                    return null;
                }
            };
            const judgeResList = await Promise.all(
                mostwordsList
                    .filter(({ text }) => text)
                    .map(({ text, label }) => judgeMostwords(text, label))
            );
            const message = judgeResList
                .filter((item) => item)
                .map(({ label, desc }) => `<div>${label}：${desc}</div>`)
                .join("");

            if (message) {
                const confirm = () => {
                    return new Promise((resolve) => {
                        this.$confirm(`${message}`, "极限词提示", {
                            confirmButtonText: "去修改",
                            cancelButtonText: "继续提交",
                            type: "warning",
                            dangerouslyUseHTMLString: true,
                            distinguishCancelAndClose: true,
                        })
                            .then(() => {
                                this.handleClick(this.goodsinfo);
                                this.GoodsDetailVisible = false;
                            })
                            .catch((action) => {
                                if (action === "cancel") {
                                    resolve();
                                }
                            });
                    });
                };
                await confirm();
            }
            this.dialogVisible1 = true;
        },
        formatShortCode(val) {
            if (val && val.length) {
                return val.join(" ");
            } else {
                return "-";
            }
        },
        handleStatusClick(rows) {
            this.$nextTick(() => {
                this.$refs.logList.openForm(rows);
            });
        },
        closeGoodsOpDialog() {
            this.createArticleVisible = false;
            this.getData();
        },
        HandlePraise(rows) {
            this.$confirm("你确定点赞吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.$request.article
                        .praise({
                            period: rows.id,
                            periods_type: rows.periods_type,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                rows.is_praise = 1;
                                this.$message.success("操作成功！");
                            }
                        });
                })
                .catch(() => {
                    //几点取消的提示
                });
        },
        getPurchase() {
            this.$request.article
                .purchaseList({
                    type: 2,
                })
                .then((res) => {
                    this.buyerOptions = res.data.data.list;
                });
            this.$request.article
                .purchaseList({
                    type: 3,
                })
                .then((res) => {
                    this.options = res.data.data.list;
                });
        },
        handleClick(row) {
            this.detailDialogStatus = true;
            let type = 0;
            if (row.periods_type == 0) {
                type = 0;
            } else if (row.periods_type == 1) {
                type = 20;
            } else if (row.periods_type == 2) {
                type = 9;
            } else if (row.periods_type == 3) {
                type = 22;
            } else if (row.periods_type == 4) {
                type = 4;
            }
            this.createArticleVisible = true;
            this.$nextTick(() => {
                this.$refs.articleform.openForm(type, row);
            });
        },
        onsale_statusFormat(val) {
            switch (val) {
                case 0:
                    return "待上架";
                case 1:
                    return "待售中";
                case 2:
                    return "在售中";
                case 3:
                    return "已下架";
                default:
                    return "-";
            }
        },
        tableRowClassName(row) {
            switch (row.row.status) {
                case 0:
                    return "warning-row";
                case 1:
                    return "danger-row";
                case 2:
                    return "success-row";
                default:
                    return;
            }
        },
        search() {
            this.page = 1;
            this.getData();
        },
        getData() {
            // let params = {
            //     buyer_review_status: 2,
            //     page: this.page,
            //     limit: this.limit,
            // };
            this.$request.article
                .articleList({
                    ...this.query,
                    not_merchant_second: 1,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.tableData = res.data.data.list || [];
                        this.total = res.data.data.total;
                    }
                });
        },
        closeAuditDialog() {
            this.dialogVisible = false;
            this.entity.reason = "";
            this.entity.remark = "";
        },
        HandleAudit(rows, review_status, reason) {
            this.goodsinfo = rows;
            this.GoodsDetailVisible = true;
            this.rows = rows;
            this.auditTime = 5;
            if (rows.periods_type == 0) {
                this.$request.article
                    .getFlashDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            console.log(res.data.data.product_img, 44444444);
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            } else if (rows.periods_type == 1) {
                this.$request.article
                    .getSecondDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            } else if (rows.periods_type == 2) {
                this.$request.article
                    .getCrossDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            } else if (rows.periods_type == 3) {
                this.$request.article
                    .getLeftoverDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            }
            this.countDown();
        },
        audit(rows, review_status, reason) {
            if (review_status == 3 && !this.validateForm()) {
                return;
            }
            const params = {
                period: rows.id,
                buyer_review_status: review_status,
                reject_reason: reason,
                periods_type: rows.periods_type,
                is_supplier_delivery: this.goodsData.is_supplier_delivery,
                is_presell: this.goodsData.is_presell,
                import_type: this.goodsData.import_type,
                supplier: this.goodsData.suppliers
                    ? this.goodsData.suppliers.supplier_name
                    : "",
                supplier_id: this.goodsData.suppliers
                    ? this.goodsData.suppliers.id
                    : "",
                remark: this.entity.remark,
                payee_merchant_id: this.goodsData.payee_merchant_id,
                payee_merchant_name: this.goodsData.payee_merchant_name,
                supplier_delivery_address:
                    this.goodsData.supplier_delivery_address,
            };
            this.$request.article.updateStatus1(params).then((res) => {
                if (res.data.error_code == 0) {
                    this.getData();
                    if (review_status == 4) {
                        this.dialogVisible = false;
                    }
                    this.GoodsDetailVisible = false;
                    this.closeDialogVisible1Dialog();
                    this.$message.success("操作成功！");
                }
            });
        },
        closeDialogVisible1Dialog() {
            this.dialogVisible1 = false;
            this.goodsData = {
                period: "",
                supplier: "",
                is_supplier_delivery: 0,
                is_presell: 0,
                import_type: "",
                reject_reason: "",
            };
        },
        HandleReject(rows) {
            this.rows = rows;
            this.entity.reason = "";
            this.dialogVisible = !this.dialogVisible;
        },
        rejects() {
            if (this.validateForm()) {
                if (!this.entity.reason) {
                    return;
                }
                this.audit(this.rows, 4, this.entity.reason);
            }
        },
        handleRemark(rows) {
            this.$nextTick(() => {
                this.$refs.remarklist.openForm(rows);
            });
        },
        countDown() {
            this.auditTime = this.auditTime - 1;
            if (this.auditTime == 0) {
                return;
            }
            setTimeout(() => {
                this.countDown();
            }, 1000);
        },
        validateForm() {
            let flag = null;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
        onSuppliersInput(value) {
            if (this.disabledSupplierToPayee) {
                this.goodsData = Object.assign({}, this.goodsData, {
                    suppliers: value,
                });
                return;
            }
            if ([1, 2].includes(this.goodsinfo.periods_type)) {
                const { value: payee_merchant_id, label: payee_merchant_name } =
                    this.payee_merchant_options[1];
                this.goodsData = Object.assign({}, this.goodsData, {
                    suppliers: value,
                    payee_merchant_id,
                    payee_merchant_name,
                });
                return;
            }
            if (
                !(
                    value.corps &&
                    Object.keys(value.corps).some((item) =>
                        ["001", "002", "008", "032"].includes(item)
                    )
                )
            ) {
                this.$message.error("请先维护磐石【供应商管理】");
                return;
            }
            if (Object.keys(value.corps).length > 1) {
                this.corpSelectDialogVisible = true;
                this.corpsObj = value.corps;
                this.corpName = this.goodsData.payee_merchant_name;
                this.entitySuppliers = value;
            } else {
                this.corpName = Object.values(value.corps)[0];
                const findItem = this.payee_merchant_options.find(
                    (item) => item.label === this.corpName
                );
                if (findItem) {
                    const {
                        label: payee_merchant_name,
                        value: payee_merchant_id,
                    } = findItem;
                    this.goodsData = Object.assign({}, this.goodsData, {
                        suppliers: value,
                        payee_merchant_id,
                        payee_merchant_name,
                    });
                } else {
                    this.goodsData = Object.assign({}, this.goodsData, {
                        suppliers: value,
                    });
                }
            }
        },
        onCorpNameConfirm() {
            if (this.isSelfImportToPayee) {
                this.disabledSupplierToPayee = true;
                const findItem = this.payee_merchant_options.find(
                    (item) => item.label === this.corpName
                );
                if (findItem) {
                    const {
                        label: payee_merchant_name,
                        value: payee_merchant_id,
                    } = findItem;
                    this.goodsData = Object.assign({}, this.goodsData, {
                        import_type: this.entityImportType,
                        suppliers: "",
                        payee_merchant_id,
                        payee_merchant_name,
                    });
                }
                this.corpSelectDialogVisible = false;
                return;
            }
            const findItem = this.payee_merchant_options.find(
                (item) => item.label === this.corpName
            );
            if (findItem) {
                const { label: payee_merchant_name, value: payee_merchant_id } =
                    findItem;
                this.goodsData = Object.assign({}, this.goodsData, {
                    suppliers: this.entitySuppliers,
                    payee_merchant_id,
                    payee_merchant_name,
                });
            } else {
                this.goodsData = Object.assign({}, this.goodsData, {
                    suppliers: this.entitySuppliers,
                });
            }
            this.corpSelectDialogVisible = false;
        },
        onImportTypeChange(importType) {
            this.isSelfImportToPayee = false;
            this.disabledSupplierToPayee = false;
            if (importType !== 0) {
                this.goodsData = Object.assign({}, this.goodsData, {
                    import_type: importType,
                    suppliers: "",
                    payee_merchant_id: "",
                    payee_merchant_name: "",
                });
                return;
            }
            if (![0, 3].includes(this.goodsinfo.periods_type)) {
                this.goodsData = Object.assign({}, this.goodsData, {
                    import_type: importType,
                });
                return;
            }
            this.isSelfImportToPayee = true;
            this.entityImportType = importType;
            if (this.goodsinfo.product_list.some(({ corp }) => !corp)) {
                this.corpSelectDialogVisible = true;
                this.corpName = this.goodsData.payee_merchant_name;
                this.corpsObj = this.corpToName;
                return;
            }
            const corpList = [
                ...new Set(
                    this.goodsinfo.product_list
                        .map(({ corp }) => corp)
                        .filter(Boolean)
                        .map((corp) => corp.split(","))
                        .reduce((prev, curr) => [...prev, ...curr], [])
                ),
            ];
            if (corpList.length === 1) {
                const [corp] = corpList;
                const corpName = this.corpToName[corp];
                const findItem = this.payee_merchant_options.find(
                    (item) => item.label === corpName
                );
                if (findItem) {
                    const {
                        label: payee_merchant_name,
                        value: payee_merchant_id,
                    } = findItem;
                    this.goodsData = Object.assign({}, this.goodsData, {
                        import_type: importType,
                        suppliers: "",
                        payee_merchant_id,
                        payee_merchant_name,
                    });
                    this.disabledSupplierToPayee = true;
                }
            } else {
                this.corpSelectDialogVisible = true;
                this.corpName = this.goodsData.payee_merchant_name;
                const corpsObj = {};
                Object.entries(this.corpToName).forEach(([key, value]) => {
                    if (corpList.includes(key)) corpsObj[key] = value;
                });
                this.corpsObj = corpsObj;
            }
        },
        getRegionList() {
            this.$request.article.getRegionList().then((res) => {
                if (res.data.error_code == 0) {
                    this.regionList = res.data.data.list.map(
                        ({ id, name }) => ({ id, name })
                    );
                }
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.article-layout {
    .article-main {
        .el-button--mini,
        .el-button--mini.is-round {
            padding: 0;
            background-color: #ffffff;
            border-color: #ffffff;
            color: #66b1ff;
        }

        margin: 10px 0;

        /deep/ .el-table .warning-row {
            background: oldlace;
        }

        /deep/ .el-table .danger-row {
            background: oldlace;
        }

        /deep/ .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
</style>
