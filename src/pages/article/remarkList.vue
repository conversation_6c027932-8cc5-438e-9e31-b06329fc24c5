<template>
    <el-dialog
        title="备注列表"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
    >
        <div class="article-layout">
            <el-button
                size="mini"
                type="warning"
                @click="handleClick"
                v-if="!isAll"
                >新增</el-button
            >
            <div class="article-main">
                <el-card>
                    <el-table
                        border
                        size="mini"
                        :data="tableData"
                        style="width: 100%"
                    >
                        <el-table-column
                            prop="id"
                            label="ID"
                            min-width="100"
                            align="center"
                        />
                        <el-table-column
                            prop="remark"
                            label="备注"
                            min-width="220"
                            align="center"
                        />
                        <el-table-column
                            prop="operator_name"
                            label="操作人"
                            width="80"
                            align="center"
                        />
                        <el-table-column
                            prop="created_time"
                            label="操作时间"
                            width="170"
                            align="center"
                        />
                    </el-table>
                </el-card>
            </div>
            <div class="pagination-block">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="query.page"
                    :page-size="query.limit"
                    :page-sizes="[10, 30, 50, 100, 200]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
            <div>
                <remarkOp ref="remarkOp" :parentObj="this"></remarkOp>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import remarkOp from "./remarkOp.vue";
export default {
    props: {
        isAll: {
            default: 0,
            type: Number,
        },
    },
    components: {
        remarkOp,
    },
    data() {
        return {
            dialogVisible: false,
            btnVisible: false,
            tableData: [],
            query: {
                page: 1,
                limit: 10,
                buyer_review_status: "",
                periods_type: "",
                periods: "",
                title: "",
            },
            total: 0,
            rows: {},
        };
    },
    mounted() {},
    methods: {
        openForm(rows) {
            this.rows = rows;
            this.dialogVisible = true;
            this.getData();
        },
        getData() {
            let params = {
                period: this.rows.id,
                limit: this.query.limit,
                page: this.query.page,
            };
            this.$request.article.remarkList(params).then((res) => {
                if (res.data.error_code == 0) {
                    this.tableData = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        handleClick() {
            this.$nextTick(() => {
                this.$refs.remarkOp.openForm(this.rows);
            });
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.article-layout {
    .article-main {
        margin: 10px 0;

        /deep/ .el-table .warning-row {
            background: oldlace;
        }

        /deep/ .el-table .danger-row {
            background: oldlace;
        }

        /deep/ .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
</style>
