<template>
    <div>
        <div class="fixed-menu">
            <!-- 抽屉触发按钮 -->
            <div
                class="menu-trigger"
                @click="toggleMenu"
                :class="{ active: menuExpanded }"
            >
                <i class="el-icon-menu"></i>
            </div>

            <!-- 抽屉菜单内容 -->
            <transition name="menu-slide">
                <ul v-show="menuExpanded" class="menu-content">
                    <li
                        @click="handleMenuClick('adds')"
                        v-if="!disab && entity.buyer_review_status !== 3"
                        class="add-btn"
                    >
                        添加产品
                    </li>
                    <li @click="handleMenuClick('scrollToTop')">顶部</li>
                    <li
                        @click="
                            handleMenuClick('scrollToSection', 'purchase-info')
                        "
                    >
                        采购信息
                    </li>
                    <li
                        @click="
                            handleMenuClick('scrollToSection', 'goods-info')
                        "
                    >
                        商品信息
                    </li>
                    <li @click="handleMenuClick('scrollToBottom')">底部</li>
                </ul>
            </transition>
        </div>
        <el-form
            ref="labelAlignForm"
            :model="entity"
            :rules="rules"
            label-width="65px"
            style="overflow-y: auto; height: calc(100vh - 100px)"
        >
            <el-card class="box-card" id="purchase-info">
                <div slot="header" class="clearfix">
                    <span>采购信息</span
                    ><!-- ({{nums}}) -->
                    <!--  <el-button size="small" type="primary" style="margin-left:20px;" @click="confirm">编码搜索</el-button> -->
                </div>
                <div
                    v-for="(item, index) in entity.purchaseList"
                    :key="index"
                    class="purchase-item"
                >
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item
                                label="简码"
                                :prop="'purchaseList.' + index + '.short_code'"
                                :rules="rules.short_code"
                            >
                                <div class="code-input-group">
                                    <el-input
                                        v-model="item.short_code"
                                        :disabled="disab"
                                        placeholder="请输入简码"
                                    />
                                    <el-button
                                        v-if="!disab"
                                        type="primary"
                                        size="mini"
                                        @click="searchCode(item)"
                                        >搜索
                                    </el-button>
                                    <el-button
                                        v-if="item.showProduct"
                                        type="warning"
                                        size="mini"
                                        @click="updateProduct()"
                                        >修改
                                    </el-button>
                                    <el-button
                                        v-if="item.showProduct"
                                        type="danger"
                                        size="mini"
                                        @click="creatArticle(item)"
                                        >AI智能文案
                                    </el-button>
                                    <el-button
                                        v-if="
                                            entity.purchaseList &&
                                            entity.purchaseList.length > 1 &&
                                            !disab
                                        "
                                        size="mini"
                                        type="danger"
                                        icon="el-icon-delete"
                                        @click="del(index)"
                                        >删除</el-button
                                    >
                                </div>
                                <div
                                    class="btn_visib_wrap"
                                    v-show="item.codeVisible"
                                >
                                    <div class="btn_visib_content">
                                        <div
                                            class="code_close"
                                            @click="
                                                item.codeVisible =
                                                    !item.codeVisible
                                            "
                                        >
                                            <i class="el-icon-close"></i>
                                        </div>
                                        <div class="code_content">
                                            <div
                                                v-for="(v, i) in codeList"
                                                class="code_wrap"
                                                @click="
                                                    confirmCode(v, item, index)
                                                "
                                                :key="i"
                                            >
                                                {{ v.en_product_name }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <div v-if="item.showProduct" class="product-info">
                        <el-divider content-position="left"
                            >产品基本信息</el-divider
                        >
                        <div class="info-grid">
                            <div class="info-item">
                                <label>品名：</label>
                                <span>{{ item.en_product_name || "-" }}</span>
                            </div>
                            <div class="info-item">
                                <label>类别：</label>
                                <span>{{
                                    item.productcategoryname || "-"
                                }}</span>
                            </div>
                            <div class="info-item">
                                <label>酒庄：</label>
                                <span>{{ item.chateau_name || "-" }}</span>
                            </div>
                            <div class="info-item">
                                <label>产区：</label>
                                <span>{{
                                    item.producing_area_name || "-"
                                }}</span>
                            </div>
                            <div class="info-item">
                                <label>国家：</label>
                                <span>{{ item.country_name || "-" }}</span>
                            </div>
                            <div class="info-item">
                                <label>葡萄：</label>
                                <span>
                                    <template
                                        v-if="
                                            item.Grapelist &&
                                            item.Grapelist.length
                                        "
                                    >
                                        <span
                                            v-for="(v, index) in item.Grapelist"
                                            :key="index"
                                        >
                                            {{ v.gname_cn
                                            }}{{
                                                index <
                                                item.Grapelist.length - 1
                                                    ? "、"
                                                    : ""
                                            }}
                                        </span>
                                    </template>
                                    <template v-else>-</template>
                                </span>
                            </div>
                        </div>

                        <el-divider content-position="left"
                            >产品参数信息</el-divider
                        >
                        <div class="info-grid">
                            <div class="info-item">
                                <label>残糖：</label>
                                <span>
                                    {{ item.residual_sugar || "-" }}
                                    <span v-if="item.residual_sugar">g/L</span>
                                </span>
                            </div>
                            <div class="info-item">
                                <label>酒精度：</label>
                                <span>
                                    {{ item.alcohol || "-" }}
                                    <span v-if="item.alcohol">%vol</span>
                                </span>
                            </div>
                            <div class="info-item double-item">
                                <div class="sub-item">
                                    <label>容量：</label>
                                    <span>{{ item.capacity || "-" }}</span>
                                </div>
                                <div class="sub-item">
                                    <label>类型：</label>
                                    <span>{{
                                        item.product_type_name || "-"
                                    }}</span>
                                </div>
                            </div>
                            <div class="info-item full-width">
                                <label>关键词：</label>
                                <span>
                                    <template
                                        v-if="
                                            item.product_keywords_id &&
                                            item.product_keywords_id.length
                                        "
                                    >
                                        <el-tag
                                            v-for="(
                                                v, index
                                            ) in item.product_keywords_id"
                                            :key="index"
                                            size="small"
                                            class="keyword-tag"
                                        >
                                            {{ v.name }}
                                        </el-tag>
                                    </template>
                                    <template v-else>-</template>
                                </span>
                            </div>
                        </div>

                        <el-divider content-position="left"
                            >产品描述信息</el-divider
                        >
                        <div class="description-section">
                            <div class="description-item">
                                <label>酿造工艺：</label>
                                <el-input
                                    type="textarea"
                                    disabled
                                    @input="$forceUpdate()"
                                    :autosize="$componentsConfig.textareaRow()"
                                    v-model="item.brewing"
                                />
                            </div>
                            <div class="description-item">
                                <label>评分：</label>
                                <el-input
                                    type="textarea"
                                    @input="$forceUpdate()"
                                    :autosize="$componentsConfig.textareaRow()"
                                    placeholder="请输入评分"
                                    v-model="item.score"
                                />
                            </div>
                            <div class="description-item">
                                <label>Tasting Notes：</label>
                                <el-input
                                    type="textarea"
                                    @input="$forceUpdate()"
                                    :autosize="$componentsConfig.textareaRow()"
                                    placeholder="请输入Tasting Notes"
                                    v-model="item.tasting_notes"
                                />
                            </div>
                            <div class="description-item">
                                <label>获奖：</label>
                                <el-input
                                    type="textarea"
                                    :autosize="$componentsConfig.textareaRow()"
                                    @input="$forceUpdate()"
                                    placeholder="请输入获奖"
                                    v-model="item.prize"
                                />
                            </div>
                            <div class="description-item">
                                <label>饮用建议：</label>
                                <el-input
                                    type="textarea"
                                    @input="$forceUpdate()"
                                    :autosize="$componentsConfig.textareaRow()"
                                    placeholder="请输入饮用建议"
                                    v-model="item.drinking_suggestion"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </el-card>
            <el-card
                class="box-card"
                shadow="hover"
                style="margin: 0 0 10px"
                id="goods-info"
            >
                <div slot="header" class="clearfix">
                    <span>商品信息</span>
                </div>
                <div class="goods-info-container">
                    <!-- 基本信息区域 -->
                    <div class="section-container">
                        <div class="section-title">基本信息</div>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="商品标题" prop="title">
                                    <el-input
                                        v-model="entity.title"
                                        placeholder="请输入商品标题"
                                        :disabled="disab"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="一句话介绍" prop="brief">
                                    <el-input
                                        v-model="entity.brief"
                                        placeholder="请输入商品副标题"
                                        :disabled="disab"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>

                    <!-- 图片上传区域 -->
                    <div class="section-container">
                        <div class="section-title">图片信息</div>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="题图" prop="filelist1">
                                    <div class="upload-tip">
                                        建议尺寸: 750x464
                                    </div>
                                    <vos-oss
                                        @on-error="onError"
                                        list-type="picture-card"
                                        :showFileList="true"
                                        :dir="dir"
                                        :file-list="entity.filelist1"
                                        :disabled="disab"
                                        :limit="1"
                                        :limitWhList="[750, 464]"
                                    >
                                        <i
                                            slot="default"
                                            class="el-icon-plus"
                                        ></i>
                                    </vos-oss>
                                </el-form-item>
                            </el-col>

                            <el-col :span="12">
                                <el-form-item
                                    :label="
                                        type === 20 ? '方形产品图' : '产品图'
                                    "
                                    prop="filelist3"
                                >
                                    <div class="upload-tip">
                                        最多8张，建议尺寸: 800x800
                                    </div>
                                    <vos-oss
                                        @on-error="onError"
                                        list-type="picture-card"
                                        :showFileList="true"
                                        :dir="dir"
                                        :file-list="entity.filelist3"
                                        :limit="8"
                                        :multiple="true"
                                        :disabled="disab"
                                        :limitWhList="[800, 800]"
                                    >
                                        <i
                                            slot="default"
                                            class="el-icon-plus"
                                        ></i>
                                    </vos-oss>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row v-if="type === 20">
                            <el-col :span="24">
                                <el-form-item label="竖版题图" prop="filelist2">
                                    <div class="upload-tip">
                                        建议尺寸: 800x800
                                    </div>
                                    <vos-oss
                                        @on-error="onError"
                                        list-type="picture-card"
                                        :showFileList="true"
                                        :limit="1"
                                        :dir="dir"
                                        :file-list="entity.filelist2"
                                        :disabled="disab"
                                    >
                                        <i
                                            slot="default"
                                            class="el-icon-plus"
                                        ></i>
                                    </vos-oss>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>

                    <!-- 视频信息区域 -->
                    <div class="section-container">
                        <div class="section-title">视频信息</div>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="视频封面">
                                    <vos-oss
                                        @on-error="onError"
                                        list-type="picture-card"
                                        :showFileList="true"
                                        :dir="dir"
                                        :file-list="video_coverList"
                                        :limit="1"
                                        :multiple="true"
                                        :disabled="disab"
                                    >
                                        <i
                                            slot="default"
                                            class="el-icon-plus"
                                        ></i>
                                    </vos-oss>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="视频上传">
                                    <div class="video-upload-container">
                                        <vos-vod
                                            @uploadSuccess="uploadSuccess"
                                            :playurl.sync="entity.video"
                                        ></vos-vod>
                                        <div
                                            v-if="entity.video"
                                            class="video-preview"
                                        >
                                            <span class="video-tip"
                                                >已上传视频文件</span
                                            >
                                        </div>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <el-form-item label="采购说">
                        <el-input
                            type="textarea"
                            maxlength="500"
                            :autosize="$componentsConfig.textareaRow()"
                            v-model="entity.purchasing_said"
                            show-word-limit
                        >
                        </el-input>
                    </el-form-item>
                    <!-- 视频预览弹窗 -->
                    <el-dialog
                        title="视频预览"
                        :visible.sync="videoPreviewVisible"
                        width="60%"
                        :before-close="handleVideoPreviewClose"
                        append-to-body
                        center
                    >
                        <div class="video-player-container">
                            <video
                                v-if="videoPreviewVisible"
                                :src="entity.video"
                                controls
                                class="video-player"
                            >
                                您的浏览器不支持 video 标签。
                            </video>
                        </div>
                    </el-dialog>

                    <!-- 商品详情区域 -->
                    <div class="section-container">
                        <div class="section-title">商品详情</div>
                        <el-form-item label="详情内容" prop="detail">
                            <Tinymce
                                ref="editor"
                                :readonly="disab"
                                @singleValidate="singleValidate"
                                v-model.trim="entity.detail"
                                :height="300"
                            />
                        </el-form-item>
                    </div>

                    <!-- 采购人信息 -->
                    <div class="section-container">
                        <div class="section-title">其他信息</div>
                        <el-form-item label="采购人" prop="buyers">
                            <el-select
                                :disabled="disab"
                                v-model="entity.buyers"
                                placeholder="请选择"
                                value-key="id"
                                style="width: 200px"
                            >
                                <el-option
                                    v-for="item in options"
                                    :key="item.id"
                                    :label="item.realname"
                                    :value="item"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                </div>
            </el-card>
            <el-form-item label class="clearfix">
                <!-- v-if="rows.buyer_review_status != 2" -->

                <div style="float: right; margin-top: 20px">
                    <el-button type="primary" @click="submits">{{
                        rows.id > 0 ? "保存" : "提交"
                    }}</el-button>
                </div>
            </el-form-item>
        </el-form>

        <el-dialog
            title="完善AI参数"
            :visible.sync="addDialogVisible"
            width="580px"
            :before-close="closeUploadDialog"
            destroy-on-close
            append-to-body
            custom-class="article-dialog"
        >
            <div class="dialog-notice">
                <i class="el-icon-info"></i>
                <span
                    >下面的内容为非必填项，但丰富下面的内容，可以让AI更好的生成文案。生成后的文案内容会自动填入到商品信息区域。</span
                >
            </div>

            <el-form
                :model="articleData"
                ref="articleData"
                label-width="100px"
                class="article-form"
            >
                <el-form-item label="市场价:" prop="marketPrice">
                    <el-input
                        v-model="articleData.marketPrice"
                        type="number"
                        placeholder="请输入市场价"
                        class="form-input"
                    >
                        <template slot="append">元</template>
                    </el-input>
                </el-form-item>

                <el-form-item label="卖价:" prop="price">
                    <el-input
                        v-model="articleData.price"
                        type="number"
                        placeholder="请输入卖价"
                        class="form-input"
                    >
                        <template slot="append">元</template>
                    </el-input>
                </el-form-item>

                <el-form-item label="卖点:" prop="sellingPoint">
                    <el-input
                        v-model="articleData.sellingPoint"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入商品卖点描述"
                        class="form-textarea"
                    />
                </el-form-item>
            </el-form>

            <div slot="footer" class="dialog-footer">
                <div style="width: 400px; margin-left: 100px">
                    <el-button @click="closeUploadDialog">取 消</el-button>
                    <el-button type="primary" @click="sureBtnClick()"
                        >确 定</el-button
                    >
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import vosOss from "vos-oss";
import vosVod from "vos-vod";
import Tinymce from "@/components/Tinymce";
import axios from "axios";
export default {
    props: {
        parentObj: Object,
        isArticle: Boolean,
    },
    components: {
        Tinymce,
        vosOss,
        vosVod,
    },
    data() {
        return {
            dialogVisible: false,
            imgVisible: false,
            codeVisible: false,
            title: "闪购介绍",
            uploadDisabled: true,
            isProgress: false,
            uploader: null,
            type: 0,
            options: [],
            video_coverList: [],
            articleData: {
                marketPrice: "",
                price: "",
                sellingPoint: "",
            },
            infoItem: {},
            addDialogVisible: false,
            entity: {
                title: "",
                brief: "",
                purchasing_said: "",
                banner_img: "",
                video_cover: "",
                product_img: "",
                detail: "",
                filelist1: [],
                filelist2: [],
                filelist3: [],
                product_id: [],
                purchaseList: [
                    {
                        short_code: "",
                        showProduct: false,
                    },
                ],
                buyers: "",
                video: "",
            },
            dialogVisible: false,
            loading: false,
            ids: [
                {
                    name: "酒庄id",
                    mode: "chateau_id",
                },
            ],
            addDataRules: {
                marketPrice: [
                    {
                        required: true,
                        message: "请输入",
                        trigger: "blur",
                    },
                ],
                price: [
                    {
                        required: true,
                        message: "请输入",
                        trigger: "blur",
                    },
                ],
                sellingPoint: [
                    {
                        required: true,
                        message: "请输入",
                        trigger: "blur",
                    },
                ],
            },
            rules: {
                title: [
                    {
                        required: true,
                        message: "请输入商品标题",
                        trigger: "blur",
                    },
                ],
                brief: [
                    {
                        required: true,
                        message: "请输入商品副标题",
                        trigger: "blur",
                    },
                ],
                detail: [
                    {
                        required: true,
                        message: "请输入商品详情",
                        trigger: "blur",
                    },
                ],
                filelist1: [
                    {
                        required: true,
                        message: "请先上传图片",
                        trigger: "change",
                    },
                ],
                filelist3: [
                    {
                        required: true,
                        // validator: validateimg,
                        message: "请先上传图片",
                        trigger: "change",
                    },
                ],
                // short_code: [
                //     {
                //         required: true,
                //         message: "请输入简码",
                //         trigger: "blur",
                //     },
                // ],
                buyers: [
                    {
                        required: true,
                        message: "请选择采购人",
                        trigger: "change",
                    },
                ],
            },
            loading: false,
            disab: false,
            codeList: [],
            rows: {
                buyer_review_status: 1,
            },
            dir: "vinehoo/goods-images/",
            localStorageKey: "articleDraftData",
            videoPreviewVisible: false,
            menuExpanded: false, // 抽屉菜单展开状态
        };
    },
    created() {},
    mounted() {
        // 添加点击外部区域关闭菜单的事件监听
        document.addEventListener("click", this.handleClickOutside);
    },
    beforeDestroy() {
        // 移除事件监听器
        document.removeEventListener("click", this.handleClickOutside);
    },
    methods: {
        // 切换抽屉菜单展开状态
        toggleMenu() {
            this.menuExpanded = !this.menuExpanded;
        },
        // 处理点击外部区域关闭菜单
        handleClickOutside(event) {
            const menuElement = event.target.closest(".fixed-menu");
            if (!menuElement && this.menuExpanded) {
                this.menuExpanded = false;
            }
        },
        // 处理菜单项点击
        handleMenuClick(action, param) {
            // 执行对应的方法
            if (action === "adds") {
                this.adds();
            } else if (action === "scrollToTop") {
                this.scrollToTop();
            } else if (action === "scrollToSection") {
                this.scrollToSection(param);
            } else if (action === "scrollToBottom") {
                this.scrollToBottom();
            }

            // 关闭菜单
            this.menuExpanded = false;
        },
        uploadSuccess(data) {
            this.entity.video = data.url;
        },
        updateProduct() {
            this.$message.success("请前往磐石系统修改！");
        },
        onSuccess(url, type) {
            console.log(url, 22);
            // if (type == 1) {
            //     this.entity.filelist1.push(url.file);

            // } else if (type == 2) {
            //     this.entity.filelist2.push(url.file);
            // } else if (type == 3) {
            //     this.entity.filelist3.push(url.file);
            // }
        },
        onRemove(event, type) {
            if (type == 1) {
                this.entity.filelist1 = event.list;
            } else if (type == 2) {
                this.entity.filelist2 = event.list;
            } else if (type == 3) {
                this.entity.filelist3 = event.list;
            }
        },
        onError(a, b) {
            console.warn(a, b);
            this.$message.error("上传失败，请重新上传");
        },
        getPurchase() {
            this.$request.article
                .purchaseList({
                    type: 2,
                })
                .then((res) => {
                    this.options = res.data.data.list;
                });
        },
        async submits() {
            if (this.validateForm()) {
                if (this.entity.purchaseList.length == 0) {
                    this.$Message.error("请先搜索简码/条码!");
                    return;
                }
                let product_id = [];
                let flag = false;
                this.entity.purchaseList.map((item) => {
                    if (!item.showProduct) {
                        flag = true;
                    }
                    product_id.push(item.id);
                });
                if (flag) {
                    this.$message.error("请选择产品！");
                    return;
                }
                if (!this.video_coverList.length !== !this.entity.video) {
                    this.$Message.error("请完善视频信息");
                    return;
                }

                const mostwordsList = [
                    { text: this.entity.title, label: "商品标题" },
                    { text: this.entity.brief, label: "一句话介绍" },
                    { text: this.entity.detail, label: "商品详情" },
                ];

                const data = {
                    id: this.entity.id,
                    title: this.entity.title,
                    brief: this.entity.brief,
                    banner_img: this.entity.filelist1.join(","),
                    product_img: this.entity.filelist3.join(","),
                    detail: this.entity.detail,
                    product_id: product_id.join(","),
                    video_cover: this.video_coverList.join(","),
                    video: this.entity.video,
                    horizontal_img:
                        this.type == 20 ? this.entity.filelist2.join(",") : "",
                    buyer_id: this.entity.buyers.id,
                    buyer_name: this.entity.buyers.realname,
                    product_info: this.entity.purchaseList.map((item) => ({
                        tasting_notes: item.tasting_notes,
                        score: item.score,
                        prize: item.prize,
                        drinking_suggestion: item.drinking_suggestion,
                        short_code: item.short_code,
                        product_id: item.id,
                    })),
                    purchasing_said: this.entity.purchasing_said,
                };

                const judgeMostwords = async (text, label) => {
                    try {
                        const res = await this.$request.article.judgeMostwords({
                            text,
                        });
                        const { found = [] } = res?.data || {};
                        return found.length
                            ? { label, desc: found.join("，") }
                            : null;
                    } catch (e) {
                        return null;
                    }
                };
                const judgeResList = await Promise.all(
                    mostwordsList
                        .filter(({ text }) => text)
                        .map(({ text, label }) => judgeMostwords(text, label))
                );
                const message = judgeResList
                    .filter((item) => item)
                    .map(({ label, desc }) => `<div>${label}：${desc}</div>`)
                    .join("");
                if (message) {
                    const confirm = () => {
                        return new Promise((resolve) => {
                            this.$confirm(`${message}`, "极限词提示", {
                                confirmButtonText: "去修改",
                                cancelButtonText: "继续提交",
                                type: "warning",
                                dangerouslyUseHTMLString: true,
                            })
                                .then(() => {
                                    // resolve();
                                })
                                .catch(() => {
                                    resolve();
                                });
                        });
                    };
                    await confirm();
                }
                if (this.isArticle) {
                    this.saveToLocalStorage();
                }
                try {
                    let res;
                    if (this.entity.id > 0) {
                        res = await this.updateArticle(data, 2);
                        // this.$message.error('提交失败,已保存到本地草稿');
                        if (res.data.error_code == 0) {
                            this.articalCallback(res);
                            if (this.isArticle) {
                                localStorage.removeItem(this.localStorageKey);
                            }
                        } else {
                            if (this.isArticle) {
                                this.$message.error(
                                    "提交失败,已保存到本地草稿"
                                );
                            }
                        }
                    } else {
                        res = await this.addArticle(data);
                        // this.saveToLocalStorage();

                        if (res.data.error_code == 0) {
                            this.articalCallback(res);
                            if (this.isArticle) {
                                localStorage.removeItem(this.localStorageKey);
                            }
                        } else {
                            if (this.isArticle) {
                                this.$message.error(
                                    "提交失败,已保存到本地草稿"
                                );
                            }
                        }
                    }
                } catch (error) {
                    // console.error('提交失败:', error);
                    // this.$message.error('提交失败,已保存到本地草稿');
                }
            }
        },
        articalCallback(res) {
            if (res.data.error_code == 0) {
                this.$emit("closeGoodsOpDialog");
                this.$message.success("操作成功！");
            }
        },
        checkedStore(row, purchase) {
            purchase.map((item) => {
                this.$set(item, "checked", row.checked);
                if (row.short_code == item.short_code) {
                } else {
                    item.checked = false;
                }
            });
        },

        openForm(type, row) {
            this.dialogVisible = true;
            this.entity = {
                title: "",
                brief: "",
                banner_img: "",
                product_img: "",
                detail: "",
                filelist1: [],
                filelist2: [],
                filelist3: [],
                product_id: [],
                purchaseList: [
                    {
                        short_code: "",
                        showProduct: false,
                    },
                ],
                buyers: "",
            };
            this.type = type;
            this.disab = false;
            this.rows = {};
            this.rows.buyer_review_status = row ? row.buyer_review_status : 1;

            this.getPurchase();
            if (this.isArticle) {
                // 尝试从本地存储加载数据
                this.loadFromLocalStorage();
            }

            if (row) {
                this.rows.id = row.id;
                this.disab =
                    this.rows.buyer_review_status == 2 &&
                    this.rows.copywriting_review_status == 2;

                // 根据不同类型获取详情
                const getDetailMethod = {
                    0: this.$request.article.getFlashDetail,
                    20: this.$request.article.getSecondDetail,
                    9: this.$request.article.getCrossDetail,
                    22: this.$request.article.getLeftoverDetail,
                    4: this.$request.article.rabbitDetail,
                }[this.type];

                if (getDetailMethod) {
                    getDetailMethod({ id: row.id }).then((res) => {
                        if (res.data.error_code == 0) {
                            this.handleDetailResponse(res.data.data);
                        }
                    });
                }
            }
        },
        getVal() {
            return this.formAuditEditList;
        },
        searchCode(rows) {
            if (rows.short_code == "") {
                this.$message.error("请输入简码/条码");
                return;
            }
            let nums = 0;
            this.entity.purchaseList.map((item) => {
                if (item.short_code == rows.short_code) {
                    nums++;
                }
            });
            if (nums > 1) {
                this.$message.error("请输入不同的简码/条码");
                return;
            }
            this.$request.article
                .getProducts({
                    short_code: rows.short_code,
                })
                .then((res) => {
                    if (res.data.error_code == "0") {
                        this.codeList = [];
                        console.log(this.entity, 222);
                        if (res.data.data.length > 0) {
                            this.codeList = res.data.data;
                            this.$set(rows, "codeVisible", true);
                        } else {
                            this.$set(rows, "codeVisible", false);
                            this.$message.error("暂无数据！");
                        }
                    }
                });
        },
        confirmCode(rows, v, index) {
            v.codeVisible = false;

            this.$set(rows, "showProduct", true);
            this.$set(rows, "codeVisible", false);
            this.entity.purchaseList[index] = Object.assign(
                this.entity.purchaseList[index],
                rows
            );
            console.log(this.entity, 223);
        },
        creatArticle(item) {
            console.log("----item", item);
            this.infoItem = item;
            this.addDialogVisible = true;
        },
        closeUploadDialog() {
            this.articleData = this.$options.data().articleData;
            this.addDialogVisible = false;
        },
        async sureBtnClick() {
            this.$refs["articleData"].validate(async (valid) => {
                if (valid) {
                    const userinfo = JSON.parse(
                        localStorage.getItem("userinfo")
                    );
                    var text;
                    text = "品名：" + this.infoItem.en_product_name + "\n";
                    if (this.infoItem.chateau_name) {
                        text =
                            text + "酒庄：" + this.infoItem.chateau_name + "\n";
                    }
                    if (this.infoItem.producing_area_name) {
                        text =
                            text +
                            "产区：" +
                            this.infoItem.producing_area_name +
                            "\n";
                    }
                    if (this.infoItem.score) {
                        text = text + "评分：" + this.infoItem.score + "\n";
                    }
                    if (this.infoItem.tasting_notes) {
                        text =
                            text +
                            "Tasting Notes：" +
                            this.infoItem.tasting_notes +
                            "\n";
                    }
                    if (this.infoItem.prize) {
                        text = text + "获奖：" + this.infoItem.prize + "\n";
                    }
                    if (this.infoItem.drinking_suggestion) {
                        text =
                            text +
                            "饮用建议：" +
                            this.infoItem.drinking_suggestion +
                            "\n";
                    }
                    text =
                        text + "卖点：" + this.articleData.sellingPoint + "\n";
                    text =
                        text + "市场价：" + this.articleData.marketPrice + "\n";
                    text = text + "卖价：" + this.articleData.price;
                    const data = {
                        text: text,
                        op_id: userinfo.uid,
                        op_name: userinfo.realname,
                        short_code: this.infoItem.short_code,
                    };
                    try {
                        // Make the POST request with Axios
                        const res = await axios.post(
                            "https://chatgpt.vinehoo.com/emb/embedding/v3/completion/copywriting_generation",
                            data,
                            {
                                headers: {},
                                // responseType: "json" // Expecting a PDF file in response
                            }
                        );
                        // console.log
                        if (res.data.error_code == "0") {
                            this.entity.title = res.data.data.title;
                            this.entity.brief = res.data.data.subtitle;
                            this.entity.detail = res.data.data.text.replaceAll(
                                "\n",
                                "<p>\n</p>"
                            );
                            this.$refs.editor.setContent(this.entity.detail);
                            this.closeUploadDialog();
                            // 自动滚动到商品信息区域
                            this.$nextTick(() => {
                                setTimeout(() => {
                                    this.scrollToSection("goods-info");
                                }, 1000);
                            });
                        }
                    } catch (error) {
                        console.error("Error posting data:", error);
                    }
                    // this.$request.article
                    // .generateCopy({
                    //     text:text,
                    //     op_id:userinfo.uid,
                    //     op_name:userinfo.realname,
                    //     short_code:this.infoItem.short_code,
                    // })
                    // .then((res) => {
                    //     if (res.data.error_code == "0") {
                    //         this.entity.title = res.data.data.title;
                    //         this.entity.brief = res.data.data.subtitle;
                    //         this.entity.detail = res.data.data.text.replaceAll("\n","<p>\n</p>");
                    //         this.closeUploadDialog();
                    //     }
                    // });
                }
            });
        },

        // 继续添加
        adds() {
            const datas = {
                short_code: "",
                purchase: [],
            };
            this.entity.purchaseList.push(datas);
            this.nums++;

            // 等待DOM更新后滚动到新元素
            this.$nextTick(() => {
                const purchaseItems =
                    document.getElementsByClassName("purchase-item");
                const lastItem = purchaseItems[purchaseItems.length - 1];
                if (lastItem) {
                    lastItem.scrollIntoView({
                        behavior: "smooth",
                        block: "center",
                    });
                }
            });
        },
        // 删除
        del(index) {
            this.entity.purchaseList.splice(index, 1);
            this.nums--;
        },
        singleValidate() {
            let flag = null;
            this.$refs["labelAlignForm"].validateField("detail", (valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
        // 表单验证
        validateForm() {
            let flag = null;
            this.$refs["labelAlignForm"].validate((valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
        colorChange(val, index, type) {},
        // 新增方法: 保存到本地存储
        saveToLocalStorage() {
            const dataToSave = {
                entity: this.entity,
                type: this.type,
                video_coverList: this.video_coverList,
            };
            console.log("dataToSave---------------", dataToSave);
            localStorage.setItem(
                this.localStorageKey,
                JSON.stringify(dataToSave)
            );
        },
        // 新增方法: 从本地存储加载数据
        loadFromLocalStorage() {
            const savedData = localStorage.getItem(this.localStorageKey);

            if (savedData) {
                this.$confirm("是否从草稿加载数据？")
                    .then((_) => {
                        this.loadForLocal(savedData);
                    })
                    .catch((_) => {
                        localStorage.removeItem(this.localStorageKey);
                    });
            }
        },
        loadForLocal(savedData) {
            try {
                const parsedData = JSON.parse(savedData);
                const oss_url =
                    process.env.NODE_ENV === "development"
                        ? "https://images.wineyun.com"
                        : "https://images.vinehoo.com";

                // 处理所有图片列表的URL
                ["filelist1", "filelist2", "filelist3"].forEach(
                    (filelistKey) => {
                        if (Array.isArray(parsedData.entity[filelistKey])) {
                            parsedData.entity[filelistKey] = parsedData.entity[
                                filelistKey
                            ].map((url) => {
                                return url.includes("https")
                                    ? url
                                    : oss_url + url;
                            });
                        }
                    }
                );

                // 处理视频封面图片
                if (Array.isArray(parsedData.video_coverList)) {
                    parsedData.video_coverList = parsedData.video_coverList.map(
                        (url) => {
                            return url.includes("https") ? url : oss_url + url;
                        }
                    );
                }

                // 更新数据
                this.entity = parsedData.entity;
                this.type = parsedData.type;
                this.video_coverList = parsedData.video_coverList;

                // 如果有富文本编辑器内容,需要重新设置
                if (this.entity.detail && this.$refs.editor) {
                    this.$refs.editor.setContent(this.entity.detail);
                }

                this.$message.success("已加载本地保存的草稿");
                localStorage.removeItem(this.localStorageKey);
            } catch (error) {
                console.error("加载本地草稿失败:", error);
                this.$message.error("加载本地草稿失败");
                localStorage.removeItem(this.localStorageKey);
            }
        },
        // 新增方法: 更新文章
        async updateArticle(data, type) {
            switch (this.type) {
                case 0:
                    return await this.$request.article.flashAdd(data, type);
                case 20:
                    return await this.$request.article.secondAdd(data, type);
                case 9:
                    return await this.$request.article.crossAdd(data, type);
                case 22:
                    return await this.$request.article.leftoverAdd(data, type);
                case 4:
                    return await this.$request.article.rabbitAdd(data, type);
            }
        },
        // 新增方法: 添加文章
        async addArticle(data) {
            switch (this.type) {
                case 0:
                    return await this.$request.article.flashAdd(data);
                case 20:
                    return await this.$request.article.secondAdd(data);
                case 9:
                    return await this.$request.article.crossAdd(data);
                case 22:
                    return await this.$request.article.leftoverAdd(data);
                case 4:
                    return await this.$request.article.rabbitAdd(data);
            }
        },
        // 新增方法: 处理详情响应
        handleDetailResponse(data) {
            data.purchaseList = data.product_list.map((item) => ({
                ...item,
                showProduct: true,
            }));
            data.buyers = {
                id: data.buyer_id,
                realname: data.buyer_name,
            };
            data.filelist1 = data.banner_img;
            data.filelist3 = data.product_img;
            this.video_coverList = data.video_cover
                ? data.video_cover.split(",")
                : [];

            if (this.type == 20) {
                data.filelist2 = data.horizontal_img;
            }

            this.entity = Object.assign({}, data);
            this.$refs.editor.setContent(this.entity.detail);
            setTimeout(() => {
                this.singleValidate();
            }, 0);
        },
        scrollToSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                section.scrollIntoView({ behavior: "smooth" });
            }
        },
        previewVideo() {
            this.videoPreviewVisible = true;
        },
        handleVideoPreviewClose() {
            this.videoPreviewVisible = false;
        },
        scrollToTop() {
            const formEl = this.$refs.labelAlignForm.$el;
            formEl.scrollTo({ top: 0, behavior: "smooth" });
        },
        scrollToBottom() {
            const formEl = this.$refs.labelAlignForm.$el;
            formEl.scrollTo({
                top: formEl.scrollHeight,
                behavior: "smooth",
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.f-12 {
    width: 170px;
    display: inline-block;
    overflow: hidden;
    line-height: 20px;
    text-overflow: ellipsis;
    word-wrap: break-word;
    word-break: break-all;
    white-space: nowrap;
}

.purchase {
    border: 1px solid #cccccc;
    padding: 15px 0 0px;
    margin-bottom: 15px;
    display: flex;
}

.purchaseActive {
    border: 1px solid #00ffe2;
    padding: 15px 0 0px;
    margin-bottom: 15px;
    display: flex;
}

.article_product_wrap {
    margin-left: 78px;
    width: 80%;
    .article_product_item-wrap {
        & > div {
            text-overflow: ellipsis;
            word-wrap: break-word;
            word-break: break-all;
            // white-space: nowrap;
            min-width: 200px;
            max-width: 1000px;
            margin-right: 10px;
            color: #333;
            // display: flex;

            b {
                line-height: 2;
                opacity: 1;
                display: inline-block;
                font-weight: bold;
            }
        }
    }
    .article_product_item {
        display: flex;
        justify-content: space-between;

        & > div {
            text-overflow: ellipsis;
            word-wrap: break-word;
            word-break: break-all;
            white-space: nowrap;
            min-width: 200px;
            margin-right: 10px;
            color: #333;
            display: flex;

            b {
                line-height: 2;
                opacity: 1;
                display: inline-block;
                font-weight: bold;
            }
        }
    }
}
/deep/ .el-descriptions__body {
    color: #333 !important;
}

.fixed-menu {
    position: fixed;
    right: 57px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1000;

    // 抽屉触发按钮
    .menu-trigger {
        width: 40px;
        height: 40px;
        background: #409eff;
        position: absolute;
        right: -38px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        color: #fff;
        font-size: 16px;

        &:hover {
            background: #66b1ff;
            transform: scale(1.05);
        }

        &.active {
            background: #66b1ff;

            i {
                transform: rotate(90deg);
            }
        }

        i {
            transition: transform 0.3s ease;
        }
    }

    // 抽屉菜单内容
    .menu-content {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        list-style: none;
        padding: 0;
        margin: 0;
        min-width: 120px;

        li {
            padding: 12px 16px;
            background: #fff;
            border: 1px solid #dcdfe6;
            margin-bottom: 8px;
            cursor: pointer;
            border-radius: 6px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            font-size: 14px;
            white-space: nowrap;

            &:hover {
                background: #409eff;
                color: #fff;
                border-color: #409eff;
                transform: translateX(-5px);
            }

            &.add-btn {
                background: #409eff;
                color: #fff;
                border-color: #409eff;

                &:hover {
                    background: #66b1ff;
                    border-color: #66b1ff;
                }
            }
        }
    }
}

// 抽屉菜单动画
.menu-slide-enter-active,
.menu-slide-leave-active {
    transition: all 0.3s ease;
}

.menu-slide-enter {
    opacity: 0;
    transform: translateY(-50%) translateX(20px);
}

.menu-slide-leave-to {
    opacity: 0;
    transform: translateY(-50%) translateX(20px);
}

.purchase-item {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .code-input-group {
        display: flex;
        align-items: center;
        gap: 10px;

        .el-input {
            flex: 1;
        }
    }
}

.product-info {
    margin-top: 20px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, 280px);
    gap: 16px 40px;
    padding: 16px 16px 16px 65px;
    background: #f8f9fa;
    border-radius: 4px;

    .info-item {
        display: flex;
        align-items: flex-start;

        &.full-width {
            grid-column: 1 / -1;
        }

        &.double-item {
            grid-column: span 2;
            display: flex;
            gap: 40px;

            .sub-item {
                display: flex;
                align-items: flex-start;
                flex: 1;

                label {
                    width: 50px;
                }
            }
        }

        label {
            width: 65px;
            color: #606266;
            font-weight: 500;
            margin-right: 8px;
            flex-shrink: 0;
            text-align: justify;
            text-align-last: justify;
        }

        span {
            color: #303133;
            flex: 1;
            word-break: break-all;
        }
    }
}

.description-section {
    padding: 16px 16px 16px 65px;
    background: #f8f9fa;
    border-radius: 4px;

    .description-item {
        margin-bottom: 16px;

        &:last-child {
            margin-bottom: 0;
        }

        label {
            display: inline-block;
            width: auto;
            min-width: 85px;
            color: #606266;
            font-weight: 500;
            margin-bottom: 8px;
            white-space: nowrap;
        }

        .el-input {
            width: 100%;
        }
    }
}

.keyword-tag {
    margin-right: 8px;
    margin-bottom: 8px;
}

.el-divider {
    margin: 24px 0 16px;

    .el-divider__text {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
    }
}

.goods-info-container {
    padding: 0 20px;

    .section-container {
        margin-bottom: 32px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 24px;
        position: relative;
        padding-left: 12px;

        &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background: #409eff;
            border-radius: 2px;
        }
    }

    .upload-tip {
        font-size: 12px;
        color: #909399;
        margin-bottom: 8px;
    }

    /deep/ .el-form-item {
        margin-bottom: 22px;
        display: flex;

        &:last-child {
            margin-bottom: 0;
        }

        .el-form-item__label {
            padding-right: 16px;
            white-space: nowrap;
            width: 85px !important;
            text-align: right;
            float: none;
            display: inline-block;
            line-height: 40px;
        }

        .el-form-item__content {
            margin-left: 0 !important;
            flex: 1;
        }
    }

    /deep/ .el-upload--picture-card {
        width: 148px;
        height: 148px;
        line-height: 148px;
    }

    /deep/ .el-upload-list--picture-card .el-upload-list__item {
        width: 148px;
        height: 148px;
    }

    .el-row {
        margin-bottom: 20px;

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.video-upload-container {
    .video-preview {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-top: 12px;

        .video-tip {
            color: #67c23a;
            font-size: 14px;
        }
    }
}

.video-player-container {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #000;
    border-radius: 4px;
    overflow: hidden;

    .video-player {
        width: 100%;
        max-height: 70vh;
        object-fit: contain;
    }
}

/deep/ .el-dialog__body {
    padding: 0;
}

.article-dialog {
    /deep/ .el-dialog {
        @media screen and (max-width: 768px) {
            width: 90% !important;
            margin: 20px auto !important;
        }
    }

    /deep/ .el-dialog__body {
        padding: 20px 30px;

        @media screen and (max-width: 768px) {
            padding: 15px 20px;
        }
    }

    .dialog-notice {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        background-color: #fef0f0;
        border-radius: 4px;
        margin-bottom: 20px;

        .el-icon-info {
            color: #f56c6c;
            font-size: 16px;
            margin-right: 8px;
        }

        span {
            color: #f56c6c;
            font-size: 14px;
            line-height: 1.4;
        }
    }

    .article-form {
        .form-input,
        .form-textarea {
            width: 100%;
            max-width: 400px;

            @media screen and (max-width: 768px) {
                max-width: 100%;
            }
        }

        /deep/ .el-input__inner {
            height: 40px;
            line-height: 40px;
        }

        /deep/ .el-input-group__append {
            padding: 0 15px;
            background-color: #f5f7fa;
            color: #909399;
        }

        /deep/ .el-form-item {
            margin-bottom: 24px;

            @media screen and (max-width: 768px) {
                margin-bottom: 20px;
            }

            &:last-child {
                margin-bottom: 0;
            }

            .el-form-item__label {
                font-weight: 500;
                color: #606266;

                @media screen and (max-width: 768px) {
                    padding-bottom: 8px;
                }
            }
        }
    }

    .dialog-footer {
        text-align: right;
        padding-top: 20px;

        @media screen and (max-width: 768px) {
            div {
                width: 100% !important;
                margin-left: 0 !important;
            }
        }

        .el-button {
            padding: 9px 20px;
            font-size: 14px;

            & + .el-button {
                margin-left: 12px;
            }
        }
    }
}
</style>
