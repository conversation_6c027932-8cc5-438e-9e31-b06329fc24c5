<template>
    <el-dialog
        title="新增备注"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
        width="40%"
        append-to-body
    >
        <el-form ref="form" :model="entity" :rules="rules" label-width="0px">
            <el-form-item label="" prop="remark">
                <el-input
                    :rows="7"
                    v-model="entity.remark"
                    type="textarea"
                    placeholder="请输入备注"
                />
            </el-form-item>
            <el-form-item label class="clearfix">
                <div class="flex-bt">
                    <el-button type="primary" @click="submits">提交</el-button>
                    <el-button @click="dialogVisible = false">取消</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>

<script>
export default {
    props: {
        parentObj: Object
    },
    data() {
        return {
            dialogVisible: false,
            tableData: [],
            entity: {
                remark: ""
            },
            rules: {
                remark: [
                    {
                        required: true,
                        message: "请输入备注",
                        trigger: "blur"
                    }
                ]
            },
            rows: {}
        };
    },
    mounted() {},
    methods: {
        submits() {
            if (!this.validateForm()) {
                return;
            }
            this.entity.period = this.rows.id;
            this.entity.periods_type = this.rows.periods_type;
            this.entity.operator = "1";
            this.$request.article.createRemark(this.entity).then(res => {
                if (res.data.error_code == 0) {
                    this.dialogVisible = false;
                    this.parentObj.getData();
                    this.$message.success("操作成功！");
                }
            });
        },
        openForm(rows) {
            this.rows = rows;
            this.entity.remark = "";
            this.dialogVisible = true;
        },
        validateForm() {
            let flag = null;
            this.$refs["form"].validate(valid => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        }
    }
};
</script>
<style></style>
<style lang="scss" scoped>
.flex-bt {
    display: flex;
    justify-content: center;
}
</style>
