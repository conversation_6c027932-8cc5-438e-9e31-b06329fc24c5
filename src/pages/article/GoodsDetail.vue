<template>
    <div class="wy_goods_detail">
        <div class="tooltip-text">鼠标移动到<span class="highlight"><i class="el-icon-mobile"></i></span>(刘海处)可查看手机浏览二维码</div>
        <div class="phone-frame">
            <div class="notch">
                <div class="qr-icon" @mouseover="showQRCode" @mouseleave="hideQRCode">
                    <i class="el-icon-mobile"></i>
                    <div class="qrcode-popup" v-show="isQRCodeVisible">
                        <div id="qrCode" ref="qrCodeDiv"></div>
                        <div class="qrcode-text">手机扫码浏览</div>
                    </div>
                </div>
            </div>
            <iframe :src="src" frameborder="0"></iframe>
        </div>
    </div>
</template>

<script>
import QRCode from "qrcodejs2";
export default {
    props: {
        goodsinfo: {
            type: Object,
            default: {},
        },
    },
    data() {
        return {
            src: "",
            mainImg: "",
            currentImg: 0,
            area_message: false,
            grape_message: false,
            wine_message: false,
            selectedGoodInfo: 0,
            isQRCodeVisible: false,
            tabs: [
                {
                    name: "商品详情",
                    id: "detail",
                },
                {
                    name: "商品点评",
                    id: "comment",
                },
            ],
        };
    },
    mounted() {
        console.warn(this.goodsinfo, 777777777777);
        if (this.goodsinfo.periods_type === 4) {
            this.src =
                this.$BASE.Domain +
                "/packageB/pages/rabbit-head-goods-detail/rabbit-head-goods-detail?from=3";
        } else {
            this.src =
                this.$BASE.Domain + "/pages/goods-detail/goods-detail?from=3";
        }
        this.src =
            this.src +
            "&_t=" +
            new Date().getTime() +
            "&id=" +
            this.goodsinfo.id;

        this.getQRCode();
    },
    methods: {
        showQRCode() {
            this.isQRCodeVisible = true;
        },
        hideQRCode() {
            this.isQRCodeVisible = false;
        },
        getQRCode() {
            this.$nextTick(() => {
                if (this.$refs.qrCodeDiv) {
                    this.$refs.qrCodeDiv.innerHTML = '';
                    new QRCode(this.$refs.qrCodeDiv, {
                        text: this.src,
                        width: 120,
                        height: 120,
                        colorDark: "#333333",
                        colorLight: "#ffffff",
                        correctLevel: QRCode.CorrectLevel.L,
                    });
                }
            });
        }
    },
};
</script>
<style></style>
<style>
.el-dialog__body {
    overflow: auto;
}

.effective-width {
    width: 1200px;
}

.wy_goods_detail .price .rmb {
    font-size: 36px;
}

.wy_goods_detail .price .line-through {
    font-size: 16px;
    margin-left: 25px;
    text-decoration: line-through;
}

.wy_goods_detail .tips p,
.wy_goods_detail .tips span {
    font-size: 12px;
    font-weight: 400;
    color: rgba(153, 153, 153, 1);
    line-height: normal;
}

.wy_goods_detail .tips {
    margin-left: 108px;
}

.wy_goods_detail .tips p {
    margin-bottom: 8px;
}

.wy_goods_detail .tips img {
    height: 28px;
    margin-right: 5px;
}

.wy_goods_detail .tips .rqcode {
    width: 112px;
    height: 112px;
    visibility: hidden;
    position: absolute;
    top: 44px;
    left: 0;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    opacity: 0;
}

.wy_goods_detail .tips > .flex-box:hover .rqcode {
    visibility: visible;
    opacity: 1;
    top: 32px;
    z-index: 12;
}

.btn-buy .good_qrimg {
    visibility: hidden;
    position: absolute;
    top: 70px;
    left: 0;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    opacity: 0;
}

.btn-buy:hover .good_qrimg {
    visibility: visible;
    opacity: 1;
    top: 46px;
    z-index: 12;
}

.wy_goods_detail {
    margin: 0;
    padding: 0 50px;
    max-width: 475px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
}

.qrcode-container {
    text-align: center;
    margin-bottom: 10px;
}

.qrcode-text {
    margin-top: 5px;
    color: #333333;
    font-size: 12px;
}

#qrCode {
    display: inline-block;
    transform: scale(0.6);
    transform-origin: center;
    margin: -30px;
}

.phone-frame {
    position: relative;
    width: 375px;
    height: 667px;
    background: #fff;
    border-radius: 40px;
    box-shadow: 0 0 0 11px #1f1f1f, 0 0 0 13px #191919, 0 0 0 20px #111;
    overflow: hidden;
}

.phone-frame:before {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 0px;
    width: 56%;
    height: 30px;
    background: #1f1f1f;
    border-radius: 0 0 40px 40px;
}

.phone-frame:after {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 7px;
    width: 140px;
    height: 4px;
    background: #f2f2f2;
    border-radius: 10px;
}

.phone-frame iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.wy_goods_detail .imgs_wrap {
    margin-right: 25px;
}

.wy_goods_detail > h2 {
    font-size: 20px;
    font-weight: 600;
    color: rgba(51, 51, 51, 1);
    text-indent: -10px;
    margin-bottom: 12px;
    line-height: 27px;
    /* height: 54px; */
    /* max-height: 54px; */
}

.collection_wrap {
    border: 1px solid #d4d4d4;
    padding: 6px 14px;
    width: 54px;
    height: 50px;
    margin-left: 20px;
    cursor: pointer;
    font-size: 12px;
}

.collection_wrap > img {
    display: block;
    margin-bottom: 2px;
    margin-left: 1px;
}

.wy_goods_detail > p {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 21px;
    height: 21px;
}

.wy_goods_detail .packs {
    margin-top: 25px;
}

.wy_goods_detail .packs .packname {
    flex-wrap: wrap;
    width: 595px;
}

.wy_goods_detail .name {
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    margin-right: 20px;
    margin-left: 20px;
    display: inline-block;
    width: 67px;
    padding-top: 4px;
}

.wy_goods_detail > h2 {
    font-size: 20px;
    font-weight: 600;
    color: rgba(51, 51, 51, 1);
    text-indent: -10px;
    margin-bottom: 12px;
    line-height: 27px;
}

.indent {
    text-indent: 0px !important;
}

.sg_goods_wrap {
    background: linear-gradient(to left, #ff0013, #e50012);
    height: 43px;
    line-height: 43px;
    padding-left: 20px;
    font-size: 18px;
    color: #ffffff;
    margin-top: 20px;
}

.flex-box {
    display: flex;
}

.just-between {
    justify-content: space-between;
}

.wy_goods_detail_content {
    padding: 25px 10px 35px 25px;
    background-color: #f8f8f8;
}

.wy_goods_detail_content .content_item {
}

.wy_goods_detail_content .content_item > label {
    color: #666666;
    font-size: 14px;
    margin-right: 20px;
    padding-top: 12px;
    width: 56px;
}

.wy_goods_detail_content .content_item > label > .l {
    margin-left: 7px;
}

.wy_goods_detail_content .content_item > label > i {
    margin-left: 28px;
    font-style: normal;
}

.wy_goods_detail_content .content_item .top {
    padding-top: 18px;
}

.wy_goods_detail_content .content_item .top1 {
    padding-top: 5px;
}

.wy_goods_detail_content .content_item .mj_wrap {
    padding-top: 14px;
}

.wy_goods_detail_content .content_item .mj_wrap > span {
    color: #ca101a;
    border: 1px solid #ca101a;
    padding: 0px 5px;
    font-size: 12px;
}

.wy_goods_detail_content .content_line {
    margin: 24px 0;
    border-bottom: 1px dashed #dddddd;
}

.wy_goods_detail_content .content_item > span {
    padding-top: 5px;
    font-size: 14px;
}

.secrecy {
    color: #ff0013;
    font-size: 24px;
    font-weight: 600;
}

.btn-buy {
    position: relative;
    display: inline-block;
    color: #fff;
    width: 168px;
    height: 50px;
    background: rgba(202, 16, 26, 1);
    border-radius: 4px;
    line-height: 50px;
    text-align: center;
    cursor: pointer;
    font-size: 16px;
}

.btn-tz {
    min-width: 90px;
    height: 30px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(221, 221, 221, 1);
    font-size: 12px;
    color: #333333;
    margin-right: 20px;
    line-height: 30px;
    text-align: center;
    cursor: pointer;
    box-sizing: border-box;
    padding: 0 10px;
    margin-bottom: 10px;
}

.wy_goods_img_wrap img {
    vertical-align: middle;
}

.wy_goods_img_wrap {
    width: 460px;
    position: relative;
    margin-right: 20px;
}

.wy_goods_img_wrap .imgs {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.wy_goods_img_wrap .main1 {
    position: relative;
    width: 460px;
    height: 462px;
    text-align: center;
    border: 1px solid rgba(204, 204, 204, 1);
}

.wy_goods_img_wrap .img-bar {
    margin-top: 15px;
}

.wy_goods_img_wrap .smaller {
    position: relative;
    overflow: hidden;
    width: 100000px;
}

.wy_goods_img_wrap .smaller li {
    position: relative;
    float: left;
    width: 90px;
    height: 91px;
    background: rgba(255, 255, 255, 1);
    margin-right: 3px;
    cursor: pointer;
    box-sizing: border-box;
    overflow-x: auto;
    overflow-y: hidden;
    border-radius: 6px;
    border: 1px solid rgba(204, 204, 204, 1);
}

.wy_goods_img_wrap .smaller li img,
.wy_goods_img_wrap .main1 img {
    max-height: 100%;
    max-width: 100%;
    /*  width: auto;
          height: auto; */
}

.wy_goods_img_wrap .smaller li:first-child {
    margin-left: 0;
}

.wy_goods_img_wrap .smaller .current {
    border-color: rgba(191, 8, 37, 1);
}

.wy_goods_img_wrap .img-bar .btn-left,
.wy_goods_img_wrap .img-bar .btn-right {
    width: 26px;
    display: block;
    height: 91px;
    cursor: pointer;
    line-height: 91px;
    text-align: center;
    border-radius: 4px;
    background: #f4f4f4;
    color: #999;
}

.wy_goods_img_wrap .biger {
    position: absolute;
    overflow: hidden;
    display: none;
    left: 488px;
    width: 488px;
    height: 489px;
    z-index: 2;
}

.wy_goods_img_wrap .mask {
    position: absolute;
    display: none;
    left: 0;
    top: 0;
    width: 200px;
    height: 200px;
    overflow: hidden;
    background-color: #fff;
    -moz-opacity: 0.6;
    opacity: 0.6;
    filter: alpha(opacity=60);
    z-index: 120;
    border: 1px solid #ccc;
    z-index: 9;
    cursor: crosshair;
}

.flex-box {
    display: flex;
}

.wy_goods_detail .content .packs {
    margin-top: 25px;
}

.wy_goods_detail .content .packs .packname {
    flex-wrap: wrap;
    width: 595px;
}
.btn-right > i {
    width: 26px;
}
.btn-left > i {
    width: 26px;
}

/* 商品详情 */

.M_detailTop-a {
    position: relative;
    top: -100px;
}

.goods_detail_top {
    background: rgba(239, 239, 239, 1);
    height: 50px;
    /* margin-top: 30px; */
}

.goods_detail_top .detail_top_item {
    display: block;
    /* width: 100px; */
    cursor: pointer;
    text-align: left;
    padding-left: 10px;
    padding-right: 10px;
    font-size: 14px;
    font-weight: 400;
    line-height: 50px;
    border-top: 2px solid rgba(239, 239, 239, 1);
    border-right: 1px solid rgba(239, 239, 239, 1);
}

.goods_detail_top .selected {
    /* border-top-color: #ae0031;
      background-color: #ffffff; */
    color: rgba(202, 16, 26, 1);
}

.goods_detailinfo {
    font-size: 14px;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    line-height: 20px;
    min-height: 40px;
    padding: 10px;
}

.goods_detailinfo .detailinfo {
    overflow: hidden;
    padding: 10px;
    transition: all 0.4s linear 0s;
}

.goods_detailinfo .detailinfo img {
    max-width: 100%;
}

.goods_detailinfo > span {
    padding: 10px 5px 10px 10px;
}

.goods_detailinfo ul li:nth-child(1) {
    border-top: 1px solid #fff;
}

.goods_detailinfo ul li {
    border-bottom: 1px solid rgba(231, 231, 231, 1);
    min-height: 40px;
    line-height: 40px;
    padding-left: 10px;
    display: flex;
    align-items: center;
}

.goods_detailinfo ul li > span {
    width: 93%;
    line-height: 22px;
    padding: 10px 0;
}

.goods_detailinfo ul li label {
    display: inline-block;
    width: 80px;
    /* padding-left: 10px; */
    margin-bottom: 0;
}
ul {
    display: block;
    list-style-type: disc;
    padding-inline-start: 0px;
}

.qrcode-container {
    text-align: center;
    margin-bottom: 20px;
}

.qrcode-text {
    margin-top: 10px;
    color: #333333;
    font-size: 14px;
}

#qrCode {
    display: inline-block;
}

.notch {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 0px;
    width: 56%;
    height: 30px;
    background: #1f1f1f;
    border-radius: 0 0 40px 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
}

.qr-icon {
    position: relative;
    color: #666;
    cursor: pointer;
    padding: 5px;
}

.qr-icon i {
    font-size: 18px;
}

.qr-icon:hover {
    color: #fff;
}

.qrcode-popup {
    position: absolute;
    top: 40px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
    z-index: 1000;
    text-align: center;
}

#qrCode {
    display: flex;
    justify-content: center;
    align-items: center;
}

#qrCode img {
    display: block;
    margin: 0 auto;
}

.qrcode-text {
    margin-top: 8px;
    color: #333333;
    font-size: 12px;
    white-space: nowrap;
    text-align: center;
}

.tooltip-text {
    text-align: center;
    color: #666;
    font-size: 13px;
    margin: 0px 0 25px 0;
}

.tooltip-text .highlight {
    color: #409EFF;
    margin: 0 3px;
    display: inline-flex;
    align-items: center;
}

.tooltip-text .highlight i {
    font-size: 16px;
}
</style>
