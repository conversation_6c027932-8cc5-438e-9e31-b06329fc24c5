<template>
    <div class="article-layout">
        <el-card>
            <div class="article-form">
                <div>
                    <el-input
                        v-model="query.periods"
                        class="w-mini m-r-10"
                        placeholder="期数"
                        @keyup.enter.native="search"
                        size="mini"
                        clearable
                    >
                    </el-input>
                </div>
                <div>
                    <el-input
                        size="mini"
                        class="w-mini m-r-10"
                        v-model="query.short_code"
                        placeholder="简码"
                        @keyup.enter.native="search"
                        clearable
                    >
                    </el-input>
                </div>

                <div>
                    <el-input
                        v-model="query.title"
                        class="w-normal m-r-10"
                        placeholder="标题"
                        @keyup.enter.native="search"
                        clearable
                        size="mini"
                    >
                    </el-input>
                </div>

                <div>
                    <el-select
                        class="w-mini m-r-10"
                        v-model="query.periods_type"
                        filterable
                        size="mini"
                        placeholder="频道"
                        clearable
                    >
                        <el-option
                            v-for="item in channelOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-select
                        class="w-mini m-r-10"
                        v-model="query.creator_id"
                        filterable
                        size="mini"
                        placeholder="文案制作人"
                        clearable
                    >
                        <el-option
                            v-for="item in options"
                            :key="item.id"
                            :label="item.realname"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-select
                        class="w-mini m-r-10"
                        v-model="query.buyer_id"
                        filterable
                        size="mini"
                        placeholder="采购人"
                        clearable
                    >
                        <el-option
                            v-for="item in buyerOptions"
                            :key="item.id"
                            :label="item.realname"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-select
                        class="w-normal m-r-10"
                        v-model="query.buyer_review_status"
                        filterable
                        size="mini"
                        placeholder="状态"
                        clearable
                    >
                        <el-option
                            v-for="item in statusOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div class="form-actions">
                    <div style="display: flex">
                        <el-button size="mini" type="primary" @click="search"
                            >查询</el-button
                        >
                        <div style="margin-left: 10px">
                            <el-button
                                size="mini"
                                type="warning"
                                @click="btnVisible = !btnVisible"
                                >新增</el-button
                            >
                            <div class="btn_visib_wrap" v-show="btnVisible">
                                <div class="btn_visib_title">
                                    <div>请选择要创建的文案频道</div>
                                    <div
                                        class="btn_close"
                                        @click="btnVisible = !btnVisible"
                                    >
                                        <i class="el-icon-close"></i>
                                    </div>
                                </div>
                                <div class="btn_visib_content">
                                    <el-button
                                        size="mini"
                                        type="danger"
                                        @click="showDialog(0)"
                                        >闪购</el-button
                                    >
                                    <el-button
                                        size="mini"
                                        type="warning"
                                        @click="showDialog(20)"
                                        >秒发</el-button
                                    >
                                    <el-button
                                        size="mini"
                                        type="success"
                                        @click="showDialog(9)"
                                        >跨境</el-button
                                    >
                                    <el-button
                                        size="mini"
                                        type="info"
                                        @click="showDialog(22)"
                                        >尾货</el-button
                                    >
                                    <el-button
                                        size="mini"
                                        type="warning"
                                        @click="showDialog(4)"
                                        >兔头商城实物</el-button
                                    >
                                    <el-button
                                        size="mini"
                                        type="warning"
                                        @click="showDialog(5)"
                                        >兔头商城优惠券</el-button
                                    >
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-card>
        <div class="article-main">
            <el-card shadow="never">
                <el-table
                    border
                    stripe
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                    :row-class-name="tableRowClassName"
                    cell-class-name="table-cell-class"
                >
                    <el-table-column
                        prop="id"
                        label="期数"
                        width="70"
                        align="center"
                    />
                    <el-table-column
                        prop="id"
                        show-overflow-tooltip
                        label="简码"
                        width="120"
                        align="center"
                    >
                        <template slot-scope="row">
                            {{ formatShortCode(row.row.short_code) }}
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="period"
                        label="频道"
                        width="70"
                        align="center"
                    >
                        <template slot-scope="{ row }">
                            <el-tag
                                :type="getChannelTagType(row.periods_type)"
                                size="mini"
                            >
                                {{ periods_typeTxt[row.periods_type] }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="title"
                        label="标题"
                        min-width="240"
                    >
                        <template slot-scope="scope">
                            <span class="title-cell">{{ scope.row.title }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="creator_name"
                        label="文案"
                        width="80"
                        align="center"
                    />
                    <el-table-column
                        prop="buyer_name"
                        label="采购"
                        width="80"
                        align="center"
                    />
                    <el-table-column
                        prop="address"
                        align="center"
                        label="状态"
                        width="110"
                    >
                        <template slot-scope="{ row }">
                            <el-tag
                                :type="getStatusTagType(row)"
                                size="mini"
                                @click="handleStatusClick(row)"
                                style="cursor: pointer"
                            >
                                {{ statusTxt(row) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="created_time"
                        label="创建时间"
                        width="160"
                        align="center"
                    />
                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        width="160"
                        align="center"
                    >
                        <template slot-scope="{ row }">
                            <el-button
                                size="mini"
                                type="primary"
                                :disabled="row.periods_type === 5"
                                @click="handleView(row)"
                            >
                                预览
                            </el-button>
                            <el-button
                                size="mini"
                                type="primary"
                                @click="handleClick(row)"
                            >
                                <!-- v-if=" row.buyer_review_status == 4 ||
                                row.copywriting_review_status == 3 ||
                                row.copywriting_review_status == 0 " -->
                                编辑
                            </el-button>

                            <el-button
                                size="mini"
                                @click="handleSub(row)"
                                type="warning"
                                v-if="
                                    row.copywriting_review_status == 0 ||
                                    row.buyer_review_status == 4 ||
                                    row.copywriting_review_status == 3
                                "
                            >
                                提交</el-button
                            >
                            <el-button
                                size="mini"
                                @click="handleAudit(row)"
                                type="warning"
                                v-if="row.copywriting_review_status == 1"
                                >审核</el-button
                            >
                            <el-button
                                size="mini"
                                type="danger"
                                @click="handleDel(row)"
                                v-if="row.copywriting_review_status == 0"
                                >删除</el-button
                            >

                            <!-- <el-button size="mini" type="primary" @click="handleRemark(row)">备注</el-button> -->
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <el-dialog
            fullscreen
            :visible.sync="createArticleVisible"
            title="文案信息"
            :before-close="closeGoodsOpDialog"
            custom-class="dialogwid"
        >
            <createArticle
                ref="articleform"
                @closeGoodsOpDialog="closeGoodsOpDialog"
                v-if="createArticleVisible"
                :parentObj="this"
                :isArticle="true"
            ></createArticle>
        </el-dialog>
        <remarkList ref="remarklist"></remarkList>
        <logList ref="logList"></logList>
        <el-dialog
            :visible.sync="GoodsDetailVisible"
            title="商品详情浏览"
            custom-class="goods-detail-dialog"
            :fullscreen="false"
            width="475px"
            :top="'5vh'"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div>
                <goods-detail
                    ref="goodsdetail"
                    :goodsinfo="goodsinfo"
                    v-if="GoodsDetailVisible"
                ></goods-detail>
                <div
                    slot="footer"
                    class="dialog-footer"
                    v-if="isAudit"
                    style="text-align: right; margin-top: 20px"
                >
                    <el-button
                        type="primary"
                        @click="passBtnClick"
                    >
                        通过
                    </el-button>
                    <el-button type="primary" @click="HandleReject()"
                        >驳回</el-button
                    >
                </div>
            </div>
        </el-dialog>
        <el-dialog title="驳回信息" :visible.sync="dialogVisible1" width="30%">
            <el-form
                label-width="100px"
                ref="form"
                :model="entity"
                :rules="rules"
            >
                <el-form-item label="驳回理由" prop="reason">
                    <el-select
                        class="w-mini m-r-10"
                        v-model="entity.reject_reason"
                        filterable
                        size="mini"
                        placeholder="状态"
                    >
                        <el-option
                            v-for="item in reasonOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input
                        v-model="entity.remark"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入备注"
                    />
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible1 = false">取 消</el-button>
                <el-button type="primary" @click="rejects()">确 定</el-button>
            </span>
        </el-dialog>
        <el-dialog
            :visible.sync="rabbitcouponaddDialogStatus"
            title="兔头优惠券信息"
            :before-close="closeGoodsOpDialog"
            custom-class="dialogwid"
            width="70%"
        >
            <RabbitCouponAdd
                @closeGoodsOpDialog="closeGoodsOpDialog"
                v-if="rabbitcouponaddDialogStatus"
                ref="rabbitcouponadd"
                :parentObj="this"
            ></RabbitCouponAdd>
        </el-dialog>
    </div>
</template>

<script>
import createArticle from "./CreateArticle.vue";
import remarkList from "./remarkList.vue";
import logList from "../goods/components/LogList.vue";
import GoodsDetail from "./GoodsDetail.vue";
import RabbitCouponAdd from "../goods/components/RabbitCouponAdd.vue";
export default {
    components: {
        createArticle,
        remarkList,
        logList,
        GoodsDetail,
        RabbitCouponAdd,
    },
    data() {
        return {
            rabbitcouponaddDialogStatus: false,
            dialogVisible: false,
            createArticleVisible: false,
            buyerOptions: [],
            options: [],
            btnVisible: false,
            dialogVisible1: false,
            isAudit: false,
            total: 0,
            tableData: [],
            query: {
                page: 1,
                limit: 10,
                buyer_id: "",
                buyer_review_status: "",
                creator_id: "",
                periods_type: "",
                short_code: "",
                periods: "",
                title: "",
            },
            entity: {
                reject_reason: "",
                remark: "",
            },

            periods_typeTxt: {
                0: "闪购",
                1: "秒发",
                2: "跨境",
                3: "尾货",
                4: "实物",
                5: "优惠券",
                9: "商家秒发",
            },
            statusOptions: [
                {
                    label: "未提交",
                    value: 6,
                },
                {
                    label: "文案已提交",
                    value: 1,
                },
                {
                    label: "文案主管已通过",
                    value: 2,
                },
                {
                    label: "文案主管已驳回",
                    value: 3,
                },
                {
                    label: "采购已通过",
                    value: 4,
                },
                {
                    label: "采购已驳回",
                    value: 5,
                },
            ],

            channelOptions: [
                {
                    label: "闪购",
                    value: 0,
                },
                {
                    label: "秒发",
                    value: 1,
                },
                {
                    label: "跨境",
                    value: 2,
                },
                {
                    label: "尾货",
                    value: 3,
                },
            ],
            goodsinfo: {},
            GoodsDetailVisible: false,
            reasonOptions: [
                {
                    label: "酒瓶信息错误",
                    value: "酒瓶信息错误",
                },
                {
                    label: "年份错误",
                    value: "年份错误",
                },
                {
                    label: "详细资料错误",
                    value: "详细资料错误",
                },
                {
                    label: "采购人不符",
                    value: "采购人不符",
                },
                {
                    label: "其他",
                    value: "其他",
                },
            ],
            rules: {
                reject_reason: [
                    {
                        required: true,
                        message: "请选择驳回原因",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    mounted() {
        this.getPurchase();
        this.getData();
        this.$request.article
            .purchaseList({
                type: 3,
            })
            .then((res) => {
                this.options = res.data.data.list;
            });
    },
    methods: {
        formatShortCode(val) {
            if (val && val.length) {
                return val.join(" ");
            } else {
                return "-";
            }
        },
        closeGoodsOpDialog() {
            this.createArticleVisible = false;
            this.rabbitcouponaddDialogStatus = false;
            this.getData();
        },
        statusTxt(rows) {
            let statusName = "";
            if (rows.copywriting_review_status == 0) {
                statusName = "未提交";
            } else if (rows.copywriting_review_status == 1) {
                statusName = "文案已提交";
            } else if (rows.copywriting_review_status == 2) {
                statusName = "文案主管已通过";
                if (rows.buyer_review_status == 3) {
                    statusName = "采购已通过";
                } else if (rows.buyer_review_status == 4) {
                    statusName = "采购已驳回";
                }
            } else if (rows.copywriting_review_status == 3) {
                statusName = "文案主管已驳回";
            } else if (rows.buyer_review_status == 3) {
                statusName = "采购已通过";
            } else if (rows.buyer_review_status == 4) {
                statusName = "采购已驳回";
            } else {
                statusName = "未知";
            }

            return statusName;
        },

        handleAudit(row) {
            this.isAudit = true;
            this.GoodsDetailVisible = true;
            this.goodsinfo = row;
           console.log('row----', row);
           
            if (row.periods_type == 0) {
                this.$request.article
                    .getFlashDetail({
                        id: row.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            console.log(res.data.data.product_img, 44444444);
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.periods_type = row.periods_type;
                            res.data.data.num = 1;
                            this.goodsinfo = res.data.data;
                            
                        }
                    });
            } else if (row.periods_type == 1) {
                this.$request.article
                    .getSecondDetail({
                        id: row.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = row.periods_type;
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.num = 1;
                            this.goodsinfo = res.data.data;
                            
                        }
                    });
            } else if (row.periods_type == 2) {
                this.$request.article
                    .getCrossDetail({
                        id: row.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = row.periods_type;
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.num = 1;
                            this.goodsinfo = res.data.data;
                            
                        }
                    });
            } else if (row.periods_type == 3) {
                this.$request.article
                    .getLeftoverDetail({
                        id: row.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = row.periods_type;
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.num = 1;
                            this.goodsinfo = res.data.data;
                            
                        }
                    });
            }
        },
        async  passBtnClick(){
            const mostwordsList = [
                    { text: this.goodsinfo.title, label: "商品标题" },
                    { text: this.goodsinfo.brief, label: "一句话介绍" },
                    { text: this.goodsinfo.detail, label: "商品详情" },
                ];
            const judgeMostwords = async (text, label) => {
                    try {
                        const res = await this.$request.article.judgeMostwords({
                            text,
                        });
                        const { found = [] } = res?.data || {};
                        return found.length
                            ? { label, desc: found.join("，") }
                            : null;
                    } catch (e) {
                        return null;
                    }
                };
                const judgeResList = await Promise.all(
                    mostwordsList
                        .filter(({ text }) => text)
                        .map(({ text, label }) => judgeMostwords(text, label))
                );
                const message = judgeResList
                    .filter((item) => item)
                    .map(({ label, desc }) => `<div>${label}：${desc}</div>`)
                    .join("");
                    console.log('message', message);
                    
                if (message) {
                    const confirm = () => {
                        return new Promise((resolve) => {
                            this.$confirm(`${message}`, "极限词提示", {
                                confirmButtonText: "去修改",
                                cancelButtonText: "继续提交",
                                type: "warning",
                                dangerouslyUseHTMLString: true,
                                distinguishCancelAndClose: true,
                            })
                                .then(() => {
                                    // resolve();
                                    this.handleClick(this.goodsinfo);
                                    this.GoodsDetailVisible = false;
                                })
                                .catch((action) => {
                                    if( action === 'cancel'){
                                        resolve();
                                    }
                                   
                                });
                        });
                    };
                    await confirm();
                }
               this.agreeAudit(this.goodsinfo, 2, '')
        },
        agreeAudit(rows, review_status) {
            if (review_status == 2) {
                this.$confirm("你确定审核通过吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        this.audit(this.goodsinfo, review_status);
                    })
                    .catch(() => {
                        //几点取消的提示
                    });
            }
        },
        audit(rows, review_status, reason) {
            const params = {
                period: rows.id,
                copywriting_review_status: review_status,
                reject_reason: reason,
                remark: this.entity.remark,
                periods_type: rows.periods_type,
                version: this.goodsinfo.version,
            };
            this.$request.article.copyWriterReview(params).then((res) => {
                if (res.data.error_code == 0) {
                    this.page = 1;
                    this.getData();
                    if (review_status == 3) {
                        this.dialogVisible1 = false;
                    }
                    this.GoodsDetailVisible = false;
                    this.$message.success("操作成功！");
                }
            });
        },
        HandleReject() {
            this.$confirm("你确定驳回吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.audit(this.goodsinfo, 3);
                })
                .catch(() => {
                    //几点取消的提示
                });
            // this.entity.reject_reason = "";
            // this.entity.remark = "";
            // this.dialogVisible1 = !this.dialogVisible1;
        },
        rejects() {
            if (this.validateForm()) {
                this.audit(this.goodsinfo, 3, this.entity.reject_reason);
            }
        },
        async handleView(row) {
            const data = {
                period: row.id,
            };
            const res = await this.$request.article.isPreView(data);
            if (res.data.error_code == 0) {
                if (res.data.data) {
                    this.GoodsDetailVisible = true;
                    this.isAudit = false;
                    this.goodsinfo = row;
                } else {
                    this.$message.error("预览文件正在渲染生成中，请稍后再试！");
                }
            }
        },
        handleStatusClick(rows) {
            this.$nextTick(() => {
                this.$refs.logList.openForm(rows);
            });
        },
        getPurchase() {
            this.$request.article
                .purchaseList({
                    type: 2,
                })
                .then((res) => {
                    this.buyerOptions = res.data.data.list;
                });
        },
        showRemark(row) {
            if (!row.content) {
                this.$request.article
                    .getReviewLog({
                        period: row.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.$set(row, "visible", true);
                            this.$set(row, "content", res.data.data.describe);
                            this.$set(row, "remark", res.data.data.remark);
                        }
                    });
            } else {
                row.visible = !row.visible;
            }
        },
        tableRowClassName(row) {
            switch (row.row.status) {
                case 0:
                    return "warning-row";
                case 1:
                    return "danger-row";
                case 2:
                    return "success-row";
                default:
                    return;
            }
        },
        showDialog(type) {
            if (type == 5) {
                this.rabbitcouponaddDialogStatus = true;
                this.$nextTick(() => {
                    this.$refs.rabbitcouponadd.openForm();
                });
                return;
            }
            // this.dialogVisible = true;
            this.createArticleVisible = true;

            this.btnVisible = false;
            this.$nextTick(() => {
                this.$refs.articleform.openForm(type);
            });
        },
        async getData() {
            let params = {
                page: this.query.page,
                limit: this.query.limit,
                periods_type: this.query.periods_type,
                buyer_id: this.query.buyer_id,
                periods: this.query.periods,
                creator_id: this.query.creator_id,
                title: this.query.title,
                short_code: this.query.short_code,
                not_merchant_second: 1,
            };
            console.log(this.query.buyer_review_status, 22323);
            if (this.query.buyer_review_status == 6) {
                params.copywriting_review_status = 0;
            } else if (this.query.buyer_review_status == 1) {
                params.copywriting_review_status = 1;
            } else if (this.query.buyer_review_status == 2) {
                params.copywriting_review_status = 2;
                params.buyer_review_status = 2;
            } else if (this.query.buyer_review_status == 3) {
                params.copywriting_review_status = 3;
            } else if (this.query.buyer_review_status == 4) {
                params.buyer_review_status = 3;
            } else if (this.query.buyer_review_status == 5) {
                params.buyer_review_status = 4;
            }
            console.log(params, 3333);
            await this.$request.article.articleList(params).then((res) => {
                if (res.data.error_code == 0) {
                    this.tableData = res.data.data.list || [];
                    this.total = res.data.data.total;
                    console.log(this.tableData, 555555555);
                }
            });
        },

        handleClick(row) {
            console.log();
            if (row.periods_type == 5) {
                this.rabbitcouponaddDialogStatus = true;
                this.$nextTick(() => {
                    this.$refs.rabbitcouponadd.openForm(row);
                });
            } else {
                let type = 0;
                if (row.periods_type == 0) {
                    type = 0;
                } else if (row.periods_type == 1) {
                    type = 20;
                } else if (row.periods_type == 2) {
                    type = 9;
                } else if (row.periods_type == 3) {
                    type = 22;
                } else if (row.periods_type == 4) {
                    type = 4;
                }
                this.createArticleVisible = true;
                this.$nextTick(() => {
                    this.$refs.articleform.openForm(type, row);
                });
            }
        },
        handleSub(row) {
            this.$confirm("你确定提交吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.$request.article
                        .updateStatus({
                            period: row.id,
                            periods_type: row.periods_type,
                            copywriting_review_status: 1,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.page = 1;
                                this.getData();
                                this.$message.success("操作成功！");
                            }
                        });
                })
                .catch(() => {
                    //几点取消的提示
                });
        },
        handleDel(row) {
            this.$confirm("你确定删除吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.$request.article
                        .articalDelete({
                            period: row.id,
                            periods_type: row.periods_type,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.page = 1;
                                this.getData();
                                this.$message.success("操作成功！");
                            }
                        });
                })
                .catch(() => {
                    //几点取消的提示
                });
        },
        validateForm() {
            let flag = null;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
        handleRemark(rows) {
            this.$nextTick(() => {
                this.$refs.remarklist.openForm(rows);
            });
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
        getChannelTagType(periods_type) {
            switch (periods_type) {
                case 0:
                    return 'success';
                case 1:
                    return 'warning';
                case 2:
                    return 'danger';
                case 3:
                    return 'info';
                case 4:
                    return 'primary';
                case 5:
                    return 'success';
                case 9:
                    return 'warning';
                default:
                    return '';
            }
        },
        getStatusTagType(row) {
            // 未提交
            if (row.copywriting_review_status == 0) {
                return 'info';
            }
            // 文案已提交
            else if (row.copywriting_review_status == 1) {
                return 'warning';
            }
            // 文案主管已通过
            else if (row.copywriting_review_status == 2) {
                if (row.buyer_review_status == 3) {
                    // 采购已通过
                    return 'success';
                } else if (row.buyer_review_status == 4) {
                    // 采购已驳回
                    return 'danger';
                }
                return 'success';
            }
            // 文案主管已驳回
            else if (row.copywriting_review_status == 3) {
                return 'danger';
            }
            // 采购已通过
            else if (row.buyer_review_status == 3) {
                return 'success';
            }
            // 采购已驳回
            else if (row.buyer_review_status == 4) {
                return 'danger';
            }
            return 'info';
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.article-layout {
    .article-main {
        .el-button--mini,
        .el-button--mini.is-round {
            padding: 0;
            background-color: #ffffff;
            border-color: #ffffff;
            color: #66b1ff;
        }

        margin: 10px 0;

        /deep/ .el-card {
            border: none;
            background: transparent;

            &.is-never-shadow {
                box-shadow: none;
            }
        }

        /deep/ .el-table {
            .warning-row {
                background: oldlace;
            }

            .danger-row {
                background: oldlace;
            }

            .success-row {
                background: #f0f9eb;
            }

            .el-table__fixed-right {
                height: 100% !important;
            }
        }

        /deep/ .table-cell-class {
            padding-top: 12px !important;
            padding-bottom: 12px !important;
        }

        /deep/ .el-card__body {
            padding: 0;
            border: none;
            
            .el-table__header-wrapper {
                width: 100%;
                
                table {
                    width: 100% !important;
                }
            }
        }

        /deep/ .title-cell {
            white-space: pre-wrap;
            word-break: break-all;
            line-height: 1.5;
        }
    }

    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}

:deep(.goods-detail-dialog) {
    .el-dialog__body {
        padding: 10px;
        overflow: hidden;
    }
    
    .el-dialog__header {
        padding: 10px;
    }
}
</style>
