<template>
    <div>
        <el-card shadow="hover">
            <!-- 高级查询 -->
            <el-form :inline="true" size="small" class="demo-form-inline">
                <el-form-item label="">
                    <el-input
                        v-model="query.period"
                        placeholder="请输入期数"
                        @keyup.enter.native="search"
                    />
                </el-form-item>
                <el-form-item label="">
                    <el-input
                        class="w-large"
                        v-model="query.short_code"
                        @keyup.enter.native="search"
                        placeholder="简码(多个简码用英文逗号隔开)"
                    />
                </el-form-item>

                <el-form-item>
                    <el-button type="warning" @click="search" size="mini"
                        >查询</el-button
                    >
                    <!-- <el-button type="primary" @click="isShowDialog" icon="el-icon-circle-plus-outline" v-action="'Comment_Add'">添加评论</el-button> -->
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover">
            <el-table
                stripe
                :data="DataList"
                size="mini"
                fit
                highlight-current-row
                style="width: 100%"
                @expand-change="expandChange"
            >
                <el-table-column type="expand">
                    <template slot-scope="props" class="orderExpand">
                        <el-form
                            label-position="left"
                            inline
                            class="demo-table-expand"
                        >
                            <el-table
                                :data="props.row.ChildDataList"
                                @expand-change="ChildExpandChange"
                                size="mini"
                                cell-class-name="flo"
                                fit
                                highlight-current-row
                                style="width: 90%; margin-left: 30px"
                            >
                                <el-table-column type="expand">
                                    <template
                                        slot-scope="props1"
                                        class="orderExpand"
                                    >
                                        <el-form
                                            label-position="left"
                                            inline
                                            class="demo-table-expand"
                                        >
                                            <div
                                                class="goods_comment_wrap"
                                                v-for="(v, i) in props1.row
                                                    .ChildDataList"
                                                :key="i"
                                            >
                                                <div class="goods_header_wrap">
                                                    <img
                                                        :src="v.avatar_image"
                                                    />
                                                    <div
                                                        class="goods_title_wrap"
                                                        :style="
                                                            v.reply &&
                                                            v.reply.length > 0
                                                                ? 'border-bottom: 1px solid #CCCCCC;'
                                                                : ''
                                                        "
                                                    >
                                                        <div
                                                            class="goods_name_wrap"
                                                        >
                                                            <div
                                                                style="
                                                                    width: 800px;
                                                                    display: flex;
                                                                "
                                                            >
                                                                <div>
                                                                    {{
                                                                        v.nickname
                                                                    }}
                                                                </div>
                                                                <div
                                                                    class="level_wrap"
                                                                >
                                                                    <div
                                                                        class="level"
                                                                    >
                                                                        {{
                                                                            v.user_level
                                                                        }}
                                                                    </div>
                                                                    <img
                                                                        src="../../static/imgs/level.png"
                                                                    />
                                                                </div>
                                                                <div
                                                                    style="
                                                                        width: auto;
                                                                    "
                                                                >
                                                                    {{
                                                                        v.created_time
                                                                    }}
                                                                </div>
                                                                <div
                                                                    class="isbuy"
                                                                    v-if="
                                                                        v.is_buy ==
                                                                        1
                                                                    "
                                                                >
                                                                    <img
                                                                        src="../../static/imgs/buy.png"
                                                                    />
                                                                </div>
                                                            </div>
                                                            <div
                                                                style="
                                                                    margin: -4px
                                                                        0 0 10px;
                                                                    display: flex;
                                                                    align-items: center;
                                                                "
                                                            >
                                                                <el-checkbox
                                                                    v-model="
                                                                        v.is_show
                                                                    "
                                                                    style="
                                                                        margin-bottom: 0;
                                                                    "
                                                                    @change="
                                                                        onchanges(
                                                                            v,
                                                                            1
                                                                        )
                                                                    "
                                                                    >显示</el-checkbox
                                                                >
                                                                <el-checkbox
                                                                    v-model="
                                                                        v.is_goods
                                                                    "
                                                                    style="
                                                                        margin-bottom: 0;
                                                                    "
                                                                    @change="
                                                                        onchanges(
                                                                            v,
                                                                            2
                                                                        )
                                                                    "
                                                                    >精选</el-checkbox
                                                                >
                                                                <div
                                                                    style="
                                                                        margin-left: 20px;
                                                                        display: flex;
                                                                    "
                                                                >
                                                                    <label
                                                                        class="click_label"
                                                                        >点赞数</label
                                                                    >
                                                                    <el-input
                                                                        size="mini"
                                                                        style="
                                                                            width: 70px;
                                                                        "
                                                                        v-model="
                                                                            v.likenums
                                                                        "
                                                                        @blur="
                                                                            onchanges(
                                                                                v,
                                                                                3
                                                                            )
                                                                        "
                                                                        class="p_inp"
                                                                    />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div
                                                            class="goods_comment_conetent"
                                                        >
                                                            {{ v.content }}
                                                            {{ v.emoji_image }}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div
                                                    class="goods_child_wrap"
                                                    v-if="v.reply"
                                                >
                                                    <div
                                                        class="goods_child_item"
                                                        v-for="(
                                                            k, i
                                                        ) in v.reply"
                                                        :key="i"
                                                    >
                                                        <div
                                                            class="goods_header_wrap"
                                                        >
                                                            <img
                                                                :src="
                                                                    k.avatar_image
                                                                "
                                                            />
                                                            <div
                                                                class="goods_title_wrap"
                                                            >
                                                                <div
                                                                    class="goods_name_wrap"
                                                                >
                                                                    <div
                                                                        style="
                                                                            width: 800px;
                                                                            display: flex;
                                                                        "
                                                                    >
                                                                        <div>
                                                                            {{
                                                                                k.nickname
                                                                            }}
                                                                        </div>
                                                                        <div
                                                                            class="level_wrap"
                                                                        >
                                                                            <div
                                                                                class="level"
                                                                            >
                                                                                {{
                                                                                    k.user_level
                                                                                }}
                                                                            </div>
                                                                            <img
                                                                                src="../../static/imgs/level.png"
                                                                            />
                                                                        </div>
                                                                        <div>
                                                                            {{
                                                                                k.created_time
                                                                            }}
                                                                        </div>
                                                                        <div
                                                                            class="isbuy"
                                                                            v-if="
                                                                                k.is_buy
                                                                            "
                                                                        >
                                                                            <img
                                                                                src="../../static/imgs/buy.png"
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        style="
                                                                            margin-left: 164px;
                                                                            display: flex;
                                                                        "
                                                                    >
                                                                        <label
                                                                            class="click_label"
                                                                            >点赞数</label
                                                                        >
                                                                        <el-input
                                                                            size="mini"
                                                                            style="
                                                                                width: 70px;
                                                                            "
                                                                            v-model="
                                                                                k.likenums
                                                                            "
                                                                            @blur="
                                                                                onchanges(
                                                                                    k,
                                                                                    3
                                                                                )
                                                                            "
                                                                            class="p_inp"
                                                                        />
                                                                    </div>
                                                                </div>
                                                                <div
                                                                    class="goods_comment_conetent"
                                                                >
                                                                    {{
                                                                        k.content
                                                                    }}
                                                                    {{
                                                                        k.emoji_image
                                                                    }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </el-form>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    label="期数"
                                    prop="period"
                                    width="80"
                                    align="center"
                                />
                                <el-table-column
                                    label="上架日期"
                                    prop="onsale_time"
                                    width="160"
                                    align="center"
                                />
                                <el-table-column
                                    label="商品名称"
                                    prop="title"
                                    align="center"
                                >
                                    <!--  <template slot-scope="{row}">
                    <a :href="detailurl+row.id" target="_blank">{{row.periods_type}}</a>
                  </template> -->
                                </el-table-column>
                                <el-table-column
                                    label="精选数量"
                                    prop="goods_count"
                                    width="80"
                                    align="center"
                                />
                                <el-table-column
                                    label="总评论数"
                                    prop="count"
                                    width="80"
                                    align="center"
                                />
                                <el-table-column
                                    label="已选评论"
                                    prop="selected_count"
                                    width="80"
                                    align="center"
                                />
                                <el-table-column
                                    align="center"
                                    label="销售数量"
                                    prop="sells_count"
                                    width="80"
                                />
                            </el-table>
                        </el-form>
                    </template>
                </el-table-column>
                <el-table-column
                    label="简码"
                    prop="short_code"
                    width="120"
                    align="center"
                />
                <el-table-column
                    label="品名"
                    prop="product_name"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        <div>{{ row.product_name }}</div>
                        <div>{{ row.en_product_name }}</div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="历史期数"
                    align="center"
                    prop="history_periods_count"
                    width="80"
                />
                <el-table-column label="条码" prop="bar_code" align="center" />
                <el-table-column
                    align="center"
                    label="总评论数"
                    prop="comment_count"
                    width="80"
                />
                <el-table-column
                    label="已选评论"
                    align="center"
                    prop="selected_count"
                    width="80"
                />
                <el-table-column
                    label="购买瓶数"
                    prop="sells_count"
                    width="80"
                    align="center"
                />
            </el-table>
        </el-card>
        <!-- 分页 -->
        <div class="page-center">
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="pageSize"
                :current-page="currentPage"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script>
export default {
    name: "commentpool",
    data() {
        return {
            currentPage: 1, // 当前页
            pageSize: 10, // 每页条数
            total: 0, // 总条数
            query: {
                short_code: "",
                period: "",
            },
            DataList: [],
            ChildDataList: [],
            ChildDataList1: [],
            dialogVisible: false,
            dialogVisible1: false,
            rows: {},
        };
    },
    methods: {
        onchanges(rows, type) {
            const params = {
                id: rows.id,
                likenums: rows.likenums,
                is_goods: rows.is_goods,
                is_show: rows.is_show,
            };
            this.$request.article.upcomment(params).then((res) => {
                if (res.data.error_code == 0) {
                    this.$message.success("操作成功！");
                }
            });
        },
        ChildExpandChange(rows, expandedRows) {
            if (expandedRows.length > 0) {
                const params = {
                    short_code: this.rows.short_code,
                    period: rows.period,
                };
                this.$request.article
                    .getCommentByPeriodAndShortCode(params)
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.map((item) => {
                                item.reply =
                                    typeof item.reply == "string"
                                        ? JSON.parse(item.reply)
                                        : item.reply;
                                item.is_goods =
                                    item.is_goods == 1 ? true : false;
                                item.is_show = item.is_show == 1 ? true : false;
                            });
                            rows.ChildDataList = res.data.data;
                            console.log(rows.ChildDataList);
                        }
                    });
            }
        },
        expandChange(rows, expandedRows) {
            this.rows = rows;
            console.log(rows, 222);
            console.log(expandedRows, 222);
            if (expandedRows.length > 0) {
                const params = {
                    short_code: rows.short_code,
                };
                this.$request.article
                    .getPeriodsByShortCode(params)
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.map((item) => {
                                item.ChildDataList = [];
                            });
                            rows.ChildDataList = res.data.data;
                        }
                    });
            }
        },
        // 获取数据
        async getData() {
            const params = {
                page: this.currentPage,
                limit: this.pageSize,
                short_code: this.query.short_code.trim(),
                period: this.query.period.trim(),
            };
            this.$request.article.getCommentPoolList(params).then((res) => {
                if (res.data.error_code == 0) {
                    res.data.data.list.map((item) => {
                        item.ChildDataList = [];
                    });
                    this.DataList = res.data.data.list || [];
                    this.total = res.data.data.total;
                }
            });
        },
        detail(row) {
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs["detailList"].getData(row.comment_id);
            });
        },
        async search() {
            this.currentPage = 1;
            this.getData();
        },
        dateformat(date) {
            var d = new Date(date);
            var date = d.getDate();
            var month = d.getMonth() + 1;
            var year = d.getFullYear();
            var date = year + "-" + month + "-" + date;
            return date;
        },
        // 编辑
        edit(row) {
            this.isShowDialog();
            this.id = row.id;
            this.$nextTick(function () {
                this.$refs["operation"].dataEcho(0, 1);
            });
        },
        async changeEnabled(row) {
            const { status, data, msg } = await this.$request(
                "/mall/comment/setBetter",
                {
                    comment_id: row.comment_id,
                    is_better: row.is_better ? 1 : 0,
                },
                2
            );
            if (status == "success") {
                this.$message.success("操作成功");
            } else {
                this.$message.error(msg);
            }
        },
        // 改变每页条数
        handleSizeChange(val) {
            this.pageSize = val;
            this.getData();
        },
        // 改变当前页
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getData();
        },
        // 清除查询
        clearSelect() {
            this.alllistsearch = {
                goods: "", // 闪购名称
                goodsid: "", // 闪购ID
                kind: "", // 类别
            };
            this.currentPage = 1;
            this.pageSize = 10; // 每页条数
            this.getData();
        },
    },
};
</script>

<style lang="scss" scoped>
.p_inp .el-input__inner {
    width: 100px;
    padding-left: 40px;
}
.demo-table-expand {
    font-size: 12px;
}

.goods_title_wrap {
    width: 100%;
}

.goods_comment_wrap {
    width: 80%;
    margin-left: 50px;
    margin-top: 20px;
    font-size: 12px !important;
}

.goods_header_wrap {
    display: flex;
}

.goods_header_wrap > img {
    width: 32px;
    height: 32px;
    margin-right: 22px;
    border-radius: 100%;
}

.goods_name_wrap {
    display: flex;
    justify-content: space-between;
}

.goods_name_wrap .level_wrap {
    position: relative;
    width: 58px;
    text-align: center;
}

.goods_name_wrap .level_wrap .level {
    position: absolute;
    top: 0;
    left: 36px;
    color: #ffffff;
}

.goods_name_wrap .level_wrap > img {
    margin-top: -2px;
}

.goods_name_wrap .isbuy > img {
    width: 44px;
    height: 18px;
    margin-top: -3px;
    margin-left: 5px;
}

.goods_comment_conetent {
    margin-top: 10px;
    width: 800px;
}
.click_label {
    margin-bottom: 0;
    display: flex;
    user-select: none;
    justify-content: center;
    align-items: center;
    width: 60px;
    font-size: 14px;
    font-weight: 500;
    color: #606266;
    font-family: "Open Sans", sans-serif;
}
/deep/ .el-input--mini .el-input__inner {
    text-align: center !important;
}
.goods_child_wrap {
    margin: 20px 0 0 80px;
}

.goods_child_item {
    margin-bottom: 20px;
}
.page-center {
    text-align: center;
}
</style>
