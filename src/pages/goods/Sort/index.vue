<template>
    <div class="goods-sort-container">
        <!-- 顶部操作栏 -->
        <div class="top-actions">
            <div class="action-buttons">
                <el-button
                    type="success"
                    size="small"
                    icon="el-icon-position"
                    @click="openPromoteDialog"
                    >投放</el-button
                >
                <el-button
                    type="primary"
                    size="small"
                    icon="el-icon-setting"
                    @click="openConfigDialog"
                    >配置权重</el-button
                >
                <el-button
                    type="info"
                    size="small"
                    icon="el-icon-refresh"
                    @click="refreshData"
                    :loading="refreshLoading"
                    >刷新</el-button
                >
                <div class="auto-refresh-container">
                    <el-checkbox
                        v-model="autoRefresh"
                        @change="handleAutoRefreshChange"
                        >自动刷新</el-checkbox
                    >
                    <el-select
                        v-model="refreshInterval"
                        size="small"
                        placeholder="选择频率"
                        :disabled="!autoRefresh"
                        @change="handleIntervalChange"
                    >
                        <el-option
                            v-for="item in refreshIntervalOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
            </div>
            <div class="coin-info" v-if="promotionCoins > 0">
                <span class="coin-label">投放币:</span>
                <span class="coin-value">{{ promotionCoins }}</span>
            </div>
        </div>
        <div class="content-wrapper">
            <!-- 商品列表 - 左侧 -->
            <div class="goods-list-container">
                <div class="goods-list" ref="goodsList">
                    <div
                        v-for="(item, index) in displayedGoods"
                        :key="index"
                        class="goods-item"
                        :data-item-id="item.id"
                    >
                        <!-- 商品图片 -->
                        <div class="goods-image">
                            <img :src="item.banner_img" alt="商品图片" />
                            <!-- 商品曝光值指示器 - 始终显示，初始值为0 -->
                            <div class="item-exposure-badge">
                                {{ item.score.toFixed(2) }}
                            </div>
                        </div>

                        <!-- 商品信息 -->
                        <div class="goods-info">
                            <!-- 商品标题和标签 -->
                            <div class="goods-header">
                                <h3 class="goods-title">{{ item.title }}</h3>
                            </div>

                            <!-- 商品描述 -->
                            <div class="goods-desc">
                                <p>{{ item.brief }}</p>
                            </div>

                            <!-- 商品底部价格信息 -->
                            <div class="goods-footer">
                                <div class="goods-price">
                                    <span class="price-symbol">¥</span>
                                    <span class="price-value">{{
                                        item.price
                                    }}</span>
                                </div>
                                <div class="goods-sales">
                                    已售{{ item.purchased
                                    }}<span v-if="item.limit_number">
                                        /限量{{ item.limit_number }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 加载更多指示器 -->
                    <div v-if="loading" class="loading-indicator">
                        <i class="el-icon-loading"></i> 加载中...
                    </div>

                    <!-- 无更多数据提示 -->
                    <div v-if="!hasMoreData && !loading" class="no-more-data">
                        没有更多商品了
                    </div>
                </div>
            </div>

            <!-- 已投放商品 - 右侧（表格形式） -->
            <div class="promoted-goods-container">
                <div class="promoted-header">
                    <h3>已投放商品</h3>
                </div>
                <el-table
                    v-loading="promotedLoading"
                    :data="promotedGoods"
                    style="width: 100%"
                    size="small"
                    border
                    stripe
                    class="promoted-table"
                >
                    <el-table-column label="商品" min-width="180">
                        <template slot-scope="scope">
                            <div class="promoted-product-cell">
                                <div class="promoted-image">
                                    <img
                                        :src="scope.row.banner_img"
                                        alt="商品图片"
                                    />
                                </div>
                                <div class="promoted-title-container">
                                    <div class="promoted-title">
                                        {{ scope.row.title }}
                                    </div>
                                    <div class="promoted-brief">
                                        {{ scope.row.brief }}
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="period"
                        label="期数"
                        width="70"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="sort"
                        label="排序值"
                        width="70"
                        align="center"
                    ></el-table-column>

                    <el-table-column label="价格" width="100" align="center">
                        <template slot-scope="scope">
                            <span class="promoted-price"
                                >¥{{ scope.row.price }}</span
                            >
                        </template>
                    </el-table-column>

                    <el-table-column label="曝光量" width="80" align="center">
                        <template slot-scope="scope">
                            <span class="promoted-score">{{
                                scope.row.score.toFixed(2)
                            }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="purchased"
                        label="已售"
                        width="70"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="end_time"
                        label="结束时间"
                        width="150"
                        align="center"
                    ></el-table-column>
                    <!-- 取消投放的按钮 -->
                    <el-table-column label="操作" width="110" align="center">
                        <template slot-scope="scope">
                            <el-button
                                type="danger"
                                size="mini"
                                @click="cancelPromotion(scope.row)"
                                :loading="scope.row.cancelLoading"
                            >
                                取消投放
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="pagination-container" v-if="promotedTotal > 0">
                    <el-pagination
                        @size-change="handlePromotedSizeChange"
                        @current-change="handlePromotedCurrentChange"
                        :current-page="promotedQuery.page"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="promotedQuery.limit"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="promotedTotal"
                    >
                    </el-pagination>
                </div>
            </div>
        </div>

        <!-- 配置权重弹窗 -->
        <el-dialog
            title="配置权重"
            :visible.sync="configDialogVisible"
            width="500px"
            :close-on-click-modal="false"
        >
            <div class="config-form">
                <el-form label-position="top" :model="config" size="small">
                    <el-form-item label="好评占比 (%)">
                        <el-slider
                            v-model="config.ratingRatio"
                            :max="100"
                            :min="0"
                            :step="1"
                            show-input
                            @change="adjustOtherRatios('ratingRatio')"
                        ></el-slider>
                    </el-form-item>
                    <el-form-item label="收藏占比 (%)">
                        <el-slider
                            v-model="config.favoriteRatio"
                            :max="100"
                            :min="0"
                            :step="1"
                            show-input
                            @change="adjustOtherRatios('favoriteRatio')"
                        ></el-slider>
                    </el-form-item>
                    <el-form-item label="点赞好评占比 (%)">
                        <el-slider
                            v-model="config.likeRatio"
                            :max="100"
                            :min="0"
                            :step="1"
                            show-input
                            @change="adjustOtherRatios('likeRatio')"
                        ></el-slider>
                    </el-form-item>
                </el-form>
                <div class="config-summary">
                    <el-progress
                        type="circle"
                        :percentage="config.ratingRatio"
                        color="#67c23a"
                    >
                        <template slot="format">
                            好评<br />{{ config.ratingRatio }}%
                        </template>
                    </el-progress>
                    <el-progress
                        type="circle"
                        :percentage="config.favoriteRatio"
                        color="#409eff"
                    >
                        <template slot="format">
                            收藏<br />{{ config.favoriteRatio }}%
                        </template>
                    </el-progress>
                    <el-progress
                        type="circle"
                        :percentage="config.likeRatio"
                        color="#e6a23c"
                    >
                        <template slot="format">
                            点赞<br />{{ config.likeRatio }}%
                        </template>
                    </el-progress>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="resetConfig">重置</el-button>
                <el-button type="primary" @click="saveConfigAndClose"
                    >保存配置</el-button
                >
            </span>
        </el-dialog>

        <!-- 投放弹窗 -->
        <el-dialog
            title="商品投放"
            :visible.sync="promoteDialogVisible"
            width="600px"
            :close-on-click-modal="false"
        >
            <div class="promote-form">
                <el-form label-position="top" :model="promoteForm" size="small">
                    <!-- 商品搜索/选择 -->
                    <el-form-item label="选择商品">
                        <el-input
                            v-model="searchKeyword"
                            placeholder="输入商品期数"
                            prefix-icon="el-icon-search"
                            clearable
                            @blur="searchGoods"
                            @keyup.enter.native="searchGoods"
                        ></el-input>

                        <div
                            class="search-results"
                            v-if="searchResults.length > 0"
                        >
                            <el-radio-group
                                v-model="promoteForm.selectedGoodId"
                            >
                                <div
                                    v-for="item in searchResults"
                                    :key="item.id"
                                    class="search-item"
                                >
                                    <el-radio :label="item.id">
                                        <div class="search-item-content">
                                            <div class="search-item-image">
                                                <img
                                                    :src="item.imageUrl"
                                                    alt="商品图片"
                                                />
                                            </div>
                                            <div class="search-item-info">
                                                <div class="search-item-title">
                                                    {{ item.title }}
                                                </div>
                                                <div class="search-item-price">
                                                    ¥{{ item.price }}
                                                </div>
                                                <!-- <div
                                                    class="search-item-position"
                                                    v-if="
                                                        getCurrentPositionIndex(
                                                            item.id
                                                        ) !== -1
                                                    "
                                                >
                                                    当前位置:
                                                    <span class="position-value"
                                                        >第{{
                                                            getCurrentPositionIndex(
                                                                item.id
                                                            ) + 1
                                                        }}位</span
                                                    >
                                                </div> -->
                                            </div>
                                        </div>
                                    </el-radio>
                                </div>
                            </el-radio-group>
                        </div>
                        <div
                            class="no-results"
                            v-else-if="searchKeyword && !loading"
                        >
                            未找到相关商品
                        </div>
                    </el-form-item>

                    <!-- 目标排序位置 -->

                    <!-- 曝光持续时间 -->
                    <el-form-item label="曝光持续时间">
                        <el-select
                            v-model="promoteForm.durationId"
                            placeholder="请选择曝光持续时间"
                            :disabled="!promoteForm.selectedGoodId"
                            @change="handleDurationChange"
                        >
                            <el-option
                                v-for="item in sortConfigs"
                                :key="item.id"
                                :label="formatDuration(item.duration)"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="目标排序位置">
                        <el-select
                            v-model="promoteForm.targetPosition"
                            placeholder="请选择目标排序位置"
                            :disabled="
                                !promoteForm.selectedGoodId ||
                                !promoteForm.duration
                            "
                        >
                            <el-option
                                v-for="position in availablePositions"
                                :key="position"
                                :label="`第${position}位`"
                                :value="position"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- 投放币信息 - 电商余额样式 -->
                    <el-form-item label="投放币信息">
                        <div class="coin-balance-container">
                            <div class="coin-balance">
                                <div class="balance-label">可用投放币</div>
                                <div class="balance-value">
                                    {{ promotionCoins }}
                                </div>
                            </div>
                            <div class="coin-divider"></div>
                            <div class="coin-consumption">
                                <div class="consumption-label">消耗投放币</div>
                                <div class="consumption-value">
                                    {{ calculateCoinConsumption() }}
                                </div>
                            </div>
                            <div class="coin-divider"></div>
                            <div class="coin-remain">
                                <div class="remain-label">剩余投放币</div>
                                <div class="remain-value">
                                    {{
                                        promotionCoins -
                                        calculateCoinConsumption()
                                    }}
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="promoteDialogVisible = false"
                    >取消</el-button
                >
                <el-button
                    type="primary"
                    @click="submitPromotion"
                    :disabled="!canPromote"
                >
                    确认投放
                </el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "GoodsSort",
    data() {
        return {
            allGoods: [],
            displayedGoods: [],
            currentPage: 1,
            pageSize: 4,
            total: 0,
            loading: false,
            hasMoreData: true,
            exposureCount: 0,
            exposedItems: new Set(),
            itemExposureCounts: {},
            observer: null,
            config: {
                ratingRatio: 10,
                favoriteRatio: 50,
                likeRatio: 40,
            },
            defaultConfig: {
                ratingRatio: 10,
                favoriteRatio: 50,
                likeRatio: 40,
            },
            configDialogVisible: false,
            promoteDialogVisible: false,
            promotionCoins: 0, // 投放币余额
            searchKeyword: "",
            searchResults: [],
            promoteForm: {
                selectedGoodId: null,
                targetPosition: 1,
                durationId: null, // 曝光持续时间ID
                duration: 0, // 曝光持续时间（分钟）
            },
            sortConfigs: [], // 投放配置
            availablePositions: [], // 可选的排序位置
            // 已投放商品列表
            promotedGoods: [],
            promotedTotal: 0,
            promotedLoading: false,
            promotedQuery: {
                status: 1, // 0:所有商品 1:在投商品
                page: 1,
                limit: 10,
            },
            refreshLoading: false, // 刷新按钮加载状态
            autoRefresh: false, // 是否自动刷新
            refreshInterval: 5, // 刷新间隔（分钟）
            refreshTimer: null, // 定时器ID
            refreshIntervalOptions: [
                { value: 5, label: "5分钟" },
                { value: 10, label: "10分钟" },
                { value: 20, label: "20分钟" },
                { value: 30, label: "30分钟" },
            ],
        };
    },
    created() {
        // Initialize with page 1
        this.currentPage = 1;
    },
    mounted() {
        // 从本地存储加载自动刷新设置
        this.loadRefreshSettings();

        // Load initial data from API
        this.getProductsByPage();
        this.setupIntersectionObserver();

        // 获取已投放商品列表
        this.getPromotedGoods();

        // 获取投放配置
        this.getSortConfig();

        // 获取投放币余额
        this.getUserLimit();

        // Add scroll event listener to handle pagination
        this.$nextTick(() => {
            if (this.$refs.goodsList) {
                this.$refs.goodsList.addEventListener(
                    "scroll",
                    this.handleScroll
                );
            }
        });
    },
    beforeDestroy() {
        if (this.observer) {
            this.observer.disconnect();
        }

        // Remove scroll event listener
        if (this.$refs.goodsList) {
            this.$refs.goodsList.removeEventListener(
                "scroll",
                this.handleScroll
            );
        }

        // Clear auto-refresh timer if it exists
        this.clearRefreshTimer();
    },
    computed: {
        canPromote() {
            // 检查是否选择了商品且投放币足够
            return (
                this.promoteForm.selectedGoodId &&
                this.calculateCoinConsumption() <= this.promotionCoins
            );
        },
    },
    methods: {
        async getProductsByPage() {
            if (this.loading) return;

            this.loading = true;
            const data = {
                page: this.currentPage,
                limit: this.pageSize,
            };
            try {
                const res = await this.$request.purchase.getProductsByPage(
                    data
                );
                if (res.data.error_code == 0) {
                    const newItems = res.data.data.list;
                    this.total = res.data.data.total;

                    if (this.currentPage === 1) {
                        // First page - replace all items
                        this.allGoods = newItems;
                        this.displayedGoods = [...newItems];
                    } else {
                        // Append new items to existing list
                        this.allGoods = [...this.allGoods, ...newItems];
                        this.displayedGoods = [
                            ...this.displayedGoods,
                            ...newItems,
                        ];
                    }

                    // Check if we have more data
                    this.hasMoreData = this.displayedGoods.length < this.total;

                    // Setup intersection observer for new items
                    this.$nextTick(() => {
                        this.observeItems();
                    });
                }
            } catch (error) {
                console.error("Error fetching products:", error);
            } finally {
                this.loading = false;
            }
        },
        // Method removed as we're now loading directly from API
        setupIntersectionObserver() {
            if ("IntersectionObserver" in window) {
                this.observer = new IntersectionObserver(
                    (entries) => {
                        entries.forEach((entry) => {
                            if (entry.isIntersecting) {
                                const itemId = entry.target.dataset.itemId;
                                if (itemId) {
                                    const item = this.displayedGoods.find(
                                        (item) => item.id.toString() === itemId
                                    );
                                    if (item) {
                                        this.increaseExposure(item);
                                    }
                                }
                            }
                        });
                    },
                    {
                        root: this.$refs.goodsList,
                        threshold: 0.5,
                    }
                );

                this.$nextTick(() => {
                    this.observeItems();
                });
            }
        },
        observeItems() {
            const itemElements = this.$el.querySelectorAll(".goods-item");
            itemElements.forEach((el) => {
                if (this.observer) {
                    this.observer.observe(el);
                }
            });
        },
        handleScroll() {
            const element = this.$refs.goodsList;
            if (!element) return;

            const isBottom =
                element.scrollHeight - element.scrollTop <=
                element.clientHeight + 100;

            if (isBottom && !this.loading && this.hasMoreData) {
                // Increment page number and load next page
                this.currentPage++;
                this.getProductsByPage();
            }
        },
        // Method removed as we're now loading directly from API with getProductsByPage
        increaseExposure(item) {
            console.log("增加曝光:", item.id);
            if (!this.exposedItems.has(item.id)) {
                this.exposedItems.add(item.id);
                this.exposureCount++;
                this.$set(this.itemExposureCounts, item.id, 1);
            } else {
                const newCount = (this.itemExposureCounts[item.id] || 0) + 1;
                this.$set(this.itemExposureCounts, item.id, newCount);
            }
            this.$forceUpdate();
        },
        getItemExposureCount(item) {
            return this.itemExposureCounts[item.id] || 0;
        },
        adjustOtherRatios(changedRatio) {
            const otherRatios = Object.keys(this.config).filter(
                (key) => key !== changedRatio
            );

            const currentTotal = Object.values(this.config).reduce(
                (sum, val) => sum + val,
                0
            );

            if (currentTotal > 100) {
                const excess = currentTotal - 100;
                const otherTotal =
                    this.config[otherRatios[0]] + this.config[otherRatios[1]];

                if (otherTotal > 0) {
                    const ratio1 = this.config[otherRatios[0]] / otherTotal;
                    const ratio2 = this.config[otherRatios[1]] / otherTotal;

                    this.config[otherRatios[0]] = Math.max(
                        0,
                        Math.floor(
                            this.config[otherRatios[0]] - excess * ratio1
                        )
                    );
                    this.config[otherRatios[1]] = Math.max(
                        0,
                        Math.floor(
                            this.config[otherRatios[1]] - excess * ratio2
                        )
                    );

                    const newTotal = Object.values(this.config).reduce(
                        (sum, val) => sum + val,
                        0
                    );
                    if (newTotal < 100) {
                        this.config[otherRatios[0]] += 100 - newTotal;
                    } else if (newTotal > 100) {
                        this.config[otherRatios[0]] = Math.max(
                            0,
                            this.config[otherRatios[0]] - (newTotal - 100)
                        );
                    }
                } else {
                    this.config[changedRatio] = 100;
                }
            }
        },
        openConfigDialog() {
            this.$message.info("暂未开放");
            return;
            this.configDialogVisible = true;
        },
        openPromoteDialog() {
            this.promoteDialogVisible = true;
            this.searchKeyword = "";
            this.searchResults = [];
            this.promoteForm = {
                selectedGoodId: null,
                targetPosition: 1,
                durationId: null,
                duration: 0,
            };
            this.availablePositions = [];
        },

        // 获取投放配置
        async getSortConfig() {
            try {
                const res = await this.$request.purchase.getSortConfig();
                if (res.data.error_code === 0) {
                    this.sortConfigs = res.data.data.list || [];
                }
            } catch (error) {
                console.error("获取投放配置失败:", error);
                this.$message.error("获取投放配置失败");
            }
        },

        // 格式化持续时间
        formatDuration(minutes) {
            if (minutes < 60) {
                return `${minutes}分钟`;
            } else {
                const hours = Math.floor(minutes / 60);
                const remainingMinutes = minutes % 60;
                if (remainingMinutes === 0) {
                    return `${hours}小时`;
                } else {
                    return `${hours}小时${remainingMinutes}分钟`;
                }
            }
        },

        // 处理持续时间变化
        handleDurationChange() {
            const selectedConfig = this.sortConfigs.find(
                (item) => item.id === this.promoteForm.durationId
            );
            if (selectedConfig) {
                this.promoteForm.duration = selectedConfig.duration;
                // 更新可选的排序位置
                this.availablePositions = Object.keys(selectedConfig.rule)
                    .map(Number)
                    .sort((a, b) => a - b);
                // 如果当前选择的位置不在可选范围内，重置为第一个可选位置
                if (
                    !this.availablePositions.includes(
                        this.promoteForm.targetPosition
                    )
                ) {
                    this.promoteForm.targetPosition =
                        this.availablePositions[0] || 1;
                }
            } else {
                this.promoteForm.duration = 0;
                this.availablePositions = [];
            }
        },
        async searchGoods() {
            if (!this.searchKeyword.trim()) {
                this.searchResults = [];
                return;
            }

            this.loading = true;
            try {
                const params = {
                    periods_type: 0, // 期数频道
                    is_leftover: 0, // 不显示尾货
                    periods: this.searchKeyword.trim(), // 搜索期数
                    page: 1,
                    limit: 10,
                };

                const res = await this.$request.purchase.getPeriodsList(params);
                if (res.data.error_code === 0) {
                    // 将API返回的数据转换为我们需要的格式
                    this.searchResults = (res.data.data.list || []).map(
                        (item) => ({
                            id: item.id,
                            title: item.title,
                            description: item.brief,
                            imageUrl: item.banner_img,
                            price: item.price,
                            soldCount: item.purchased || 0,
                            totalCount: item.inventory || 0,
                        })
                    );
                }
            } catch (error) {
                console.error("搜索期数失败:", error);
                this.$message.error("搜索期数失败");
                this.searchResults = [];
            } finally {
                this.loading = false;
            }
        },
        getCurrentPositionIndex(goodId) {
            return this.displayedGoods.findIndex((item) => item.id === goodId);
        },
        getMaxTargetPosition() {
            if (!this.promoteForm.selectedGoodId)
                return this.displayedGoods.length;

            const currentIndex = this.getCurrentPositionIndex(
                this.promoteForm.selectedGoodId
            );
            if (currentIndex === -1) return this.displayedGoods.length;

            // 如果当前已经在第一位，则不能再往前移动
            // 目标位置的最大值应该是当前位置-1
            return currentIndex === 0 ? 1 : currentIndex - 1;
        },
        calculateCoinConsumption() {
            if (
                !this.promoteForm.selectedGoodId ||
                !this.promoteForm.durationId ||
                !this.promoteForm.targetPosition
            )
                return 0;

            // 从配置中获取消耗规则
            const selectedConfig = this.sortConfigs.find(
                (item) => item.id === this.promoteForm.durationId
            );
            if (!selectedConfig || !selectedConfig.rule) return 0;

            // 从规则中获取对应位置的消耗
            const positionCost =
                selectedConfig.rule[this.promoteForm.targetPosition];
            if (positionCost === undefined) return 0;

            return positionCost;
        },
        async submitPromotion() {
            if (!this.canPromote) return;

            // 显示确认对话框
            try {
                // 获取选中商品的信息
                const selectedGood = this.searchResults.find(
                    (item) => item.id === this.promoteForm.selectedGoodId
                );
                const goodTitle = selectedGood ? selectedGood.title : "商品";
                const duration = this.formatDuration(this.promoteForm.duration);
                const position = this.promoteForm.targetPosition;
                const consumption = this.calculateCoinConsumption();

                await this.$confirm(
                    `您将投放商品"${goodTitle}"到第${position}位，持续${duration}，消耗${consumption}投放币。确定要投放吗？`,
                    "投放确认",
                    {
                        confirmButtonText: "确定投放",
                        cancelButtonText: "取消",
                        type: "warning",
                    }
                );
            } catch (error) {
                // 用户取消操作
                return;
            }

            this.$set(this, "loading", true);

            try {
                // 调用投放接口
                const params = {
                    period: this.promoteForm.selectedGoodId, // 商品期数
                    sort: this.promoteForm.targetPosition, // 排序值
                    config_id: this.promoteForm.durationId, // 配置ID
                };

                const res = await this.$request.purchase.addSort(params);

                if (res.data.error_code === 0) {
                    // 投放成功，关闭弹窗
                    this.promoteDialogVisible = false;

                    // 刷新左侧商品列表
                    this.currentPage = 1; // 重置到第一页
                    await this.getProductsByPage();

                    // 刷新右侧已投放商品列表
                    await this.getPromotedGoods();

                    // 更新投放币余额
                    await this.getUserLimit();

                    // 成功提示
                    this.$message.success(
                        `商品投放成功，将持续${this.formatDuration(
                            this.promoteForm.duration
                        )}`
                    );
                }
            } catch (error) {
                console.error("投放失败:", error);
                this.$message.error("投放失败，请重试");
            } finally {
                this.$set(this, "loading", false);
            }
        },
        saveConfig() {
            const total =
                this.config.ratingRatio +
                this.config.favoriteRatio +
                this.config.likeRatio;

            if (total !== 100) {
                this.adjustAllRatiosToTotal();
                this.$message.info("已自动调整配置以确保总和为100%");
            }

            this.$message.success("配置保存成功");
            this.defaultConfig = { ...this.config };
        },
        saveConfigAndClose() {
            this.saveConfig();
            this.configDialogVisible = false;
        },
        adjustAllRatiosToTotal() {
            const total = Object.values(this.config).reduce(
                (sum, val) => sum + val,
                0
            );
            if (total === 0) {
                this.config.ratingRatio = 33;
                this.config.favoriteRatio = 33;
                this.config.likeRatio = 34;
                return;
            }

            const scale = 100 / total;
            this.config.ratingRatio = Math.floor(
                this.config.ratingRatio * scale
            );
            this.config.favoriteRatio = Math.floor(
                this.config.favoriteRatio * scale
            );
            this.config.likeRatio = Math.floor(this.config.likeRatio * scale);

            const newTotal =
                this.config.ratingRatio +
                this.config.favoriteRatio +
                this.config.likeRatio;
            if (newTotal < 100) {
                const maxRatio = Object.keys(this.config).reduce((a, b) =>
                    this.config[a] > this.config[b] ? a : b
                );
                this.config[maxRatio] += 100 - newTotal;
            }
        },
        resetConfig() {
            this.config = { ...this.defaultConfig };
            this.$message.info("配置已重置");
        },

        // 获取已投放商品列表
        async getPromotedGoods() {
            this.promotedLoading = true;
            try {
                const res = await this.$request.purchase.getPeriodsSortList(
                    this.promotedQuery
                );
                if (res.data.error_code === 0) {
                    this.promotedGoods = res.data.data.list || [];
                    this.promotedTotal = res.data.data.total || 0;
                }
            } catch (error) {
                console.error("获取已投放商品列表失败:", error);
                this.$message.error("获取已投放商品列表失败");
            } finally {
                this.promotedLoading = false;
            }
        },

        // 处理已投放商品分页大小变化
        handlePromotedSizeChange(val) {
            this.promotedQuery.limit = val;
            this.promotedQuery.page = 1;
            this.getPromotedGoods();
        },

        // 处理已投放商品分页页码变化
        handlePromotedCurrentChange(val) {
            this.promotedQuery.page = val;
            this.getPromotedGoods();
        },

        // 获取投放币余额
        async getUserLimit() {
            try {
                const res = await this.$request.purchase.getUserLimit();
                if (res.data.error_code === 0) {
                    this.promotionCoins = res.data.data.limit || 0;
                }
            } catch (error) {
                console.error("获取投放币余额失败:", error);
                this.$message.error("获取投放币余额失败");
            }
        },

        // 保存刷新设置到本地存储
        saveRefreshSettings() {
            try {
                const settings = {
                    autoRefresh: this.autoRefresh,
                    refreshInterval: this.refreshInterval,
                };
                localStorage.setItem(
                    "goodsSortRefreshSettings",
                    JSON.stringify(settings)
                );
                console.log("刷新设置已保存到本地存储");
            } catch (error) {
                console.error("保存刷新设置失败:", error);
            }
        },

        // 从本地存储加载刷新设置
        loadRefreshSettings() {
            try {
                const settingsStr = localStorage.getItem(
                    "goodsSortRefreshSettings"
                );
                if (settingsStr) {
                    const settings = JSON.parse(settingsStr);
                    this.autoRefresh = settings.autoRefresh || false;
                    this.refreshInterval = settings.refreshInterval || 5;

                    // 如果自动刷新开启，启动定时器
                    if (this.autoRefresh) {
                        this.$nextTick(() => {
                            this.startRefreshTimer();
                        });
                    }
                    console.log("已从本地存储加载刷新设置");
                }
            } catch (error) {
                console.error("加载刷新设置失败:", error);
                // 出错时使用默认设置
                this.autoRefresh = false;
                this.refreshInterval = 5;
            }
        },

        // 处理自动刷新开关变化
        handleAutoRefreshChange(value) {
            if (value) {
                this.startRefreshTimer();
            } else {
                this.clearRefreshTimer();
            }

            // 保存设置到本地存储
            this.saveRefreshSettings();
        },

        // 处理刷新间隔变化
        handleIntervalChange() {
            if (this.autoRefresh) {
                // 重新启动定时器以应用新的间隔
                this.clearRefreshTimer();
                this.startRefreshTimer();
            }

            // 保存设置到本地存储
            this.saveRefreshSettings();
        },

        // 启动刷新定时器
        startRefreshTimer() {
            this.clearRefreshTimer(); // 先清除现有定时器

            // 将分钟转换为毫秒
            const intervalMs = this.refreshInterval * 60 * 1000;

            this.refreshTimer = setInterval(() => {
                this.refreshData(false); // 不显示成功提示
            }, intervalMs);

            console.log(`已启动自动刷新，间隔: ${this.refreshInterval} 分钟`);
        },

        // 清除刷新定时器
        clearRefreshTimer() {
            if (this.refreshTimer) {
                clearInterval(this.refreshTimer);
                this.refreshTimer = null;
                console.log("已停止自动刷新");
            }
        },

        // 刷新数据
        async refreshData(showMessage = true) {
            if (this.refreshLoading) return;

            this.refreshLoading = true;
            try {
                // 重置左侧商品列表到第一页
                this.currentPage = 1;
                this.promotedQuery.page = 1;
                await this.getProductsByPage();

                // 刷新右侧已投放商品列表
                await this.getPromotedGoods();

                // 更新投放币余额
                await this.getUserLimit();

                if (showMessage) {
                    this.$message.success("刷新成功");
                }
            } catch (error) {
                console.error("刷新数据失败:", error);
                if (showMessage) {
                    this.$message.error("刷新数据失败");
                }
            } finally {
                this.refreshLoading = false;
            }
        },

        // 取消投放
        async cancelPromotion(item) {
            if (!item || !item.id) {
                this.$message.error("无效的投放记录");
                return;
            }

            // 显示确认对话框
            try {
                await this.$confirm("确定要取消该商品的投放吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                });
            } catch (error) {
                // 用户取消操作
                return;
            }

            // 设置加载状态
            this.$set(item, "cancelLoading", true);

            try {
                // 调用取消投放接口
                const params = {
                    id: item.id, // 去除可能的前缀
                };

                const res = await this.$request.purchase.cancelSort(params);

                if (res.data.error_code === 0) {
                    // 取消投放成功，更新投放币余额
                    await this.getUserLimit();

                    // 刷新已投放商品列表
                    this.getPromotedGoods();

                    this.$message.success("已成功取消投放");
                }
            } catch (error) {
                console.error("取消投放失败:", error);
                this.$message.error("取消投放失败，请重试");
            } finally {
                this.$set(item, "cancelLoading", false);
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.goods-sort-container {
    /* 投放弹窗样式 */
    .promote-form {
        .search-results {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
            border: 1px solid #ebeef5;
            border-radius: 4px;

            .search-item {
                padding: 10px;
                border-bottom: 1px solid #ebeef5;

                &:last-child {
                    border-bottom: none;
                }

                .search-item-content {
                    display: flex;
                    align-items: center;

                    .search-item-image {
                        width: 60px;
                        height: 60px;
                        margin-right: 10px;
                        overflow: hidden;
                        border-radius: 4px;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }

                    .search-item-info {
                        flex: 1;

                        .search-item-title {
                            font-size: 14px;
                            font-weight: 500;
                            color: #333;
                            margin-bottom: 5px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        .search-item-price {
                            font-size: 14px;
                            color: #ff6600;
                            font-weight: bold;
                            margin-bottom: 5px;
                        }

                        .search-item-position {
                            font-size: 12px;
                            color: #666;
                            margin-top: 3px;
                            background-color: #f0f9ff;
                            padding: 3px 8px;
                            border-radius: 4px;
                            display: inline-block;

                            .position-value {
                                color: #409eff;
                                font-weight: 600;
                            }
                        }
                    }
                }
            }
        }

        .no-results {
            text-align: center;
            padding: 20px;
            color: #909399;
            font-size: 14px;
        }

        .coin-balance-container {
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

            .coin-balance,
            .coin-consumption,
            .coin-remain {
                flex: 1;
                text-align: center;

                .balance-label,
                .consumption-label,
                .remain-label {
                    font-size: 12px;
                    color: #909399;
                    margin-bottom: 5px;
                }

                .balance-value {
                    font-size: 20px;
                    font-weight: bold;
                    color: #67c23a;
                }

                .consumption-value {
                    font-size: 20px;
                    font-weight: bold;
                    color: #f56c6c;
                }

                .remain-value {
                    font-size: 20px;
                    font-weight: bold;
                    color: #409eff;
                }
            }

            .coin-divider {
                width: 1px;
                height: 40px;
                background-color: #dcdfe6;
            }
        }
    }

    padding: 20px;

    .top-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .action-buttons {
            display: flex;
            gap: 10px;
            align-items: center;

            .auto-refresh-container {
                display: flex;
                align-items: center;
                margin-left: 15px;
                background-color: #f8f8f8;
                padding: 5px 10px;
                border-radius: 4px;

                .el-checkbox {
                    margin-right: 10px;
                }
                /deep/ label {
                    margin: 0 !important;
                }
                .el-select {
                    margin: 10px;
                    width: 100px;
                }
            }
        }

        .coin-info {
            display: flex;
            align-items: center;
            background-color: #f8f8f8;
            padding: 5px 12px;
            border-radius: 20px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

            .coin-label {
                font-size: 14px;
                color: #666;
                margin-right: 5px;
            }

            .coin-value {
                font-size: 16px;
                font-weight: bold;
                color: #ff9900;
            }
        }
    }

    .content-wrapper {
        display: flex;
        flex-direction: column;
        gap: 20px;

        @media screen and (min-width: 768px) {
            flex-direction: row;
            justify-content: space-between;
        }
    }

    .goods-list-container {
        width: 100%;
        max-width: 375px; /* 标准H5宽度 */
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        height: calc(100vh - 80px);
        border-radius: 20px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative;
        background-color: #f8f8f8;

        /* 手机顶部状态栏 */
        &::before {
            content: "";
            display: block;
            height: 25px;
            background-color: #fff;
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;
            border-bottom: 1px solid #eee;
        }

        /* 手机底部导航条 */
        &::after {
            content: "";
            display: block;
            height: 5px;
            width: 40%;
            background-color: #ddd;
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 5px;
            z-index: 10;
        }

        @media screen and (min-width: 768px) {
            flex: 0 0 375px; /* 固定宽度 */
            margin: 0;
        }

        .goods-list {
            flex: 1;
            overflow-y: auto;
            padding: 30px 10px 40px;
            margin-right: -5px;
            scroll-behavior: smooth;

            /* 滚动条样式 */
            &::-webkit-scrollbar {
                width: 4px;
            }

            &::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 4px;
            }

            &::-webkit-scrollbar-thumb {
                background: #ddd;
                border-radius: 4px;
            }

            &::-webkit-scrollbar-thumb:hover {
                background: #ccc;
            }
        }

        /* 移除曝光值显示样式 */
        .exposure-counter {
            display: none;
        }
    }

    /* 已投放商品区域样式 - 表格形式 */
    .promoted-goods-container {
        width: 100%;
        flex: 1;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        padding: 15px;
        margin-top: 20px;

        @media screen and (min-width: 768px) {
            margin-top: 0;
            height: calc(100vh - 80px);
            overflow-y: auto;
            margin-left: 20px;
        }

        .promoted-header {
            margin-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            h3 {
                font-size: 16px;
                color: #333;
                margin: 0;
                font-weight: 600;
            }

            .promoted-header-actions {
                display: flex;
                gap: 10px;
            }

            .search-container {
                width: 200px;
            }
        }

        .pagination-container {
            margin-top: 15px;
            display: flex;
            justify-content: flex-end;
        }

        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;

            &.status-active {
                background-color: #67c23a;
                color: white;
            }

            &.status-inactive {
                background-color: #909399;
                color: white;
            }
        }

        .promoted-table {
            .el-table__header th {
                background-color: #f5f7fa;
                color: #606266;
                font-weight: 600;
                padding: 8px 0;
            }

            .el-table__row {
                transition: background-color 0.2s;

                &:hover {
                    background-color: #f0f9ff !important;
                }
            }
        }

        .promoted-product-cell {
            display: flex;
            align-items: center;

            .promoted-image {
                width: 50px;
                height: 50px;
                flex-shrink: 0;
                margin-right: 10px;
                border-radius: 4px;
                overflow: hidden;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .promoted-title-container {
                display: flex;
                flex-direction: column;

                .promoted-title {
                    font-size: 13px;
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 4px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .promoted-brief {
                    font-size: 12px;
                    color: #666;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }

        .promoted-price {
            font-size: 14px;
            font-weight: 600;
            color: #ff6600;
        }

        .no-expiry {
            font-size: 12px;
            color: #999;
        }

        .promoted-score {
            font-size: 14px;
            font-weight: 600;
            color: #409eff;
        }
    }

    .config-form {
        .config-summary {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;

            .el-progress {
                margin-bottom: 15px;
            }
        }
    }

    .goods-list {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .goods-item {
        position: relative;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
        width: 100%;
        max-width: 355px; /* 适合容器的宽度 */
        margin: 0 auto 0px; /* 减少商品之间的间距 */
        transition: transform 0.3s, box-shadow 0.3s;

        &:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.12);
        }

        .goods-image {
            width: 100%;
            height: 160px; /* 减小图片高度，使页面能够显示更多商品 */
            overflow: hidden;
            position: relative;

            &::after {
                content: "";
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 30px;
                background: linear-gradient(
                    to top,
                    rgba(0, 0, 0, 0.15),
                    transparent
                );
                pointer-events: none;
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.5s;
                border-radius: 4px;

                &:hover {
                    transform: scale(1.05);
                }
            }

            /* 商品曝光标识 - 优化样式 */
            .item-exposure-badge {
                position: absolute;
                top: 8px;
                right: 8px;
                min-width: 22px;
                height: 22px;
                line-height: 22px;
                text-align: center;
                background-color: rgba(255, 72, 0, 0.9);
                color: #fff;
                font-size: 12px;
                font-weight: bold;
                padding: 0 6px;
                border-radius: 11px;
                z-index: 100;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }
        }

        .goods-info {
            padding: 12px; /* 减少内边距 */
            display: flex;
            flex-direction: column;

            .goods-header {
                margin-bottom: 6px; /* 减少间距 */

                .goods-tags {
                    margin-bottom: 6px;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 5px;

                    .tag-item {
                        display: inline-block;
                        padding: 2px 8px;
                        background-color: #ff9900;
                        color: #fff;
                        font-size: 12px;
                        border-radius: 20px;
                        letter-spacing: 0.5px;
                    }
                }

                .goods-title {
                    margin: 0;
                    font-size: 15px; /* 稍微减小字体 */
                    font-weight: 600;
                    color: #333;
                    line-height: 1.3;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }
            }

            .goods-desc {
                margin-bottom: 6px;
                font-size: 13px; /* 减小字体 */
                color: #666;
                line-height: 1.4;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2; /* 减少显示行数 */
                -webkit-box-orient: vertical;
                overflow: hidden;
                p {
                    margin-bottom: 0;
                }
            }

            .goods-features {
                display: flex;
                flex-wrap: wrap;
                gap: 6px;
                margin-bottom: 8px;

                .feature-tag {
                    padding: 2px 8px;
                    background-color: #f5f5f5;
                    color: #666;
                    font-size: 11px; /* 减小字体 */
                    border-radius: 20px;
                }
            }

            .goods-footer {
                margin-top: 6px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-top: 6px;
                border-top: 1px solid #f5f5f5;

                .goods-price {
                    color: #ff6600;

                    .price-symbol {
                        font-size: 13px;
                    }

                    .price-value {
                        font-size: 20px; /* 减小字体 */
                        font-weight: 700;
                    }
                }

                .goods-sales {
                    font-size: 11px;
                    color: #999;
                    background-color: #f9f9f9;
                    padding: 2px 6px;
                    border-radius: 20px;
                }
            }
        }
    }

    .loading-indicator {
        text-align: center;
        padding: 15px 0;
        color: #999;
        font-size: 14px;
    }

    .no-more-data {
        text-align: center;
        padding: 15px 0;
        color: #999;
        font-size: 14px;
    }
}
</style>
