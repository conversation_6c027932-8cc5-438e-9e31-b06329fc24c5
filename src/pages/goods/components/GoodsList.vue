<template>
    <!-- 非全部商品列表 -->
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <div class="box_flex box_wrap">
                    <div>
                        <el-input
                            v-model="query.periods"
                            class="w-mini m-r-10"
                            placeholder="期数"
                            @keyup.enter.native="search"
                            size="mini"
                            clearable
                        >
                        </el-input>
                    </div>
                    <div>
                        <el-input
                            v-model="query.title"
                            class="w-normal m-r-10"
                            placeholder="标题"
                            clearable
                            @keyup.enter.native="search"
                            size="mini"
                        >
                        </el-input>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.product_category"
                            filterable
                            size="mini"
                            placeholder="类别"
                            clearable
                        >
                            <el-option
                                v-for="item in typeOptions"
                                :key="item.label"
                                :label="item.label"
                                :value="item.label"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-input
                            size="mini"
                            class="w-mini m-r-10"
                            v-model="query.short_code"
                            placeholder="简码"
                            @keyup.enter.native="search"
                            clearable
                        >
                        </el-input>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.product_type"
                            filterable
                            remote
                            clearable
                            :loading="loadings"
                            reserve-keyword
                            placeholder="产品类型"
                            size="mini"
                            :remote-method="remoteMethod"
                        >
                            <el-option
                                v-for="(item, index) in product_typeOptions"
                                :key="index"
                                :label="item.name"
                                :value="item.name"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.is_channel"
                            size="mini"
                            placeholder="是否渠道"
                            clearable
                        >
                            <el-option
                                v-for="item in is_channelOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.creator_id"
                            filterable
                            size="mini"
                            placeholder="文案制作人"
                            clearable
                        >
                            <el-option
                                v-for="item in options"
                                :key="item.id"
                                :label="item.realname"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.sort_rule"
                            filterable
                            size="mini"
                            placeholder="排序"
                            clearable
                        >
                            <el-option
                                v-for="item in sortOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div v-if="periods_type !== 2">
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.import_type"
                            filterable
                            size="mini"
                            placeholder="进口类型"
                            clearable
                        >
                            <el-option
                                v-for="item in import_typeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div v-if="status != 3">
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.onsale_review_status"
                            filterable
                            size="mini"
                            placeholder="状态"
                            clearable
                        >
                            <el-option
                                v-for="item in statusOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div v-if="status == 0">
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.is_fail"
                            filterable
                            size="mini"
                            placeholder="是否有效状态"
                            clearable
                        >
                            <el-option
                                v-for="item in effectiveOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.buyer_id"
                            filterable
                            size="mini"
                            placeholder="采购人"
                            clearable
                        >
                            <el-option
                                v-for="item in buyerOptions"
                                :key="item.id"
                                :label="item.realname"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-input
                            size="mini"
                            class="w-mini m-r-10"
                            v-model="query.country"
                            placeholder="国家"
                            @keyup.enter.native="search"
                            clearable
                        >
                        </el-input>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.label_id"
                            filterable
                            multiple
                            collapse-tags
                            size="mini"
                            placeholder="商品标签"
                            clearable
                        >
                            <el-option
                                v-for="item in goodTagOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <el-date-picker
                        v-model="query.time"
                        size="mini"
                        type="datetimerange"
                        range-separator="-"
                        class="m-r-10"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="上架时间-开始日期"
                        end-placeholder="上架时间-结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                    <el-date-picker
                        v-model="query.time1"
                        size="mini"
                        type="datetimerange"
                        range-separator="-"
                        class="m-r-10"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="下架时间-开始日期"
                        end-placeholder="下架时间-结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                    <el-date-picker
                        v-model="query.time2"
                        size="mini"
                        type="datetimerange"
                        range-separator="-"
                        class="m-r-10"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="发货时间-开始日期"
                        end-placeholder="发货时间-结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                    <el-select
                        v-if="['/flash'].includes($route.path)"
                        class="w-mini m-r-10"
                        v-model="query.sales_model"
                        filterable
                        size="mini"
                        placeholder="售卖模式"
                        clearable
                    >
                        <el-option
                            v-for="item in [
                                { value: 1, text: '代发' },
                                { value: 2, text: '订金' },
                                { value: 3, text: '预售' },
                            ]"
                            :key="item.value"
                            :label="item.text"
                            :value="item.value"
                        />
                    </el-select>
                    <el-select
                        v-model="query.seller_id"
                        placeholder="供应商"
                        size="mini"
                        clearable
                        filterable
                        remote
                        :remote-method="supplierRemoteMethod"
                        :loading="supplierRemoteMethodLoading"
                        class="m-r-10"
                    >
                        <el-option
                            v-for="item in supplierOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                    <el-checkbox
                        class="m-r-10"
                        v-show="status == 2"
                        :true-label="1"
                        :false-label="0"
                        v-model="query.is_front_lt_saled"
                        >前端小于已售
                    </el-checkbox>
                    <!-- <el-button @click="reset" size="mini">重置</el-button> -->
                    <div class="action-btn">
                        <el-button type="warning" size="mini" @click="search"
                            >查询</el-button
                        >
                        <el-button
                            type="primary"
                            size="mini"
                            @click="handleBatchOperation"
                            >批量操作</el-button
                        >
                        <el-button
                            v-if="status === 0"
                            size="mini"
                            type="success"
                            @click="updatefileVisible = true"
                            >批量导入商品信息</el-button
                        >
                        <el-button
                            v-if="periods_type == 3 && status === 0"
                            type="warning"
                            size="mini"
                            @click="handleExport"
                            >导入尾货</el-button
                        >
                    </div>
                </div>
            </el-card>
        </div>
        <el-dialog
            title="导入"
            :visible.sync="updatefileVisible"
            width="40%"
            :close-on-click-modal="false"
            @close="closeUpdatefile"
        >
            <div v-if="updatefileVisible">
                <vos-oss
                    list-type="text"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="filelist"
                    filesType="/"
                >
                    <el-button type="primary" size="default"
                        >上传文件</el-button
                    >
                </vos-oss>
            </div>

            <span
                slot="footer"
                style="display: flex; justify-content: space-between"
            >
                <div>
                    <div>
                        <el-button type="text" @click="downloadTemp"
                            >下载模版</el-button
                        >
                    </div>
                </div>
                <div>
                    <el-button @click="updatefileVisible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="comfirmUpdateFile"
                        >确定</el-button
                    >
                </div>
            </span>
        </el-dialog>
        <!-- 批量操作按钮 -->
        <div v-if="isBatchSelecting" class="batch-operation-bar">
            <div class="batch-info">
                已选择
                <span class="selected-count">{{ selectedItems.length }}</span>
                个商品
            </div>
            <div class="batch-actions">
                <el-button type="primary" size="small" @click="selectAllItems"
                    >全选</el-button
                >
                <el-button type="warning" size="small" @click="deselectAllItems"
                    >取消全选</el-button
                >
                <el-button type="primary" size="small" @click="batchEditTags"
                    >批量编辑标签</el-button
                >
                <el-button
                    type="info"
                    size="small"
                    @click="cancelBatchSelection"
                    >取消</el-button
                >
            </div>
        </div>

        <!-- <el-empty></el-empty> -->
        <el-card v-for="(v, k) in tableData" :key="k" shadow="hover">
            <div class="good-card">
                <div class="checkbox-container" v-if="isBatchSelecting">
                    <el-checkbox
                        v-model="v.isSelected"
                        @change="(val) => handleCheckboxChange(v, val)"
                    ></el-checkbox>
                </div>
                <div class="good-cardinfo">
                    <div class="good-head">
                        <div class="good-tag">
                            <el-tag
                                v-if="v.is_deposit_period"
                                type="danger"
                                size="mini"
                                effect="dark"
                                >订金</el-tag
                            >
                            <el-tag type="primary" size="mini">
                                {{ v.import_type | import_typeFormat }}
                            </el-tag>
                            <el-tag
                                type="primary"
                                size="mini"
                                v-if="v.is_presell"
                                >{{ v.is_presell ? "预售" : "" }}</el-tag
                            >
                            <el-tag
                                type="warning"
                                size="mini"
                                v-if="v.is_supplier_delivery"
                            >
                                {{ v.is_supplier_delivery ? "代发" : "" }}
                            </el-tag>
                            <el-tag
                                type="success"
                                size="mini"
                                v-if="v.is_channel"
                            >
                                {{ v.is_channel == 1 ? "渠道" : "" }}</el-tag
                            >
                        </div>
                        <div
                            v-if="
                                statusTxt(
                                    v.onsale_review_status,
                                    v.onsale_status
                                ) === '在售中'
                            "
                        >
                            <el-tag
                                size="mini"
                                type="danger"
                                effect="dark"
                                class="m-r-4"
                            >
                                在售
                            </el-tag>
                        </div>
                        <div class="good-title">
                            <el-link
                                :underline="false"
                                class="m-r-10"
                                @click="copy(v.id)"
                            >
                                {{ v.id }}
                            </el-link>
                            <el-link
                                v-if="v.encrypt_id"
                                :underline="false"
                                class="m-r-10"
                                @click="copy(`encrypt_id=${v.encrypt_id}`)"
                            >
                                encrypt_id
                            </el-link>
                            <el-link :underline="false">
                                <span @click="$viewPcGoods(v.id)">{{
                                    v.title
                                }}</span>
                            </el-link>
                            <i
                                :class="
                                    v.is_hot == 1
                                        ? 'el-icon-star-on'
                                        : 'el-icon-star-off'
                                "
                                :style="v.is_hot == 1 ? 'color:red;' : ''"
                                style="
                                    cursor: pointer;
                                    font-size: 18px;
                                    margin-left: 6px;
                                "
                                @click="handleStars(v)"
                            ></i>
                        </div>
                        <el-tag
                            size="mini"
                            type="danger"
                            effect="dark"
                            class="m-l-4"
                            v-if="v.is_seckill"
                        >
                            秒杀
                        </el-tag>
                    </div>
                    <div class="good-content">
                        <div class="good-info">
                            <p>
                                国家：<el-link :underline="false">{{
                                    v.country.join(",")
                                        ? v.country.join(",")
                                        : "-"
                                }}</el-link>
                            </p>
                            <p>
                                类型：<el-link :underline="false">{{
                                    v.product_category.join(",")
                                        ? v.product_category.join(",")
                                        : "-"
                                }}</el-link>
                            </p>
                            <p>
                                容量：<el-link :underline="false">{{
                                    v.capacity || "-"
                                }}</el-link>
                            </p>
                            <p
                                :class="v.price > 0 ? 'p_blue' : ''"
                                style="vertical-align: middle"
                                @click="handlePrice(v)"
                            >
                                售价：<el-link
                                    :underline="false"
                                    v-if="v.price > 0"
                                    class="p_blue ellipsis"
                                    >{{ v.package_prices }}</el-link
                                >
                                <el-link :underline="false" v-else>-</el-link>
                            </p>
                            <div
                                style="
                                    display: flex;
                                    align-content: center;
                                    align-items: center;
                                    max-width: 240px;
                                "
                            >
                                <span style="display: block; min-width: 60px"
                                    >商品标签：</span
                                >
                                <div
                                    style="display: flex; flex-wrap: wrap"
                                    v-if="v.label_arr.length > 0"
                                >
                                    <el-tag
                                        type="primary"
                                        size="mini"
                                        v-for="item in v.label_arr"
                                        :key="item.id"
                                        style="
                                            margin-right: 5px;
                                            margin-bottom: 4px;
                                            cursor: pointer;
                                        "
                                        >{{ item.name }}
                                    </el-tag>
                                </div>
                                <div v-else>
                                    <el-link :underline="false" type="primary"
                                        >-</el-link
                                    >
                                </div>

                                <i
                                    class="el-icon-edit"
                                    @click="handleTag(v)"
                                    style="cursor: pointer"
                                ></i>
                            </div>
                            <!-- <p v-if="v.periods_type === 1">
                                预览标题：
                                <el-link :underline="false">
                                    <span>{{ v.title1 || "-" }}</span>
                                </el-link>
                            </p> -->
                            <p v-if="[0, 3].includes(v.periods_type)">
                                收款单位：
                                <el-link :underline="false">{{
                                    v.payee_merchant_name || "-"
                                }}</el-link>
                            </p>
                        </div>
                        <!-- <el-link :underline="false"></el-link> -->
                        <div class="good-popularize">
                            <p>
                                文案：<el-link :underline="false">{{
                                    v.creator_name || "-"
                                }}</el-link>
                            </p>
                            <p @click="handleBuyerClick(v)">
                                采购：<el-link
                                    type="primary"
                                    :underline="false"
                                    >{{ v.buyer_name || "-" }}</el-link
                                >
                            </p>
                            <p>
                                上架：<el-link :underline="false">{{
                                    v.operation_name || "-"
                                }}</el-link>
                            </p>
                            <p>
                                运营：<el-link :underline="false">{{
                                    v.operation_review_name || "-"
                                }}</el-link>
                            </p>
                            <p>
                                曝光方式：<el-link
                                    :underline="false"
                                    v-for="(item, index) in v.exposure_method"
                                    :key="index"
                                    :style="{
                                        color:
                                            item.status === 1
                                                ? '#409eff'
                                                : '#000000',
                                    }"
                                >
                                    {{ item.type
                                    }}{{
                                        index < v.exposure_method.length - 1
                                            ? "/"
                                            : ""
                                    }}
                                </el-link>
                            </p>
                        </div>
                        <div class="good-time">
                            <div>
                                <div class="p_blue">上架：</div>
                                <el-link
                                    class="p_blue"
                                    :underline="false"
                                    @click="updateTime(v)"
                                    >{{ v.onsale_time | timeFormat
                                    }}<i
                                        class="el-icon-edit"
                                        v-if="
                                            (v.onsale_status == 0 &&
                                                v.onsale_review_status == 3) ||
                                            v.onsale_review_status == 4
                                        "
                                    ></i>
                                </el-link>
                            </div>
                            <div class="p_blue">
                                开售：
                                <el-link
                                    class="p_blue"
                                    :underline="false"
                                    @click="updateTime(v)"
                                    >{{ v.sell_time | timeFormat
                                    }}<i
                                        class="el-icon-edit"
                                        v-if="
                                            (v.onsale_status == 0 &&
                                                v.onsale_review_status == 3) ||
                                            v.onsale_review_status == 4
                                        "
                                    ></i>
                                </el-link>
                            </div>
                            <div class="p_blue">
                                下架：
                                <el-link
                                    class="p_blue"
                                    :underline="false"
                                    @click="updateTime(v)"
                                    >{{ v.sold_out_time | timeFormat
                                    }}<i
                                        class="el-icon-edit"
                                        v-if="
                                            (v.onsale_status == 0 &&
                                                v.onsale_review_status == 3) ||
                                            v.onsale_review_status == 4
                                        "
                                    ></i>
                                </el-link>
                            </div>
                            <div class="p_blue">
                                发货：
                                <el-link
                                    class="p_blue"
                                    :underline="false"
                                    @click="updateTime(v)"
                                >
                                    {{ v.predict_shipment_time | timeFormat
                                    }}<i
                                        class="el-icon-edit"
                                        v-if="
                                            (v.onsale_status == 0 &&
                                                v.onsale_review_status == 3) ||
                                            v.onsale_review_status == 4
                                        "
                                    ></i>
                                </el-link>
                            </div>
                        </div>
                        <div class="good-stock">
                            <p class="p_blue" @click="updateStock(v)">
                                库存：<el-link
                                    :underline="false"
                                    class="p_blue"
                                >
                                    {{ v.inventory
                                    }}<i class="el-icon-edit"></i>
                                </el-link>
                            </p>
                            <el-popover
                                placement="top-start"
                                title="已售"
                                width="200"
                                trigger="click"
                                content="已售：显示数值为实际已售出瓶数"
                            >
                                <p class="p_blue" slot="reference">
                                    已售<i
                                        class="fas fa-question-circle"
                                    />：<el-link
                                        @click.stop="handleBuy(v, 1)"
                                        :underline="false"
                                        class="p_blue"
                                    >
                                        {{ v.saled_count }}
                                    </el-link>
                                </p>
                            </el-popover>
                            <el-popover
                                placement="top-start"
                                title="已购"
                                width="200"
                                trigger="click"
                                content="已购：购买人数/套餐数量"
                            >
                                <p slot="reference" class="p_blue">
                                    已购<i
                                        class="fas fa-question-circle"
                                    />:<el-link
                                        :underline="false"
                                        class="p_blue"
                                        @click.stop="handleBuy(v, 2)"
                                    >
                                        {{ v.purchased_person }}/{{
                                            v.purchased_number
                                        }}
                                    </el-link>
                                </p>
                            </el-popover>
                            <p>
                                前端：<el-link :underline="false"
                                    >{{ v.purchased + v.vest_purchased }}/{{
                                        v.limit_number
                                    }}
                                </el-link>
                            </p>
                        </div>
                        <div class="good-comment">
                            <p class="p_blue" @click="handleVest(v)">
                                马甲：<el-link
                                    :underline="false"
                                    class="p_blue"
                                >
                                    {{ v.vest_count }}
                                </el-link>
                                <i
                                    class="el-icon-edit"
                                    style="cursor: pointer"
                                ></i>
                            </p>
                            <p>
                                评论：<el-link :underline="false">
                                    本{{ v.periods_comment_count }}/编{{
                                        v.virtual_comment_count
                                    }}/总{{ v.product_comment_count }}
                                </el-link>
                            </p>
                            <p>
                                浏览：<el-link :underline="false">{{
                                    v.pageviews
                                }}</el-link>
                            </p>
                            <div style="display: flex; align-items: center">
                                转化率：<el-link :underline="false">
                                    {{ v.rate }}‰</el-link
                                >
                                <p
                                    style="
                                        font-size: 20px;
                                        color: #409eff;
                                        cursor: pointer;
                                        margin-left: 10px;
                                    "
                                    v-if="
                                        [0, 1, 2, 3].includes(periods_type) &&
                                        [2, 3].includes(status)
                                    "
                                    @click="lineChart(v)"
                                >
                                    <i class="el-icon-s-data"></i>
                                </p>
                            </div>
                        </div>

                        <div class="good-saleinfo">
                            <div v-if="v.periods_type === 0 && status">
                                <p
                                    v-if="v.total_order_amount"
                                    class="p_blue"
                                    @click="viewPoint(v)"
                                >
                                    产品评分：{{ v.conversion_rate_score }}
                                </p>
                                <!-- <p v-else>暂无评分</p> -->
                            </div>
                            <p>
                                套餐：<el-link :underline="false">{{
                                    v.periods_set_count
                                }}</el-link>
                            </p>
                            <div class="good-remark">
                                <p class="p_blue" @click="handleRemark(v)">
                                    备注:<i
                                        class="el-icon-edit"
                                        style="cursor: pointer"
                                    ></i>
                                </p>
                            </div>
                            <p class="list-checkbox-label">
                                售完不下架：
                                <el-checkbox
                                    @change="
                                        handleGoodsList(
                                            $event,
                                            v,
                                            'sellout_sold_out'
                                        )
                                    "
                                    size="mini"
                                    v-model="v.sellout_sold_out"
                                    :true-label="1"
                                    :false-label="0"
                                >
                                </el-checkbox>
                            </p>
                            <p class="list-checkbox-label">
                                未售完自动延期：
                                <el-checkbox
                                    size="mini"
                                    @change="
                                        handleGoodsList(
                                            $event,
                                            v,
                                            'is_postpone'
                                        )
                                    "
                                    v-model="v.is_postpone"
                                    :true-label="1"
                                    :false-label="0"
                                >
                                </el-checkbox>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- <div class="good-status">

                    <p
                        style="color: #409eff; cursor: pointer"
                        @click="handleStatusClick(v)"
                    >
                        {{ statusTxt(v.onsale_review_status, v.onsale_status) }}
                    </p>
                </div> -->

                <div
                    style="
                        display: flex;
                        flex-direction: column;
                        align-items: flex-end;
                    "
                >
                    <el-input
                        v-model="v.sort"
                        class="inp_center"
                        style="width: 80px; margin-right: 5px"
                        @blur="updateSort(v)"
                        size="mini"
                        oninput="value=value.replace(/[^\d]/g,'')"
                    />
                    <div class="opration">
                        <!-- <el-input v-model="num" size="mini"></el-input> -->
                        <el-button
                            type="primary"
                            size="mini"
                            @click="HandleClick(v)"
                            >编辑
                            <!-- v-if="v.onsale_review_status != 1" -->
                        </el-button>
                        <el-button
                            type="primary"
                            size="mini"
                            @click="HandleAudit(v, 3, '')"
                            v-if="v.onsale_review_status == 1"
                        >
                            审核</el-button
                        >
                        <!-- <el-button type="warning" size="mini">达人说</el-button> -->
                        <el-button
                            type="info"
                            size="mini"
                            v-if="
                                v.onsale_review_status == 3 &&
                                v.onsale_status == 0 &&
                                v.onsale_verify_status == 0
                            "
                            @click="onShelf(v)"
                            >上架</el-button
                        >
                        <el-button
                            type="danger"
                            size="mini"
                            style="color: red"
                            v-if="
                                v.onsale_review_status == 3 &&
                                v.onsale_status == 2
                            "
                            @click="offShelf(v)"
                            >下架</el-button
                        >

                        <div
                            v-if="
                                ['待上架', '待审核', '待绑定'].includes(
                                    statusTxt(
                                        v.onsale_review_status,
                                        v.onsale_status
                                    )
                                )
                            "
                            class="m-t-5"
                        >
                            <el-button
                                type="text"
                                size="mini"
                                v-if="v.is_fail === 1"
                                @click="updateUncUsed(v, 0)"
                                style="color: red; background-color: #ffffff"
                                >失效</el-button
                            >
                            <el-button
                                type="text"
                                size="mini"
                                v-if="v.is_fail === 0"
                                style="background-color: #ffffff"
                                @click="updateUncUsed(v, 1)"
                                >生效</el-button
                            >
                        </div>
                        <el-button
                            type="primary"
                            size="mini"
                            v-if="
                                v.onsale_review_status == 3 &&
                                (v.onsale_status == 2 ||
                                    v.onsale_status == 3 ||
                                    v.onsale_status == 4) &&
                                v.periods_type != 4 &&
                                v.periods_type != 5
                            "
                            @click="handleCopy(v)"
                            >复制</el-button
                        >
                        <el-popover
                            placement="left-start"
                            width="200"
                            trigger="click"
                            @show="getMinipogramCode(v)"
                            style="margin-top: 10px; margin: 0 auto"
                            v-if="
                                v.onsale_review_status == 3 &&
                                v.onsale_status == 2
                            "
                        >
                            <img :src="minipogram_code" style="width: 180px" />
                            <el-button
                                slot="reference"
                                type="primary"
                                size="mini"
                                >小程序码</el-button
                            >
                        </el-popover>
                        <el-button
                            type="primary"
                            size="mini"
                            v-if="
                                periods_type == 0 &&
                                v.onsale_review_status == 3 &&
                                (v.onsale_status == 2 ||
                                    v.onsale_status == 3 ||
                                    v.onsale_status == 4)
                            "
                            @click="oneClickTailGoods(v)"
                            >一键尾货</el-button
                        >

                        <el-button
                            type="primary"
                            size="mini"
                            v-if="
                                v.onsale_review_status == 3 &&
                                v.onsale_status == 2
                            "
                            @click="addCommonClick(v)"
                            >生成评论</el-button
                        >
                    </div>
                </div>
            </div>
        </el-card>
        <div class="pagination-block" v-if="!isEdit">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>

        <el-dialog
            fullscreen
            title="商品信息"
            :close-on-click-modal="false"
            :visible.sync="goodsOpenDialogStatus"
            :before-close="closeGoodsOpDialog"
        >
            <goodsop
                @closeGoodsOpDialog="closeGoodsOpDialog"
                v-if="goodsOpenDialogStatus"
                ref="goodsop"
                :parentObj="this"
            ></goodsop>
        </el-dialog>
        <el-dialog
            title="驳回信息"
            :visible.sync="dialogVisible"
            width="30%"
            :close-on-click-modal="false"
        >
            <el-form
                label-width="100px"
                ref="form"
                :model="entity"
                :rules="rules"
            >
                <el-form-item label="驳回理由" prop="reason">
                    <el-select
                        class="w-mini m-r-10"
                        v-model="entity.reason"
                        filterable
                        size="mini"
                        placeholder="状态"
                    >
                        <el-option
                            v-for="item in reasonOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input
                        v-model="entity.remark"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入备注"
                    />
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="rejects()">确 定</el-button>
            </span>
        </el-dialog>
        <el-dialog
            :visible.sync="GoodsDetailVisible"
            title="商品详情浏览"
            custom-class="dialogwid"
            width="81%"
            destroy-on-close
        >
            <!-- <iframe v-if="GoodsDetailVisible" :src="ifreameurl" frameborder="0"
                style="width: 100%;height: 800px;"></iframe> -->
            <div>
                <goodsdetail
                    ref="goodsdetail"
                    :goodsinfo="goodsinfo"
                ></goodsdetail>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button
                    type="primary"
                    @click="agreeAudit(rows, 3, '')"
                    :disabled="auditTime > 0"
                >
                    通过{{ auditTime > 0 ? auditTime : "" }}
                </el-button>
                <el-button type="primary" @click="HandleReject(rows)"
                    >驳回</el-button
                >
            </div>
        </el-dialog>
        <el-dialog
            :visible.sync="dialogVisible1"
            title="提示"
            :close-on-click-modal="false"
            custom-class="dialogwid"
            width="500px"
        >
            <div
                style="
                    display: flex;
                    justify-content: center;
                    margin-bottom: 20px;
                    font-size: 16px;
                "
            >
                请选择要复制到的位置
            </div>
            <div v-if="copyStatus == 0">
                <div>
                    <el-button
                        style="margin-right: 10px"
                        type="primary"
                        size="mini"
                        @click="copys(1)"
                        >文案列表</el-button
                    >

                    <el-radio-group v-model="to_periods">
                        <el-radio
                            :label="v.value"
                            v-for="(v, i) in goodsType"
                            :key="i"
                        >
                            {{ v.label }}</el-radio
                        >
                    </el-radio-group>
                </div>

                <el-button
                    type="warning"
                    size="mini"
                    style="margin-right: 10px; margin-top: 20px"
                    @click="copys(2)"
                    >商品列表</el-button
                >
                <el-radio-group v-model="goodsCopyPeriodsType">
                    <!-- v-show="rows.periods_type === v.value" -->

                    <el-radio
                        v-show="isShowCopy(goodsCopyPeriodsType, v.value)"
                        :label="v.value"
                        v-for="(v, i) in goodsType"
                        :key="i"
                    >
                        {{ v.label }}</el-radio
                    >
                </el-radio-group>
            </div>
            <!-- <div
                v-if="copyStatus == 0"
                style="
                    display: flex;
                    justify-content: center;
                    margin-bottom: 20px;
                    font-size: 16px;
                "
            >
                <div
                    style="
                        display: flex;
                        justify-content: space-between;
                        width: 500px;
                    "
                >
                    <el-button type="primary" size="mini" @click="copys(1)"
                        >文案列表</el-button
                    >
                    <el-button type="primary" size="mini" @click="copys(2)"
                        >商品列表</el-button
                    >
                </div>
            </div> -->
        </el-dialog>
        <remarkList ref="remarklist"></remarkList>
        <stockList ref="stocklist" :parentObj="this"></stockList>
        <VestList ref="vestlist" :parentObj="this"></VestList>
        <FlashVestList ref="flashVestList" :parentObj="this"></FlashVestList>
        <BuyList
            ref="buylist"
            :parentObj="this"
            v-if="dialogVisible2"
        ></BuyList>
        <SoldList
            ref="soldlist"
            :parentObj="this"
            v-if="dialogVisible3"
        ></SoldList>
        <logList ref="logList"></logList>
        <packList ref="packList"></packList>

        <updateTime ref="updateTime" :parentObj="this"></updateTime>
        <el-dialog
            :visible.sync="viewPointsDialogStatus"
            custom-class="dialogwid"
            width="600px"
        >
            <el-descriptions class="margin-top" :column="2" border>
                <el-descriptions-item>
                    <template slot="label">实时销量</template>
                    {{ points.total_order_amount }}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">实时成本</template>
                    {{ points.total_order_cost }}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">实时利润率</template>
                    {{
                        (
                            (points.total_order_amount -
                                points.total_order_cost) /
                            points.total_order_amount
                        ).toFixed(2) * 100
                    }}%
                </el-descriptions-item>
            </el-descriptions>
        </el-dialog>
        <!-- 折线图 -->
        <el-dialog
            title=""
            :visible.sync="lineChartVisible"
            width="70%"
            :before-close="lineChartHandleClose"
        >
            <LineChart v-if="lineChartVisible" :rowData="rowData" />
        </el-dialog>
        <good-tags ref="goodTags"></good-tags>
        <el-dialog
            :close-on-click-modal="false"
            title="你确定要上架吗?"
            :visible.sync="listingPopShow"
            width="50%"
        >
            <ListingPop
                ref="ListingPopref"
                v-if="listingPopShow"
                :row="rowData"
            >
            </ListingPop>
            <div style="margin-top: 30px">
                <el-button @click="listingPopShow = false">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
        <el-dialog
            title="采购人员"
            :visible.sync="buyerDialogVisible"
            width="60%"
            :close-on-click-modal="false"
        >
            <div v-if="buyerDialogVisible">
                <div
                    v-for="(item, index) in buyerList"
                    :key="index"
                    class="buyer-item"
                    style="position: relative"
                >
                    <div class="buyer-row">
                        <div class="buyer-label">
                            {{ index === 0 ? "主要采购：" : "协助采购：" }}
                        </div>
                        <el-select
                            v-model="item.buyer_id"
                            placeholder="请选择采购人"
                            @change="(val) => handleBuyerChange(val, index)"
                            style="width: 200px"
                            :disabled="index === 0"
                        >
                            <el-option
                                v-for="buyer in buyerOptions"
                                :key="buyer.id"
                                :label="buyer.realname"
                                :value="buyer.id"
                            />
                        </el-select>
                        <div class="ratio-label">分账比例：</div>
                        <el-input-number
                            v-model="item.distribution_ratio"
                            :min="0"
                            :max="100"
                            @change="(val) => handleRatioChange(val, index)"
                            style="margin: 0 10px"
                            :disabled="index === 0"
                        />
                        <span>%</span>
                        <el-button
                            v-if="index > 0"
                            type="danger"
                            icon="el-icon-delete"
                            style="margin-left: 10px"
                            circle
                            @click="removeBuyer(index)"
                        />
                        <el-button
                            v-if="index === 0"
                            type="primary"
                            icon="el-icon-plus"
                            circle
                            @click="addBuyer"
                            :disabled="buyerList.length >= 5"
                            style="margin-left: 10px"
                            title="添加协助采购"
                        />
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buyerDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="saveBuyers">确 定</el-button>
            </span>
        </el-dialog>
        <el-dialog
            :visible.sync="addCommentdialogVisible1"
            :close-on-click-modal="false"
            title="添加评论"
            width="700px"
            custom-class="dialogwid"
        >
            <AddComment
                @close="closeaddcommon"
                :externalPeriod="commonperiosId"
                v-if="addCommentdialogVisible1"
            ></AddComment>
        </el-dialog>
        <BatchGoodTags
            ref="batchGoodTags"
            @batchUpdateTagsSuccess="batchUpdateTagsSuccess"
        />
    </div>
</template>
<script>
import vosOss from "vos-oss";
import copy from "copy-to-clipboard";
import goodsop from "./GoodsOp.vue";
import goodsdetail from "../../article/GoodsDetail.vue";
import remarkList from "../../article/remarkList.vue";
import stockList from "./stockList.vue";
import VestList from "./VestList.vue";
import BuyList from "./BuyList.vue";
import SoldList from "./SoldList.vue";
import logList from "./LogList.vue";
import packList from "./packList.vue";
import updateTime from "./updateTime.vue";
import LineChart from "./lineChart.vue";
import GoodTags from "@/components/GoodTags/index";
import ListingPop from "./ListingPop.vue";
// import flashVestList from "./flashVestList.vue";
import FlashVestList from "./flashVestList.vue";
import AddComment from "../../goodscomment/AddComment.vue";
import BatchGoodTags from "@/components/BatchGoodTags/index.vue";
export default {
    props: {
        status: {
            type: Number,
            default: 0,
        },
        periods_type: {
            type: Number,
            default: 0,
        },
        period: {
            type: String,
            default: "",
        },
    },
    components: {
        goodsop,
        vosOss,
        goodsdetail,
        remarkList,
        stockList,
        VestList,
        BuyList,
        SoldList,
        logList,
        packList,
        updateTime,
        LineChart,
        GoodTags,
        ListingPop,
        FlashVestList,
        AddComment,
        BatchGoodTags,
    },
    data() {
        return {
            selectedItems: [],
            isBatchSelecting: false,
            points: {
                total_order_amount: "",
                total_order_cost: "",
            },
            listingPopShow: false,
            rowData: {},
            lineChartVisible: false,
            viewPointsDialogStatus: false,
            updatefileVisible: false,
            filelist: [],
            buyerOptions: [],
            getOrderList: [],
            goodsOpenDialogStatus: false,
            product_typeOptions: [],
            loadings: false,
            query: {
                page: 1,
                product_type: "",
                buyer_id: "",
                country: "",
                creator_id: "",
                limit: 10,
                periods: "",
                title: "",
                short_code: "",
                product_category: "",
                import_type: "",
                is_channel: "",
                periods_type: "",
                onsale_review_status: "",
                is_fail: 1,
                time: [],
                time1: [],
                time2: [],
                sales_model: "",
                seller_id: "",
                is_front_lt_saled: 0,
                label_id: [],
            },
            goodsType: [
                {
                    label: "闪购",
                    value: 0,
                },
                {
                    label: "秒发",
                    value: 1,
                },
                {
                    label: "跨境",
                    value: 2,
                },
                {
                    label: "尾货",
                    value: 3,
                },
            ],
            tableData: [],
            dir: "vinehoo/goods-images/",
            goodsData: {},
            payOrderTimes: [],
            options: [],
            total: 0,
            isEdit: false,
            activeName: "beshelved",
            editInput: true,
            dialogVisible: false,
            GoodsDetailVisible: false,
            dialogVisible1: false,
            addCommentdialogVisible1: false,
            commonperiosId: "",
            dialogVisible2: false,
            dialogVisible3: false,
            entity: {
                reason: "",
                remark: "",
            },

            reasonOptions: [
                {
                    label: "酒瓶信息错误",
                    value: "酒瓶信息错误",
                },
                {
                    label: "年份错误",
                    value: "年份错误",
                },
                {
                    label: "详细资料错误",
                    value: "详细资料错误",
                },
                {
                    label: "采购人不符",
                    value: "采购人不符",
                },
                {
                    label: "其他",
                    value: "其他",
                },
            ],
            sortOption: [
                {
                    label: "排序值降序",
                    value: 8,
                },
                {
                    label: "排序值升序",
                    value: 7,
                },

                {
                    label: "转化率降序",
                    value: 6,
                },
                {
                    label: "转化率升序",
                    value: 5,
                },
                {
                    label: "销量降序",
                    value: 4,
                },
                {
                    label: "销量升序",
                    value: 3,
                },
                {
                    label: "库存值降序",
                    value: 10,
                },

                {
                    label: "库存值升序",
                    value: 9,
                },

                {
                    label: "价格降序",
                    value: 2,
                },
                {
                    label: "价格升序",
                    value: 1,
                },

                {
                    label: "下架时间降序",
                    value: 12,
                },
                {
                    label: "下架时间升序",
                    value: 11,
                },
                {
                    label: "上架时间降序",
                    value: 13,
                },
                {
                    label: "上架时间升序",
                    value: 14,
                },
            ],
            typeOptions: [
                {
                    label: "酒类",
                    value: 0,
                },
                {
                    label: "饮料",
                    value: 2,
                },
                {
                    label: "食品",
                    value: 3,
                },
                {
                    label: "物料",
                    value: 4,
                },
                {
                    label: "虚拟",
                    value: 6,
                },
                {
                    label: "酒具",
                    value: 7,
                },
                {
                    label: "其他",
                    value: 5,
                },
            ],
            import_typeOptions: [
                {
                    label: "自进口",
                    value: 0,
                },
                {
                    label: "地采",
                    value: 1,
                },
            ],
            goodsCopyPeriodsType: 0,
            effectiveOptions: [
                //是否有效状态
                {
                    label: "全部",
                    value: 2,
                },
                {
                    label: "生效",
                    value: 1,
                },
                {
                    label: "失效",
                    value: 0,
                },
            ],
            statusOptions: [
                {
                    label: "待售中",
                    value: 1,
                },
                {
                    label: "在售中",
                    value: 2,
                },
            ],
            // statusOptions: [{
            //         label: "待绑定",
            //         value: 0
            //     },
            //     {
            //         label: "待审核",
            //         value: 1
            //     },
            //     {
            //         label: "审批中",
            //         value: 2
            //     },
            //     {
            //         label: "已审核",
            //         value: 3
            //     },
            //     {
            //         label: "已驳回",
            //         value: 4
            //     }
            // ],
            rules: {
                reason: [
                    {
                        required: true,
                        message: "请选择驳回原因",
                        trigger: "change",
                    },
                ],
            },
            auditTime: 0,
            goodsinfo: {},
            to_periods: 0,
            copyStatus: 0,
            copy_type: 1,
            rows: {},
            minipogram_code: "",
            program_row: {},
            supplierRemoteMethodLoading: false,
            supplierOptions: [],
            is_channelOptions: [
                { value: 1, label: "是" },
                { value: 0, label: "否" },
            ],
            buyerDialogVisible: false,
            buyerList: [],
            currentRow: null,
            goodTagOptions: [],
        };
    },
    methods: {
        lineChart(v) {
            this.rowData = { ...v };
            this.lineChartVisible = true;
        },
        lineChartHandleClose() {
            this.lineChartVisible = false;
        },
        isShowCopy(period, value) {
            if (
                period == value ||
                (period === 0 && value === 3) ||
                (period === 3 && value === 0)
            ) {
                return true;
            } else {
                return false;
            }
        },
        //更新字段
        async updateUncUsed(item, is_fail) {
            let data = {
                period: item.id,
                periods_type: item.periods_type,
                is_fail,
            };
            let res = await this.$request.article.updateUncUsed(data);
            if (res.data.error_code == 0) {
                this.$message.success("更新成功");
                this.getData();
            }
        },
        getMinipogramCode(params) {
            if (params.id != this.program_row.id) {
                this.$request.article
                    .getMinappQrcode({
                        scene: String(params.id),
                        page: "pages/goods-detail/goods-detail",
                    })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            console.warn(res.data.data);
                            this.minipogram_code =
                                "data:image/jpg;base64," + res.data.qrcode;
                            console.warn(this.minipogram_code);
                            this.program_row = params;
                        }
                    });
            }
        },
        viewPoint(item) {
            this.points = {
                total_order_cost: item.total_order_cost,
                total_order_amount: item.total_order_amount,
            };
            this.viewPointsDialogStatus = true;
        },
        handleBack() {
            this.copyStatus = 0;
            console.log(this.copyStatus, 232323);
        },
        handleConfirm() {
            this.$request.article
                .copyPeriod({
                    period: this.rows.id,
                    periods_type: this.rows.periods_type,
                    to_periods:
                        this.copy_type == 2
                            ? this.goodsCopyPeriodsType
                            : this.to_periods,
                    copy_type: this.copy_type,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        let url = "";
                        //跳转
                        if (this.copy_type == 2) {
                            if (this.goodsCopyPeriodsType == 0) {
                                url = "/flash?tab=wait";
                            } else if (this.goodsCopyPeriodsType == 1) {
                                url = "/second?tab=wait";
                            } else if (this.goodsCopyPeriodsType == 2) {
                                url = "/crossborder?tab=wait";
                            } else if (this.goodsCopyPeriodsType == 3) {
                                url = "/tailcargo?tab=wait";
                            }

                            this.$router.push(url);
                            this.$emit("targetPage", "wait");
                            // if (this.rows.periods_type != this.to_periods) {
                            //     this.$router.push(url);
                            // } else {
                            //     this.$emit("targetPage", "wait");
                            // }
                        } else {
                            this.$router.push("/article");
                        }
                        this.$message.success("操作成功！");
                    }
                });
        },
        downloadTemp() {
            let link =
                "https://images.vinehoo.com/download/template/%E5%95%86%E5%93%81%E4%BF%A1%E6%81%AF%E6%A8%A1%E7%89%88.xlsx";
            if (this.periods_type === 2) {
                link =
                    "https://images.vinehoo.com/download/template/%E8%B7%A8%E5%A2%83%E5%95%86%E5%93%81%E6%A8%A1%E7%89%88.xlsx";
            }
            window.location.href = link;
        },
        handleStars(row) {
            this.$request.article
                .updateUncUsed({
                    period: row.id,
                    periods_type: row.periods_type,
                    is_hot: row.is_hot == 1 ? 0 : 1,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        row.is_hot = row.is_hot == 0 ? 1 : 0;
                        this.$message.success("操作成功！");
                    }
                });
        },
        getPurchase() {
            this.$request.article
                .purchaseList({
                    type: 2,
                })
                .then((res) => {
                    this.buyerOptions = res.data.data.list;
                });
            this.$request.article
                .purchaseList({
                    type: 3,
                })
                .then((res) => {
                    this.options = res.data.data.list;
                });
        },

        // 获取商品标签选项
        getGoodTagOptions() {
            this.$request.recommendLabel
                .labelList({ page: 1, limit: 999, type: 2 })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.goodTagOptions = res.data.data.list;
                    }
                });
        },
        async handleGoodsList(value, item, handle) {
            const data = {
                period: item.id,
                periods_type: item.periods_type,
                [handle]: value,
            };
            const res = await this.$request.article.updateUncUsed(data);
            if (res.data.error_code === 0) {
                this.$message.success("操作成功");
            }
        },
        copy(data) {
            copy(data);
            this.$message.success("复制成功");
        },
        copys(copy_type) {
            this.copy_type = copy_type;
            // this.copyStatus = 1;
            this.handleConfirm();
        },
        handlePrice(rows) {
            this.$nextTick(() => {
                this.$refs.packList.openForm(rows);
            });
        },
        listingRefresh() {},
        handleStatusClick(rows) {
            this.$nextTick(() => {
                this.$refs.logList.openForm(rows);
            });
        },
        handleBuy(v, type) {
            if (type == 1) {
                this.dialogVisible3 = true;
                this.$nextTick(() => {
                    this.$refs.soldlist.openForm(v);
                });
            } else {
                this.dialogVisible2 = true;
                this.$nextTick(() => {
                    this.$refs.buylist.openForm(v);
                });
            }
        },
        handleVest(v) {
            if (v.is_seckill == 1) {
                this.$nextTick(() => {
                    this.$refs.flashVestList.openForm(v);
                });
            } else {
                this.$nextTick(() => {
                    this.$refs.vestlist.openForm(v);
                });
            }
        },
        showRemark(row) {
            if (!row.content) {
                this.$request.article
                    .getReviewLog({
                        period: row.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.$set(row, "visible", true);
                            this.$set(row, "content", res.data.data.describe);
                            this.$set(row, "remark", res.data.data.remark);
                        }
                    });
            } else {
                row.visible = !row.visible;
            }
        },
        statusTxt(operateStatus, status) {
            let statusTxt = "";
            if (operateStatus == 0) {
                statusTxt = "待绑定";
            } else if (operateStatus == 1) {
                statusTxt = "待审核";
            } else if (operateStatus == 3 && status == 0) {
                statusTxt = "待上架";
            } else if (operateStatus == 3 && status == 1) {
                statusTxt = "待售中";
            } else if (operateStatus == 3 && status == 2) {
                statusTxt = "在售中";
            } else if (operateStatus == 4) {
                statusTxt = "已驳回";
            }
            return statusTxt;
        },
        HandleClick(rows) {
            this.goodsOpenDialogStatus = true;
            this.$nextTick(() => {
                this.$refs.goodsop.openForm(rows);
            });
        },
        closeGoodsOpDialog() {
            this.goodsOpenDialogStatus = false;
            this.getData();
        },
        edit() {
            this.isEdit = true;
            this.activeName = "edit";
        },
        updateStock(row) {
            this.$refs.stocklist.openForm(row);
        },
        closeUpdatefile() {
            this.filelist = [];
        },
        comfirmUpdateFile() {
            if (this.filelist.length == 0) {
                this.$message.error("请先上传文件");
                return;
            }
            let file = this.filelist.join("");
            let type = "";
            switch (this.periods_type) {
                case 0:
                    type = "闪购";
                    break;
                case 1:
                    type = "秒发";
                    break;
                case 2:
                    type = "跨境";
                    break;
                case 3:
                    type = "尾货";
                    break;
                default:
                    break;
            }
            if (this.periods_type === 2) {
                this.$request.article
                    .importCrossByExcel({ file, type })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.updatefileVisible = false;
                            this.$message.success("上传成功");
                            this.getData();
                        }
                    });
            } else {
                this.$request.article
                    .importInvoiceFile({ file, type })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.updatefileVisible = false;
                            this.$message.success("上传成功");
                            this.getData();
                        }
                    });
            }
        },
        viewGoods(item) {
            window.open(
                this.$BASE.PCDomain +
                    "/pages/goods-detail/goods-detail?id=" +
                    (item.encrypt_id || item.id)
            );
        },
        getData() {
            this.goodsCopyPeriodsType = this.periods_type;
            const params = {
                periods: this.query.periods,
                title: this.query.title,
                product_category: this.query.product_category,
                import_type: this.query.import_type,
                is_channel: this.query.is_channel,
                product_type: this.query.product_type,
                creator_id: this.query.creator_id,
                buyer_id: this.query.buyer_id,
                periods_type: this.periods_type,
                short_code: this.query.short_code,
                onsale_status: this.status,
                country: this.query.country,
                onsale_time_start:
                    (this.query.time && this.query.time[0]) || "",
                onsale_time_end: (this.query.time && this.query.time[1]) || "",
                sold_out_time_start:
                    (this.query.time1 && this.query.time1[0]) || "",
                sold_out_time_end:
                    (this.query.time1 && this.query.time1[1]) || "",
                predict_shipment_time_start:
                    (this.query.time2 && this.query.time2[0]) || "",
                predict_shipment_time_end:
                    (this.query.time2 && this.query.time2[1]) || "",
                page: this.query.page,
                limit: this.query.limit,
                sales_model: this.query.sales_model,
                seller_id: this.query.seller_id,
                is_front_lt_saled: this.query.is_front_lt_saled,
                label_id:
                    this.query.label_id.length > 0
                        ? this.query.label_id.join(",")
                        : "",
            };
            if (this.status === 0)
                params.is_fail =
                    this.query.is_fail == 2 ? "" : this.query.is_fail;
            if (this.query.sort_rule == 1) {
                params.sort_rule = JSON.stringify({
                    price: "asc",
                });
            } else if (this.query.sort_rule == 2) {
                params.sort_rule = JSON.stringify({
                    price: "desc",
                });
            } else if (this.query.sort_rule == 3) {
                params.sort_rule = JSON.stringify({
                    saled_count: "asc",
                });
            } else if (this.query.sort_rule == 4) {
                params.sort_rule = JSON.stringify({
                    saled_count: "desc",
                });
            } else if (this.query.sort_rule == 5) {
                params.sort_rule = JSON.stringify({
                    conversion_rate: "asc",
                });
            } else if (this.query.sort_rule == 6) {
                params.sort_rule = JSON.stringify({
                    conversion_rate: "desc",
                });
            } else if (this.query.sort_rule == 7) {
                params.sort_rule = JSON.stringify({
                    sort: "asc",
                });
            } else if (this.query.sort_rule == 8) {
                params.sort_rule = JSON.stringify({
                    sort: "desc",
                });
            } else if (this.query.sort_rule == 9) {
                params.sort_rule = JSON.stringify({
                    inventory: "asc",
                });
            } else if (this.query.sort_rule == 10) {
                params.sort_rule = JSON.stringify({
                    inventory: "desc",
                });
            } else if (this.query.sort_rule == 11) {
                params.sort_rule = JSON.stringify({
                    sold_out_time: "asc",
                });
            } else if (this.query.sort_rule == 12) {
                params.sort_rule = JSON.stringify({
                    sold_out_time: "desc",
                });
            } else if (this.query.sort_rule == 13) {
                params.sort_rule = JSON.stringify({
                    onsale_time: "desc",
                });
            } else if (this.query.sort_rule == 14) {
                params.sort_rule = JSON.stringify({
                    onsale_time: "asc",
                });
            }
            if (this.status == 2) {
                if (this.query.onsale_review_status) {
                    params.onsale_status = this.query.onsale_review_status;
                } else {
                    delete params.onsale_status;
                    params.onsale_status_t = 1;
                }
            }
            if (this.status == 0) {
                params.buyer_review_status = 3;
                params.onsale_review_status = this.query.onsale_review_status;
            }

            this.$request.article.articleList(params).then((res) => {
                if (res.data.error_code == 0) {
                    res.data.data.list.map((item) => {
                        item.onsale_time_visible = true;
                        item.sell_time_visible = true;
                        item.predict_shipment_time_visible = true;
                        item.sold_out_time_visible = true;
                        // item.country = [...new Set(item.country)][0]
                        // item.product_category = [...new Set(item.product_category)].join("、")
                        // item.onsale_time = this.timeFormat(item.onsale_time)
                        // item.sell_time = this.timeFormat(item.sell_time)
                        // item.sold_out_time = this.timeFormat(item.sold_out_time)
                        // item.predict_shipment_time = this.timeFormat(item.predict_shipment_time)
                    });
                    this.tableData = res.data.data.list || [];
                    console.log(this.tableData, 555555);
                    this.total = res.data.data.total;
                }
            });
        },
        updateSort(rows) {
            this.$request.article
                .updateUncUsed({
                    period: rows.id,
                    periods_type: rows.periods_type,
                    sort: rows.sort,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功！");
                    }
                });
        },
        // timeFormat(val) {
        //     if (val == '1970-01-01 08:00:00') {
        //         return "-"
        //     } else {
        //         return val
        //     }
        // },
        updateTime(rows, type) {
            console.log(11111);
            this.$nextTick(() => {
                this.$refs.updateTime.openForm(rows);
            });
            // let params = {
            //     period: rows.id,
            //     periods_type: rows.periods_type,
            // };
            // if(new Date(rows.sell_time).getTime()<=new Date(rows.onsale_time).getTime()){
            //     this.$message.error("开售时间必须大于上架时间！")
            //     return
            // }
            //  if(new Date(rows.sold_out_time).getTime()<=new Date(rows.sell_time).getTime()){
            //     this.$message.error("下架时间必须大于开售时间！")
            //     return
            // }
            // if (type == 1) {
            //     if (!rows.onsale_time) {
            //         this.$message.error("请选择上架时间！")
            //         return;
            //     }
            //     params.onsale_time = rows.onsale_time;
            // } else if (type == 2) {
            //     if (!rows.sell_time) {
            //         this.$message.error("请选择开售时间！")
            //         return;
            //     }
            //     params.sell_time = rows.sell_time;
            // } else if (type == 3) {
            //     if (!rows.sold_out_time) {
            //         this.$message.error("请选择下架时间！")
            //         return;
            //     }
            //     params.sold_out_time = rows.sold_out_time;
            // } else if (type == 4) {
            //     if (!rows.predict_shipment_time) {
            //         this.$message.error("请选择发货时间！")
            //         return;
            //     }
            //     params.predict_shipment_time = rows.predict_shipment_time;
            // }
            // this.$request.article.goodsUpdate(params).then(res => {
            //     if (res.data.error_code == 0) {
            //         if (type == 1) {
            //             rows.onsale_time_visible = !rows.onsale_time_visible;
            //         } else if (type == 2) {
            //             rows.sell_time_visible = !rows.sell_time_visible;
            //         } else if (type == 3) {
            //             rows.sold_out_time_visible = !rows.sold_out_time_visible;
            //         } else if (type == 4) {
            //             rows.predict_shipment_time_visible = !rows.predict_shipment_time_visible;
            //         }

            //     }

            // });
        },
        remoteMethod(query) {
            console.log(query);
            if (query.length >= 1) {
                this.loadings = true;
                setTimeout(() => {
                    let data = {
                        keywords: query,
                    };
                    this.$request.purchase
                        .getProductListForKeywords(data)
                        .then((res) => {
                            this.loadings = false;
                            if (res.data.error_code == 0) {
                                console.log(res.data);
                                this.product_typeOptions = res.data.data.list;
                            }
                        });
                }, 300);
            } else {
                this.goodsOptions = [];
            }
            console.log(this.goodsOptions);
        },
        HandleAudit(rows, review_status, reason) {
            this.GoodsDetailVisible = true;
            this.goodsinfo = rows;
            this.rows = rows;
            this.auditTime = 5;
            if (rows.periods_type == 0) {
                this.$request.article
                    .getFlashDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            } else if (rows.periods_type == 1) {
                this.$request.article
                    .getSecondDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            } else if (rows.periods_type == 2) {
                this.$request.article
                    .getCrossDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            } else if (rows.periods_type == 3) {
                this.$request.article
                    .getLeftoverDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            }

            this.countDown();
        },
        agreeAudit(rows, review_status) {
            if (review_status == 3) {
                this.$confirm("你确定审核通过吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        this.audit(rows, review_status);
                    })
                    .catch(() => {
                        //几点取消的提示
                    });
            }
        },
        audit(rows, review_status, reason) {
            // if (review_status == 3 && !this.validateForm()) {
            //     return
            // }
            console.log(rows, 3333333);

            const params = {
                period: rows.id,
                onsale_review_status: review_status,
                reject_reason: reason,
                remark: this.entity.remark,
                periods_type: rows.periods_type,
                version: this.goodsinfo.version,
            };
            this.$request.article.updateOnsale(params).then((res) => {
                if (res.data.error_code == 0) {
                    this.getData();
                    if (review_status == 4) {
                        this.dialogVisible = false;
                    }
                    this.GoodsDetailVisible = false;
                    this.$message.success("操作成功！");
                }
            });
        },
        HandleReject(rows) {
            this.rows = rows;
            this.entity.reason = "";
            this.entity.remark = "";
            this.dialogVisible = !this.dialogVisible;
        },
        rejects() {
            if (this.validateForm()) {
                this.audit(this.rows, 4, this.entity.reason);
            }
        },
        handleRemark(rows) {
            this.$nextTick(() => {
                this.$refs.remarklist.openForm(rows);
            });
        },
        judgeTime(time) {
            var strtime = time.replace("/-/g", "/"); //时间转换
            //时间
            var date1 = new Date(strtime);
            //现在时间
            var date2 = new Date();
            //判断时间是否过期
            return date1 < date2 ? true : false;
        },
        async onShelf(rows) {
            const isDropshipping = rows.warehouse.includes("代发") ? 1 : 0;
            if (rows.is_supplier_delivery === isDropshipping) {
                if (rows.periods_type === 3) {
                    const res = await this.$request.article.getunpushorder({
                        period: rows.id,
                    });
                    if (res.data.error_code == 0) {
                        if (!res.data.data.list.length) {
                            this.listingPopShow = true;
                            this.rowData = rows;
                        } else {
                            this.$confirm(
                                "订单号未推送到萌牙，确认是否继续上架?",
                                "提示",
                                {
                                    confirmButtonText: "继续上架",
                                    cancelButtonText: "取消",
                                    type: "warning",
                                }
                            )
                                .then(() => {
                                    this.listingPopShow = true;
                                    this.rowData = rows;
                                })
                                .catch(() => {});
                        }
                    }
                } else {
                    this.listingPopShow = true;
                    this.rowData = rows;
                }
            } else {
                if (rows.is_supplier_delivery) {
                    this.$message.error("代发期数请选择代发仓");
                } else {
                    this.$message.error("非代发期数不能选择代发仓");
                }
            }
            // this.$confirm(message, "提示", {
            //     dangerouslyUseHTMLString: true,
            //     confirmButtonText: "确定",
            //     cancelButtonText: "取消",
            //     type: "warning",
            // })
            //     .then(() => {
            // this.$request.article
            //     .updateoffSale({
            //         period: rows.id,
            //         periods_type: rows.periods_type,
            //         onsale_status: 2,
            //         operation_id: "1",
            //         operation_name: "操作人",
            //     })
            //     .then((res) => {
            //         if (res.data.error_code == 0) {
            //             this.query.page = 1;
            //             this.getData();
            //             this.$message.success("操作成功");
            //         }
            //     });
            //     })
            //     .catch(() => {
            //         //几点取消的提示
            //     });
        },
        submitForm() {
            const {
                chooseCard,
                chooseColumn,
                title,
                card_filter,
                column_filter,
            } = this.$refs.ListingPopref.getData();
            console.log(title, chooseCard, chooseColumn);
            this.$request.article
                .updateoffSale({
                    period: this.rowData.id,
                    periods_type: this.rowData.periods_type,
                    onsale_status: 2,
                    operation_id: "1",
                    operation_name: "操作人",
                    marketing_section: {
                        card: chooseCard,
                        column: chooseColumn,
                        title: title,
                        card_filter: card_filter,
                        column_filter: column_filter,
                    },
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.query.page = 1;
                        this.getData();
                        this.listingPopShow = false;
                        this.$message.success("操作成功");
                    }
                });
        },
        offShelf(rows) {
            this.$prompt("请输入下架原因", "下架", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                inputPlaceholder: "请输入下架原因",
                inputValidator: (value) => {
                    if (!value) {
                        return "下架原因不能为空";
                    }
                    if (value.trim().length < 3) {
                        return "下架原因至少需要3个字符";
                    }
                    return true;
                },
            })
                .then(({ value }) => {
                    this.$request.article
                        .updateoffSale({
                            period: rows.id,
                            periods_type: rows.periods_type,
                            onsale_status: 3,
                            operation_id: "1",
                            operation_name: "操作人",
                            remark: value,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.query.page = 1;
                                this.getData();
                                this.$message.success("下架成功");
                            }
                        });
                })
                .catch(() => {
                    // 取消操作
                });
        },
        addCommonClick(row) {
            this.commonperiosId = row.id;
            this.addCommentdialogVisible1 = true;
        },
        closeaddcommon() {
            this.addCommentdialogVisible1 = false;
        },
        oneClickTailGoods(rows) {
            this.$confirm("你确定要同步到尾货吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.$request.article
                        .synchronizeTailGoods({
                            period: rows.id,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                //this.$router.push("/tailcargo?tab=wait");
                                // this.$emit("targetPage", "wait");
                                this.$message.success("操作成功！");
                            }
                        });
                })
                .catch(() => {
                    //几点取消的提示
                });
        },
        handleCopy(rows) {
            this.copyStatus = 0;
            this.rows = rows;
            this.to_periods = this.rows.periods_type;
            this.dialogVisible1 = true;
        },
        countDown() {
            this.auditTime = this.auditTime - 1;
            if (this.auditTime == 0) {
                return;
            }
            setTimeout(() => {
                this.countDown();
            }, 1000);
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        reset() {
            this.$confirm("您确定要重置所有筛选条件吗?", "重置操作", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.payOrderTimes = [];
                this.form = {
                    page: 1,
                };
                this.getData();
            });
        },

        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
        validateForm() {
            let flag = null;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
        handleTag(rows) {
            // this.goodTagVisible = true;
            console.log(rows.id, rows.periods_type);
            this.$nextTick(() => {
                this.$refs.goodTags.show({
                    id: rows.id,
                    periods_type: rows.periods_type,
                    label_arr: rows.label_arr,
                });
            });
        },
        supplierRemoteMethod(query) {
            if (query !== "") {
                this.supplierRemoteMethodLoading = true;
                this.$request.article
                    .supplierList({ page: 1, limit: 99, keyword: query })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.supplierOptions = res.data.data.list.map(
                                (item) => ({
                                    value: item.id,
                                    label: item.supplier_name,
                                })
                            );
                            this.supplierRemoteMethodLoading = false;
                        }
                    });
            } else {
                this.supplierOptions = [];
            }
        },
        async handleExport() {
            try {
                const result = await this.$confirm(
                    "此功能将根据在线表格中的商品信息自动创建期数并完成自动上架，要确认导入吗？",
                    "提示",
                    {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }
                );
                if (result === "confirm") {
                    this.$request.purchase
                        .batchOnSaleChannel({})
                        .then((res) => {
                            if (res.data.error_code === 0) {
                                this.$message.success(
                                    "已发送请求，请查看在线表格中的处理结果"
                                );
                            }
                        });
                }
            } catch (error) {
                // 用户点击取消按钮时会进入这里
                return;
            }
        },
        handleBuyerClick(row) {
            this.currentRow = row;
            this.buyerDialogVisible = true;
            this.getBuyerList();
        },

        async getBuyerList() {
            try {
                const res = await this.$request.article.getPeriodsBuyerList({
                    period: this.currentRow.id,
                    periods_type: this.currentRow.periods_type,
                });
                if (res.data.error_code === 0) {
                    this.buyerList = res.data.data.list || [
                        {
                            buyer_id: this.currentRow.buyer_id,
                            buyer_name: this.currentRow.buyer_name,
                            distribution_ratio: 98,
                        },
                    ];
                }
            } catch (err) {
                console.error(err);
            }
        },

        handleBuyerChange(val, index) {
            const buyer = this.buyerOptions.find((item) => item.id === val);
            if (buyer) {
                this.buyerList[index].buyer_name = buyer.realname;
            }
        },

        addBuyer() {
            if (this.buyerList.length >= 5) return;
            this.buyerList.push({
                buyer_id: "",
                buyer_name: "",
                distribution_ratio: 0,
            });
        },

        removeBuyer(index) {
            this.buyerList.splice(index, 1);
            // 重新计算主要采购的分账比例
            const assistantTotal = this.buyerList.reduce((sum, item, i) => {
                return i === 0 ? sum : sum + Number(item.distribution_ratio);
            }, 0);
            this.buyerList[0].distribution_ratio = (
                100 - assistantTotal
            ).toFixed(2);
        },

        checkTotal() {
            const total = this.buyerList
                .reduce((sum, item) => sum + Number(item.distribution_ratio), 0)
                .toFixed(2);
            console.log("total--------", total);

            if (Number(total) !== 100) {
                this.$message.warning("所有采购人员分账比例之和必须为100%");
                return false;
            }
            return true;
        },

        async saveBuyers() {
            if (!this.checkTotal()) return;

            try {
                const res = await this.$request.article.periodsBuyerSave({
                    period: this.currentRow.id,
                    periods_type: this.currentRow.periods_type,
                    param: this.buyerList.map((item) => ({
                        buyer_id: item.buyer_id,
                        buyer_name: item.buyer_name,
                        distribution_ratio: item.distribution_ratio,
                    })),
                });

                if (res.data.error_code === 0) {
                    this.$message.success("保存成功");
                    this.buyerDialogVisible = false;
                    this.getData(); // 刷新列表
                }
            } catch (err) {
                console.error(err);
            }
        },
        handleRatioChange(val, index) {
            // 计算协助采购的总比例
            const assistantTotal = this.buyerList.reduce((sum, item, i) => {
                return i === 0 ? sum : sum + Number(item.distribution_ratio);
            }, 0);

            // 自动调整主要采购的比例
            this.buyerList[0].distribution_ratio = 100 - assistantTotal;

            // 检查是否合法
            if (this.buyerList[0].distribution_ratio < 0) {
                this.$message.error("协助采购分账比例总和不能超过100%");
                // 重置当前修改的值
                const oldTotal =
                    assistantTotal -
                    val +
                    this.buyerList[index].distribution_ratio;
                this.buyerList[index].distribution_ratio = 100 - oldTotal;
                this.buyerList[0].distribution_ratio =
                    100 -
                    (assistantTotal -
                        val +
                        this.buyerList[index].distribution_ratio);
            }
        },

        // 批量操作相关方法
        handleBatchOperation() {
            // 检查是否有选中的商品
            if (this.tableData.length === 0) {
                this.$message.warning("当前没有可操作的商品");
                return;
            }

            // 弹出确认对话框
            this.$confirm("请选择要批量操作的商品", "批量操作", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "info",
            })
                .then(() => {
                    // 清空之前选择的商品
                    this.selectedItems = [];

                    // 为每个商品添加选择状态
                    this.tableData.forEach((item) => {
                        this.$set(item, "isSelected", false);
                    });

                    // 显示选择界面
                    this.$set(this, "isBatchSelecting", true);

                    // 显示批量操作按钮
                    this.$message.success(
                        '请选择要操作的商品，然后点击"批量编辑标签"按钮'
                    );
                })
                .catch(() => {
                    // 用户取消操作
                });
        },

        // 处理复选框变化
        handleCheckboxChange(item, isChecked) {
            // 更新选中的商品列表
            if (isChecked) {
                this.selectedItems.push({
                    id: item.id,
                    periods_type: item.periods_type,
                    label_arr: item.label_arr || [], // 包含标签信息
                });
            } else {
                const index = this.selectedItems.findIndex(
                    (selected) => selected.id === item.id
                );
                if (index !== -1) {
                    this.selectedItems.splice(index, 1);
                }
            }
        },

        // 全选
        selectAllItems() {
            this.tableData.forEach((item) => {
                if (!item.isSelected) {
                    this.$set(item, "isSelected", true);
                    // 避免重复添加
                    const exists = this.selectedItems.some(
                        (selected) => selected.id === item.id
                    );
                    if (!exists) {
                        this.selectedItems.push({
                            id: item.id,
                            periods_type: item.periods_type,
                            label_arr: item.label_arr || [], // 包含标签信息
                        });
                    }
                }
            });
            this.$message.success(`已选择全部 ${this.tableData.length} 个商品`);
        },

        // 取消全选
        deselectAllItems() {
            this.tableData.forEach((item) => {
                this.$set(item, "isSelected", false);
            });
            this.selectedItems = [];
            this.$message.success("已取消全部选择");
        },

        // 批量编辑标签
        batchEditTags() {
            if (this.selectedItems.length === 0) {
                this.$message.warning("请至少选择一个商品");
                return;
            }

            // 打开批量标签编辑弹窗
            this.$nextTick(() => {
                this.$refs.batchGoodTags.show(this.selectedItems);
            });
        },

        // 取消批量选择
        cancelBatchSelection() {
            // 清除选择状态
            this.tableData.forEach((item) => {
                this.$set(item, "isSelected", false);
            });

            // 清空选中的商品列表
            this.selectedItems = [];

            // 隐藏选择界面
            this.$set(this, "isBatchSelecting", false);
        },

        // 批量更新标签成功回调
        batchUpdateTagsSuccess() {
            // 刷新数据
            this.getData();

            // 清除选择状态
            this.cancelBatchSelection();
        },
    },
    filters: {
        import_typeFormat(val) {
            switch (val) {
                case 0:
                    return "自进口";
                case 1:
                    return "地采";
                case 2:
                    return "跨境";
                default:
                    return "未知";
            }
        },
        timeFormat(val) {
            if (val == "1970-01-01 08:00:00") {
                return "-";
            } else {
                return val;
            }
        },
    },
    mounted() {
        if (this.status == 0) {
            this.statusOptions = [
                {
                    label: "待绑定",
                    value: 0,
                },
                {
                    label: "待审核",
                    value: 1,
                },
                {
                    label: "待上架",
                    value: 3,
                },
                {
                    label: "已驳回",
                    value: 4,
                },
            ];
        }
        this.getPurchase();
        this.getGoodTagOptions();
        if (this.period) {
            this.query.periods = this.period;
            console.log("98kkkk------------");
        }
        this.getData();
    },
};
</script>
<style>
.inp_center .el-input__inner {
    text-align: center;
}
</style>
<style lang="scss" scoped>
.ellipsis {
    display: inline-block;
    text-align: left;
    width: 220px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    height: 22px;
    line-height: 22px;
}

.el-card {
    margin-bottom: 10px;
}

.good-card {
    .el-button--mini,
    .el-button--mini.is-round {
        // padding: 0;
        background-color: #ecf5ff;
        border-color: #ffffff;
        color: #66b1ff;
    }

    display: flex;
    justify-content: space-between;

    .checkbox-container {
        display: flex;
        align-items: center;
        padding-right: 15px;
    }

    .good-cardinfo {
        width: 80%;

        .p_blue {
            color: #409eff;
            cursor: pointer;
        }

        .good-head {
            width: 100%;
            display: flex;
            justify-content: flex-start;
            white-space: nowrap;
            overflow: hidden;

            .good-tag {
                & > .el-tag {
                    margin-right: 4px;
                }
            }
        }

        .good-content {
            margin-top: 10px;
            display: flex;
            justify-content: space-between;

            .el-icon-edit {
                margin-left: 5px;
                color: #409eff;
                font-size: 14px;
            }

            p {
                margin-bottom: 0;
            }

            .good-time {
                width: 240px;

                > div {
                    display: flex;
                    align-items: center;
                }

                .times {
                    display: flex;

                    i {
                        cursor: pointer;
                        font-size: 20px;
                        color: #409eff;
                        padding: 2px 0 0 3px;
                    }
                }
            }

            .el-link {
                font-size: 12px;
            }

            .good-saleinfo {
                .good-remark {
                    display: flex;
                    align-items: center;
                    // height: 30px;

                    & > p {
                        margin-bottom: 0;
                    }

                    & > .el-button {
                        margin-left: 20px;
                    }
                }
            }
        }
    }

    .good-status {
        width: 50px;
    }

    .opration {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 80px;

        & > .el-button {
            margin-left: 0;
            margin-top: 10px;
            margin-right: 10px;
            width: 80px;
        }

        &:has(.el-button:nth-child(n + 5)) {
            width: 180px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            padding: 5px;
            height: 120px;
            & > .el-button {
                margin: 0;
                width: auto;
            }

            & > .el-input {
                grid-column: 1 / -1;
                width: 100%;
                margin: 0;
            }
        }
    }
}

.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .table {
        margin-top: 10px;

        .f-12 {
            font-size: 12px;
        }

        .card {
            margin-bottom: 8px;

            .card-title {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;

            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }

                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}

.el-link--default {
    vertical-align: inherit !important;
}
.list-checkbox-label {
    label {
        margin-bottom: 0 !important;
    }
}

.batch-operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #f0f9ff;
    border: 1px solid #d9ecff;
    border-radius: 4px;
    margin-bottom: 10px;

    .batch-info {
        font-size: 14px;

        .selected-count {
            color: #409eff;
            font-weight: bold;
            font-size: 16px;
        }
    }

    .batch-actions {
        display: flex;
        gap: 10px;
    }
}

.selectable-card {
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
        box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.2) !important;
        border-color: #c6e2ff;
    }
}

.selected-card {
    border: 2px solid #409eff !important;
    box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.2) !important;
}
.buyer-item {
    margin-bottom: 10px;

    .buyer-row {
        display: flex;
        align-items: center;

        .buyer-label {
            width: 80px;
            text-align: right;
            margin-right: 10px;
        }

        .ratio-label {
            margin: 0 10px;
        }
    }
}
</style>
