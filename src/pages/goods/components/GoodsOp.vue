<template>
    <el-form
        ref="labelAlignForm"
        :model="entity"
        :rules="rules"
        label-width="118px"
        style="overflow-y: auto"
    >
        <el-card class="box-card">
            <div style="display: flex; align-items: center">
                <span style="font-weight: bold; margin-right: 20px"
                    >采购信息</span
                >
                <el-button
                    type="primary"
                    size="mini"
                    @click="purchaseDialog = !purchaseDialog"
                    >编辑</el-button
                >
            </div>
            <!-- 采购信息 -->
            <div
                style="display: flex; flex-wrap: wrap; margin-top: 20px"
                v-if="purList.length > 0"
            >
                <div
                    v-for="(item, i) in purList"
                    :key="i"
                    v-show="!item.is_use_comment"
                    style="
                        min-width: 300px;
                        border: 1px solid #ddd;
                        padding: 10px;
                        margin-right: 10px;
                        margin-bottom: 10px;
                    "
                >
                    <div class="purchase_list">
                        <label>英文名</label>

                        <span>{{ item.en_product_name }}</span>
                    </div>
                    <div class="purchase_list">
                        <label>品名</label>
                        <span>{{ item.cn_product_name }}</span>
                    </div>
                    <div class="purchase_list">
                        <label>简码</label>
                        <span>{{ item.short_code }}</span>
                    </div>

                    <div class="purchase_list">
                        <label>发货仓库</label>
                        <span>{{
                            item.warehouse && typeof item.warehouse === "object"
                                ? item.warehouse.fictitious_name
                                : item.warehouse
                        }}</span>
                    </div>
                </div>
            </div>
            <div style="display: flex; align-items: center; margin: 20px 0">
                <span style="font-weight: bold; margin-right: 20px"
                    >套餐信息</span
                >
                <el-button type="primary" size="mini" @click="packEdit()"
                    >编辑</el-button
                >
            </div>
            <!-- 套餐信息 -->
            <div
                style="display: flex; flex-wrap: wrap; margin-top: 20px"
                v-if="
                    entity.salesList && entity.salesList[0].package_name != ''
                "
            >
                <div
                    v-for="(v, i) in entity.salesList"
                    :key="i"
                    style="
                        margin: 0px 20px 20px 0;
                        display: flex;
                        flex-direction: column;
                        border: 1px solid #ddd;
                        padding: 7px 15px;
                        width: 300px;
                    "
                >
                    <div
                        style="
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: 5px;
                        "
                    >
                        <span>{{ v.package_name }}</span>
                        <span v-if="rows.periods_type === 4"
                            >{{ v.rabbit }}
                        </span>
                        <span v-else>{{ v.price }}</span>
                        <span
                            style="text-decoration: line-through; color: silver"
                            >{{ v.market_price }}</span
                        >
                    </div>
                    <!-- 显示订金优惠券信息 -->
                    <div
                        v-if="v.deposit_coupon_id && v.deposit_coupon_id != 0"
                        style="font-size: 12px; color: #666; margin-top: 5px"
                    >
                        <div>订金优惠券ID: {{ v.deposit_coupon_id }}</div>
                        <div v-if="v.deposit_coupon_value">
                            订金优惠券面额: ¥{{ v.deposit_coupon_value }}
                        </div>
                    </div>
                </div>
            </div>
            <div style="display: flex; align-items: center">
                <span style="font-weight: bold; margin-right: 20px"
                    >商品信息</span
                >
                <el-button type="primary" size="mini" @click="goodsEdit"
                    >编辑</el-button
                >
            </div>
            <div
                style="display: flex; flex-wrap: wrap; margin-top: 20px"
                v-if="entity.onsale_time"
            >
                <div class="box_flex">
                    <el-form-item label="上架时间" label-width="70px">
                        <el-date-picker
                            v-model="entity.onsale_time"
                            :disabled="true"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            type="datetime"
                            placeholder="选择日期"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="开售时间">
                        <el-date-picker
                            v-model="entity.sell_time"
                            :disabled="true"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            type="datetime"
                            placeholder="选择日期"
                        >
                        </el-date-picker>
                    </el-form-item>
                </div>
                <div class="box_flex">
                    <el-form-item label="下架时间" label-width="70px">
                        <el-date-picker
                            v-model="entity.sold_out_time"
                            :disabled="true"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            type="datetime"
                            placeholder="选择日期"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="发货时间">
                        <el-date-picker
                            v-model="entity.predict_shipment_time"
                            :disabled="true"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            type="datetime"
                            placeholder="选择日期"
                        >
                        </el-date-picker>
                    </el-form-item>
                </div>
            </div>

            <div style="display: flex; align-items: center; margin-top: 20px">
                <span style="font-weight: bold; margin-right: 20px"
                    >商品标签</span
                >
                <el-button type="primary" size="mini" @click="goodTagEdit"
                    >编辑</el-button
                >
            </div>
        </el-card>
        <!-- 商品信息 -->
        <el-card class="box-card" shadow="hover" style="margin: 10px 0 10px">
            <div slot="header" class="clearfix">
                <span>基本信息</span
                ><!-- ({{nums}}) -->
            </div>
            <div class="text item">
                <el-row :gutter="10">
                    <el-col
                        :md="10"
                        :lg="10"
                        :xl="10"
                        v-if="rows.periods_type === 1"
                    >
                        <el-form-item label="商品标题" prop="title">
                            <el-input
                                v-model="entity.title"
                                placeholder="请输入商品标题"
                                maxlength="60"
                                show-word-limit
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :md="10" :lg="10" :xl="10" v-else>
                        <el-form-item label="商品标题" prop="title">
                            <el-input
                                v-model="entity.title"
                                placeholder="请输入商品标题"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :md="10" :lg="10" :xl="10" :offset="2">
                        <el-form-item label="一句话介绍" prop="brief">
                            <el-input
                                v-model="entity.brief"
                                placeholder="请输入商品副标题"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- <el-row :gutter="10" v-if="rows.periods_type === 1">
                    <el-col :md="10" :lg="10" :xl="10">
                        <el-form-item label="预览标题" prop="title1">
                            <el-input
                                v-model="entity.title1"
                                placeholder="请输入商品标题"
                                :disabled="disab"
                                maxlength="30"
                                show-word-limit
                            />
                        </el-form-item>
                    </el-col>
                </el-row> -->
                <el-row :gutter="10">
                    <el-col :md="7" :lg="7" :xl="7">
                        <el-form-item
                            label="上传题图"
                            class="upload"
                            prop="filelist1"
                            style="margin-bottom: 30px"
                        >
                            <vos-oss
                                list-type="picture-card"
                                :showFileList="true"
                                :limit="1"
                                :dir="dir"
                                :file-list="entity.filelist1"
                            >
                                <i slot="default" class="el-icon-plus"></i>
                            </vos-oss>
                            <!-- <SingleImage ref="singleupload" v-model="entity.banner_img"></SingleImage> -->
                        </el-form-item>
                    </el-col>
                    <el-col :md="6" :lg="6" :xl="6">
                        <div style="width: 100%; height: 200px">
                            <el-form-item
                                label="上传题图(竖)"
                                prop="filelist2"
                                v-if="rows.periods_type == 1"
                            >
                                <br />
                                <div style="display: flex">
                                    <vos-oss
                                        list-type="picture-card"
                                        :showFileList="true"
                                        :limit="8"
                                        :dir="dir"
                                        :file-list="entity.filelist2"
                                    >
                                        <i
                                            slot="default"
                                            class="el-icon-plus"
                                        ></i>
                                    </vos-oss>
                                </div>
                            </el-form-item>
                        </div>
                    </el-col>
                    <el-col :md="11" :lg="11" :xl="11">
                        <el-form-item
                            :label="
                                rows.periods_type == 1
                                    ? '上传产品图(方)'
                                    : '上传产品图'
                            "
                            prop="filelist3"
                        >
                            <div>(最多支持8张)</div>
                            <vos-oss
                                list-type="picture-card"
                                :showFileList="true"
                                :dir="dir"
                                :file-list="entity.filelist3"
                                :limit="8"
                                :multiple="true"
                            >
                                <i slot="default" class="el-icon-plus"></i>
                            </vos-oss>
                            <!-- <UploadImage ref="UploadImage" v-model="entity.product_img1"></UploadImage> -->
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :md="10" :lg="10" :xl="10">
                        <el-form-item label="视频封面图">
                            <vos-oss
                                list-type="picture-card"
                                :showFileList="true"
                                :dir="dir"
                                :file-list="coverList"
                                :limit="1"
                                :multiple="true"
                            >
                                <i slot="default" class="el-icon-plus"></i>
                            </vos-oss>
                        </el-form-item>
                    </el-col>
                    <el-col :md="10" :lg="10" :xl="10" :offset="2">
                        <el-form-item label="视频上传">
                            <vos-vod
                                @uploadSuccess="uploadSuccess"
                                :playurl.sync="entity.video"
                            ></vos-vod>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="采购说">
                    <el-input
                        type="textarea"
                        maxlength="500"
                        :autosize="$componentsConfig.textareaRow()"
                        v-model="entity.purchasing_said"
                        show-word-limit
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="商品详情" prop="detail">
                    <Tinymce
                        ref="editor"
                        @singleValidate="singleValidate"
                        v-model.trim="entity.detail"
                        :height="300"
                    />
                </el-form-item>
                <!-- <el-form-item label="采购人" prop="description">
                        <el-select v-model="entity.buyers" placeholder="请选择">
                            <el-option v-for="item in purchaseOptions" :key="item.id" :label="item.realname"
                                :value="item" :value-key="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item> -->
            </div>
        </el-card>
        <!-- 产品信息 -->
        <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
                <span>产品信息</span>
            </div>
            <el-card
                v-for="(item, i) in entity.product_list"
                :key="i"
                style="margin-bottom: 10px"
            >
                <div class="article_product_wrap">
                    <div class="article_product_item">
                        <div>
                            <label>品名：</label>
                            <span>
                                {{
                                    item.en_product_name
                                        ? item.en_product_name
                                        : "-"
                                }}
                            </span>
                        </div>
                    </div>
                    <div class="article_product_item">
                        <div>
                            <label>类别：</label>
                            <span>
                                {{
                                    item.productcategoryname
                                        ? item.productcategoryname
                                        : "-"
                                }}</span
                            >
                        </div>
                        <div>
                            <label>酒庄：</label>
                            <span class="f-12">
                                {{
                                    item.chateau_name ? item.chateau_name : "-"
                                }}
                            </span>
                        </div>
                        <div>
                            <label>产区：</label>
                            <span class="f-12">
                                {{
                                    item.producing_area_name
                                        ? item.producing_area_name
                                        : "-"
                                }}
                            </span>
                        </div>
                        <div>
                            <label>国家：</label>
                            <span class="f-12">
                                {{
                                    item.country_name ? item.country_name : "-"
                                }}
                            </span>
                        </div>
                        <div>
                            <label>葡萄：</label>
                            <span
                                v-for="(v, index) in item.Grapelist"
                                :key="index"
                                style="margin-right: 2px"
                            >
                                {{ v.gname_cn }}
                            </span>
                        </div>
                    </div>
                    <div class="article_product_item">
                        <div>
                            <label>残糖：</label>
                            <span>
                                {{
                                    item.residual_sugar
                                        ? item.residual_sugar
                                        : "-"
                                }}<span v-if="item.residual_sugar"
                                    >g/L</span
                                ></span
                            >
                        </div>
                        <div>
                            <label>酒精度：</label>
                            <span class="f-12">
                                {{ item.alcohol ? item.alcohol : "-"
                                }}<span v-if="item.alcohol">%vol</span>
                            </span>
                        </div>
                        <div>
                            <label>容量：</label>
                            <span class="f-12">
                                {{ item.capacity ? item.capacity : "-" }}
                            </span>
                        </div>
                        <div>
                            <label>类型：</label>
                            <span class="f-12">
                                {{
                                    item.product_type_name
                                        ? item.product_type_name
                                        : "-"
                                }}
                            </span>
                        </div>
                        <div>
                            <label>关键词：</label>
                            <template
                                v-if="item.product_keywords_id.length > 0"
                            >
                                <span
                                    v-for="(
                                        v, index
                                    ) in item.product_keywords_id"
                                    :key="index"
                                    style="margin-right: 5px"
                                >
                                    {{ v.name }}
                                </span>
                            </template>
                            <template v-else>
                                <span style="margin-right: 5px"> - </span>
                            </template>
                        </div>
                    </div>
                    <div class="m-b-10">
                        <label>酿造工艺：</label>
                        <el-input
                            type="textarea"
                            disabled
                            @input="$forceUpdate()"
                            :autosize="$componentsConfig.textareaRow()"
                            v-model="item.brewing"
                        >
                        </el-input>
                    </div>
                    <div class="m-b-10">
                        <label>评分：</label>
                        <el-input
                            type="textarea"
                            @input="$forceUpdate()"
                            :autosize="$componentsConfig.textareaRow()"
                            placeholder="请输入评分"
                            v-model="item.score"
                        >
                        </el-input>
                    </div>

                    <div class="m-b-10">
                        <label>Tasting Notes：</label>
                        <el-input
                            type="textarea"
                            @input="$forceUpdate()"
                            :autosize="$componentsConfig.textareaRow()"
                            placeholder="请输入Tasting Notes"
                            v-model="item.tasting_notes"
                        >
                        </el-input>
                    </div>
                    <div class="m-b-10">
                        <label>获奖：</label>
                        <el-input
                            type="textarea"
                            :autosize="$componentsConfig.textareaRow()"
                            @input="$forceUpdate()"
                            placeholder="请输入获奖"
                            v-model="item.prize"
                        >
                        </el-input>
                    </div>
                    <div class="m-b-10">
                        <label>饮用建议：</label>
                        <el-input
                            type="textarea"
                            @input="$forceUpdate()"
                            :autosize="$componentsConfig.textareaRow()"
                            placeholder="请输入饮用建议"
                            v-model="item.drinking_suggestion"
                        >
                        </el-input>
                    </div>
                </div> </el-card
        ></el-card>
        <el-form-item label class="clearfix">
            <div style="float: right; margin-top: 20px">
                <el-button type="primary" @click="submits">保存</el-button>
            </div>
        </el-form-item>
        <el-dialog
            title="采购信息"
            :visible.sync="purchaseDialog"
            append-to-body
            destroy-on-close
            width="1200px"
            :close-on-click-modal="false"
        >
            <div v-if="purchaseDialog">
                <div class="pur_rwap" style="justify-content: space-between">
                    <div class="pur_rwap">
                        <div class="pur_inline">
                            <label>进口类型</label>
                            <el-select
                                :value="entity.import_type"
                                @change="onImportTypeChange"
                                style="width: 110px"
                            >
                                <el-option
                                    label="跨境"
                                    :value="2"
                                    v-if="rows.periods_type === 2"
                                />
                                <el-option
                                    label="自进口"
                                    :value="0"
                                    v-if="rows.periods_type !== 2"
                                />
                                <el-option
                                    label="地采"
                                    :value="1"
                                    v-if="rows.periods_type !== 2"
                                />
                            </el-select>
                        </div>
                        <div>
                            <div class="pur_inline">
                                <label>供应商</label>
                                <el-select
                                    :value="entity.suppliers"
                                    @input="onSuppliersInput"
                                    filterable
                                    remote
                                    reserve-keyword
                                    placeholder="请输入供应商"
                                    :remote-method="remoteMethod"
                                    value-key="id"
                                    :loading="loading"
                                    :disabled="
                                        (entity.onsale_review_status === 3 &&
                                            entity.onsale_status) ||
                                        entity.import_type === ''
                                    "
                                >
                                    <el-option
                                        v-for="item in supplierOpration"
                                        :key="item.id"
                                        :label="item.supplier_name"
                                        :value="item"
                                    >
                                    </el-option>
                                </el-select>
                            </div>
                            <!--  <el-select v-model="entity.suppliers" @change="updatePurchase" placeholder="供应商"
                                value-key="id">
                                <el-option v-for="item in supplierOpration" :key="item.id" :label="item.supplier_name"
                                    :value="item">
                                </el-option>
                            </el-select> -->
                            <div style="color: red">
                                收款账户：{{
                                    entity.payee_merchant_name || "无"
                                }}
                            </div>
                        </div>
                        <div class="pur_inline">
                            <label>采购人</label>
                            <el-select
                                @change="updatePurchase"
                                v-model="entity.buyers1"
                                value-key="id"
                                style="width: 110px"
                            >
                                <el-option
                                    v-for="(item, i) in purchaseOptions"
                                    :key="item.id"
                                    :label="item.realname"
                                    :value="item"
                                />
                            </el-select>
                        </div>
                        <div>
                            <div
                                class="pur_inline"
                                v-if="
                                    rows.periods_type != 2 &&
                                    rows.periods_type != 3
                                "
                            >
                                <label>是否代发</label>
                                <!--  <el-switch style="display: block;margin-top: 8px;" @change="updatePurchase"
                                    v-model="entity.is_supplier_delivery" active-color="#13ce66" inactive-color="#ff4949">
                                </el-switch> -->
                                <el-radio-group
                                    style="padding-top: 12px"
                                    :value="entity.is_supplier_delivery"
                                    @input="onIsSupplierDeliveryInput"
                                >
                                    <el-radio
                                        v-for="(item, key) in operations"
                                        :key="key"
                                        :label="item.label"
                                    >
                                        {{ item.value }}
                                    </el-radio>
                                </el-radio-group>
                            </div>
                            <div v-if="entity.is_supplier_delivery == 1">
                                <span
                                    style="color: red"
                                    v-if="entity.supplier_delivery_weekend == 0"
                                    >周末发货、</span
                                >
                                <span style="color: red">{{
                                    supplier_delivery_time_text[
                                        entity.supplier_delivery_time
                                    ]
                                }}</span>
                            </div>
                        </div>

                        <div
                            class="pur_inline"
                            v-if="
                                rows.periods_type != 3 && rows.periods_type != 1
                            "
                        >
                            <label>是否预售</label>
                            <!-- <el-switch style="display: block;margin-top: 8px;" @change="updatePurchase"
                                    v-model="entity.is_presell" active-color="#13ce66" inactive-color="#ff4949">
                                </el-switch> -->
                            <el-radio-group
                                style="padding-top: 12px"
                                v-model="entity.is_presell"
                                @change="updatePurchase"
                            >
                                <el-radio
                                    v-for="(item, key) in operations"
                                    :key="key"
                                    :label="item.label"
                                >
                                    {{ item.value }}
                                </el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div>
                        <el-button size="small" type="primary" @click="adds"
                            >继续添加
                        </el-button>
                    </div>
                </div>
                <div
                    v-for="(v, index) in entity.purchaseList"
                    :key="index"
                    style="margin-top: 15px"
                >
                    <el-row>
                        <el-col :md="8" :lg="8" :xl="8">
                            <el-form-item
                                label="简码"
                                :prop="'purchaseList.' + index + '.short_code'"
                                :rules="rules.short_code"
                            >
                                <el-input
                                    v-model="v.short_code"
                                    :disabled="!v.isDel"
                                    placeholder="请输入简码"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :md="2" :lg="2" :xl="2">
                            <el-button
                                v-if="v.isDel"
                                style="margin-left: 20px; margin-top: 6px"
                                type="primary"
                                size="mini"
                                @click="searchCode(v.short_code)"
                                >搜索
                            </el-button>
                        </el-col>
                        <el-col :md="2" :lg="2" :xl="2">
                            <el-button
                                v-if="
                                    entity.purchaseList &&
                                    entity.purchaseList.length > 1 &&
                                    v.isDel
                                "
                                size="mini"
                                type="danger"
                                style="margin-left: 20px; margin-top: 6px"
                                icon="el-icon-delete"
                                @click="del(index)"
                                >删除
                            </el-button>
                        </el-col>
                    </el-row>
                    <div
                        v-if="v.purchase.length > 0"
                        v-for="(item, i) in v.purchase"
                        :class="item.checked ? 'purchaseActive' : 'purchase'"
                    >
                        <!-- <div>
                                <el-row :gutter="10">
                                    <el-col :md="2" :lg="2" :xl="2">
                                        <el-checkbox v-model="item.checked" @change="checkedStore(item,v.purchase)"
                                            style="margin: 63px 10px;">
                                        </el-checkbox>
                                    </el-col>
                                </el-row>
                            </div> -->
                        <div
                            style="
                                padding-left: 20px;
                                width: 100%;
                                position: relative;
                            "
                        >
                            <div class="pur_rwap">
                                <div class="pur_inline">
                                    <label>英文名</label>
                                    <span>
                                        <div style="width: 700px">
                                            {{ item.en_product_name
                                            }}<i
                                                @click="
                                                    copyName(
                                                        item.en_product_name
                                                    )
                                                "
                                                style="
                                                    font-size: 18px;
                                                    color: #409eff;
                                                    cursor: pointer;
                                                    margin-left: 5px;
                                                "
                                                class="el-icon-document-copy"
                                            ></i>
                                        </div>
                                    </span>
                                    <el-checkbox
                                        v-model="item.is_use_comment"
                                        :true-label="1"
                                        @change="$forceUpdate()"
                                        :false-label="0"
                                        >仅用于同步评论</el-checkbox
                                    >
                                    <el-checkbox
                                        v-model="item.is_hidden_param"
                                        :true-label="1"
                                        @change="$forceUpdate()"
                                        :false-label="0"
                                        >前端隐藏参数</el-checkbox
                                    >
                                </div>
                            </div>
                            <div class="pur_rwap">
                                <div class="pur_inline">
                                    <label>品名</label>
                                    <span>
                                        {{ item.cn_product_name }}
                                        <i
                                            @click="
                                                copyName(item.cn_product_name)
                                            "
                                            style="
                                                font-size: 18px;
                                                color: #409eff;
                                                cursor: pointer;
                                                margin-left: 5px;
                                            "
                                            class="el-icon-document-copy"
                                        ></i
                                    ></span>
                                </div>
                            </div>
                            <div class="pur_rwap">
                                <div class="pur_inline">
                                    <label>自选名称</label>
                                    <span>
                                        <el-input
                                            v-model="item.custom_product_name"
                                            placeholder="请输入自选名称"
                                            style="
                                                width: 260px;
                                                margin-right: 5px;
                                            "
                                        >
                                        </el-input>
                                        <el-tooltip
                                            slot="suffix"
                                            effect="dark"
                                            content="当产品在自选套餐中，设置‘自选名称’后用户会看到该名称"
                                            placement="top"
                                        >
                                            <i class="el-icon-question"></i>
                                        </el-tooltip>
                                    </span>
                                </div>
                            </div>
                            <div class="pur_rwap">
                                <div class="pur_inline">
                                    <label>国家</label>

                                    <span> {{ item.country_name }}</span>
                                </div>
                                <div class="pur_inline">
                                    <label>容量</label>
                                    <span> {{ item.capacity }}</span>
                                </div>
                                <div class="pur_inline">
                                    <label>年份</label>
                                    <span> {{ item.grape_picking_years }}</span>
                                </div>
                                <div class="pur_inline">
                                    <label>简码</label>
                                    <span> {{ item.short_code }}</span>
                                </div>
                                <div class="pur_inline">
                                    <label>箱规</label>
                                    <span> {{ item.carton_dimension }}</span>
                                </div>
                            </div>
                            <div class="pur_rwap">
                                <div class="pur_inline">
                                    <label>类型</label>
                                    <span> {{ item.product_type_name }}</span>
                                </div>
                                <div class="pur_inline">
                                    <label>参考成本</label>
                                    <span style="display: flex">
                                        {{ item.costprice1 }}
                                    </span>
                                </div>
                                <div class="pur_inline">
                                    <label>发货仓库</label>
                                    <span>
                                        <el-form-item
                                            label-width="0px"
                                            :prop="
                                                'purchaseList.' +
                                                index +
                                                '.purchase.' +
                                                i +
                                                '.warehouse'
                                            "
                                            :rules="rules.warehouse"
                                        >
                                            <!-- :disabled="!v.isDel" -->
                                            <el-select
                                                v-model="item.warehouse"
                                                filterable
                                                :disabled="
                                                    !(
                                                        !onsale_verify_status ||
                                                        v.isDel
                                                    )
                                                "
                                                size="mini"
                                                style="width: 400px"
                                                value-key="fictitious_id"
                                            >
                                                <el-option
                                                    v-for="(
                                                        v, i
                                                    ) in sendWarehouseList"
                                                    :key="v.id"
                                                    :label="v.fictitious_name"
                                                    :value="v"
                                                    :disabled="v.disabled"
                                                >
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </span>
                                </div>
                            </div>
                            <div class="pur_rwap">
                                <div class="pur_inline">
                                    <div style="display: flex">
                                        <label style="padding-top: 10px"
                                            >仓库信息</label
                                        >
                                        <div style="margin: 0 10px">
                                            <div
                                                v-show="
                                                    item.warehouseList &&
                                                    item.warehouseList.length >
                                                        0
                                                "
                                                v-for="(
                                                    v, i
                                                ) in item.warehouseList"
                                                style="display: flex"
                                                :key="v.fictitious_id"
                                            >
                                                <div style="min-width: 160px">
                                                    {{ v.fictitious_name }}
                                                </div>
                                                <label
                                                    style="
                                                        display: inline-block;
                                                        width: 40px;
                                                    "
                                                    >库存</label
                                                >
                                                <span>
                                                    {{ v.goods_count }}</span
                                                >
                                            </div>
                                            <div
                                                v-show="
                                                    item.warehouseList &&
                                                    item.warehouseList.length ==
                                                        0
                                                "
                                                style="margin-top: 10px"
                                            >
                                                暂无仓库信息
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="pur_inline">
                                        <label>售卖库存</label>
                                        <span>
                                            <el-form-item
                                                label-width="0px"
                                                :prop="
                                                    'purchaseList.' +
                                                    index +
                                                    '.purchase.' +
                                                    i +
                                                    '.inventory'
                                                "
                                                :rules="rules.inventory"
                                            >
                                                <!-- :disabled="!v.isDel" -->
                                                <el-input
                                                    :disabled="
                                                        !(
                                                            !onsale_verify_status ||
                                                            v.isDel
                                                        )
                                                    "
                                                    v-model="item.inventory"
                                                    size="mini"
                                                    placeholder="售卖库存"
                                                />
                                            </el-form-item>
                                        </span>
                                    </div>
                                    <div class="pur_inline">
                                        <label>本期成本</label>
                                        <span>
                                            <el-form-item
                                                label-width="0px"
                                                :prop="
                                                    'purchaseList.' +
                                                    index +
                                                    '.purchase.' +
                                                    i +
                                                    '.costprice'
                                                "
                                                :rules="rules.costprice"
                                            >
                                                <!-- :disabled="!v.isDel" -->
                                                <el-input
                                                    :disabled="
                                                        !(
                                                            !onsale_verify_status ||
                                                            v.isDel
                                                        )
                                                    "
                                                    v-model="item.costprice"
                                                    size="mini"
                                                    style="width: 100px"
                                                    placeholder="本期成本"
                                                />
                                            </el-form-item>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div
                                @click="deletewithGoods(v, index)"
                                style="
                                    position: absolute;
                                    right: 0;
                                    bottom: 0;
                                    width: 80px;
                                    height: 60px;
                                    text-align: center;
                                    color: red;
                                "
                            >
                                删除
                            </div>
                        </div>
                    </div>
                </div>
                <span
                    slot="footer"
                    class="dialog-footer"
                    style="display: flex; justify-content: flex-end"
                >
                    <el-button type="primary" @click="handleNext(1, false)"
                        >下一步</el-button
                    >
                    <el-button type="primary" @click="handleNext(1, true)"
                        >保存</el-button
                    >
                </span>
            </div>
        </el-dialog>
        <el-dialog
            title="套餐信息"
            :visible.sync="packDialog"
            append-to-body
            destroy-on-close
            width="70%"
            :close-on-click-modal="false"
        >
            <div v-if="packDialog">
                <div style="text-align: right; margin-bottom: 20px">
                    <el-button size="small" type="primary" @click="addSales"
                        >继续添加
                    </el-button>
                </div>
                <div
                    v-for="(v, index) in entity.salesList"
                    :key="index"
                    class="sale_list_wrap"
                >
                    <div style="display: flex">
                        <el-form-item
                            label="套餐名称"
                            :prop="'salesList.' + index + '.package_name'"
                            :rules="rules.package_name"
                            label-width="100px"
                        >
                            <el-select
                                v-model="v.package_name"
                                :disabled="v.is_onsale"
                                placeholder="请选择"
                                @change="packageChange(v)"
                            >
                                <el-option
                                    v-for="item in parckageOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <div
                            v-if="
                                v.package_name == '其他' ||
                                v.is_mystery_box == 1 ||
                                v.package_name == '自选'
                            "
                        >
                            <el-form-item
                                label="自定义名称"
                                :prop="'salesList.' + index + '.package_name1'"
                                :rules="rules.package_name1"
                                label-width="100px"
                            >
                                <el-input
                                    v-model="v.package_name1"
                                    :disabled="v.is_onsale"
                                    @blur="changePack(v)"
                                    style="width: 200px"
                                    placeholder="自定义名称"
                                />
                            </el-form-item>
                        </div>
                        <div
                            style="margin: 8px 0 0 30px"
                            v-if="
                                v.package_name != '其他' &&
                                v.is_mystery_box != 1 &&
                                v.package_name != '单支套餐' &&
                                v.package_name != '自选'
                            "
                        >
                            <el-checkbox
                                v-model="v.same"
                                :disabled="v.is_onsale"
                                @change="sameChange(v)"
                            >
                                套餐内不相同</el-checkbox
                            >
                        </div>
                        <el-row v-if="rows.periods_type == 0" type="flex">
                            <el-checkbox
                                style="margin: 8px 0 0 30px"
                                v-model="v.is_deposit"
                                :true-label="1"
                                :false-label="0"
                                :disabled="v.is_onsale"
                                >订金套餐</el-checkbox
                            >
                            <el-row v-if="v.is_deposit" type="flex">
                                <el-form-item
                                    label="订金金额"
                                    label-width="100px"
                                    :prop="
                                        'salesList.' + index + '.deposit_price'
                                    "
                                    :rules="rules.deposit_price"
                                >
                                    <el-input
                                        v-model="v.deposit_price"
                                        :disabled="v.is_onsale"
                                    />
                                </el-form-item>
                                <el-form-item
                                    style="margin-left: 5px"
                                    label=" "
                                    label-width="10px"
                                    required
                                >
                                    <el-row type="flex">
                                        <span style="margin-right: 5px"
                                            >满</span
                                        >
                                        <el-form-item
                                            label-width="0"
                                            :prop="
                                                'salesList.' +
                                                index +
                                                '.deposit_coupon_threshold'
                                            "
                                            :rules="rules.deposit_price"
                                        >
                                            <el-input
                                                v-model="
                                                    v.deposit_coupon_threshold
                                                "
                                                :disabled="v.is_onsale"
                                            />
                                        </el-form-item>
                                        <span style="margin: 0 5px">减</span>
                                        <el-form-item
                                            label-width="0"
                                            :prop="
                                                'salesList.' +
                                                index +
                                                '.deposit_coupon_value'
                                            "
                                            :rules="rules.deposit_price"
                                        >
                                            <el-input
                                                v-model="v.deposit_coupon_value"
                                                :disabled="v.is_onsale"
                                            />
                                        </el-form-item>
                                    </el-row>
                                </el-form-item>
                            </el-row>
                        </el-row>
                        <div v-if="entity.salesList.length > 1">
                            <el-button
                                :disabled="v.is_onsale"
                                v-if="entity.salesList && v.isDel"
                                size="mini"
                                type="danger"
                                style="margin-left: 20px; margin-top: 6px"
                                icon="el-icon-delete"
                                @click="delSales(index)"
                                >删除套餐</el-button
                            >

                            <div v-else>
                                <el-button
                                    v-if="!rows.onsale_verify_status"
                                    type="danger"
                                    size="mini"
                                    style="margin-left: 20px; margin-top: 6px"
                                    icon="el-icon-delete"
                                    @click="deletePackage(v, index)"
                                    >删除套餐
                                </el-button>
                            </div>
                        </div>
                    </div>
                    <div style="display: flex">
                        <el-form-item
                            label="兔头"
                            v-if="rows.periods_type == 4"
                            :prop="'salesList.' + index + '.rabbit'"
                            :rules="rules.rabbit"
                            label-width="60px"
                        >
                            <el-input
                                v-model="v.rabbit"
                                @blur="changeRabbit(v)"
                                :disabled="v.is_onsale"
                                placeholder="兔头"
                            />
                        </el-form-item>
                        <span
                            v-if="rows.periods_type == 4"
                            style="padding-top: 10px; padding-left: 10px"
                            >=</span
                        >
                        <el-form-item
                            label="售价"
                            :prop="'salesList.' + index + '.price'"
                            :rules="rules.price"
                            label-width="60px"
                        >
                            <el-input
                                v-model="v.price"
                                :disabled="
                                    v.is_onsale || rows.periods_type == 4
                                "
                                placeholder="售价"
                            />
                        </el-form-item>
                        <el-form-item
                            label="市场价"
                            :prop="'salesList.' + index + '.market_price'"
                            :rules="rules.market_price"
                            label-width="80px"
                        >
                            <el-input
                                v-model="v.market_price"
                                :disabled="v.is_onsale"
                                placeholder="市场价"
                            />
                        </el-form-item>
                        <el-form-item
                            label="套餐图"
                            label-width="80px"
                            style="flex: 1; overflow: hidden"
                        >
                            <div style="display: flex; overflow: scroll">
                                <div
                                    v-for="(item, index) in getProductImgList(
                                        v
                                    )"
                                    :key="index"
                                    :style="{
                                        flexShrink: 0,
                                        position: 'relative',
                                        display: 'flex',
                                        width: '80px',
                                        height: '80px',
                                        marginLeft: index ? '10px' : 0,
                                        borderRadius: '4px',
                                        cursor: 'pointer',
                                    }"
                                    @click="
                                        v.is_onsale
                                            ? ''
                                            : (v.package_img =
                                                  v.package_img === item
                                                      ? ''
                                                      : item)
                                    "
                                >
                                    <img
                                        :src="`${$BASE.OSSDomain}${item}`"
                                        :style="{
                                            width: '100%',
                                            height: '100%',
                                        }"
                                    />
                                    <div
                                        v-if="
                                            v.package_img.replace(
                                                $BASE.OSSDomain,
                                                ''
                                            ) === item
                                        "
                                        :style="{
                                            position: 'absolute',
                                            inset: 0,
                                            display: 'flex',
                                            flexDirection: 'column',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            background:
                                                'rgba(206,206,206,0.69)',
                                        }"
                                    >
                                        <i
                                            class="el-icon-check"
                                            style="
                                                font-size: 25px;
                                                color: rgb(189, 49, 36);
                                            "
                                        ></i>
                                        <div style="color: rgb(189, 49, 36)">
                                            已选中
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-form-item>
                    </div>
                    <!-- 套餐内设置 -->
                    <div
                        v-if="v.package_name"
                        style="margin-left: 30px; position: relative"
                        v-for="(child, j) in v.numList"
                        :key="j"
                    >
                        <div class="box_flex">
                            <div>
                                <div
                                    class="sale_addInfo sale_addInfo_p"
                                    @click="handleCodeVisible(v, child)"
                                    v-if="!child.visible"
                                >
                                    <i class="el-icon-circle-plus-outline"></i>
                                </div>
                                <!-- 选择框 -->
                                <div
                                    class="btn_visib_wrap sale_visi_wrap"
                                    v-show="child.codeVisible"
                                >
                                    <div class="btn_visib_content">
                                        <div
                                            class="code_close"
                                            style="margin-top: 4px"
                                            @click="handleCodeVisible(v, child)"
                                        >
                                            <i class="el-icon-close"></i>
                                        </div>
                                        <div class="code_content">
                                            <el-select
                                                v-if="
                                                    v.is_mystery_box == 1 ||
                                                    v.package_name == '自选'
                                                "
                                                v-model="child.productList"
                                                multiple
                                                collapse-tags
                                                style="width: 600px"
                                                @change="productChange(child)"
                                                @visible-change="
                                                    visibleChange(
                                                        $event,
                                                        child,
                                                        v
                                                    )
                                                "
                                                placeholder="请选择"
                                                value-key="id"
                                            >
                                                <el-option
                                                    v-for="j in purList"
                                                    :key="j.id"
                                                    :label="j.en_product_name"
                                                    :value="j"
                                                >
                                                </el-option>
                                            </el-select>
                                            <div
                                                v-else
                                                v-for="(j, i) in purList"
                                                class="code_wrap"
                                                :key="i"
                                                @click="confirmPur(j, child, v)"
                                            >
                                                {{ j.en_product_name }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 选择之后的数据 -->
                                <!-- 选择之后的数据 -->
                                <div
                                    class="sale_addInfo"
                                    v-if="
                                        child.visible &&
                                        child.productList.length > 0
                                    "
                                    @click="handleCodeVisible(v, child)"
                                >
                                    <div
                                        v-for="(p, k) in child.productList"
                                        :style="
                                            k > 0
                                                ? 'border-top: 1px solid #ddd;'
                                                : ''
                                        "
                                        style="padding: 10px"
                                        :key="k"
                                    >
                                        <div class="pur_list_rwap">
                                            <div class="pur_item">
                                                <label>英文名</label>
                                                <span>
                                                    {{
                                                        p.en_product_name
                                                    }}</span
                                                >
                                                <span
                                                    v-if="child.isGift"
                                                    style="
                                                        color: #ebb563;
                                                        margin-left: 20px;
                                                    "
                                                    >赠品</span
                                                >
                                            </div>
                                        </div>
                                        <div class="pur_list_rwap">
                                            <div class="pur_item">
                                                <label>品名</label>
                                                <span>
                                                    {{
                                                        p.cn_product_name
                                                    }}</span
                                                >
                                            </div>
                                        </div>
                                        <div class="pur_list_rwap">
                                            <div class="pur_item">
                                                <label>容量</label>
                                                <span> {{ p.capacity }}</span>
                                            </div>
                                            <div class="pur_item">
                                                <label>年份</label>
                                                <span>
                                                    {{
                                                        p.grape_picking_years
                                                    }}</span
                                                >
                                            </div>
                                            <div class="pur_item">
                                                <label>简码</label>
                                                <span> {{ p.short_code }}</span>
                                            </div>
                                             <div class="pur_item">
                                    <label>箱规</label>
                                    <span> {{ p.carton_dimension }}</span>
                                </div>
                                            <!-- <div class="pur_item">
                                                 <label>匹配度</label>
                                                 <span> {{child.product.short_code}}</span>
                                             </div> -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="margin-left: 20px">
                                <div
                                    v-if="
                                        v.numList.length > 1 &&
                                        child.isDel &&
                                        j > 0
                                    "
                                    style="padding-bottom: 30px"
                                >
                                    <el-button
                                        :disabled="v.is_onsale"
                                        type="danger"
                                        style="margin-left: 5px"
                                        size="mini"
                                        @click="delProduct(v, j)"
                                        >删除
                                    </el-button>
                                </div>
                                <div
                                    v-if="
                                        j == 0 &&
                                        (v.package_name == '其他' ||
                                            v.is_mystery_box == 1 ||
                                            v.package_name == '自选')
                                    "
                                    style="padding-bottom: 30px"
                                >
                                    <el-button
                                        type="primary"
                                        :disabled="v.is_onsale"
                                        style="margin-left: 5px"
                                        size="mini"
                                        @click="addProduct(v)"
                                        >添加
                                    </el-button>
                                </div>
                                <el-form-item
                                    :label="
                                        v.package_name == '自选'
                                            ? '自选数量'
                                            : '数量'
                                    "
                                    label-width="100px"
                                    :prop="
                                        'salesList.' +
                                        index +
                                        '.numList.' +
                                        j +
                                        (v.package_name == '自选'
                                            ? '.custom_product_count'
                                            : '.nums')
                                    "
                                    :rules="numberRules"
                                >
                                    <el-input-number
                                        v-if="v.package_name == '自选'"
                                        v-model.number="
                                            child.custom_product_count
                                        "
                                        :min="0"
                                        :disabled="child.disabled"
                                        label="自选数量"
                                    ></el-input-number>
                                    <el-input-number
                                        v-else
                                        v-model.number="child.nums"
                                        :min="0"
                                        :disabled="child.disabled"
                                        label="数量"
                                    ></el-input-number>
                                </el-form-item>
                                <template
                                    v-if="
                                        v.numList.length > 1 &&
                                        rows.periods_type === 2
                                    "
                                >
                                    <el-form-item
                                        label="拆分套餐名"
                                        label-width="100px"
                                        :prop="
                                            'salesList.' +
                                            index +
                                            '.numList.' +
                                            j +
                                            '.sub_package_name'
                                        "
                                        :rules="[
                                            {
                                                required: true,
                                                message: '请输入拆分套餐名',
                                                trigger: 'blur',
                                            },
                                        ]"
                                    >
                                        <el-input
                                            v-model="child.sub_package_name"
                                            maxlength="6"
                                            show-word-limit
                                            :disabled="v.is_onsale"
                                        ></el-input>
                                    </el-form-item>
                                    <el-form-item
                                        v-if="child.sub_package_price"
                                        label="拆分套餐金额"
                                        label-width="100px"
                                    >
                                        <el-input
                                            v-model="child.sub_package_price"
                                            disabled
                                        ></el-input>
                                    </el-form-item>
                                </template>
                                <!-- <el-form-item label="套餐图" label-width="60px">
                                    <vos-oss
                                        list-type="picture-card"
                                        :showFileList="true"
                                        :limit="1"
                                        :dir="dir"
                                        :file-list="
                                            v.package_img ? [v.package_img] : []
                                        "
                                        :limitWhList="[750, 464]"
                                        :disabled="v.is_onsale"
                                        @on-success="
                                            v.package_img = $event.file
                                        "
                                    >
                                        <i
                                            slot="default"
                                            class="el-icon-plus"
                                        ></i>
                                        <div
                                            style="
                                                margin-top: 5px;
                                                font-size: 12px;
                                                line-height: 17px;
                                                text-align: left;
                                                width: 180px;
                                            "
                                        >
                                            *如不上传则默认显示商品图
                                        </div>
                                    </vos-oss>
                                </el-form-item> -->
                            </div>
                        </div>
                    </div>
                    <div
                        class="box_flex"
                        style="line-height: 40px; margin-left: 25px"
                        v-if="v.visible"
                    >
                        <el-checkbox v-model="v.is_hidden"
                            >隐藏套餐</el-checkbox
                        >
                        <el-checkbox
                            v-if="rows.periods_type != 2"
                            v-model="v.is_original_package"
                            :disabled="v.is_onsale || v.force_original_package"
                            >原箱发货
                        </el-checkbox>
                        <el-checkbox
                            v-if="rows.periods_type != 2"
                            @change="force_original_packageChange($event, v)"
                            v-model="v.force_original_package"
                            :disabled="v.is_onsale"
                            >强制原箱
                        </el-checkbox>
                    </div>
                    <div
                        class="box_flex"
                        style="line-height: 40px; margin-left: 25px"
                        v-if="v.visible"
                    >
                        <div
                            style="margin-left: 20px"
                            v-if="rows.periods_type != 4"
                        >
                            赠送优惠券
                            <el-popover
                                placement="top"
                                width="200"
                                trigger="click"
                                content="此设置会在用户确认收货后发放优惠券一张"
                            >
                                <i
                                    class="el-icon-question"
                                    slot="reference"
                                ></i>
                            </el-popover>
                            <el-input
                                v-model="v.coupons_id"
                                :disabled="v.is_onsale"
                                size="mini"
                                style="width: 160px; margin-left: 10px"
                                placeholder="多个请用·分开"
                            />
                        </div>
                        <div
                            style="margin-left: 20px; display: flex"
                            v-if="
                                rows.periods_type != 2 && rows.periods_type != 4
                            "
                        >
                            优惠减免
                            <el-popover
                                placement="top"
                                width="200"
                                trigger="click"
                                content="此设置会在最终计算金额时优先在套餐售价上扣减,最大设置不能超过售价的50%(百分之五十)"
                            >
                                <i
                                    class="el-icon-question"
                                    slot="reference"
                                ></i>
                            </el-popover>
                            <el-form-item
                                :prop="
                                    'salesList.' +
                                    index +
                                    '.preferential_reduction'
                                "
                                :rules="rules.preferential_reduction"
                                label-width="0"
                            >
                                <div
                                    style="
                                        display: flex;
                                        flex-direction: column;
                                    "
                                >
                                    <el-input
                                        v-model="v.preferential_reduction"
                                        :disabled="v.is_onsale"
                                        size="mini"
                                        style="width: 160px; margin-left: 10px"
                                        placeholder="请输入优惠减免"
                                    />
                                    <div
                                        v-show="
                                            v.price * 0.5 <
                                            v.preferential_reduction
                                        "
                                        style="
                                            height: 15px;
                                            margin-left: 10px;
                                            line-height: 15px;
                                            color: #ebb563;
                                            font-size: 11px;
                                        "
                                    >
                                        输入金额不能大于售价50%
                                    </div>
                                </div>
                            </el-form-item>
                        </div>
                        <!-- 是否不限量 -->
                        <div
                            style="margin-left: 20px"
                            v-if="rows.periods_type != 4"
                        >
                            <el-checkbox
                                v-model="v.unlimited"
                                @change="changeUnlimit"
                                >不限量
                            </el-checkbox>
                        </div>
                        <div>
                            <el-checkbox
                                style="margin-left: 20px"
                                v-model="v.is_display_price"
                                :true-label="1"
                                :false-label="0"
                                @change="changeDisplayPrice(index, $event)"
                                >优先展示价
                            </el-checkbox>
                        </div>
                        <!--  <div style="margin-left: 20px;" v-if="!v.unlimited&&rows.periods_type!=4">
                                套餐库存
                                <el-input v-model="v.inventory"  size="mini"
                                    style="width: 160px;margin-left: 10px;" placeholder="请输入套餐库存" />
                            </div> -->
                        <div
                            style="margin-left: 20px"
                            v-if="
                                rows.periods_type != 2 &&
                                rows.periods_type != 4 &&
                                !v.is_onsale
                            "
                        >
                            <!-- <el-checkbox v-model="v.is_gift">是否赠品</el-checkbox> -->
                            <el-button
                                type="warning"
                                style="margin-left: 34px"
                                size="mini"
                                @click="addProduct(v, true)"
                                >添加赠品
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>
            <span
                slot="footer"
                class="dialog-footer"
                style="display: flex; justify-content: flex-end"
            >
                <el-button type="primary" @click="handleNext(2, false)"
                    >下一步</el-button
                >
                <el-button type="primary" @click="handleNext(2, true)"
                    >保存</el-button
                >
            </span>
        </el-dialog>
        <el-dialog
            title="商品信息"
            :visible.sync="goodsDialog"
            append-to-body
            destroy-on-close
            width="70%"
            :close-on-click-modal="false"
        >
            <div v-if="goodsDialog">
                <div class="box_flex" style="margin: 0 0 0 31px">
                    <el-form-item
                        label="初始量"
                        label-width="80px"
                        prop="limit_number"
                    >
                        <el-input
                            v-model="entity.limit_number"
                            size="mini"
                            style="width: 160px; margin-left: 10px"
                            placeholder="初始量"
                        />
                    </el-form-item>
                    <el-form-item
                        label="临界值"
                        label-width="80px"
                        prop="critical_value"
                    >
                        <el-input
                            v-model="entity.critical_value"
                            size="mini"
                            style="width: 160px; margin-left: 10px"
                            placeholder="临界值"
                        />
                    </el-form-item>
                    <el-form-item
                        label="自增量"
                        label-width="80px"
                        prop="incremental"
                    >
                        <el-input
                            v-model="entity.incremental"
                            size="mini"
                            style="width: 160px; margin-left: 10px"
                            placeholder="自增量"
                        />
                    </el-form-item>
                </div>
                <div
                    class="box_flex"
                    style="
                        display: flex;
                        flex-wrap: wrap;
                        margin: 0 0 15px 31px;
                    "
                >
                    <el-checkbox
                        v-model="entity.is_channel"
                        v-if="containsName('is_channel')"
                        @change="channelChange"
                        >渠道销售</el-checkbox
                    >
                    <el-checkbox
                        v-model="entity.is_hidden_price"
                        v-if="containsName('is_hidden_price')"
                        >隐藏价格</el-checkbox
                    >
                    <el-checkbox
                        v-model="entity.is_support_ts"
                        @change="tsChange"
                        v-if="containsName('is_support_ts')"
                        >{{ tsString }}</el-checkbox
                    >
                    <el-checkbox
                        v-model="entity.is_postpone"
                        v-if="containsName('is_postpone')"
                        >未售完自动延期</el-checkbox
                    >
                    <el-checkbox
                        v-model="entity.is_cold_chain"
                        v-if="containsName('is_cold_chain')"
                        >支持温控包裹</el-checkbox
                    >
                    <el-checkbox
                        v-model="entity.is_cold_free_shipping"
                        v-if="containsName('is_cold_free_shipping')"
                        >温控包裹包邮</el-checkbox
                    >
                    <el-checkbox
                        v-model="entity.sellout_sold_out"
                        v-if="containsName('sellout_sold_out')"
                        >售完不下架</el-checkbox
                    >
                    <el-checkbox
                        v-model="entity.is_sold_out_lock"
                        v-if="containsName('is_sold_out_lock')"
                        >下架后锁定</el-checkbox
                    >
                    <el-checkbox
                        v-if="containsName('is_parcel_insurance')"
                        v-model="entity.is_parcel_insurance"
                        >保价</el-checkbox
                    >
                    <el-checkbox
                        v-if="containsName('is_support_reduction')"
                        v-model="entity.is_support_reduction"
                        >支持满减
                    </el-checkbox>
                    <el-checkbox
                        v-if="containsName('is_support_coupon')"
                        v-model="entity.is_support_coupon"
                        >支持优惠券</el-checkbox
                    >
                    <el-checkbox
                        v-model="entity.is_user_filter"
                        v-if="containsName('is_user_filter')"
                        >是否过滤</el-checkbox
                    >
                    <el-checkbox
                        v-model="entity.is_leftover"
                        v-if="containsName('is_leftover')"
                        >跨境尾货</el-checkbox
                    >
                    <el-checkbox
                        v-model="entity.is_insured"
                        v-if="containsName('is_insured')"
                        >是否投保</el-checkbox
                    >
                    <el-checkbox
                        v-if="containsName('is_after_sale')"
                        v-model="entity.is_after_sale"
                        :true-label="1"
                        :false-label="0"
                        >下架后允许售后</el-checkbox
                    >
                    <el-checkbox
                        v-if="containsName('is_support_invoicing')"
                        v-model="entity.is_support_invoicing"
                        :true-label="0"
                        :false-label="1"
                        >不支持开票</el-checkbox
                    >
                    <el-checkbox
                        v-if="containsName('is_seckill')"
                        v-model="entity.is_seckill"
                        :true-label="1"
                        :false-label="0"
                        >秒杀</el-checkbox
                    >
                    <el-checkbox
                        v-if="containsName('is_timing_pushorder')"
                        v-model="entity.is_timing_pushorder"
                        :true-label="1"
                        :false-label="0"
                        >定时发货</el-checkbox
                    >
                    <el-checkbox
                        v-if="containsName('is_defective_goods')"
                        v-model="entity.is_defective_goods"
                        :disabled="entity.onsale_verify_status == 1"
                        :true-label="1"
                        :false-label="0"
                        >瑕疵品</el-checkbox
                    >
                    <!-- <el-checkbox v-model="entity.instruction">是否过滤</el-checkbox> -->
                </div>
                <el-form-item label="加指令" prop="instruction">
                    <!-- <el-select
                        filterable
                        :clearable="false"
                        v-model="entity.instruction"
                        size="mini"
                        class="w-normal"
                        multiple
                        placeholder="请选择指令"
                    >
                        <el-option
                            v-for="item in instructionOptions"
                            :key="item.attach_id"
                            :label="item.cn_product_name"
                            :value="item.attach_id"
                        >
                        </el-option>
                    </el-select> -->
                    <el-select
                        v-model="entity.instruction"
                        multiple
                        filterable
                        remote
                        reserve-keyword
                        placeholder="请输入指令名称"
                        size="mini"
                        :remote-method="getDirectListRemoteMethod"
                        :loading="loading"
                        @focus="instructionFocus"
                    >
                        <el-option
                            v-for="item in instructionOptions"
                            :key="item.id"
                            :label="item.cn_product_name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="快递模版" prop="express_id">
                    <el-select
                        filterable
                        :clearable="false"
                        v-model="entity.express_id"
                        size="mini"
                        class="w-normal"
                        placeholder="请选择快递模版"
                    >
                        <el-option
                            v-for="item in expressOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                    <el-button
                        type="primary"
                        style="margin-left: 34px"
                        size="mini"
                        @click="limitBuy = !limitBuy"
                    >
                        {{ limitBuy ? "取消限购" : "设置限购" }}
                    </el-button>
                </el-form-item>

                <!-- <div style="margin-bottom: 20px;margin-left: 40px;"> -->
                <!-- 添加指令 <el-select v-model="entity.instruction1" size="mini" multiple collapse-tags
                       filterable
                            style="width: 400px;margin-right: 20px;" placeholder="请选择指令">
                            <el-option v-for="item in instructionsList" :key="item.id" :label="item.cn_product_name"
                                :value="item.id">
                            </el-option>
                        </el-select>
                        移除指令 <el-select filterable v-model="entity.uninstruction1" size="mini" multiple collapse-tags
                             style="width: 400px;" placeholder="请选择指令">
                             <el-option v-for="item in instructionsList" :key="item.id" :label="item.cn_product_name"
                                 :value="item.id">
                             </el-option>
                         </el-select> -->

                <!-- </div> -->

                <div style="margin: 10px 33px" v-if="limitBuy">
                    <div>
                        限购 每人<el-select
                            v-model="entity.quota_rule.quota_type"
                            size="mini"
                            style="width: 90px"
                            placeholder="请选择"
                        >
                            <el-option label="每次" value="1"> </el-option>
                            <el-option label="永久" value="0"> </el-option>
                        </el-select>
                        限购
                        <el-input
                            v-model="entity.quota_rule.quota_number"
                            size="mini"
                            style="width: 60px; margin-left: 5px"
                            placeholder="件数"
                        />件
                    </div>
                    <div style="margin: 5px">
                        特殊限购
                        <el-checkbox
                            @change="handleAddrChange"
                            v-model="entity.quota_rule.check_addr"
                            >地区
                        </el-checkbox>
                        <el-checkbox
                            @change="handleLevelChange"
                            v-model="entity.quota_rule.check_level"
                            >等级</el-checkbox
                        >
                        <el-checkbox
                            @change="handleTimeChange"
                            v-model="entity.quota_rule.check_time"
                            >注册时间</el-checkbox
                        >
                    </div>
                    <div style="display: flex">
                        <div
                            v-if="entity.quota_rule.check_addr"
                            style="width: 327px"
                        >
                            <el-checkbox-group
                                v-model="entity.quota_rule.addrs"
                            >
                                <el-checkbox
                                    :label="v.id"
                                    v-for="v in cascaderOptions"
                                    :key="v.name"
                                >
                                    {{ v.name }}
                                </el-checkbox>
                            </el-checkbox-group>
                        </div>
                        <div
                            style="margin: 0 10px"
                            v-if="entity.quota_rule.check_level"
                        >
                            等级
                            <el-input
                                v-model="entity.quota_rule.min_level"
                                size="mini"
                                style="width: 80px; margin-left: 5px"
                                placeholder="最低等级"
                            />-
                            <el-input
                                v-model="entity.quota_rule.max_level"
                                size="mini"
                                style="width: 80px; margin-left: 5px"
                                placeholder="最高等级"
                            />
                        </div>
                        <div v-if="entity.quota_rule.check_time">
                            注册时间<el-date-picker
                                v-model="entity.quota_rule.register_time"
                                size="mini"
                                value-format="yyyy-mm-dd"
                                style="margin-left: 5px"
                                type="date"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </div>
                    </div>
                </div>
                <!--     <el-button type="primary" v-if="showNext2Btn" style="margin-left: 34px;" size="mini"
                        @click="handleNext(3)">下一步
                    </el-button> -->
                <div class="box_flex">
                    <el-form-item label="上架时间" prop="onsale_time">
                        <el-date-picker
                            v-model="entity.onsale_time"
                            @change="changeTime"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            type="datetime"
                            placeholder="选择日期"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="开售时间" prop="sell_time">
                        <el-date-picker
                            v-model="entity.sell_time"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            type="datetime"
                            placeholder="选择日期"
                        >
                        </el-date-picker>
                    </el-form-item>
                </div>
                <div class="box_flex">
                    <el-form-item label="下架时间" prop="sold_out_time">
                        <el-date-picker
                            v-model="entity.sold_out_time"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            type="datetime"
                            placeholder="选择日期"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="发货时间" prop="predict_shipment_time">
                        <el-date-picker
                            v-model="entity.predict_shipment_time"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            type="datetime"
                            placeholder="选择日期"
                        >
                        </el-date-picker>
                    </el-form-item>
                </div>
                <!-- || rows.periods_type === 3 -->
                <div v-if="rows.periods_type === 0 || rows.periods_type === 3">
                    <el-form-item label="收款商户" prop="payee_merchant_id">
                        <el-select
                            v-model="entity.payee_merchant_id"
                            placeholder="请选择收款商户"
                            @change="changePayeeMerchant"
                            disabled
                        >
                            <el-option
                                v-for="item in payee_merchant_options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="handleNext(3)"
                    >保存</el-button
                >
            </span>
        </el-dialog>
        <el-dialog
            title="产品对比信息"
            :visible.sync="differenceDialogStatus"
            append-to-body
            destroy-on-close
            width="70%"
            :close-on-click-modal="false"
        >
            <!-- this.entity.purchaseList this.entity.product_list -->
            <div class="difference-layout">
                <div>
                    <el-card class="box-card">
                        <div slot="header" class="clearfix">
                            <span>运营产品信息</span>
                        </div>
                        <el-descriptions
                            v-for="(item, index) in this.originalList"
                            :key="index"
                        >
                            <el-descriptions-item label="简码">{{
                                item.short_code
                            }}</el-descriptions-item>
                            <el-descriptions-item label="产品英文名">{{
                                item.en_product_name
                            }}</el-descriptions-item>
                        </el-descriptions>
                    </el-card>
                </div>
                <div style="margin-top: 20px">
                    <el-card class="box-card">
                        <div slot="header" class="clearfix">
                            <span>文案产品信息</span>
                        </div>
                        <el-descriptions
                            v-for="(item, index) in this.differenceList"
                            :key="index"
                        >
                            <el-descriptions-item label="简码">{{
                                item.short_code
                            }}</el-descriptions-item>
                            <el-descriptions-item label="产品英文名">{{
                                item.en_product_name
                            }}</el-descriptions-item>
                        </el-descriptions>
                    </el-card>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="differenceCheck"
                    >提交</el-button
                >
                <el-button @click="differenceDialogStatus = false"
                    >取消</el-button
                >
            </span>
        </el-dialog>
        <good-tags
            ref="goodTags"
            @updateTagsSuccess="updateTagsSuccess"
        ></good-tags>
        <el-dialog
            :visible.sync="corpSelectDialogVisible"
            title="选择收款商户"
            width="30%"
            append-to-body
            :close-on-click-modal="false"
        >
            <div
                v-if="
                    [0, 3].includes(rows.periods_type) && entityImportType === 0
                "
                style="margin-bottom: 20px"
            >
                <div v-for="item in entity.product_list" :key="item.short_code">
                    <span>{{ item.short_code }}</span>
                    <span>-</span>
                    <span style="color: red">{{
                        item.corp
                            .split(",")
                            .map((corp) => corpToName[corp] || "未知")
                            .join()
                    }}</span>
                </div>
            </div>
            <el-radio-group v-model="corpName">
                <div v-for="value in corpsObj" :key="value">
                    <el-radio :label="value">{{ value }}</el-radio>
                </div>
            </el-radio-group>
            <div slot="footer" class="dialog-footer">
                <el-button
                    type="primary"
                    :disabled="!corpName"
                    @click="onCorpNameConfirm"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
        <el-dialog
            :visible.sync="supplierDASDialogVisible"
            width="30%"
            append-to-body
            :close-on-click-modal="false"
        >
            <el-form
                ref="entitySupplierDASFormRef"
                :model="entitySupplierDASForm"
                :rules="entitySupplierDASFormRules"
                label-width="100px"
            >
                <el-form-item label="发货地" prop="supplierDeliveryAddress">
                    <el-select
                        :value="
                            entitySupplierDASForm.supplierDeliveryAddress
                                .split(',')
                                .filter(Boolean)
                        "
                        @change="
                            entitySupplierDASForm.supplierDeliveryAddress =
                                $event.join()
                        "
                        multiple
                        filterable
                    >
                        <el-option
                            v-for="item in regionList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.name"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="发货时效" prop="supplier_delivery_time">
                    <el-radio-group
                        v-model="entitySupplierDASForm.supplier_delivery_time"
                    >
                        <el-radio :label="0">24小时内</el-radio>
                        <el-radio :label="1">48小时内</el-radio>
                        <el-radio :label="2">72小时内</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="发货时效" prop="supplier_delivery_weekend">
                    <el-checkbox
                        v-model="
                            entitySupplierDASForm.supplier_delivery_weekend
                        "
                        :true-label="0"
                        :false-label="1"
                        >周末发货</el-checkbox
                    >
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button type="primary" @click="onSupplierDASConfirm"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
        <el-dialog
            :visible.sync="timeChooseVisbible"
            width="40%"
            append-to-body
            :before-close="handleClose"
        >
            <el-form
                ref="timeChooseVisbibleRef"
                :model="entity"
                :rules="timeChooseRules"
                label-width="100px"
            >
                <el-form-item label="发货时间" prop="latest_storage_time">
                    <el-date-picker
                        v-model="entity.latest_storage_time"
                        type="datetime"
                        placeholder="发货时间"
                        size="small"
                        value-format="yyyy-MM-dd"
                    />
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button type="cancle" @click="cancleClick"
                    >不设置时间</el-button
                >
                <el-button type="primary" @click="sureChangeTime"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
    </el-form>
</template>

<script>
import Tinymce from "@/components/Tinymce";
import vosOss from "vos-oss";
import vosVod from "vos-vod";
import GoodTags from "@/components/GoodTags/index";
export default {
    props: {
        parentObj: Object,
    },
    components: {
        Tinymce,
        vosOss,
        vosVod,
        GoodTags,
    },
    data() {
        const validataprice = (rule, value, callback) => {
            console.log(rule, value, 4444);
            let reg =
                /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
            if (value) {
                if (!reg.test(value)) {
                    callback(new Error("请输入正确的金额，可保留两位小数"));
                }
                // else if (value) {
                //     let index = rule.field.split(".")[1];
                //     if (
                //         this.entity.salesList[index].price * 0.5 <
                //         Number(value)
                //     ) {
                //         callback(new Error("输入金额不能大于售价50%"));
                //     } else {
                //         callback();
                //     }
                // }
                else {
                    callback();
                }
            } else {
                callback();
            }
        };
        return {
            numberRules: [
                {
                    required: true,
                    message: "请输入",
                    trigger: "blur",
                },
                {
                    validator(rule, value, callback) {
                        if (
                            Number.isInteger(Number(value)) &&
                            Number(value) > 0
                        ) {
                            callback();
                        } else {
                            callback(new Error("请输入正整数"));
                        }
                    },
                    trigger: "blur",
                },
            ],
            dialogVisible: false,
            dialogVisible1: false,
            dialogVisible2: false,
            differenceDialogStatus: false,
            differenceParams: {},
            originalList: [],
            differenceList: [],
            differenceIsSave: false,
            GoodsDetailVisible: false,
            timeChooseVisbible: false,
            expressOptions: [],
            imgVisible: false,
            purchaseDialog: false,
            onsale_verify_status: 0,
            coverList: [],
            packDialog: false,
            goodsDialog: false,

            instructionOptions: [],
            loading: false,
            title: "闪购介绍",
            tsString: "支持暂存",
            type: 0,
            options: [
                {
                    value: "选项1",
                    label: "黄金糕",
                },
            ],
            sendWarehouseList: [],
            cascaderOptions: [], //地区
            buyerList: [],
            entity: {
                is_seckill: 0,
                title: "",
                title1: "",
                brief: "",
                banner_img: "",
                product_img: "",
                detail: "",
                filelist1: [],
                filelist2: [],
                express_id: "",
                filelist3: [],
                product_id: [],
                purchaseList: [
                    {
                        short_code: "",
                        purchase: [],
                        isDel: true,
                    },
                ],
                salesList: [
                    {
                        package_name: "",
                    },
                ],
                is_channel: 0,
                is_hidden_price: false,
                sellout_sold_out: 1,
                video: "",
                quota_rule: {},
                payee_merchant_id: "",
                payee_merchant_name: "",
                is_defective_goods: "",
                purchasing_said: "",
            },
            purchaseOptions: [],
            limitBuy: false, //限购
            entity1: {
                reason: "",
            },
            dir: "vinehoo/goods-images/",
            supplier_delivery_time_text: {
                0: "24小时内发货",
                1: "48小时内发货",
                2: "72小时内发货",
            },
            parckageOptions: [
                {
                    value: "单支",
                    label: "单支",
                },
                {
                    value: "双支装",
                    label: "双支装",
                },
                {
                    value: "三支装",
                    label: "三支装",
                },
                {
                    value: "四支装",
                    label: "四支装",
                },
                {
                    value: "五支装",
                    label: "五支装",
                },
                {
                    value: "六支装",
                    label: "六支装",
                },
                {
                    value: "单支礼盒装",
                    label: "单支礼盒装",
                },
                {
                    value: "六支原箱",
                    label: "六支原箱",
                },
                {
                    value: "六支原箱（无冰袋）",
                    label: "六支原箱（无冰袋）",
                },
                {
                    value: "十二支装",
                    label: "十二支装",
                },
                {
                    value: "十二支原箱",
                    label: "十二支原箱",
                },
                {
                    value: "十二支原箱（无冰袋）",
                    label: "十二支原箱（无冰袋）",
                },
                {
                    value: "二十四支原箱",
                    label: "二十四支原箱",
                },
                {
                    value: "二十四支原箱（无冰袋）",
                    label: "二十四支原箱（无冰袋）",
                },
                {
                    value: "自选",
                    label: "自选",
                },
                {
                    value: "盲盒",
                    label: "盲盒",
                },
                {
                    value: "其他",
                    label: "其他",
                },
            ],

            ids: [
                {
                    name: "酒庄id",
                    mode: "chateau_id",
                },
            ],
            operations: [
                {
                    value: "是",
                    label: 1,
                },
                {
                    value: "否",
                    label: 0,
                },
            ],
            timeChooseRules: {
                latest_storage_time: [
                    {
                        required: true,
                        message: "请选择最晚发货时间",
                        trigger: "change",
                    },
                ],
            },
            rules: {
                // instruction: [
                //     {
                //         required: true,
                //         message: "请选择指令",
                //         trigger: "blur",
                //     },
                // ],
                incremental: [
                    {
                        required: true,
                        message: "请输入自增量",
                        trigger: "blur",
                    },
                ],
                limit_number: [
                    {
                        required: true,
                        message: "请输入初始值",
                        trigger: "blur",
                    },
                ],
                critical_value: [
                    {
                        required: true,
                        message: "请输入临界值",
                        trigger: "blur",
                    },
                ],
                title: [
                    {
                        required: true,
                        message: "请输入商品标题",
                        trigger: "blur",
                    },
                ],
                brief: [
                    {
                        required: true,
                        message: "请输入商品副标题",
                        trigger: "blur",
                    },
                ],
                detail: [
                    {
                        required: true,
                        message: "请输入商品详情",
                        trigger: "blur",
                    },
                ],
                banner_img: [
                    {
                        required: true,
                        message: "请先上传图片",
                        trigger: "change",
                    },
                ],
                product_img1: [
                    {
                        required: true,
                        // validator: validateimg,
                        message: "请先上传图片",
                        trigger: "change",
                    },
                ],
                short_code: [
                    {
                        required: true,
                        message: "请输入简码",
                        trigger: "blur",
                    },
                ],
                package_name: [
                    {
                        required: true,
                        message: "请选择套餐名称",
                        trigger: "change",
                    },
                ],
                package_name1: [
                    {
                        required: true,
                        message: "请输入套餐名称",
                        trigger: "blur",
                    },
                ],
                price: [
                    {
                        required: true,
                        message: "请输入售价",
                        trigger: "blur",
                    },
                    {
                        pattern:
                            /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
                        message: "请输入正确售价，可保留两位小数",
                    },
                ],
                rabbit: [
                    {
                        required: true,
                        message: "请输入兔头",
                        trigger: "blur",
                    },
                    {
                        pattern: /^[0-9]*[1-9][0-9]*$/,
                        message: "请输入正确兔头数量",
                    },
                ],

                market_price: [
                    {
                        required: true,
                        message: "请输入市场价",
                        trigger: "blur",
                    },
                    {
                        pattern:
                            /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
                        message: "请输入正确市场价，可保留两位小数",
                    },
                ],
                costprice: [
                    {
                        required: true,
                        message: "请输入本期成本价",
                        trigger: "blur",
                    },
                    {
                        pattern:
                            /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
                        message: "请输入正确本期成本价，可保留两位小数",
                    },
                ],
                // preferential_reduction: [
                //     {
                //         required: true,
                //         validator: validataprice,
                //         trigger: "blur",
                //     },
                // ],
                express_id: [
                    {
                        required: true,
                        message: "请选择快递模版",
                        trigger: "blur",
                    },
                ],
                reason: [
                    {
                        required: true,
                        message: "请选择驳回理由",
                        trigger: "blur",
                    },
                ],
                onsale_time: [
                    {
                        required: true,
                        message: "请选择上架时间",
                        trigger: "change",
                    },
                ],
                sold_out_time: [
                    {
                        required: true,
                        message: "请选择下架时间",
                        trigger: "change",
                    },
                ],
                sell_time: [
                    {
                        required: true,
                        message: "请选择开售时间",
                        trigger: "change",
                    },
                ],
                predict_shipment_time: [
                    {
                        required: true,
                        message: "请选择发货时间",
                        trigger: "change",
                    },
                ],
                payee_merchant_id: [
                    {
                        required: true,
                        message: "请选择收款商户",
                        trigger: "change",
                    },
                ],
                tasting_notes: [
                    {
                        required: true,
                        message: "请输入Tasting Notes",
                        trigger: "blur",
                    },
                ],
                warehouse: [
                    {
                        required: true,
                        message: "请选择发货仓库",
                        trigger: "change",
                    },
                ],
                inventory: [
                    {
                        required: true,
                        message: "请输入售卖库存",
                        trigger: "blur",
                    },
                ],
                filelist1: [
                    {
                        required: true,
                        message: "请先上传图片",
                        trigger: "change",
                    },
                ],
                filelist3: [
                    {
                        required: true,
                        // validator: validateimg,
                        message: "请先上传图片",
                        trigger: "change",
                    },
                ],
                deposit_price: [
                    {
                        required: true,
                        message: "请输入金额",
                        trigger: "blur",
                    },
                    {
                        pattern:
                            /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
                        message: "请输入正确的金额",
                    },
                ],
            },
            loading: false,
            disab: false,
            salesVisible: false,
            purcharseVisible: true,
            nextBtn: true,
            purList: [],
            numList: [],
            rows: {},
            sendWarehouseList: [],
            nextBtn1: false,
            ParametersList: [],
            purchases: {
                period: "",
                periods_type: "",
                import_type: "",
                supplier: "",
                buyer_id: "",
                buyer_name: "",
                is_supplier_delivery: "",
                is_presell: "",
            },
            product_disab: true,
            supplierOpration: [],
            instructionsListBackup: [],
            payee_merchant_options: [
                { label: "重庆云酒佰酿电子商务有限公司", value: 1 },
                { label: "佰酿云酒（重庆）科技有限公司", value: 2 },
                { label: "渝中区微醺酒业商行", value: 5 },
                { label: "海南一花一世界科技有限公司", value: 10 },
            ],
            corpSelectDialogVisible: false,
            corpsObj: {},
            corpName: "",
            entitySuppliers: null,
            entityImportType: "",
            isSelfImportToPayee: false,
            disabledSupplierToPayee: false,
            corpToName: {
                "001": "佰酿云酒（重庆）科技有限公司",
                "002": "重庆云酒佰酿电子商务有限公司",
                "008": "渝中区微醺酒业商行",
                "032": "海南一花一世界科技有限公司",
            },
            regionList: [],
            supplierDASDialogVisible: false,
            entitySupplierDASForm: {
                isSupplierDelivery: "",
                supplierDeliveryAddress: "",
                supplier_delivery_time: 0,
                supplier_delivery_weekend: 0,
            },
            entitySupplierDASFormRules: {
                supplierDeliveryAddress: [
                    {
                        required: true,
                        message: "请选择发货地",
                        trigger: "change",
                    },
                ],
            },
            productImgList: [],
        };
    },
    watch: {
        purchaseDialog(newVal) {
            if (newVal) {
                this.remoteMethod(this.entity.suppliers.supplier_name);
            }
        },
        packDialog(newVal) {
            if (newVal) {
                this.entity.salesList.forEach((item) => {
                    if (!item.package_name && !item.package_img) {
                        item.package_img = (
                            this.productImgList[0] || ""
                        ).replace(this.$BASE.OSSDomain, "");
                    }
                });
            }
        },
        "entity.payee_merchant_id"(newVal, oldVal) {
            console.log("newVal", newVal);
            this.getNewWarehouseList();
            if (oldVal) {
                console.log("ddgg", this.entity);
                this.entity.purchaseList.forEach(function (element) {
                    element.purchase.forEach(function (value) {
                        value.warehouse = "";
                    });
                });
                console.log("mmmm", this.entity);
            }
        },
    },
    created() {},
    mounted() {
        this.changePayeeMerchant();
        // this.getRecommendLabel();
        // this.getDirectList();

        this.getRegionList();
    },
    methods: {
        channelChange(val) {
            console.log(val);
            if (val) {
                this.$confirm(
                    "是否加密渠道链接？如果选择“是”则必须通过点击渠道链接才能购买，安全性高，如果选择“否”则有可能被用户提前下单。",
                    "提示",
                    {
                        confirmButtonText: "是",
                        cancelButtonText: "否",
                        type: "warning",
                    }
                )
                    .then(() => {
                        this.entity.is_enc_link = 1;
                    })
                    .catch(() => {
                        this.entity.is_enc_link = 0;
                    });
            }
        },
        async deletePackage(item, index) {
            console.log(index);

            const deleteData = {
                period_id: item.period_id,
                periods_type: this.rows.periods_type,
                package_id: item.id,
            };
            const res = await this.$request.purchase.deletePackage(deleteData);
            if (res.data.error_code === 0) {
                console.log(this.entity.salesList);
                this.entity.salesList.splice(index, 1);
            }
        },
        getProductImgList(v) {
            const list = this.productImgList.map((item) =>
                item.replace(this.$BASE.OSSDomain, "")
            );
            const packageImg = v.package_img.replace(this.$BASE.OSSDomain, "");
            if (packageImg && !list.includes(packageImg)) {
                list.unshift(packageImg);
            }
            return list;
        },
        getParametersList() {
            console.log("133--------", this.rows.periods_type);

            this.$request.article
                .getParametersChannelList({
                    periods_type: this.rows.periods_type,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.ParametersList = res.data.data.list;
                    }
                });
        },
        containsName(name) {
            return this.ParametersList.some(function (element) {
                return element.field_value === name;
            });
        },
        // //发货指令列表
        // async getDirectList() {
        //     let res = await this.$request.article.queryaddition({
        //         page: 1,
        //         limit: 100,
        //     });
        //     console.log("发货指令列表", res);
        //     if (res.data.error_code == 0) {
        //         this.instructionOptions = res.data.data.list;
        //     }
        // },
        instructionFocus() {
            this.instructionOptions = this.instructionsListBackup;
        },

        getDirectListRemoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.article
                    .queryaddition({
                        cn_product_name: query,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.instructionOptions = res.data.data.list;
                        }
                    });
            } else {
                this.instructionOptions = [];
            }
        },
        changePayeeMerchant() {
            this.payee_merchant_options.map((item) => {
                if (item.value == this.entity.payee_merchant_id) {
                    this.entity.payee_merchant_name = item.label;
                }
            });
            console.warn(
                this.entity.payee_merchant_id,
                this.entity.payee_merchant_name
            );
        },
        //兔头计算
        changeRabbit(rows) {
            console.log(parseFloat(rows.rabbit), 666);
            if (rows.price == "") {
                rows.price = 0;
            }
            rows.price = parseFloat(rows.rabbit) / 20;
        },
        //供应商查询
        remoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.article
                    .supplierList({
                        page: 1,
                        limit: 10,
                        keyword: query,
                    })
                    .then((res) => {
                        this.loading = false;
                        this.supplierOpration = res.data.data.list;
                    });
            } else {
                this.supplierOpration = [];
            }
        },
        getExpressList() {
            //获取快递模版
            this.$request.article
                .expressList({
                    page: 1,
                    limit: 200,
                })
                .then((res) => {
                    this.expressOptions = res.data.data.list;
                });
        },
        getInstructions() {
            //获取指令
            this.$request.article
                .queryaddition({
                    page: 1,
                    limit: 100,
                })
                .then((res) => {
                    this.instructionOptions = res.data.data.list;
                    this.instructionsListBackup = res.data.data.list;
                });
        },
        //是否不限量
        changeUnlimit(checked) {
            this.entity.salesList.map((item) => {
                item.inventory = "";
                item.unlimited = checked;
            });
        },
        tsChange(value) {
            console.log(value);
            if (value) {
                this.timeChooseVisbible = true;
            } else {
                this.entity.is_support_ts = false;
                this.tsString = "支持暂存";
                this.entity.latest_storage_time = "";
            }
        },
        handleClose(done) {
            this.entity.is_support_ts = false;
            done();
        },
        sureChangeTime() {
            this.$refs["timeChooseVisbibleRef"].validate((valid) => {
                if (valid) {
                    this.tsString =
                        "支持暂存 " + this.entity.latest_storage_time;
                    this.entity.is_support_ts = true;
                    this.timeChooseVisbible = false;
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        cancleClick() {
            this.tsString = "支持暂存";
            this.entity.is_support_ts = true;
            this.timeChooseVisbible = false;
        },
        changeDisplayPrice(index, checked) {
            this.entity.salesList.map((item) => {
                item.is_display_price = 0;
            });
            this.entity.salesList[index].is_display_price = checked ? 1 : 0;
        },
        //选择套餐的时候 显示隐藏
        handleCodeVisible(salesList, child) {
            console.log(child, 22222222);
            // 是否显示选择套餐下拉
            if (!salesList.is_onsale) {
                child.codeVisible = !child.codeVisible;
            }
        },
        //套餐显示
        goodsEdit() {
            if (
                this.entity.salesList &&
                this.entity.salesList[0].package_name == ""
            ) {
                this.$message.error("请先编辑套餐信息！");
                return;
            }
            this.getExpressList();
            this.getInstructions();
            this.goodsDialog = !this.goodsDialog;
        },
        //采购信息显示
        packEdit() {
            if (this.purList.length == 0) {
                this.$message.error("请先编辑采购信息！");
                return;
            }
            // this.getDetails(this.rows);

            this.packDialog = !this.packDialog;
        },
        //视频上传成功回调
        uploadSuccess(data) {
            this.entity.video = data.url;
        },
        //验证套餐名称不能和下拉列表名称一样
        changePack(v) {
            let index1 = this.parckageOptions.findIndex(
                (f) => f.value == v.package_name1
            );
            if (index1 > -1) {
                v.package_name1 = "";
                this.$message.error("请选择对应套餐！");
            }
        },
        //修改采购信息
        updatePurchase() {
            console.log(this.entity.buyers, 3333);
            let parms = {
                period: this.rows.id,
                periods_type: this.rows.periods_type,
                import_type: this.entity.import_type,
                buyer_id: this.entity.buyers1.id,
                buyer_name: this.entity.buyers1.realname,
                is_supplier_delivery: this.entity.is_supplier_delivery,
                supplier_delivery_time: this.entity.supplier_delivery_time,
                supplier_delivery_weekend:
                    this.entity.supplier_delivery_weekend,
                supplier_delivery_address:
                    this.entity.supplier_delivery_address,
                is_presell: this.entity.is_presell,
                supplier: this.entity.suppliers.supplier_name,
                supplier_id: this.entity.suppliers.id,
                payee_merchant_id: this.entity.payee_merchant_id,
                payee_merchant_name: this.entity.payee_merchant_name,
            };
            return this.$request.article
                .updatePurchaseInfo(parms)
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功");
                    }
                });
        },
        //下架时间自动填充
        changeTime() {
            console.log(222);
            if (!this.entity.onsale_time) {
                this.entity.sold_out_time = "";
                return;
            }
            let day = this.rows.periods_type == 0 ? 7 : 14;
            if (this.rows.periods_type == 0 || this.rows.periods_type == 2) {
                this.entity.sold_out_time = this.addDate(
                    this.entity.onsale_time,
                    day
                );
            }
            this.entity.sell_time = this.addDate(this.entity.onsale_time, 0);
            console.log(this.entity, 333);
        },
        //添加产品
        addProduct(rows, isGift) {
            let data = {
                nums: 1,
                visible: false,
                codeVisible: false,
                disabled: false,
                isCheck: false,
                isDel: true,
                productList: [],
                isGift: isGift ? true : false,
            };
            rows.numList.push(data);
        },
        //删除产品
        delProduct(rows, i) {
            rows.numList.splice(i, 1);
        },
        handleTimeChange() {
            if (!this.entity.quota_rule.check_time) {
                this.entity.quota_rule.register_time = "";
            }
        },
        //特殊限购
        handleLevelChange() {
            if (!this.entity.quota_rule.check_level) {
                this.entity.quota_rule.max_level = "";
                this.entity.quota_rule.min_level = "";
                this.entity.quota_rule.rank = "";
            }
        },
        force_original_packageChange(event, row) {
            console.log(event, row);
            if (event) {
                row.is_original_package = true;
            }
        },
        handleAddrChange() {
            if (this.entity.quota_rule.check_addr) {
                // this.cascaderOptions.map(item => {
                //     this.entity.quota_rule.addrs.push(item.id)
                // })
            } else {
                this.entity.quota_rule.district = "";
                this.entity.quota_rule.addrs = [];
            }
        },
        differenceCheck() {
            this.$request.article
                .createStock(this.differenceParams)
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.differenceDialogStatus = false;
                        // this.purcharseVisible = false;
                        // this.salesVisible = true;
                        this.purchaseDialog = false;
                        if (!this.differenceIsSave) {
                            //显示套餐信息
                            this.packDialog = true;
                        }
                        //不让修改套餐库存
                        this.entity.purchaseList.map((item) => {
                            item.isDel = false;
                        });
                        if (!this.entity.salesList[0].package_name) {
                            this.entity.salesList[0].numList = [
                                {
                                    product_id: 0,
                                    nums: 1,
                                    visible: false,
                                    codeVisible: false,
                                    unlimited: true,
                                    disabled: true,
                                    isCheck: false,
                                    productList: [],
                                    sub_package_name: "",
                                },
                            ];
                        }
                    }
                });
        },
        //1.采购信息 2套餐信息 3 商品信息
        handleNext(step, isSave) {
            if (step == 1) {
                console.log(this.entity.purchaseList);
                if (!this.validateForm()) {
                    return;
                }
                let list1 = [];
                let list2 = [];
                this.originalList = [];
                this.differenceList = [];
                let paramsList = [];
                let product_category = [];
                let country = [];
                let capacity = [];
                let keywords = [];
                let productcategoryname = [];
                let short_code = [];
                this.purList = [];

                this.entity.product_id = [];
                //组装库存数据
                this.entity.purchaseList.map((item) => {
                    list1.push(item.short_code);
                    item.purchase.map((child) => {
                        let original = {
                            short_code: child.short_code,
                            en_product_name: child.en_product_name,
                        };
                        this.originalList.push(original);
                        child.product_keywords_id.map((keyword) => {
                            keywords.push(keyword.name);
                        });
                        this.entity.product_id.push(child.id);
                        this.purList.push(child);
                        product_category.push(child.product_type_name);
                        country.push(child.country_name);
                        capacity.push(child.capacity);
                        productcategoryname.push(child.productcategoryname);
                        short_code.push(child.short_code);
                        paramsList.push({
                            period: this.entity.id,
                            erp_id: child.warehouse.erp_id,
                            periods_type: this.rows.periods_type,
                            product_id: child.id,
                            short_code: child.short_code,
                            is_use_comment: child.is_use_comment,
                            is_hidden_param: child.is_hidden_param,
                            id: child.product_inventory_id,
                            capacity: child.capacity,
                            inventory: child.inventory,
                            warehouse: child.warehouse.fictitious_name,
                            warehouse_id: child.warehouse.fictitious_id,
                            product_name: child.cn_product_name,
                            custom_product_name: child.custom_product_name,
                            costprice: child.costprice,
                            en_product_name: child.en_product_name,
                            bar_code: child.bar_code,
                            title: this.entity.title,
                        });
                    });
                });
                if (paramsList.length == 0) {
                    this.$message.error("请先搜索简码！");
                    return;
                }
                this.entity.product_list.map((item) => {
                    let difference = {
                        short_code: item.short_code,
                        en_product_name: item.en_product_name,
                    };
                    this.differenceList.push(difference);
                    list2.push(item.short_code);
                });
                let flag =
                    list1.length == list2.length &&
                    list1.every((a) => list2.some((b) => a == b)) &&
                    list2.every((a1) => list1.some((b1) => a1 == b1));
                if (!flag) {
                    // this.$message.error("绑定的条码和文案绑定的有差异！")
                    this.$confirm(
                        "绑定的简码和文案绑定的有差异，确认继续吗?",
                        "提示",
                        {
                            confirmButtonText: "确定",
                            cancelButtonText: "取消",
                            type: "warning",
                        }
                    )
                        .then(() => {
                            let params = {
                                list: paramsList,
                                periods_product: {
                                    product_category:
                                        product_category.join(","),
                                    country: country.join(","),
                                    capacity: capacity.join(","),
                                    productcategoryname:
                                        productcategoryname.join(","),
                                    keywords: keywords.join(","),
                                    short_code: short_code.join(","),
                                },
                            };
                            this.differenceParams = params;
                            this.differenceIsSave = isSave;
                            this.differenceDialogStatus = true;
                        })
                        .catch(() => {});
                    return;
                }

                let params = {
                    list: paramsList,
                    periods_product: {
                        product_category: product_category.join(","),
                        country: country.join(","),
                        capacity: capacity.join(","),
                        keywords: keywords.join(","),
                        productcategoryname: productcategoryname.join(","),
                        short_code: short_code.join(","),
                    },
                };
                this.$request.article.createStock(params).then((res) => {
                    if (res.data.error_code == 0) {
                        this.purchaseDialog = false;
                        if (!isSave) {
                            //显示套餐信息
                            this.packDialog = true;
                        }
                        //不让修改套餐库存
                        this.entity.purchaseList.map((item) => {
                            item.isDel = false;
                        });
                        // this.purcharseVisible = false;
                        // this.salesVisible = true;
                        if (!this.entity.salesList[0].package_name) {
                            this.entity.salesList[0].numList = [
                                {
                                    product_id: 0,
                                    nums: 1,
                                    visible: false,
                                    codeVisible: false,

                                    disabled: true,
                                    isCheck: false,
                                    productList: [],
                                    sub_package_name: "",
                                },
                            ];
                        }
                    }
                });
            } else if (step == 2) {
                this.getInstructions();
                this.getExpressList();
                // 添加套餐
                if (!this.validateForm()) {
                    return;
                }
                let list = [];
                let isPackageName = false;
                let isCheckProduct = false;
                let salesList = JSON.parse(
                    JSON.stringify(this.entity.salesList)
                );
                // let isInventory1 = 1
                // let isInventory2 = 1
                salesList.map((item) => {
                    //隐藏套餐不参与
                    // if (item.inventory && !item.is_hidden) {
                    //     isInventory1 = 2
                    // } else {
                    //     isInventory2 = 2
                    // }
                    item.periods_type = this.rows.periods_type;
                    item.is_hidden = item.is_hidden ? 1 : 0;
                    item.is_onsale = 1;
                    item.unlimited = item.unlimited ? 1 : 0;
                    item.is_mystery_box = item.package_name == "盲盒" ? 1 : 0;
                    item.is_custom_package =
                        item.package_name == "自选" ? 1 : 0;

                    item.package_name =
                        item.package_name == "其他" ||
                        item.package_name == "盲盒" ||
                        item.package_name == "自选"
                            ? item.package_name1
                            : item.package_name;
                    list = [];
                    item.numList.map((child) => {
                        let product_id = [];
                        if (
                            item.is_mystery_box == 1 ||
                            item.is_custom_package == 1
                        ) {
                            child.productList.map((v) => {
                                product_id.push(v.id);
                            });
                        }
                        const listItem = {
                            product_id:
                                item.is_mystery_box == 1 ||
                                item.is_custom_package == 1
                                    ? product_id
                                    : child.productList[0].id,
                            nums:
                                item.is_custom_package == 1
                                    ? child.custom_product_count
                                    : child.nums,
                            isGift: child.isGift ? 1 : 0,
                            sub_package_name: child.sub_package_name,
                        };
                        if (item.is_custom_package == 1) {
                            item.custom_product_count = child.nums;
                        }
                        if (child.sub_package_price) {
                            listItem.sub_package_price =
                                child.sub_package_price;
                        }
                        list.push(listItem);
                        if (!child.isCheck) {
                            isCheckProduct = true;
                        }
                        delete child.productList;
                    });
                    item.associated_products = JSON.stringify(list);
                });
                // if (isInventory1 == 2 && isInventory2 == 2 && !salesList[0].unlimited) {
                //     this.$message.error('套餐限量设置不一致');
                //     return
                // }
                if (isPackageName) {
                    this.$message.error("请选择套餐名称");
                    return;
                }
                if (isCheckProduct) {
                    this.$message.error("请选择产品");
                    return;
                }
                this.entity.periods_type = this.rows.periods_type;
                this.entity.period = this.entity.id;
                // this.entity.quota_rule = {
                //     check_addr: false,
                //     check_level: false,
                //     check_time: false,
                //     register_time: '',
                //     min_level: '',
                //     max_level: '',
                //     addrs: [],
                // }
                // let type = this.rows.onsale_status == 3 ? 2 : 1;

                salesList.map((item) => {
                    delete item["is_cold_chain"];
                    delete item["is_cold_free_shipping"];
                    delete item["is_leftover"];
                    delete item["is_postpone"];
                    delete item["invoice_progress"];
                    delete item["is_onsale"];
                    delete item["inventory"];
                    delete item["inventory_accum"];
                    delete item["created_time"];
                });

                const productIdToCostprice = {};
                this.purList.forEach((item) => {
                    productIdToCostprice[item.id] = item.costprice;
                });
                console.log("productIdToCostprice", productIdToCostprice);
                const profitList = salesList
                    .filter((item) => !item.id)
                    .map((item) => {
                        const { associated_products, price, package_name } =
                            item;
                        const costprice = JSON.parse(associated_products)
                            .map(
                                ({ product_id, nums }) =>
                                    productIdToCostprice[product_id] * nums
                            )
                            .reduce((curr, prev) => curr + prev, 0);
                        const profit = (price - costprice) / costprice;
                        return { name: package_name, profit };
                    })
                    .filter((item) => item.profit < 0.1);
                salesList.forEach((item) => {
                    item.package_img = item.package_img.replaceAll(
                        this.$BASE.OSSDomain,
                        ""
                    );
                });
                console.log("salesList", salesList);
                var isShowTips = false;
                for (const sale of this.entity.salesList) {
                    if (!sale.is_hidden) {
                        for (const item of sale.numList) {
                            console.log("234456==", item);

                            for (const element of item.productList) {
                                let cprice = parseFloat(element.costprice);
                                if (cprice <= 0) {
                                    isShowTips = true;
                                    console.log("9900---", isShowTips);
                                    break;
                                }
                            }
                            if (isShowTips) break;
                        }
                    }
                    if (isShowTips) break;
                }
                if (isShowTips) {
                    this.$confirm("零元成本产品，请添加为赠品！", "提示", {
                        confirmButtonText: "继续添加",
                        cancelButtonText: "取消",
                        type: "warning",
                        dangerouslyUseHTMLString: true,
                    })
                        .then(() => {
                            this.$request.article
                                .packageAdd(salesList)
                                .then((res) => {
                                    if (res.data.error_code == 0) {
                                        this.getDetails(this.rows);
                                        this.packDialog = false;
                                        if (!isSave) {
                                            this.goodsDialog = true;
                                        }
                                        // this.showNext = false;
                                        // this.showNext2 = true;
                                        // this.showNext2Btn = true;
                                    }
                                });
                        })
                        .catch(() => {});
                    return;
                }
                if (profitList.length) {
                    const message = profitList
                        .map(
                            (item) =>
                                `<div>${item.name}：${Math.floor(
                                    (item.profit * 100).toFixed(2)
                                )}%</div>`
                        )
                        .join("");
                    this.$confirm(
                        `<div>下列套餐毛利低于10%，是否继续</div>${message}`,
                        "提示",
                        {
                            confirmButtonText: "确定",
                            cancelButtonText: "取消",
                            type: "warning",
                            dangerouslyUseHTMLString: true,
                        }
                    )
                        .then(() => {
                            this.$request.article
                                .packageAdd(salesList)
                                .then((res) => {
                                    if (res.data.error_code == 0) {
                                        this.getDetails(this.rows);
                                        this.packDialog = false;
                                        if (!isSave) {
                                            this.goodsDialog = true;
                                        }
                                        // this.showNext = false;
                                        // this.showNext2 = true;
                                        // this.showNext2Btn = true;
                                    }
                                });
                        })
                        .catch(() => {});
                    return;
                }
                this.$request.article.packageAdd(salesList).then((res) => {
                    if (res.data.error_code == 0) {
                        this.getDetails(this.rows);
                        this.packDialog = false;
                        if (!isSave) {
                            this.goodsDialog = true;
                        }
                        // this.showNext = false;
                        // this.showNext2 = true;
                        // this.showNext2Btn = true;
                    }
                });
            } else {
                if (!this.validateForm()) {
                    return;
                }
                let type = 2;
                if (this.limitBuy) {
                    if (this.entity.quota_rule.check_level) {
                        //等级限购
                        if (
                            this.entity.quota_rule.min_level < 0 ||
                            this.entity.quota_rule.min_level > 20 ||
                            this.entity.quota_rule.min_level == ""
                        ) {
                            this.$message.error("请输入大于0少于20的最低等级");
                            return;
                        }
                        if (
                            this.entity.quota_rule.max_level > 20 ||
                            this.entity.quota_rule.max_level == ""
                        ) {
                            this.$message.error("请输入少于或等于20的最高等级");
                            return;
                        }
                    }
                    if (this.entity.quota_rule.check_addr) {
                        //地址限购
                        if (this.entity.quota_rule.addrs.length == 0) {
                            this.$message.error("请选择地区");
                            return;
                        }
                    }
                    if (this.entity.quota_rule.check_time) {
                        //时间限购
                        if (this.entity.quota_rule.register_time == "") {
                            this.$message.error("请选择注册时间");
                            return;
                        }
                    }
                }
                if (
                    new Date(this.entity.sell_time).getTime() <
                    new Date(this.entity.onsale_time).getTime()
                ) {
                    this.$message.error("开售时间必须大于上架时间！");
                    return;
                }
                if (
                    new Date(this.entity.sold_out_time).getTime() <=
                    new Date(this.entity.sell_time).getTime()
                ) {
                    this.$message.error("下架时间必须大于开售时间！");
                    return;
                }
                //修改商品信息
                if (this.entity.quota_rule.check_addr) {
                    this.entity.quota_rule.district =
                        this.entity.quota_rule.addrs.join(",");
                }
                if (this.entity.quota_rule.check_level) {
                    this.entity.quota_rule.rank =
                        this.entity.quota_rule.min_level +
                        "," +
                        this.entity.quota_rule.max_level;
                }

                // this.entity.is_channel=this.entity.is_channel?1:0;
                // this.entity.is_hidden_price=this.entity.is_hidden_price?1:0;
                // this.entity.is_support_ts=this.entity.is_support_ts?1:0;
                // this.entity.sellout_sold_out=this.entity.sellout_sold_out?1:0;
                // this.entity.is_sold_out_lock=this.entity.is_sold_out_lock?1:0;
                // this.entity.is_support_reduction=this.entity.is_support_reduction?1:0;
                // this.entity.is_support_coupon=this.entity.is_support_coupon?1:0;
                // this.entity.is_parcel_insurance=this.entity.is_parcel_insurance?1:0;
                let params = {
                    quota_rule: JSON.stringify(this.entity.quota_rule),
                    limit_number: this.entity.limit_number,
                    invariant_number: this.entity.invariant_number,
                    critical_value: this.entity.critical_value,
                    incremental: this.entity.incremental,
                    is_channel: this.entity.is_channel,
                    is_support_ts: this.entity.is_support_ts,
                    latest_storage_time: this.entity.latest_storage_time,
                    is_user_filter: this.entity.is_user_filter,
                    is_hidden_price: this.entity.is_hidden_price,
                    sellout_sold_out: this.entity.sellout_sold_out,
                    is_sold_out_lock: this.entity.is_sold_out_lock,
                    is_support_reduction: this.entity.is_support_reduction,
                    is_support_coupon: this.entity.is_support_coupon,
                    is_parcel_insurance: this.entity.is_parcel_insurance,
                    periods_type: this.rows.periods_type,
                    period: this.entity.id,
                    is_channel: this.entity.is_channel ? 1 : 0,
                    is_hidden_price: this.entity.is_hidden_price ? 1 : 0,
                    sellout_sold_out: this.entity.sellout_sold_out ? 1 : 0,
                    onsale_time: this.entity.onsale_time,
                    sold_out_time: this.entity.sold_out_time,
                    express_id: this.entity.express_id,
                    sell_time: this.entity.sell_time,
                    predict_shipment_time: this.entity.predict_shipment_time,
                    payee_merchant_id: this.entity.payee_merchant_id,
                    payee_merchant_name: this.entity.payee_merchant_name,
                    is_cold_chain: this.entity.is_cold_chain,
                    is_postpone: this.entity.is_postpone,
                    is_cold_free_shipping: this.entity.is_cold_free_shipping,
                    is_leftover: this.entity.is_leftover,
                    is_enc_link: this.entity.is_enc_link,
                    instruction: this.entity.instruction1.join(","),
                    uninstruction: this.entity.uninstruction1.join(","),
                    is_support_invoicing: this.entity.is_support_invoicing,
                    is_timing_pushorder: this.entity.is_timing_pushorder,
                    is_defective_goods: this.entity.is_defective_goods,
                };
                if (this.rows.periods_type == 0) {
                    params.is_seckill = this.entity.is_seckill;
                }
                if (this.rows.periods_type == 2) {
                    params.is_insured = this.entity.is_insured ? 1 : 0;
                }
                if (
                    this.entity.onsale_review_status == 0 ||
                    this.entity.onsale_review_status == 4
                ) {
                    params.onsale_review_status = 1;
                }
                if ([0, 1].includes(this.rows.periods_type)) {
                    params.is_after_sale = this.entity.is_after_sale;
                }
                params.instruction = this.entity.instruction.join(",");
                console.log("hhhh", this.entity.instruction.join(","));
                this.$request.article.goodsUpdate(params, type).then((res) => {
                    if (res.data.error_code == 0) {
                        this.goodsDialog = false;
                        this.dialogVisible = false;
                        this.parentObj.getData();
                        this.$message.success("操作成功！");
                        // this.showNext2Btn = false;
                        // this.showNext1 = true;
                    }
                });
            }
        },
        //基本商品信息修改
        async submits() {
            if (this.validateForm()) {
                if (this.entity.purchaseList.length == 0) {
                    this.$Message.error("请先搜索简码!");
                    return;
                }
                // this.entity.purchaseList.map(item => {
                //     if (item.purchase.length > 1) {
                //         item.purchase.map(child => {
                //             if (child.checked) {
                //                 this.entity.product_id.push(child.id)
                //             }
                //         })
                //     } else {
                //         this.entity.product_id.push(item.purchase[0].id)
                //     }
                // })
                if (
                    this.rows.periods_type === 1 &&
                    this.entity.title.length > 60
                ) {
                    this.$Message.error("秒发商品标题需小于60字");
                    return;
                }
                if (!this.coverList.length !== !this.entity.video) {
                    this.$Message.error("请完善视频信息");
                    return;
                }
                // if(this.entity.video){
                //     this.entity.video = this.entity.video[0].url;
                // }
                // console.log(this.rows.onsale_review_status,'this.rows.onsale_review_status')
                // return;
                // if (
                //     this.rows.onsale_review_status === 0 &&
                //     this.rows.label_arr.length === 0
                // ) {
                //     this.$Message.error("商品标签不能为空");
                //     return;
                // }
                const mostwordsList = [
                    { text: this.entity.title, label: "商品标题" },
                    { text: this.entity.brief, label: "一句话介绍" },
                    { text: this.entity.detail, label: "商品详情" },
                ];
                let product_info_list = [];
                this.entity.newPurchaseList.map((pitem) => {
                    console.log(pitem);
                    if (pitem.purchase) {
                        pitem.purchase.map((item) => {
                            let obj = {
                                tasting_notes: item.tasting_notes,
                                score: item.score,
                                prize: item.prize,
                                drinking_suggestion: item.drinking_suggestion,
                                short_code: item.short_code,
                                product_id: item.id,
                            };
                            product_info_list.push(obj);
                            mostwordsList.push(
                                ...[
                                    {
                                        text: item.brewing,
                                        label: `${item.short_code}-酿造工艺`,
                                    },
                                    {
                                        text: item.score,
                                        label: `${item.short_code}-评分`,
                                    },
                                    {
                                        text: item.tasting_notes,
                                        label: `${item.short_code}-Tasting Notes`,
                                    },
                                    {
                                        text: item.prize,
                                        label: `${item.short_code}-获奖`,
                                    },
                                    {
                                        text: item.drinking_suggestion,
                                        label: `${item.short_code}-饮用建议`,
                                    },
                                ]
                            );
                        });
                    }
                });
                const data = {
                    product_info: product_info_list,
                    id: this.entity.id,
                    title: this.entity.title,
                    title1: this.entity.title1,
                    brief: this.entity.brief,
                    banner_img: this.entity.filelist1.join(","),
                    product_img: this.entity.filelist3.join(","),
                    detail: this.entity.detail,
                    product_id: this.entity.product_id.join(","),
                    video_cover: this.coverList.join(","),
                    video: this.entity.video,
                    horizontal_img:
                        this.rows.periods_type === 1
                            ? this.entity.filelist2.join(",")
                            : "",
                    purchasing_said: this.entity.purchasing_said,
                };
                const judgeMostwords = async (text, label) => {
                    try {
                        const res = await this.$request.article.judgeMostwords({
                            text,
                        });
                        const { found = [] } = res?.data || {};
                        return found.length
                            ? { label, desc: found.join("，") }
                            : null;
                    } catch (e) {
                        return null;
                    }
                };
                const judgeResList = await Promise.all(
                    mostwordsList
                        .filter(({ text }) => text)
                        .map(({ text, label }) => judgeMostwords(text, label))
                );
                const message = judgeResList
                    .filter((item) => item)
                    .map(({ label, desc }) => `<div>${label}：${desc}</div>`)
                    .join("");
                if (message) {
                    const confirm = () => {
                        return new Promise((resolve) => {
                            this.$confirm(`${message}`, "极限词提示", {
                                confirmButtonText: "继续提交",
                                cancelButtonText: "去修改",
                                type: "warning",
                                dangerouslyUseHTMLString: true,
                            })
                                .then(() => {
                                    resolve();
                                })
                                .catch(() => {});
                        });
                    };
                    await confirm();
                }
                if (this.entity.id > 0) {
                    let type = 2;
                    if (this.rows.periods_type == 0) {
                        this.$request.article
                            .flashAdd(data, type)
                            .then((res) => {
                                this.articalCallback(res);
                            });
                    } else if (this.rows.periods_type == 1) {
                        this.$request.article
                            .secondAdd(data, type)
                            .then((res) => {
                                this.articalCallback(res);
                            });
                    } else if (this.rows.periods_type == 2) {
                        this.$request.article
                            .crossAdd(data, type)
                            .then((res) => {
                                this.articalCallback(res);
                            });
                    } else if (this.rows.periods_type == 3) {
                        this.$request.article
                            .leftoverAdd(data, type)
                            .then((res) => {
                                this.articalCallback(res);
                            });
                    } else if (this.rows.periods_type == 4) {
                        this.$request.article
                            .rabbitAdd(data, type)
                            .then((res) => {
                                this.articalCallback(res);
                            });
                    }
                } else {
                    if (this.rows.periods_type == 0) {
                        this.$request.article.flashAdd(data).then((res) => {
                            this.articalCallback(res);
                        });
                    } else if (this.rows.periods_type == 1) {
                        this.$request.article.secondAdd(data).then((res) => {
                            this.articalCallback(res);
                        });
                    } else if (this.rows.periods_type == 2) {
                        this.$request.article.crossAdd(data).then((res) => {
                            this.articalCallback(res);
                        });
                    } else if (this.rows.periods_type == 3) {
                        this.$request.article.leftoverAdd(data).then((res) => {
                            this.articalCallback(res);
                        });
                    } else if (this.rows.periods_type == 4) {
                        this.$request.article.rabbitAdd(data).then((res) => {
                            this.articalCallback(res);
                        });
                    }
                }
            }
        },
        articalCallback(res) {
            if (res.data.error_code == 0) {
                this.$emit("closeGoodsOpDialog");
                this.$message.success("操作成功");
            }
        },
        //简码选择
        checkedStore(row, purchase) {
            purchase.map((item) => {
                if (row.fictitious_name == item.fictitious_name) {
                    this.$set(item, "checked", row.checked);
                } else {
                    item.checked = false;
                }
            });
            this.nextBtn = false;
        },
        //获取仓库信息
        getWarehouseList() {
            //获取仓库信息
            this.entity.purchaseList.map((item) => {
                item.purchase.map((v) => {
                    this.$request.article
                        .getWarehouse({
                            bra_code: v.short_code,
                        })
                        .then((res) => {
                            console.log(res.data.data, 555588877);
                            this.$set(v, "warehouseList", res.data.data);
                        });
                });
            });
        },
        getNewWarehouseList() {
            //获取发货仓库信息
            this.$request.article
                .warehouseList({
                    channel_types: this.rows.periods_type,
                    payee_merchant_id: this.entity.payee_merchant_id,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        console.log(res.data.data, 44433);
                        this.sendWarehouseList = res.data.data.map((item) => ({
                            ...item,
                            disabled: false,
                        }));
                    }
                });
        },
        //详情数据
        openForm(row) {
            console.log("10933----------");

            this.rows = row;
            this.getParametersList();
            this.dialogVisible = true;
            this.dialogVisible2 = true;
            // this.type = type;
            this.disab = false;
            this.nextBtn1 = false;
            this.purList = [];
            this.getAddress();
            this.getPurchase();
            this.getDetails(row);
        },
        //详情获取
        getDetails(row) {
            if (row) {
                this.remoteMethod(row.supplier);
                if (this.rows.periods_type == 0) {
                    this.$request.article
                        .getFlashDetail({
                            id: row.id,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.onsale_verify_status =
                                    res.data.data.onsale_verify_status;
                                this.disab =
                                    res.data.data.buyer_review_status == 2
                                        ? true
                                        : false;
                                res.data.data.is_cold_chain =
                                    res.data.data.is_cold_chain == 1
                                        ? true
                                        : false;
                                res.data.data.is_postpone =
                                    res.data.data.is_postpone == 1
                                        ? true
                                        : false;
                                res.data.data.is_cold_free_shipping =
                                    res.data.data.is_cold_free_shipping == 1
                                        ? true
                                        : false;
                                // 供应商回显
                                res.data.data.suppliers = {
                                    supplier_name: res.data.data.supplier,
                                    id: res.data.data.supplier_id,
                                };
                                // res.data.data.is_supplier_delivery = res.data.data.is_supplier_delivery == 1 ? true :
                                //     false;
                                // res.data.data.is_presell = res.data.data.is_presell == 1 ? true : false;
                                //回显采购信息
                                res.data.data.purchaseList = [];
                                if (
                                    res.data.data.product_list.length == 0 ||
                                    !res.data.data.product_list[0].warehouse
                                ) {
                                    res.data.data.purchaseList.push({
                                        short_code: "",
                                        purchase: [],
                                        isDel: true,
                                    });
                                } else {
                                    res.data.data.product_list.map((item) => {
                                        res.data.data.purchaseList.push({
                                            purchase: [item],
                                            short_code: item.short_code,
                                            isDel: false,
                                        });
                                    });
                                }

                                console.warn(res.data.data.product_list);
                                res.data.data.newPurchaseList = []; // 用于评分等四个字段的数据组装....
                                if (res.data.data.product_list.length) {
                                    res.data.data.product_list.map((item) => {
                                        res.data.data.newPurchaseList.push({
                                            purchase: [item],
                                            short_code: item.short_code,
                                            isDel: false,
                                        });
                                    });
                                }

                                if (res.data.data.quota_rule) {
                                    res.data.data.quota_rule = JSON.parse(
                                        res.data.data.quota_rule
                                    );
                                    if (res.data.data.quota_rule) {
                                        if (
                                            res.data.data.quota_rule
                                                .check_addr ||
                                            res.data.data.quota_rule
                                                .check_level ||
                                            res.data.data.quota_rule.check_time
                                        ) {
                                            this.limitBuy = true;
                                        }
                                    }
                                } else {
                                    res.data.data.quota_rule = {
                                        check_addr: false,
                                        check_level: false,
                                        check_time: false,
                                        quota_type: "1",
                                        quota_number:
                                            this.rows.periods_type == 2
                                                ? 1
                                                : 9999,
                                        register_time: "",
                                        min_level: "",
                                        max_level: "",
                                        addrs: [],
                                        district: "",
                                        rank: "",
                                    };
                                }
                                res.data.data.product_id = [
                                    res.data.data.product_id,
                                ];

                                //套餐保存后回显
                                if (
                                    row.onsale_status == 3 ||
                                    res.data.data.package.length > 0
                                ) {
                                    this.purList = res.data.data.product_list;
                                    this.purcharseVisible = false;
                                    this.salesVisible = true;
                                    let datas = {};
                                    res.data.data.salesList = [];
                                    //套餐模拟数据
                                    res.data.data.package.map((item, index) => {
                                        item.is_onsale =
                                            item.is_onsale == 1 ? true : false;
                                        item.unlimited =
                                            item.unlimited == 1 ? true : false;
                                        item.is_original_package =
                                            item.is_original_package == 1
                                                ? true
                                                : false;

                                        item.force_original_package =
                                            item.force_original_package == 1
                                                ? true
                                                : false;

                                        item.is_hidden =
                                            item.is_hidden == 1 ? true : false;

                                        //验证套餐是否是其他或者盲盒或者自选
                                        let index1 =
                                            this.parckageOptions.findIndex(
                                                (f) =>
                                                    f.value == item.package_name
                                            );
                                        if (
                                            item.package_name != "其他" &&
                                            index1 < 0 &&
                                            item.is_mystery_box == 0 &&
                                            item.is_custom_package == 0
                                        ) {
                                            item.package_name1 =
                                                item.package_name;
                                            item.package_name = "其他";
                                        }
                                        if (item.is_mystery_box == 1) {
                                            item.package_name1 =
                                                item.package_name;
                                            item.package_name = "盲盒";
                                        }
                                        if (item.is_custom_package == 1) {
                                            item.package_name1 =
                                                item.package_name;
                                            item.package_name = "自选";
                                        }
                                        //采购信息和数量
                                        let lists = JSON.parse(
                                            item.associated_products
                                        );
                                        let isSame = [];
                                        lists.map((child, i) => {
                                            if (child.isGift == 0) {
                                                isSame.push(child);
                                            }
                                            //验证 产品 是否石盲盒
                                            console.log(
                                                typeof child.product_id,
                                                333333
                                            );
                                            if (
                                                typeof child.product_id ==
                                                "object"
                                            ) {
                                                let productList = [];
                                                child.product_id.map((item) => {
                                                    let products =
                                                        res.data.data.product_list.find(
                                                            (f) => f.id == item
                                                        );
                                                    productList.push(products);
                                                });
                                                child.productList = productList;
                                            } else {
                                                let productList = [];
                                                let products =
                                                    res.data.data.product_list.find(
                                                        (f) =>
                                                            f.id ==
                                                            child.product_id
                                                    );
                                                productList.push(products);
                                                // child.productList = [res.data.data.product_list[i]];
                                                child.productList = productList;
                                            }
                                            child.visible = true;
                                            child.isCheck = true;
                                            child.disabled = true;
                                            child.codeVisible = false;
                                            // 为自选套餐初始化 custom_product_count 字段
                                            if (!child.custom_product_count) {
                                                child.custom_product_count =
                                                    child.nums || 0;
                                            }
                                        });
                                        datas = {
                                            same:
                                                item.package_name != "其他" &&
                                                item.package_name != "盲盒" &&
                                                item.package_name != "自选" &&
                                                isSame.length > 1
                                                    ? true
                                                    : false, //套餐内不相同
                                            other: false,
                                            numList: lists,
                                            visible: true,
                                            unlimited: true,
                                        };
                                        res.data.data.salesList.push(
                                            Object.assign(datas, item)
                                        );
                                    });
                                } else if (
                                    res.data.data.product_list[0].inventory
                                ) {
                                    // 库存保存后回显
                                    res.data.data.salesList = [
                                        {
                                            period_id: res.data.data.id,
                                            periods_type: "",
                                            package_name: "",
                                            package_name1: "",
                                            price: "",
                                            market_price: "",
                                            associated_products: "",
                                            limit_number: "",
                                            inventory: "",
                                            is_hidden: false,
                                            is_original_package: 0,
                                            force_original_package: 0,
                                            coupons_id: "",
                                            is_cold_chain: 0,
                                            is_postpone: 0,
                                            is_cold_free_shipping: 0,
                                            invoice_progress: 0,
                                            same: false,
                                            other: false,
                                            visible: false,
                                            unlimited: true,
                                            is_display_price: 0,
                                            numList: [],
                                            is_deposit: 0,
                                            deposit_price: "",
                                            deposit_coupon_threshold: "",
                                            deposit_coupon_value: "",
                                            deposit_coupon_id: 0,
                                            package_img: "",
                                        },
                                    ];
                                    this.purList = res.data.data.product_list;
                                    this.purcharseVisible = false;
                                    this.salesVisible = true;
                                } else {
                                    res.data.data.salesList = [
                                        {
                                            period_id: res.data.data.id,
                                            periods_type: "",
                                            package_name: "",
                                            package_name1: "",
                                            price: "",
                                            market_price: "",
                                            associated_products: "",
                                            limit_number: "",
                                            inventory: "",
                                            is_hidden: false,
                                            is_original_package: 0,
                                            force_original_package: 0,
                                            coupons_id: "",
                                            is_cold_chain: 0,
                                            is_cold_free_shipping: 0,
                                            is_postpone: 0,
                                            invoice_progress: 0,
                                            same: false,
                                            other: false,
                                            visible: false,
                                            unlimited: true,
                                            is_display_price: 0,
                                            numList: [],
                                            is_deposit: 0,
                                            deposit_price: "",
                                            deposit_coupon_threshold: "",
                                            deposit_coupon_value: "",
                                            deposit_coupon_id: 0,
                                            package_img: "",
                                        },
                                    ];
                                    this.purcharseVisible = true;
                                    this.salesVisible = false;
                                }

                                res.data.data.filelist1 = [];
                                res.data.data.filelist1 =
                                    res.data.data.banner_img;
                                res.data.data.filelist3 =
                                    res.data.data.product_img;
                                res.data.data.video_cover
                                    ? (this.coverList =
                                          res.data.data.video_cover.split(","))
                                    : (this.coverList = []);
                                res.data.data.buyers = {
                                    id: res.data.data.buyer_id,
                                    realname: res.data.data.buyer_name,
                                };
                                res.data.data.buyers1 = {
                                    id: res.data.data.buyer_id,
                                    realname: res.data.data.buyer_name,
                                };
                                //为了回显 所以 id必须为int类型
                                res.data.data.instruction1 = [];
                                let instructionList =
                                    res.data.data.instruction &&
                                    res.data.data.instruction.split(",");
                                instructionList &&
                                    instructionList.map((item) => {
                                        res.data.data.instruction1.push(
                                            Number(item)
                                        );
                                    });
                                res.data.data.uninstruction1 = [];
                                let instructionList1 =
                                    res.data.data.uninstruction &&
                                    res.data.data.uninstruction.split(",");
                                instructionList1 &&
                                    instructionList1.map((item) => {
                                        res.data.data.uninstruction1.push(
                                            Number(item)
                                        );
                                    });

                                //invariant_number提供回显  传过去值用limit_number
                                res.data.data.limit_number =
                                    res.data.data.invariant_number;
                                this.entity = Object.assign({}, res.data.data);
                                this.clearDetial();
                                //获取仓库信息
                                this.getWarehouseList();
                                this.entity.instruction = res.data.data
                                    .instruction
                                    ? res.data.data.instruction
                                          .split(",")
                                          .map((item) => parseInt(item))
                                    : [];
                                console.log(this.entity, 2333);
                                this.entity.is_channel =
                                    this.entity.is_channel == 1 ? true : false;
                                this.entity.is_hidden_price =
                                    this.entity.is_hidden_price == 1
                                        ? true
                                        : false;
                                this.entity.sellout_sold_out =
                                    this.entity.sellout_sold_out == 1
                                        ? true
                                        : false;
                                this.entity.is_support_ts = this.entity
                                    .is_support_ts
                                    ? true
                                    : false;
                                this.tsString =
                                    "支持暂存 " +
                                    this.entity.latest_storage_time;
                                this.entity.is_user_filter = this.entity
                                    .is_user_filter
                                    ? true
                                    : false;
                                this.entity.is_sold_out_lock = this.entity
                                    .is_sold_out_lock
                                    ? true
                                    : false;
                                this.entity.is_support_reduction = this.entity
                                    .is_support_reduction
                                    ? true
                                    : false;
                                this.entity.is_support_coupon = this.entity
                                    .is_support_coupon
                                    ? true
                                    : false;
                                this.entity.is_parcel_insurance = this.entity
                                    .is_parcel_insurance
                                    ? true
                                    : false;
                                this.productImgList = [
                                    ...this.entity.filelist3,
                                ];
                            }
                        });
                } else if (this.rows.periods_type == 1) {
                    this.$request.article
                        .getSecondDetail({
                            id: row.id,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.onsale_verify_status =
                                    res.data.data.onsale_verify_status;
                                this.disab =
                                    res.data.data.buyer_review_status == 2
                                        ? true
                                        : false;
                                res.data.data.is_cold_chain =
                                    res.data.data.is_cold_chain == 1
                                        ? true
                                        : false;
                                res.data.data.is_postpone =
                                    res.data.data.is_postpone == 1
                                        ? true
                                        : false;
                                res.data.data.is_cold_free_shipping =
                                    res.data.data.is_cold_free_shipping == 1
                                        ? true
                                        : false;
                                // 供应商回显
                                res.data.data.suppliers = {
                                    supplier_name: res.data.data.supplier,
                                    id: res.data.data.supplier_id,
                                };
                                // res.data.data.is_supplier_delivery = res.data.data.is_supplier_delivery == 1 ? true :
                                //     false;
                                // res.data.data.is_presell = res.data.data.is_presell == 1 ? true : false;
                                //回显采购信息
                                res.data.data.purchaseList = [];
                                if (
                                    res.data.data.product_list.length == 0 ||
                                    !res.data.data.product_list[0].warehouse
                                ) {
                                    res.data.data.purchaseList.push({
                                        short_code: "",
                                        purchase: [],
                                        isDel: true,
                                    });
                                } else {
                                    res.data.data.product_list.map((item) => {
                                        res.data.data.purchaseList.push({
                                            purchase: [item],
                                            short_code: item.short_code,
                                            isDel: false,
                                        });
                                    });
                                }
                                console.warn(res.data.data.product_list);
                                res.data.data.newPurchaseList = []; // 用于评分等四个字段的数据组装....
                                if (res.data.data.product_list.length) {
                                    res.data.data.product_list.map((item) => {
                                        res.data.data.newPurchaseList.push({
                                            purchase: [item],
                                            short_code: item.short_code,
                                            isDel: false,
                                        });
                                    });
                                }

                                if (res.data.data.quota_rule) {
                                    res.data.data.quota_rule = JSON.parse(
                                        res.data.data.quota_rule
                                    );
                                    if (res.data.data.quota_rule) {
                                        if (
                                            res.data.data.quota_rule
                                                .check_addr ||
                                            res.data.data.quota_rule
                                                .check_level ||
                                            res.data.data.quota_rule.check_time
                                        ) {
                                            this.limitBuy = true;
                                        }
                                    }
                                } else {
                                    res.data.data.quota_rule = {
                                        check_addr: false,
                                        check_level: false,
                                        check_time: false,
                                        quota_type: "1",
                                        quota_number:
                                            this.rows.periods_type == 2
                                                ? 1
                                                : 9999,
                                        register_time: "",
                                        min_level: "",
                                        max_level: "",
                                        addrs: [],
                                        district: "",
                                        rank: "",
                                    };
                                }
                                res.data.data.product_id = [
                                    res.data.data.product_id,
                                ];
                                if (
                                    row.onsale_status == 3 ||
                                    res.data.data.package.length > 0
                                ) {
                                    this.purList = res.data.data.product_list;
                                    this.purcharseVisible = false;
                                    this.salesVisible = true;
                                    let datas = {};
                                    res.data.data.salesList = [];
                                    //套餐模拟数据
                                    res.data.data.package.map((item, index) => {
                                        item.is_onsale =
                                            item.is_onsale == 1 ? true : false;
                                        item.unlimited =
                                            item.unlimited == 1 ? true : false;
                                        item.is_original_package =
                                            item.is_original_package == 1
                                                ? true
                                                : false;
                                        item.force_original_package =
                                            item.force_original_package == 1
                                                ? true
                                                : false;

                                        item.is_hidden =
                                            item.is_hidden == 1 ? true : false;
                                        //验证套餐是否是其他或者盲盒或者自选
                                        let index1 =
                                            this.parckageOptions.findIndex(
                                                (f) =>
                                                    f.value == item.package_name
                                            );
                                        if (
                                            item.package_name != "其他" &&
                                            index1 < 0 &&
                                            item.is_mystery_box == 0 &&
                                            item.is_custom_package == 0
                                        ) {
                                            item.package_name1 =
                                                item.package_name;
                                            item.package_name = "其他";
                                        }
                                        if (item.is_mystery_box == 1) {
                                            item.package_name1 =
                                                item.package_name;
                                            item.package_name = "盲盒";
                                        }
                                        if (item.is_custom_package == 1) {
                                            item.package_name1 =
                                                item.package_name;
                                            item.package_name = "自选";
                                        }
                                        //采购信息和数量
                                        let lists = JSON.parse(
                                            item.associated_products
                                        );

                                        let isSame = [];
                                        lists.map((child, i) => {
                                            if (child.isGift == 0) {
                                                isSame.push(child);
                                            }
                                            //验证 产品 是否石盲盒
                                            console.log(
                                                typeof child.product_id,
                                                333333
                                            );
                                            if (
                                                typeof child.product_id ==
                                                "object"
                                            ) {
                                                let productList = [];
                                                child.product_id.map((item) => {
                                                    let products =
                                                        res.data.data.product_list.find(
                                                            (f) => f.id == item
                                                        );
                                                    productList.push(products);
                                                });
                                                child.productList = productList;
                                            } else {
                                                let productList = [];
                                                let products =
                                                    res.data.data.product_list.find(
                                                        (f) =>
                                                            f.id ==
                                                            child.product_id
                                                    );
                                                productList.push(products);
                                                // child.productList = [res.data.data.product_list[i]];
                                                child.productList = productList;
                                            }
                                            child.visible = true;
                                            child.isCheck = true;
                                            child.disabled = true;
                                            child.codeVisible = false;
                                            // 为自选套餐初始化 custom_product_count 字段
                                            if (!child.custom_product_count) {
                                                child.custom_product_count =
                                                    child.nums || 0;
                                            }
                                        });
                                        datas = {
                                            same:
                                                item.package_name != "其他" &&
                                                item.package_name != "盲盒" &&
                                                item.package_name != "自选" &&
                                                isSame.length > 1
                                                    ? true
                                                    : false, //套餐内不相同
                                            other: false,
                                            numList: lists,
                                            visible: true,
                                            unlimited: true,
                                        };
                                        res.data.data.salesList.push(
                                            Object.assign(datas, item)
                                        );
                                    });
                                } else if (
                                    res.data.data.product_list[0].inventory
                                ) {
                                    // 库存保存后回显
                                    res.data.data.salesList = [
                                        {
                                            period_id: res.data.data.id,
                                            periods_type: "",
                                            package_name: "",
                                            package_name1: "",
                                            price: "",
                                            market_price: "",
                                            associated_products: "",
                                            limit_number: "",
                                            inventory: "",
                                            is_hidden: false,
                                            is_original_package: 0,
                                            force_original_package: 0,
                                            coupons_id: "",
                                            is_cold_chain: 0,
                                            is_postpone: 0,
                                            is_cold_free_shipping: 0,
                                            invoice_progress: 0,
                                            same: false,
                                            other: false,
                                            visible: false,
                                            unlimited: true,
                                            is_display_price: 0,
                                            numList: [],
                                            package_img: "",
                                        },
                                    ];
                                    this.purList = res.data.data.product_list;
                                    this.purcharseVisible = false;
                                    this.salesVisible = true;
                                } else {
                                    res.data.data.salesList = [
                                        {
                                            period_id: res.data.data.id,
                                            periods_type: "",
                                            package_name: "",
                                            package_name1: "",
                                            price: "",
                                            market_price: "",
                                            associated_products: "",
                                            limit_number: "",
                                            inventory: "",
                                            is_hidden: false,
                                            is_original_package: 0,
                                            force_original_package: 0,
                                            coupons_id: "",
                                            is_cold_chain: 0,
                                            is_postpone: 0,
                                            is_cold_free_shipping: 0,
                                            invoice_progress: 0,
                                            same: false,
                                            other: false,
                                            visible: false,
                                            unlimited: true,
                                            is_display_price: 0,
                                            numList: [],
                                            package_img: "",
                                        },
                                    ];
                                    this.purcharseVisible = true;
                                    this.salesVisible = false;
                                }

                                res.data.data.filelist1 = [];
                                res.data.data.filelist2 =
                                    res.data.data.horizontal_img;
                                res.data.data.filelist1 =
                                    res.data.data.banner_img;
                                res.data.data.filelist3 =
                                    res.data.data.product_img;
                                res.data.data.video_cover
                                    ? (this.coverList =
                                          res.data.data.video_cover.split(","))
                                    : (this.coverList = []);
                                res.data.data.buyers = {
                                    id: res.data.data.buyer_id,
                                    realname: res.data.data.buyer_name,
                                };
                                res.data.data.buyers1 = {
                                    id: res.data.data.buyer_id,
                                    realname: res.data.data.buyer_name,
                                };
                                res.data.data.instruction1 = [];
                                let instructionList =
                                    res.data.data.instruction &&
                                    res.data.data.instruction.split(",");
                                instructionList &&
                                    instructionList.map((item) => {
                                        res.data.data.instruction1.push(
                                            Number(item)
                                        );
                                    });
                                res.data.data.uninstruction1 = [];
                                let instructionList1 =
                                    res.data.data.uninstruction &&
                                    res.data.data.uninstruction.split(",");
                                instructionList1 &&
                                    instructionList1.map((item) => {
                                        res.data.data.uninstruction1.push(
                                            Number(item)
                                        );
                                    });
                                //invariant_number提供回显  传过去值用limit_number
                                res.data.data.limit_number =
                                    res.data.data.invariant_number;
                                this.entity = Object.assign({}, res.data.data);
                                this.clearDetial();
                                console.log(this.entity, 2333);
                                this.entity.instruction = res.data.data
                                    .instruction
                                    ? res.data.data.instruction
                                          .split(",")
                                          .map((item) => parseInt(item))
                                    : [];
                                this.entity.is_channel =
                                    this.entity.is_channel == 1 ? true : false;
                                this.entity.is_hidden_price =
                                    this.entity.is_hidden_price == 1
                                        ? true
                                        : false;
                                this.entity.sellout_sold_out =
                                    this.entity.sellout_sold_out == 1
                                        ? true
                                        : false;
                                this.entity.is_support_ts = this.entity
                                    .is_support_ts
                                    ? true
                                    : false;
                                this.entity.is_user_filter = this.entity
                                    .is_user_filter
                                    ? true
                                    : false;
                                this.entity.is_sold_out_lock = this.entity
                                    .is_sold_out_lock
                                    ? true
                                    : false;
                                this.entity.is_support_reduction = this.entity
                                    .is_support_reduction
                                    ? true
                                    : false;
                                this.entity.is_support_coupon = this.entity
                                    .is_support_coupon
                                    ? true
                                    : false;
                                this.entity.is_parcel_insurance = this.entity
                                    .is_parcel_insurance
                                    ? true
                                    : false;
                                this.productImgList = [
                                    ...this.entity.filelist3,
                                ];
                            }
                        });
                } else if (this.rows.periods_type == 2) {
                    this.$request.article
                        .getCrossDetail({
                            id: row.id,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.onsale_verify_status =
                                    res.data.data.onsale_verify_status;
                                this.disab =
                                    res.data.data.buyer_review_status == 2
                                        ? true
                                        : false;
                                res.data.data.is_cold_chain =
                                    res.data.data.is_cold_chain == 1
                                        ? true
                                        : false;
                                res.data.data.is_postpone =
                                    res.data.data.is_postpone == 1
                                        ? true
                                        : false;
                                res.data.data.is_cold_free_shipping =
                                    res.data.data.is_cold_free_shipping == 1
                                        ? true
                                        : false;
                                res.data.data.is_leftover =
                                    res.data.data.is_leftover == 1
                                        ? true
                                        : false;

                                // 供应商回显
                                res.data.data.suppliers = {
                                    supplier_name: res.data.data.supplier,
                                    id: res.data.data.supplier_id,
                                };
                                // res.data.data.is_supplier_delivery = res.data.data.is_supplier_delivery == 1 ? true :
                                //     false;
                                // res.data.data.is_presell = res.data.data.is_presell == 1 ? true : false;
                                //回显采购信息
                                res.data.data.purchaseList = [];
                                if (
                                    res.data.data.product_list.length == 0 ||
                                    !res.data.data.product_list[0].warehouse
                                ) {
                                    //
                                    res.data.data.purchaseList.push({
                                        short_code: "",
                                        purchase: [],
                                        isDel: true,
                                    });
                                } else {
                                    res.data.data.product_list.map((item) => {
                                        res.data.data.purchaseList.push({
                                            purchase: [item],
                                            short_code: item.short_code,
                                            isDel: false,
                                        });
                                    });
                                }
                                console.warn(res.data.data.product_list);
                                res.data.data.newPurchaseList = []; // 用于评分等四个字段的数据组装....
                                if (res.data.data.product_list.length) {
                                    res.data.data.product_list.map((item) => {
                                        res.data.data.newPurchaseList.push({
                                            purchase: [item],
                                            short_code: item.short_code,
                                            isDel: false,
                                        });
                                    });
                                }
                                if (res.data.data.quota_rule) {
                                    res.data.data.quota_rule = JSON.parse(
                                        res.data.data.quota_rule
                                    );
                                    if (res.data.data.quota_rule) {
                                        if (
                                            res.data.data.quota_rule
                                                .check_addr ||
                                            res.data.data.quota_rule
                                                .check_level ||
                                            res.data.data.quota_rule.check_time
                                        ) {
                                            this.limitBuy = true;
                                        }
                                    }
                                } else {
                                    res.data.data.quota_rule = {
                                        check_addr: false,
                                        check_level: false,
                                        check_time: false,
                                        quota_type: "1",
                                        quota_number:
                                            this.rows.periods_type == 2
                                                ? 1
                                                : 9999,
                                        register_time: "",
                                        min_level: "",
                                        max_level: "",
                                        addrs: [],
                                        district: "",
                                        rank: "",
                                    };
                                }
                                res.data.data.product_id = [
                                    res.data.data.product_id,
                                ];
                                if (
                                    row.onsale_status == 3 ||
                                    res.data.data.package.length > 0
                                ) {
                                    this.purList = res.data.data.product_list;
                                    this.purcharseVisible = false;
                                    this.salesVisible = true;
                                    let datas = {};
                                    res.data.data.salesList = [];
                                    //套餐模拟数据
                                    res.data.data.package.map((item, index) => {
                                        item.is_onsale =
                                            item.is_onsale == 1 ? true : false;
                                        item.unlimited =
                                            item.unlimited == 1 ? true : false;
                                        item.is_original_package =
                                            item.is_original_package == 1
                                                ? true
                                                : false;
                                        item.force_original_package =
                                            item.force_original_package == 1
                                                ? true
                                                : false;
                                        item.is_cold_chain =
                                            item.is_cold_chain == 1
                                                ? true
                                                : false;
                                        item.is_postpone =
                                            item.is_postpone == 1
                                                ? true
                                                : false;
                                        item.is_cold_free_shipping =
                                            item.is_cold_free_shipping == 1
                                                ? true
                                                : false;
                                        item.is_leftover =
                                            item.is_leftover == 1
                                                ? true
                                                : false;

                                        item.is_hidden =
                                            item.is_hidden == 1 ? true : false;
                                        //验证套餐是否是其他或者盲盒或者自选
                                        let index1 =
                                            this.parckageOptions.findIndex(
                                                (f) =>
                                                    f.value == item.package_name
                                            );
                                        if (
                                            item.package_name != "其他" &&
                                            index1 < 0 &&
                                            item.is_mystery_box == 0 &&
                                            item.is_custom_package == 0
                                        ) {
                                            item.package_name1 =
                                                item.package_name;
                                            item.package_name = "其他";
                                        }
                                        if (item.is_mystery_box == 1) {
                                            item.package_name1 =
                                                item.package_name;
                                            item.package_name = "盲盒";
                                        }
                                        if (item.is_custom_package == 1) {
                                            item.package_name1 =
                                                item.package_name;
                                            item.package_name = "自选";
                                        }
                                        //采购信息和数量
                                        let lists = JSON.parse(
                                            item.associated_products
                                        );

                                        let isSame = [];
                                        lists.map((child, i) => {
                                            if (child.isGift == 0) {
                                                isSame.push(child);
                                            }
                                            //验证 产品 是否石盲盒
                                            console.log(
                                                typeof child.product_id,
                                                333333
                                            );
                                            if (
                                                typeof child.product_id ==
                                                "object"
                                            ) {
                                                let productList = [];
                                                child.product_id.map((item) => {
                                                    let products =
                                                        res.data.data.product_list.find(
                                                            (f) => f.id == item
                                                        );
                                                    productList.push(products);
                                                });
                                                child.productList = productList;
                                            } else {
                                                let productList = [];
                                                let products =
                                                    res.data.data.product_list.find(
                                                        (f) =>
                                                            f.id ==
                                                            child.product_id
                                                    );
                                                productList.push(products);
                                                // child.productList = [res.data.data.product_list[i]];
                                                child.productList = productList;
                                            }
                                            child.visible = true;
                                            child.isCheck = true;
                                            child.disabled = true;
                                            child.codeVisible = false;
                                        });
                                        console.log(lists, 22222222222222);
                                        datas = {
                                            same:
                                                item.package_name != "其他" &&
                                                item.package_name != "盲盒" &&
                                                item.package_name != "自选" &&
                                                isSame.length > 1
                                                    ? true
                                                    : false, //套餐内不相同
                                            other: false,
                                            numList: lists,
                                            visible: true,
                                            unlimited: true,
                                        };
                                        res.data.data.salesList.push(
                                            Object.assign(datas, item)
                                        );
                                    });
                                } else if (
                                    res.data.data.product_list[0].inventory
                                ) {
                                    // 库存保存后回显
                                    res.data.data.salesList = [
                                        {
                                            period_id: res.data.data.id,
                                            periods_type: "",
                                            package_name: "",
                                            package_name1: "",
                                            price: "",
                                            market_price: "",
                                            associated_products: "",
                                            limit_number: "",
                                            inventory: "",
                                            is_hidden: false,
                                            is_original_package: 0,
                                            force_original_package: 0,
                                            coupons_id: "",
                                            is_cold_chain: 0,
                                            is_postpone: 0,
                                            is_cold_free_shipping: 0,
                                            is_leftover: 0,
                                            invoice_progress: 0,
                                            same: false,
                                            other: false,
                                            visible: false,
                                            unlimited: true,
                                            is_display_price: 0,
                                            numList: [],
                                            package_img: "",
                                        },
                                    ];
                                    this.purList = res.data.data.product_list;
                                    this.purcharseVisible = false;
                                    this.salesVisible = true;
                                } else {
                                    res.data.data.salesList = [
                                        {
                                            period_id: res.data.data.id,
                                            periods_type: "",
                                            package_name: "",
                                            package_name1: "",
                                            price: "",
                                            market_price: "",
                                            associated_products: "",
                                            limit_number: "",
                                            inventory: "",
                                            is_hidden: false,
                                            is_original_package: 0,
                                            force_original_package: 0,
                                            coupons_id: "",
                                            is_cold_chain: 0,
                                            is_cold_free_shipping: 0,
                                            is_leftover: 0,
                                            is_postpone: 0,
                                            invoice_progress: 0,
                                            same: false,
                                            other: false,
                                            visible: false,
                                            unlimited: true,
                                            is_display_price: 0,
                                            numList: [],
                                            package_img: "",
                                        },
                                    ];
                                    this.purcharseVisible = true;
                                    this.salesVisible = false;
                                }
                                res.data.data.filelist1 = [];
                                res.data.data.filelist1 =
                                    res.data.data.banner_img;
                                res.data.data.filelist3 =
                                    res.data.data.product_img;
                                res.data.data.video_cover
                                    ? (this.coverList =
                                          res.data.data.video_cover.split(","))
                                    : (this.coverList = []);
                                res.data.data.buyers = {
                                    id: res.data.data.buyer_id,
                                    realname: res.data.data.buyer_name,
                                };
                                res.data.data.buyers1 = {
                                    id: res.data.data.buyer_id,
                                    realname: res.data.data.buyer_name,
                                };
                                res.data.data.instruction1 = [];
                                let instructionList =
                                    res.data.data.instruction &&
                                    res.data.data.instruction.split(",");
                                instructionList &&
                                    instructionList.map((item) => {
                                        res.data.data.instruction1.push(
                                            Number(item)
                                        );
                                    });
                                res.data.data.uninstruction1 = [];
                                let instructionList1 =
                                    res.data.data.uninstruction &&
                                    res.data.data.uninstruction.split(",");
                                instructionList1 &&
                                    instructionList1.map((item) => {
                                        res.data.data.uninstruction1.push(
                                            Number(item)
                                        );
                                    });
                                //invariant_number提供回显  传过去值用limit_number
                                res.data.data.limit_number =
                                    res.data.data.invariant_number;
                                this.entity = Object.assign({}, res.data.data);
                                this.clearDetial();
                                console.log(this.entity, 2333);
                                this.entity.instruction = res.data.data
                                    .instruction
                                    ? res.data.data.instruction
                                          .split(",")
                                          .map((item) => parseInt(item))
                                    : [];
                                this.entity.is_channel =
                                    this.entity.is_channel == 1 ? true : false;
                                this.entity.is_hidden_price =
                                    this.entity.is_hidden_price == 1
                                        ? true
                                        : false;
                                this.entity.sellout_sold_out =
                                    this.entity.sellout_sold_out == 1
                                        ? true
                                        : false;
                                this.entity.is_support_ts = this.entity
                                    .is_support_ts
                                    ? true
                                    : false;
                                this.tsString =
                                    "支持暂存 " +
                                    this.entity.latest_storage_time;
                                this.entity.is_user_filter = this.entity
                                    .is_user_filter
                                    ? true
                                    : false;
                                this.entity.is_sold_out_lock = this.entity
                                    .is_sold_out_lock
                                    ? true
                                    : false;
                                this.entity.is_support_reduction = this.entity
                                    .is_support_reduction
                                    ? true
                                    : false;
                                this.entity.is_support_coupon = this.entity
                                    .is_support_coupon
                                    ? true
                                    : false;
                                this.entity.is_parcel_insurance = this.entity
                                    .is_parcel_insurance
                                    ? true
                                    : false;
                                this.entity.is_insured =
                                    this.entity.is_insured == 1 ? true : false;
                                this.productImgList = [
                                    ...this.entity.filelist3,
                                ];
                            }
                        });
                } else if (this.rows.periods_type == 3) {
                    this.$request.article
                        .getLeftoverDetail({
                            id: row.id,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.onsale_verify_status =
                                    res.data.data.onsale_verify_status;
                                this.disab =
                                    res.data.data.buyer_review_status == 2
                                        ? true
                                        : false;
                                res.data.data.is_cold_chain =
                                    res.data.data.is_cold_chain == 1
                                        ? true
                                        : false;
                                res.data.data.is_postpone =
                                    res.data.data.is_postpone == 1
                                        ? true
                                        : false;
                                res.data.data.is_cold_free_shipping =
                                    res.data.data.is_cold_free_shipping == 1
                                        ? true
                                        : false;
                                // 供应商回显

                                res.data.data.suppliers = {
                                    supplier_name: res.data.data.supplier,
                                    id: res.data.data.supplier_id,
                                };
                                // res.data.data.is_supplier_delivery = res.data.data.is_supplier_delivery == 1 ? true :
                                //     false;
                                // res.data.data.is_presell = res.data.data.is_presell == 1 ? true : false;
                                //回显采购信息
                                res.data.data.purchaseList = [];
                                if (
                                    res.data.data.product_list.length == 0 ||
                                    !res.data.data.product_list[0].warehouse
                                ) {
                                    res.data.data.purchaseList.push({
                                        short_code: "",
                                        purchase: [],
                                        isDel: true,
                                    });
                                } else {
                                    res.data.data.product_list.map((item) => {
                                        res.data.data.purchaseList.push({
                                            purchase: [item],
                                            short_code: item.short_code,
                                            isDel: false,
                                        });
                                    });
                                }
                                console.warn(res.data.data.product_list);
                                res.data.data.newPurchaseList = []; // 用于评分等四个字段的数据组装....
                                if (res.data.data.product_list.length) {
                                    res.data.data.product_list.map((item) => {
                                        res.data.data.newPurchaseList.push({
                                            purchase: [item],
                                            short_code: item.short_code,
                                            isDel: false,
                                        });
                                    });
                                }
                                if (res.data.data.quota_rule) {
                                    res.data.data.quota_rule = JSON.parse(
                                        res.data.data.quota_rule
                                    );
                                    if (res.data.data.quota_rule) {
                                        if (
                                            res.data.data.quota_rule
                                                .check_addr ||
                                            res.data.data.quota_rule
                                                .check_level ||
                                            res.data.data.quota_rule.check_time
                                        ) {
                                            this.limitBuy = true;
                                        }
                                    }
                                } else {
                                    res.data.data.quota_rule = {
                                        check_addr: false,
                                        check_level: false,
                                        check_time: false,
                                        quota_type: "1",
                                        quota_number:
                                            this.rows.periods_type == 2
                                                ? 1
                                                : 9999,
                                        register_time: "",
                                        min_level: "",
                                        max_level: "",
                                        addrs: [],
                                        district: "",
                                        rank: "",
                                    };
                                }
                                res.data.data.product_id = [
                                    res.data.data.product_id,
                                ];
                                if (
                                    row.onsale_status == 3 ||
                                    res.data.data.package.length > 0
                                ) {
                                    this.purList = res.data.data.product_list;
                                    this.purcharseVisible = false;
                                    this.salesVisible = true;
                                    let datas = {};
                                    res.data.data.salesList = [];
                                    //套餐模拟数据
                                    res.data.data.package.map((item, index) => {
                                        item.is_onsale =
                                            item.is_onsale == 1 ? true : false;
                                        item.unlimited =
                                            item.unlimited == 1 ? true : false;
                                        item.is_original_package =
                                            item.is_original_package == 1
                                                ? true
                                                : false;
                                        item.force_original_package =
                                            item.force_original_package == 1
                                                ? true
                                                : false;
                                        item.is_cold_chain =
                                            item.is_cold_chain == 1
                                                ? true
                                                : false;
                                        item.is_postpone =
                                            item.is_postpone == 1
                                                ? true
                                                : false;
                                        item.is_cold_free_shipping =
                                            item.is_cold_free_shipping == 1
                                                ? true
                                                : false;
                                        item.is_hidden =
                                            item.is_hidden == 1 ? true : false;
                                        //验证套餐是否是其他或者盲盒或者自选
                                        let index1 =
                                            this.parckageOptions.findIndex(
                                                (f) =>
                                                    f.value == item.package_name
                                            );
                                        if (
                                            item.package_name != "其他" &&
                                            index1 < 0 &&
                                            item.is_mystery_box == 0 &&
                                            item.is_custom_package == 0
                                        ) {
                                            item.package_name1 =
                                                item.package_name;
                                            item.package_name = "其他";
                                        }
                                        if (item.is_mystery_box == 1) {
                                            item.package_name1 =
                                                item.package_name;
                                            item.package_name = "盲盒";
                                        }
                                        if (item.is_custom_package == 1) {
                                            item.package_name1 =
                                                item.package_name;
                                            item.package_name = "自选";
                                        }
                                        //采购信息和数量
                                        let lists = JSON.parse(
                                            item.associated_products
                                        );

                                        let isSame = [];
                                        lists.map((child, i) => {
                                            if (child.isGift == 0) {
                                                isSame.push(child);
                                            }
                                            //验证 产品 是否石盲盒
                                            if (
                                                typeof child.product_id ==
                                                "object"
                                            ) {
                                                let productList = [];
                                                child.product_id.map((item) => {
                                                    let products =
                                                        res.data.data.product_list.find(
                                                            (f) => f.id == item
                                                        );
                                                    productList.push(products);
                                                });
                                                child.productList = productList;
                                            } else {
                                                let productList = [];
                                                let products =
                                                    res.data.data.product_list.find(
                                                        (f) =>
                                                            f.id ==
                                                            child.product_id
                                                    );
                                                productList.push(products);
                                                // child.productList = [res.data.data.product_list[i]];
                                                child.productList = productList;
                                            }
                                            child.visible = true;
                                            child.isCheck = true;
                                            child.disabled = true;
                                            child.codeVisible = false;
                                        });

                                        datas = {
                                            same:
                                                item.package_name != "其他" &&
                                                item.package_name != "盲盒" &&
                                                item.package_name != "自选" &&
                                                isSame.length > 1
                                                    ? true
                                                    : false, //套餐内不相同
                                            other: false,
                                            numList: lists,
                                            visible: true,
                                            unlimited: true,
                                        };
                                        res.data.data.salesList.push(
                                            Object.assign(datas, item)
                                        );
                                    });
                                } else if (
                                    res.data.data.product_list[0].inventory
                                ) {
                                    // 库存保存后回显
                                    res.data.data.salesList = [
                                        {
                                            period_id: res.data.data.id,
                                            periods_type: "",
                                            package_name: "",
                                            package_name1: "",
                                            price: "",
                                            market_price: "",
                                            associated_products: "",
                                            limit_number: "",
                                            inventory: "",
                                            is_hidden: false,
                                            is_original_package: 0,
                                            force_original_package: 0,
                                            coupons_id: "",
                                            is_cold_chain: 0,
                                            is_postpone: 0,
                                            is_cold_free_shipping: 0,
                                            invoice_progress: 0,
                                            same: false,
                                            other: false,
                                            visible: false,
                                            unlimited: true,
                                            is_display_price: 0,
                                            numList: [],
                                            package_img: "",
                                        },
                                    ];
                                    this.purList = res.data.data.product_list;
                                    this.purcharseVisible = false;
                                    this.salesVisible = true;
                                } else {
                                    res.data.data.salesList = [
                                        {
                                            period_id: res.data.data.id,
                                            periods_type: "",
                                            package_name: "",
                                            package_name1: "",
                                            price: "",
                                            market_price: "",
                                            associated_products: "",
                                            limit_number: "",
                                            inventory: "",
                                            is_hidden: false,
                                            is_original_package: 0,
                                            force_original_package: 0,
                                            coupons_id: "",
                                            is_cold_chain: 0,
                                            is_cold_free_shipping: 0,
                                            is_postpone: 0,
                                            invoice_progress: 0,
                                            same: false,
                                            other: false,
                                            visible: false,
                                            unlimited: true,
                                            is_display_price: 0,
                                            numList: [],
                                            package_img: "",
                                        },
                                    ];
                                    this.purcharseVisible = true;
                                    this.salesVisible = false;
                                }

                                res.data.data.filelist1 = [];
                                res.data.data.filelist1 =
                                    res.data.data.banner_img;
                                res.data.data.filelist3 =
                                    res.data.data.product_img;
                                res.data.data.video_cover
                                    ? (this.coverList =
                                          res.data.data.video_cover.split(","))
                                    : (this.coverList = []);
                                res.data.data.buyers = {
                                    id: res.data.data.buyer_id,
                                    realname: res.data.data.buyer_name,
                                };
                                res.data.data.buyers1 = {
                                    id: res.data.data.buyer_id,
                                    realname: res.data.data.buyer_name,
                                };
                                res.data.data.instruction1 = [];
                                let instructionList =
                                    res.data.data.instruction &&
                                    res.data.data.instruction.split(",");
                                instructionList &&
                                    instructionList.map((item) => {
                                        res.data.data.instruction1.push(
                                            Number(item)
                                        );
                                    });
                                res.data.data.uninstruction1 = [];
                                let instructionList1 =
                                    res.data.data.uninstruction &&
                                    res.data.data.uninstruction.split(",");
                                instructionList1 &&
                                    instructionList1.map((item) => {
                                        res.data.data.uninstruction1.push(
                                            Number(item)
                                        );
                                    });
                                //invariant_number提供回显  传过去值用limit_number
                                res.data.data.limit_number =
                                    res.data.data.invariant_number;
                                this.entity = Object.assign({}, res.data.data);
                                this.clearDetial();
                                //获取仓库信息
                                this.getWarehouseList();
                                console.log(this.entity, 2333);
                                this.entity.instruction = res.data.data
                                    .instruction
                                    ? res.data.data.instruction
                                          .split(",")
                                          .map((item) => parseInt(item))
                                    : [];
                                this.entity.is_channel =
                                    this.entity.is_channel == 1 ? true : false;
                                this.entity.is_hidden_price =
                                    this.entity.is_hidden_price == 1
                                        ? true
                                        : false;
                                this.entity.sellout_sold_out =
                                    this.entity.sellout_sold_out == 1
                                        ? true
                                        : false;
                                this.entity.is_support_ts = this.entity
                                    .is_support_ts
                                    ? true
                                    : false;
                                this.tsString =
                                    "支持暂存 " +
                                    this.entity.latest_storage_time;
                                this.entity.is_user_filter = this.entity
                                    .is_user_filter
                                    ? true
                                    : false;
                                this.entity.is_sold_out_lock = this.entity
                                    .is_sold_out_lock
                                    ? true
                                    : false;
                                this.entity.is_support_reduction = this.entity
                                    .is_support_reduction
                                    ? true
                                    : false;
                                this.entity.is_support_coupon = this.entity
                                    .is_support_coupon
                                    ? true
                                    : false;
                                this.entity.is_parcel_insurance = this.entity
                                    .is_parcel_insurance
                                    ? true
                                    : false;
                                this.productImgList = [
                                    ...this.entity.filelist3,
                                ];
                            }
                        });
                } else if (this.rows.periods_type == 4) {
                    this.$request.article
                        .rabbitDetail({
                            id: row.id,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                // this.onsale_verify_status =
                                //     res.data.data.onsale_verify_status;
                                this.disab =
                                    res.data.data.buyer_review_status == 2
                                        ? true
                                        : false;
                                res.data.data.is_cold_chain =
                                    res.data.data.is_cold_chain == 1
                                        ? true
                                        : false;
                                res.data.data.is_cold_free_shipping =
                                    res.data.data.is_cold_free_shipping == 1
                                        ? true
                                        : false;
                                res.data.data.is_postpone =
                                    res.data.data.is_postpone == 1
                                        ? true
                                        : false;
                                // 供应商回显
                                res.data.data.suppliers = {
                                    supplier_name: res.data.data.supplier,
                                    id: res.data.data.supplier_id,
                                };
                                // res.data.data.is_supplier_delivery = res.data.data.is_supplier_delivery == 1 ? true :
                                //     false;
                                // res.data.data.is_presell = res.data.data.is_presell == 1 ? true : false;
                                //回显采购信息
                                res.data.data.purchaseList = [];
                                if (
                                    res.data.data.product_list.length == 0 ||
                                    !res.data.data.product_list[0].warehouse
                                ) {
                                    res.data.data.purchaseList.push({
                                        short_code: "",
                                        purchase: [],
                                        isDel: true,
                                    });
                                } else {
                                    res.data.data.product_list.map((item) => {
                                        res.data.data.purchaseList.push({
                                            purchase: [item],
                                            short_code: item.short_code,
                                            isDel: false,
                                        });
                                    });
                                }
                                res.data.data.newPurchaseList = []; // 用于评分等四个字段的数据组装....
                                if (res.data.data.product_list.length) {
                                    res.data.data.product_list.map((item) => {
                                        res.data.data.newPurchaseList.push({
                                            purchase: [item],
                                            short_code: item.short_code,
                                            isDel: false,
                                        });
                                    });
                                }
                                if (res.data.data.quota_rule) {
                                    res.data.data.quota_rule = JSON.parse(
                                        res.data.data.quota_rule
                                    );
                                    if (res.data.data.quota_rule) {
                                        if (
                                            res.data.data.quota_rule
                                                .check_addr ||
                                            res.data.data.quota_rule
                                                .check_level ||
                                            res.data.data.quota_rule.check_time
                                        ) {
                                            this.limitBuy = true;
                                        }
                                    }
                                } else {
                                    res.data.data.quota_rule = {
                                        check_addr: false,
                                        check_level: false,
                                        check_time: false,
                                        quota_type: "1",
                                        quota_number:
                                            this.rows.periods_type == 2
                                                ? 1
                                                : 9999,
                                        register_time: "",
                                        min_level: "",
                                        max_level: "",
                                        addrs: [],
                                        district: "",
                                        rank: "",
                                    };
                                }
                                res.data.data.product_id = [
                                    res.data.data.product_id,
                                ];

                                //套餐保存后回显
                                if (
                                    row.onsale_status == 3 ||
                                    res.data.data.package.length > 0
                                ) {
                                    this.purList = res.data.data.product_list;
                                    this.purcharseVisible = false;
                                    this.salesVisible = true;
                                    let datas = {};
                                    res.data.data.salesList = [];
                                    //套餐模拟数据
                                    res.data.data.package.map((item, index) => {
                                        item.is_onsale =
                                            item.is_onsale == 1 ? true : false;
                                        item.unlimited =
                                            item.unlimited == 1 ? true : false;
                                        item.is_original_package =
                                            item.is_original_package == 1
                                                ? true
                                                : false;

                                        item.force_original_package =
                                            item.force_original_package == 1
                                                ? true
                                                : false;

                                        item.is_hidden =
                                            item.is_hidden == 1 ? true : false;

                                        //验证套餐是否是其他或者盲盒或者自选
                                        let index1 =
                                            this.parckageOptions.findIndex(
                                                (f) =>
                                                    f.value == item.package_name
                                            );
                                        if (
                                            item.package_name != "其他" &&
                                            index1 < 0 &&
                                            item.is_mystery_box == 0 &&
                                            item.is_custom_package == 0
                                        ) {
                                            item.package_name1 =
                                                item.package_name;
                                            item.package_name = "其他";
                                        }
                                        if (item.is_mystery_box == 1) {
                                            item.package_name1 =
                                                item.package_name;
                                            item.package_name = "盲盒";
                                        }
                                        if (item.is_custom_package == 1) {
                                            item.package_name1 =
                                                item.package_name;
                                            item.package_name = "自选";
                                        }
                                        //采购信息和数量
                                        let lists = JSON.parse(
                                            item.associated_products
                                        );
                                        let isSame = [];
                                        lists.map((child, i) => {
                                            if (child.isGift == 0) {
                                                isSame.push(child);
                                            }
                                            //验证 产品 是否石盲盒
                                            console.log(
                                                typeof child.product_id,
                                                333333
                                            );
                                            if (
                                                typeof child.product_id ==
                                                "object"
                                            ) {
                                                let productList = [];
                                                child.product_id.map((item) => {
                                                    let products =
                                                        res.data.data.product_list.find(
                                                            (f) => f.id == item
                                                        );
                                                    productList.push(products);
                                                });
                                                child.productList = productList;
                                            } else {
                                                let productList = [];
                                                let products =
                                                    res.data.data.product_list.find(
                                                        (f) =>
                                                            f.id ==
                                                            child.product_id
                                                    );
                                                productList.push(products);
                                                // child.productList = [res.data.data.product_list[i]];
                                                child.productList = productList;
                                            }
                                            child.visible = true;
                                            child.isCheck = true;
                                            child.disabled = true;
                                            child.codeVisible = false;
                                        });
                                        datas = {
                                            same:
                                                item.package_name != "其他" &&
                                                item.package_name != "盲盒" &&
                                                item.package_name != "自选" &&
                                                isSame.length > 1
                                                    ? true
                                                    : false, //套餐内不相同
                                            other: false,
                                            numList: lists,
                                            visible: true,
                                            unlimited: true,
                                        };
                                        res.data.data.salesList.push(
                                            Object.assign(datas, item)
                                        );
                                    });
                                } else if (
                                    res.data.data.product_list[0].inventory
                                ) {
                                    // 库存保存后回显
                                    res.data.data.salesList = [
                                        {
                                            period_id: res.data.data.id,
                                            periods_type: "",
                                            package_name: "",
                                            package_name1: "",
                                            price: "",
                                            market_price: "",
                                            associated_products: "",
                                            limit_number: "",
                                            inventory: "",
                                            is_hidden: false,
                                            is_original_package: 0,
                                            force_original_package: 0,
                                            coupons_id: "",
                                            is_cold_chain: 0,
                                            is_cold_free_shipping: 0,
                                            is_postpone: 0,
                                            invoice_progress: 0,
                                            same: false,
                                            other: false,
                                            visible: false,
                                            unlimited: true,
                                            is_display_price: 0,
                                            numList: [],
                                            package_img: "",
                                        },
                                    ];
                                    this.purList = res.data.data.product_list;
                                    this.purcharseVisible = false;
                                    this.salesVisible = true;
                                } else {
                                    res.data.data.salesList = [
                                        {
                                            period_id: res.data.data.id,
                                            periods_type: "",
                                            package_name: "",
                                            package_name1: "",
                                            price: "",
                                            market_price: "",
                                            associated_products: "",
                                            limit_number: "",
                                            inventory: "",
                                            is_hidden: false,
                                            is_original_package: 0,
                                            force_original_package: 0,
                                            coupons_id: "",
                                            is_cold_chain: 0,
                                            is_cold_free_shipping: 0,
                                            is_postpone: 0,
                                            invoice_progress: 0,
                                            same: false,
                                            other: false,
                                            visible: false,
                                            unlimited: true,
                                            is_display_price: 0,
                                            numList: [],
                                            package_img: "",
                                        },
                                    ];
                                    this.purcharseVisible = true;
                                    this.salesVisible = false;
                                }

                                res.data.data.filelist1 = [];
                                res.data.data.filelist1 =
                                    res.data.data.banner_img;
                                res.data.data.filelist3 =
                                    res.data.data.product_img;
                                res.data.data.video_cover
                                    ? (this.coverList =
                                          res.data.data.video_cover.split(","))
                                    : (this.coverList = []);
                                res.data.data.buyers = {
                                    id: res.data.data.buyer_id,
                                    realname: res.data.data.buyer_name,
                                };
                                res.data.data.buyers1 = {
                                    id: res.data.data.buyer_id,
                                    realname: res.data.data.buyer_name,
                                };
                                res.data.data.instruction1 = [];
                                let instructionList =
                                    res.data.data.instruction &&
                                    res.data.data.instruction.split(",");
                                instructionList &&
                                    instructionList.map((item) => {
                                        res.data.data.instruction1.push(
                                            Number(item)
                                        );
                                    });
                                res.data.data.uninstruction1 = [];
                                let instructionList1 =
                                    res.data.data.uninstruction &&
                                    res.data.data.uninstruction.split(",");
                                instructionList1 &&
                                    instructionList1.map((item) => {
                                        res.data.data.uninstruction1.push(
                                            Number(item)
                                        );
                                    });
                                //invariant_number提供回显  传过去值用limit_number
                                res.data.data.limit_number =
                                    res.data.data.invariant_number;
                                this.entity = Object.assign({}, res.data.data);
                                this.clearDetial();
                                //获取仓库信息
                                this.getWarehouseList();
                                this.entity.instruction = res.data.data
                                    .instruction
                                    ? res.data.data.instruction
                                          .split(",")
                                          .map((item) => parseInt(item))
                                    : [];
                                console.log(this.entity, 2333);
                                this.entity.is_channel =
                                    this.entity.is_channel == 1 ? true : false;
                                this.entity.is_hidden_price =
                                    this.entity.is_hidden_price == 1
                                        ? true
                                        : false;
                                this.entity.sellout_sold_out =
                                    this.entity.sellout_sold_out == 1
                                        ? true
                                        : false;
                                // this.entity.is_support_ts = this.entity.is_support_ts ? true : false;
                                this.entity.is_user_filter = this.entity
                                    .is_user_filter
                                    ? true
                                    : false;
                                this.entity.is_sold_out_lock = this.entity
                                    .is_sold_out_lock
                                    ? true
                                    : false;
                                this.entity.is_support_reduction = this.entity
                                    .is_support_reduction
                                    ? true
                                    : false;
                                this.entity.is_support_coupon = this.entity
                                    .is_support_coupon
                                    ? true
                                    : false;
                                this.entity.is_parcel_insurance = this.entity
                                    .is_parcel_insurance
                                    ? true
                                    : false;
                                this.productImgList = [
                                    ...this.entity.filelist3,
                                ];
                            }
                        });
                }
            }
        },
        clearDetial() {
            this.$refs.editor.setContent(this.entity.detail);
            setTimeout(() => {
                this.singleValidate();
            }, 0);
        },
        //简码搜索
        async searchCode(code) {
            if (code == "") {
                this.$message.error("请输入简码");
                return;
            }
            var i = 0;
            this.entity.purchaseList.map((item) => {
                if (item.short_code == code) {
                    i++;
                }
            });
            if (i >= 2) {
                this.$message.error("当前ERP编码已存在");
                return;
            }
            var index = this.entity.purchaseList.findIndex(
                (f) => f.short_code == code
            );
            this.entity.purchaseList[index].purchase = [];
            this.$request.article
                .getProducts({
                    short_code: code,
                })
                .then((res) => {
                    if (res.data.error_code == "0") {
                        if (res.data.data.length > 0) {
                            var isHas = false;
                            let isWarehouse = true;
                            this.nextBtn1 = true;
                            //获取发货仓库信息
                            this.$request.article
                                .warehouseList({
                                    channel_types: this.rows.periods_type,
                                    payee_merchant_id:
                                        this.entity.payee_merchant_id,
                                })
                                .then((res1) => {
                                    this.sendWarehouseList = res1.data.data.map(
                                        (item) => ({
                                            ...item,
                                            disabled: false,
                                        })
                                    );
                                });
                            //获取仓库信息
                            this.$request.article
                                .getWarehouse({
                                    bra_code: code,
                                })
                                .then((res2) => {
                                    res.data.data.map((v) => {
                                        v["is_use_comment"] = 0;
                                        this.$set(
                                            v,
                                            "warehouseList",
                                            res2.data.data
                                        );
                                    });
                                });
                            res.data.data.map((item) => {
                                item.costprice1 = item.costprice;
                            });
                            // this.$set(this.entity.purchaseList[index], 'isDel', true);
                            this.entity.purchaseList[index].purchase =
                                res.data.data;
                            console.log(this.entity.purchaseList, 2222);
                            // res.data.data.forEach((item) => {
                            //     isHas = false;
                            //     if (item.warehouse&&item.warehouse.length == 0) {
                            //         isWarehouse = false;
                            //     }
                            //     this.entity.purchaseList.forEach((child, i) => {
                            //         if (item.bar_code == child.bar_code) {
                            //             isHas = true;
                            //             index = i;
                            //         }
                            //     });
                            //     if (isHas) {
                            //         item.warehouse.map(child => {
                            //             let datas = {
                            //                 goods_count: child.goods_count,
                            //                 fake_count: child.fake_count,
                            //                 fictitious_name: child.fictitious_name,
                            //             };
                            //             this.entity.purchaseList[index].purchase.push(Object
                            //                 .assign(datas, item));
                            //         })
                            //     }
                            // })
                            // if (!isWarehouse) {
                            //     this.entity.purchaseList = [{
                            //         bar_code: '',
                            //         purchase: [],
                            //     }];
                            //     this.$message.error("无仓库信息,请先绑定仓库信息！");
                            // }
                        } else {
                            this.$message.error("暂无数据！");
                        }
                    }
                });
        },
        //添加套餐数量
        packageChange(rows, status) {
            rows.is_original_package = false;
            rows.force_original_package = false;
            let type = rows.package_name;
            console.log(rows, 344444444);
            this.$set(rows, "is_mystery_box", 0);
            let len = 1;
            if (type == "单支") {
                len = 1;
                this.updateSalesList(1, rows);
            } else if (type == "双支装") {
                len = 2;
                this.updateSalesList(2, rows);
            } else if (type == "三支装") {
                len = 3;
                this.updateSalesList(3, rows);
            } else if (type == "四支装") {
                len = 4;
                this.updateSalesList(4, rows);
            } else if (type == "五支装") {
                len = 5;
                this.updateSalesList(5, rows);
            } else if (type == "六支装") {
                len = 6;
                this.updateSalesList(6, rows);
            } else if (type == "单支礼盒装") {
                len = 1;
                this.updateSalesList(1, rows);
            } else if (type == "六支原箱") {
                len = 6;
                this.updateSalesList(6, rows);
                rows.is_original_package = true;
                rows.force_original_package = true;
            } else if (type == "六支原箱（无冰袋）") {
                len = 6;
                this.updateSalesList(6, rows);
                rows.is_original_package = true;
                rows.force_original_package = true;
            } else if (type == "十二支原箱") {
                len = 12;
                this.updateSalesList(12, rows);
                rows.is_original_package = true;
                rows.force_original_package = true;
            } else if (type == "十二支装") {
                len = 12;
                this.updateSalesList(12, rows);
            } else if (type == "十二支原箱（无冰袋）") {
                len = 12;
                this.updateSalesList(12, rows);
                rows.is_original_package = true;
                rows.force_original_package = true;
            } else if (type == "二十四支原箱") {
                len = 24;
                this.updateSalesList(24, rows);
                rows.is_original_package = true;
                rows.force_original_package = true;
            } else if (type == "二十四支原箱（无冰袋）") {
                len = 24;
                this.updateSalesList(24, rows);
                rows.is_original_package = true;
                rows.force_original_package = true;
            } else if (type == "其他" || type == "盲盒") {
                rows.is_mystery_box = type == "盲盒" ? 1 : 0;
                rows.numList = [];
                let data = {
                    nums: 0,
                    visible: false,
                    codeVisible: false,
                    disabled: false,
                    isCheck: false,
                    productList: [],
                };
                rows.numList.push(data);
            } else if (type == "自选") {
                rows.numList = [];
                let data = {
                    nums: 0,
                    custom_product_count: 0,
                    visible: false,
                    codeVisible: false,
                    disabled: false,
                    isCheck: false,
                    productList: [],
                };
                rows.numList.push(data);
            }
            if (status == 2) {
                return len;
            }
        },
        updateSalesList(length, rows) {
            rows.numList = [];
            let data = {
                nums: 1,
                visible: false,
                codeVisible: false,
                disabled: true,
                isCheck: false,
                productList: [],
            };
            if (!rows.same) {
                rows.numList.push(data);
                rows.numList[0].nums = length;
                return;
            }
            for (let i = 0; i < length; i++) {
                let data1 = Object.assign({}, data);
                rows.numList.push(data1);
            }
        },
        //套餐内不同
        sameChange(rows) {
            this.$refs.labelAlignForm.clearValidate();
            if (!rows.same) {
                rows.numList = [
                    {
                        nums: this.packageChange(rows, 2),
                        visible: false,
                        codeVisible: false,
                        disabled: true,
                        isCheck: false,
                        productList: [],
                        sub_package_name: "",
                    },
                ];
            } else {
                this.packageChange(rows);
                rows.other = false;
            }
            this.show = true;
        },
        //采购信息确认
        confirmPur(data, rows, entity) {
            console.log(this.entity.salesList, 2323);
            if (
                entity.package_name == "盲盒" ||
                entity.package_name == "自选"
            ) {
                rows.productList = data;
            } else if (entity.package_name == "其他" || entity.same) {
                rows.productList = [data];
            } else {
                rows.productList = [data];
            }
            rows.codeVisible = !rows.codeVisible;
            rows.visible = true;
            entity.visible = true; //显示信息
            // let unlimited=this.entity.salesList[this.entity.salesList.length-2].unlimited?this.entity.salesList[this.entity.salesList.length-1].unlimited:true;
            // this.$set(entity,'unlimited',unlimited)
            let flag = true;
            this.entity.salesList.map((item) => {
                item.numList.map((child) => {
                    if (!child.visible) {
                        flag = false;
                    }
                    child.isCheck = true;
                });
            });
            // if (flag) {
            //     // this.showNext = true;
            // }
        },
        visibleChange(flag, rows, entity) {
            if (!flag) {
                console.log(rows, entity, 222);
                this.confirmPur(rows.productList, rows, entity);
            }
        },
        productChange(rows) {
            if (rows.productList && rows.productList.length == 0) {
                rows.visible = false;
            }
        },
        //采购人
        getPurchase() {
            this.$request.article
                .purchaseList({
                    type: 2,
                })
                .then((res) => {
                    this.purchaseOptions = res.data.data.list;
                });
        },
        //添加时间
        addDate(date, days) {
            if (days == undefined) {
                days = 1;
            }
            var date = new Date(date);
            date.setDate(date.getDate() + days);
            var month = date.getMonth() + 1;
            var day = date.getDate();
            return (
                date.getFullYear() +
                "-" +
                this.getFormatDate(month) +
                "-" +
                this.getFormatDate(day) +
                " " +
                this.getFormatDate(date.getHours()) +
                ":" +
                this.getFormatDate(date.getMinutes()) +
                ":" +
                this.getFormatDate(date.getSeconds())
            );
        },
        // 日期月份/天的显示，如果是1位数，则在前面加上'0'
        getFormatDate(arg) {
            if (arg == undefined || arg == "") {
                return "00";
            }
            var re = arg + "";
            if (re.length < 2) {
                re = "0" + re;
            }
            return re;
        },
        //获取地区
        getAddress() {
            this.$request.article.getAddress().then((res) => {
                this.cascaderOptions = res.data.data.list;
            });
        },
        //品名复制
        copyName(val) {
            var input = document.createElement("input"); // 直接构建input
            input.value = val; // 设置内容
            document.body.appendChild(input); // 添加临时实例
            input.select(); // 选择实例内容
            document.execCommand("Copy"); // 执行复制
            document.body.removeChild(input); // 删除临时实例
            this.$message.success("复制成功！");
        },
        addSales() {
            const data = {
                isDel: true,
                period_id: this.entity.id,
                periods_type: "",
                package_name: "",
                package_name1: "",
                price: "",
                market_price: "",
                associated_products: "",
                limit_number: "",
                inventory: "",
                is_hidden: false,
                is_original_package: 0,
                force_original_package: 0,

                coupons_id: "",
                is_cold_chain: 1,
                is_postpone: 0,
                is_cold_free_shipping: 0,
                is_leftover: 0,
                invoice_progress: 0,
                same: false,
                other: false,
                unlimited:
                    this.entity.salesList[this.entity.salesList.length - 1]
                        .unlimited,
                numList: [
                    {
                        product_id: [],
                        nums: 1,
                        custom_product_count: 0,
                        visible: false,
                        codeVisible: false,
                        disabled: false,
                        isCheck: false,
                        productList: [],
                    },
                ],
                is_deposit: 0,
                deposit_price: "",
                deposit_coupon_threshold: "",
                deposit_coupon_value: "",
                deposit_coupon_id: 0,
                package_img: (this.productImgList[0] || "").replace(
                    this.$BASE.OSSDomain,
                    ""
                ),
            };
            this.entity.salesList.push(data);
        },
        delSales(index) {
            this.entity.salesList.splice(index, 1);
        },
        // 继续添加
        adds() {
            const datas = {
                short_code: "",
                purchase: [],
                isDel: true,
            };
            this.entity.purchaseList.push(datas);
            this.nums++;
        },
        deletewithGoods(item, index) {
            this.$confirm("确定删除？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
                dangerouslyUseHTMLString: true,
            })
                .then(() => {
                    const params = {
                        period: this.entity.id,
                        periods_type: this.rows.periods_type,
                        short_code: item.short_code,
                    };
                    this.$request.caiGouPerform
                        .deletePeriodsProductInventory(params)
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("已删除");
                                this.del(index);
                            }
                        });
                })
                .catch(() => {});

            // console.log(index, item.short_code, this.rows.periods_type, this.entity.id);
        },
        // 删除
        del(index) {
            this.entity.purchaseList.splice(index, 1);
            this.nums--;
        },
        singleValidate() {
            let flag = null;
            this.$refs["labelAlignForm"].validateField("detail", (valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
        // 表单验证
        validateForm() {
            let flag = null;
            this.$refs["labelAlignForm"].validate((valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
        colorChange(val, index, type) {},
        goodTagEdit() {
            this.$nextTick(() => {
                this.$refs.goodTags.show({
                    id: this.rows.id,
                    periods_type: this.rows.periods_type,
                    label_arr: this.rows.label_arr,
                });
            });
        },
        updateTagsSuccess(tags_arr) {
            // this.row
            this.rows.label_arr = tags_arr;
        },
        onSuppliersInput(value) {
            if (this.disabledSupplierToPayee) {
                this.entity.suppliers = value;
                this.updatePurchase();
                return;
            }
            if ([1, 2].includes(this.rows.periods_type)) {
                const { label: payee_merchant_name, value: payee_merchant_id } =
                    this.payee_merchant_options[1];
                this.entity.suppliers = value;
                this.entity.payee_merchant_id = payee_merchant_id;
                this.entity.payee_merchant_name = payee_merchant_name;
                this.updatePurchase();
                return;
            }
            if (
                !(
                    value.corps &&
                    Object.keys(value.corps).some((item) =>
                        ["001", "002", "008", "032"].includes(item)
                    )
                )
            ) {
                this.supplierOpration = [];
                this.$message.error("请先维护磐石【供应商管理】");
                return;
            }
            if (Object.keys(value.corps).length > 1) {
                this.corpSelectDialogVisible = true;
                this.corpsObj = value.corps;
                const findItem = this.payee_merchant_options.find(
                    (item) => item.value === this.entity.payee_merchant_id
                );
                this.corpName = findItem ? findItem.label : "";
                this.entitySuppliers = value;
            } else {
                this.corpName = Object.values(value.corps)[0];
                const findItem = this.payee_merchant_options.find(
                    (item) => item.label === this.corpName
                );
                if (!findItem) return;
                const { label: payee_merchant_name, value: payee_merchant_id } =
                    findItem;
                this.entity.suppliers = value;
                this.entity.payee_merchant_id = payee_merchant_id;
                this.entity.payee_merchant_name = payee_merchant_name;
                this.updatePurchase();
            }
            console.log("ddddd", this.entity.payee_merchant_id);
        },
        onCorpNameConfirm() {
            if (this.isSelfImportToPayee) {
                this.disabledSupplierToPayee = true;
                const findItem = this.payee_merchant_options.find(
                    (item) => item.label === this.corpName
                );
                if (findItem) {
                    const {
                        label: payee_merchant_name,
                        value: payee_merchant_id,
                    } = findItem;
                    this.entity.import_type = this.entityImportType;
                    this.entity.payee_merchant_id = payee_merchant_id;
                    this.entity.payee_merchant_name = payee_merchant_name;
                }
                this.updatePurchase().then(() => {
                    this.corpSelectDialogVisible = false;
                });
                return;
            }
            const findItem = this.payee_merchant_options.find(
                (item) => item.label === this.corpName
            );
            if (!findItem) return;
            const { label: payee_merchant_name, value: payee_merchant_id } =
                findItem;
            this.entity.suppliers = this.entitySuppliers;
            this.entity.payee_merchant_id = payee_merchant_id;
            this.entity.payee_merchant_name = payee_merchant_name;
            this.updatePurchase().then(() => {
                this.corpSelectDialogVisible = false;
            });
        },
        onImportTypeChange(importType) {
            this.isSelfImportToPayee = false;
            this.entityImportType = "";
            this.disabledSupplierToPayee = false;
            if (importType !== 0) {
                this.entity.import_type = importType;
                this.updatePurchase();
                return;
            }
            if (![0, 3].includes(this.rows.periods_type)) {
                this.entity.import_type = importType;
                this.updatePurchase();
                return;
            }
            this.isSelfImportToPayee = true;
            this.entityImportType = importType;
            if (this.entity.product_list.some(({ corp }) => !corp)) {
                this.corpSelectDialogVisible = true;
                this.corpName = this.entity.payee_merchant_name;
                this.corpsObj = this.corpToName;
                return;
            }
            const corpList = [
                ...new Set(
                    this.entity.product_list
                        .map(({ corp }) => corp)
                        .filter(Boolean)
                        .map((corp) => corp.split(","))
                        .reduce((prev, curr) => [...prev, ...curr], [])
                ),
            ];
            if (corpList.length === 1) {
                const [corp] = corpList;
                const corpName = this.corpToName[corp];
                const findItem = this.payee_merchant_options.find(
                    (item) => item.label === corpName
                );
                if (findItem) {
                    const {
                        label: payee_merchant_name,
                        value: payee_merchant_id,
                    } = findItem;
                    this.entity.payee_merchant_id = payee_merchant_id;
                    this.entity.payee_merchant_name = payee_merchant_name;
                    this.disabledSupplierToPayee = true;
                    this.entity.import_type = importType;
                    this.updatePurchase();
                }
            } else {
                this.corpSelectDialogVisible = true;
                this.corpName = this.entity.payee_merchant_name;
                const corpsObj = {};
                Object.entries(this.corpToName).forEach(([key, value]) => {
                    if (corpList.includes(key)) corpsObj[key] = value;
                });
                this.corpsObj = corpsObj;
            }
        },
        getRegionList() {
            this.$request.article.getRegionList().then((res) => {
                if (res.data.error_code == 0) {
                    this.regionList = res.data.data.list.map(
                        ({ id, name }) => ({ id, name })
                    );
                }
            });
        },
        onIsSupplierDeliveryInput(is_supplier_delivery) {
            console.log("is_supplier_delivery", this.sendWarehouseList);
            if (is_supplier_delivery) {
                this.entitySupplierDASForm = Object.assign(
                    {},
                    this.entitySupplierDASForm,
                    {
                        isSupplierDelivery: is_supplier_delivery,
                        supplierDeliveryAddress:
                            this.entity.supplier_delivery_address || "",
                        supplier_delivery_time:
                            this.entity.supplier_delivery_time,
                        supplier_delivery_weekend:
                            this.entity.supplier_delivery_weekend,
                    }
                );
                this.$refs?.entitySupplierDASFormRef?.clearValidate();
                this.supplierDASDialogVisible = true;
                this.sendWarehouseList.forEach((item) => {
                    if (!item.fictitious_name.includes("代发")) {
                        item.disabled = true;
                    }
                });
            } else {
                this.entity.is_supplier_delivery = is_supplier_delivery;
                this.updatePurchase();
                this.sendWarehouseList.forEach((item) => {
                    item.disabled = false;
                });
            }
        },
        onSupplierDASConfirm() {
            this.$refs?.entitySupplierDASFormRef?.validate((valid) => {
                if (!valid) return;
                const {
                    isSupplierDelivery,
                    supplierDeliveryAddress,
                    supplier_delivery_time,
                    supplier_delivery_weekend,
                } = this.entitySupplierDASForm;
                this.entity.is_supplier_delivery = isSupplierDelivery;
                this.entity.supplier_delivery_address = supplierDeliveryAddress;
                this.entity.supplier_delivery_time = supplier_delivery_time;
                this.entity.supplier_delivery_weekend =
                    supplier_delivery_weekend;

                this.updatePurchase().then(() => {
                    this.supplierDASDialogVisible = false;
                });
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.el-checkbox {
    min-width: 150px;
}
.purchase_list {
    display: flex;
}

.purchase_list > label {
    width: 60px;
    text-align: right;
    margin-right: 10px;
    font-weight: bold;
}

.purchase_list > span {
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
}

.purchase {
    border: 1px solid #cccccc;
    padding: 15px 0 0px;
    margin-bottom: 15px;
    display: flex;
}

.purchaseActive {
    border: 1px solid #00ffe2;
    padding: 15px 0 0px;
    margin-bottom: 15px;
    display: flex;
}

.pur_rwap {
    display: flex;
    /* margin-bottom: 20px; */
}

.pur_inline {
    display: flex;
    margin-right: 10px;
}

.pur_inline > label {
    line-height: 39px;
    margin-right: 10px;
}

.pur_inline > span {
    line-height: 39px;
    min-width: 80px;
}

.sale_addInfo {
    border: 1px solid #cccccc;
    width: 650px;
    min-height: 98px;
    cursor: pointer;
    text-align: center;
    position: relative;
    margin-bottom: 20px;
}

.sale_addInfo > i {
    font-size: 40px;
}

.sale_visi_wrap {
    width: 650px;
    top: 100px;
}

.sale_visi_wrap::before {
    left: 319px !important;
}

.sale_addInfo_p {
    padding-top: 27px;
}

.pur_list_rwap {
    display: flex;
}

.pur_list_rwap .pur_item {
    height: 28px;
    line-height: 28px;
}

.pur_list_rwap .pur_item > label {
    margin-right: 10px;
    text-align: right;
    display: inline-block;
    min-width: 45px;
}

.sale_list_wrap {
    border: 1px solid #cccccc;
    padding: 10px;
    margin-bottom: 10px;
}

.article_product_wrap {
    margin-left: 78px;
    width: 80%;
    .article_product_item-wrap {
        & > div {
            text-overflow: ellipsis;
            word-wrap: break-word;
            word-break: break-all;
            white-space: nowrap;
            min-width: 200px;
            max-width: 1000px;
            margin-right: 10px;
            color: #333;
            display: flex;

            b {
                line-height: 2;
                opacity: 1;
                display: inline-block;
                font-weight: bold;
            }
        }
    }

    .article_product_item {
        display: flex;
        justify-content: space-between;

        & > div {
            text-overflow: ellipsis;
            word-wrap: break-word;
            word-break: break-all;
            // white-space: nowrap;
            min-width: 200px;
            max-width: 1000px;
            margin-right: 10px;
            color: #333;
            display: flex;

            b {
                line-height: 2;
                opacity: 1;
                display: inline-block;
                font-weight: bold;
            }
        }
    }
}
/deep/ .el-descriptions__body {
    color: #333 !important;
}
</style>
