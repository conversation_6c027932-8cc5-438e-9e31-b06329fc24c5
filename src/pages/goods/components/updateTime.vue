<template>
    <el-dialog
        title="基本信息"
        :visible.sync="dialogVisible"
        width="65%"
        append-to-body
        :close-on-click-modal="false"
    >
        <el-form ref="form" :model="entity" :rules="rules" label-width="120px">
            <div class="box_flex">
                <el-form-item label="上架时间" prop="onsale_time">
                    <el-date-picker
                        v-model="entity.onsale_time"
                        @change="changeTime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="datetime"
                        placeholder="选择日期"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="开售时间" prop="sell_time">
                    <el-date-picker
                        v-model="entity.sell_time"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="datetime"
                        placeholder="选择日期"
                    >
                    </el-date-picker>
                </el-form-item>
            </div>
            <div class="box_flex">
                <el-form-item label="下架时间" prop="sold_out_time">
                    <el-date-picker
                        v-model="entity.sold_out_time"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="datetime"
                        placeholder="选择日期"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="发货时间" prop="predict_shipment_time">
                    <el-date-picker
                        v-model="entity.predict_shipment_time"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="datetime"
                        placeholder="选择日期"
                    >
                    </el-date-picker>
                </el-form-item>
            </div>
            <div style="margin-top: 20px; text-align: center">
                <el-button type="primary" @click="submits">保存</el-button>
            </div>
        </el-form>
    </el-dialog>
</template>

<script>
export default {
    props: {
        parentObj: Object,
    },
    data() {
        return {
            dialogVisible: false,
            tableData: [],
            entity: {
                onsale_time: "",
                sell_time: "",
                predict_shipment_time: "",
                sold_out_time: "",
            },
            rules: {
                onsale_time: [
                    {
                        required: true,
                        message: "请选择上架时间",
                        trigger: "change",
                    },
                ],
                sold_out_time: [
                    {
                        required: true,
                        message: "请选择下架时间",
                        trigger: "change",
                    },
                ],
                sell_time: [
                    {
                        required: true,
                        message: "请选择开售时间",
                        trigger: "change",
                    },
                ],
                predict_shipment_time: [
                    {
                        required: true,
                        message: "请选择发货时间",
                        trigger: "change",
                    },
                ],
            },
            rows: {},
        };
    },
    mounted() {},
    methods: {
        //下架时间自动填充
        changeTime() {
            console.log(222);
            if (!this.entity.onsale_time) {
                this.entity.sold_out_time = "";
                return;
            }
            let day = this.rows.periods_type == 0 ? 7 : 14;
            if (this.rows.periods_type == 0 || this.rows.periods_type == 2) {
                this.entity.sold_out_time = this.addDate(
                    this.entity.onsale_time,
                    day
                );
            }
            this.entity.sell_time = this.addDate(this.entity.onsale_time, 0);
            console.log(this.entity, 333);
        },
        submits() {
            if (!this.validateForm()) {
                return;
            }
            if (
                new Date(this.entity.sell_time).getTime() <
                new Date(this.entity.onsale_time).getTime()
            ) {
                this.$message.error("开售时间必须大于上架时间！");
                return;
            }
            if (
                new Date(this.entity.sold_out_time).getTime() <=
                new Date(this.entity.sell_time).getTime()
            ) {
                this.$message.error("下架时间必须大于开售时间！");
                return;
            }
            let params = {
                ...this.entity,
                period: this.rows.id,
                periods_type: this.rows.periods_type,
            };
            this.$request.article.updateGoodsTimes(params).then((res) => {
                if (res.data.error_code == 0) {
                    this.$message.success("操作成功！");
                    this.dialogVisible = false;
                    this.parentObj.getData();
                }
            });
        },
        openForm(rows) {
            this.rows = rows;
            this.entity.onsale_time = rows.onsale_time;
            this.entity.sell_time = rows.sell_time;
            this.entity.predict_shipment_time = rows.predict_shipment_time;
            this.entity.sold_out_time = rows.sold_out_time;
            this.dialogVisible = true;
        },
        //添加时间
        addDate(date, days) {
            if (days == undefined) {
                days = 1;
            }
            var date = new Date(date);
            date.setDate(date.getDate() + days);
            var month = date.getMonth() + 1;
            var day = date.getDate();
            return (
                date.getFullYear() +
                "-" +
                this.getFormatDate(month) +
                "-" +
                this.getFormatDate(day) +
                " " +
                this.getFormatDate(date.getHours()) +
                ":" +
                this.getFormatDate(date.getMinutes()) +
                ":" +
                this.getFormatDate(date.getSeconds())
            );
        },
        // 日期月份/天的显示，如果是1位数，则在前面加上'0'
        getFormatDate(arg) {
            if (arg == undefined || arg == "") {
                return "00";
            }
            var re = arg + "";
            if (re.length < 2) {
                re = "0" + re;
            }
            return re;
        },
        validateForm() {
            let flag = null;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped></style>
