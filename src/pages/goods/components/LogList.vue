<template>
    <el-dialog
        title="日志"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
        width="65%"
        append-to-body
    >
        <el-card shadow="hover">
            <el-table
                size="mini"
                border
                stripe
                :data="DataList"
                fit
                highlight-current-row
                style="width: 100%;"
            >
                <el-table-column label="ID" prop="id" align="center" />
                <el-table-column label="状态" prop="status_name" align="center">
                    <template slot-scope="{ row }">
                        <div v-if="row.status_type == 4">
                            {{
                                row.status_name +
                                    "/" +
                                    row.describe +
                                    "/" +
                                    row.remark
                            }}
                        </div>
                        <div v-else>
                            {{ row.status_name }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作人"
                    prop="operator_name"
                    align="center"
                />
                <el-table-column
                    label="操作时间"
                    prop="created_time"
                    align="center"
                    width="160"
                />

                <!-- <el-table-column label="操作">
                    <template slot-scope="{row}">
                        <el-button type="danger" v-if="row.status==2" icon="el-icon-edit" size="mini"
                            @click="handleStop(row)">终止</el-button>
                    </template>
                </el-table-column> -->
            </el-table>
        </el-card>
        <!-- 分页 -->
        <!--        <el-pagination style="margin-top: 10px" :total="total" layout="total, sizes, prev, pager, next, jumper"
            :page-size="pageSize" :current-page="currentPage" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" /> -->
    </el-dialog>
</template>

<script>
export default {
    data() {
        return {
            dialogVisible: false,
            DataList: [],
            rows: {}
        };
    },
    mounted() {},
    methods: {
        getData() {
            this.$request.article.getLog({ period: this.rows.id }).then(res => {
                if (res.data.error_code == 0) {
                    this.DataList = res.data.data || [];
                    // this.total = res.data.data.total;
                }
            });
        },
        openForm(rows) {
            this.dialogVisible = true;
            this.rows = rows;
            this.getData();
        },
        handleClick(rows) {
            this.$nextTick(() => {
                this.$refs.VestOp.openForm(this.rows);
            });
        },
        search() {
            this.query.page = 1;
            this.getOrderList();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getOrderList();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getOrderList();
        }
    }
};
</script>
<style></style>
<style lang="scss" scoped>
.article-layout {
    .article-main {
        margin: 10px 0;

        /deep/ .el-table .warning-row {
            background: oldlace;
        }

        /deep/ .el-table .danger-row {
            background: oldlace;
        }

        /deep/ .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
</style>
