<template>
    <el-dialog
        title="变更记录"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
        width="80%"
        append-to-body
    >
        <el-form :inline="true" size="small" style="margin-bottom: 10px">
            <el-form-item label="">
                    <el-input clearable v-model="query.change_item" placeholder="请输入" />
                </el-form-item>
            <el-form-item >
                <el-button
                type="primary"
                v-if="!isAll"
                @click="search()"
                size="mini"
                >搜索</el-button
            >
            </el-form-item>
           
        </el-form>
        <el-card shadow="hover">
            <el-table
                stripe
                size="mini"
                border
                :data="DataList"
                fit
                highlight-current-row
                style="width: 100%"
            >
                
                <el-table-column label="变更项" prop="change_item" align="center" />
                <el-table-column label="变更内容" prop="change_content" align="center" />
                <el-table-column label="变更人" prop="operator_name" align="center" />
                <el-table-column label="变更时间" prop="created_time" align="center" />
            </el-table>
        </el-card>
        <div class="pagination-block">
            
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </el-dialog>
</template>

<script>

export default {
    props: {
        isAll: {
            default: 0,
            type: Number,
        },
        parentObj: Object,
    },
    
    data() {
        return {
            dialogVisible: false,
            btnVisible: false,
            DataList: [],
            query: {
                page: 1,
                limit: 10,
                period: "",
                change_item:"",
                change_content:""
            },
            total: 0,
           
        };
    },
    mounted() {},
    methods: {
        getData() {
            const data = {
                page: this.query.page,
                limit: this.query.limit,
                period: this.query.period,
                change_item:this.query.change_item,
                change_content:this.query.change_item
            };
          
            this.$request.article.getOperationRecordList(this.query).then((res) => {
                if (res.data.error_code == 0) {
                    this.DataList = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        openForm(rows) {
            this.query.page = 1;
            this.dialogVisible = true;
            this.query.period = rows;
            console.log("0099--", this.query);
            this.getData();
        },
        
        search() {
            this.query.page = 1;
            this.getData();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
       
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.article-layout {
    .article-main {
        margin: 10px 0;

        /deep/ .el-table .warning-row {
            background: oldlace;
        }

        /deep/ .el-table .danger-row {
            background: oldlace;
        }

        /deep/ .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}
</style>
