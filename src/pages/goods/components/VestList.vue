<template>
    <el-dialog
        title="马甲列表"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
        width="65%"
        append-to-body
    >
        <el-form :inline="true" size="small" style="margin-bottom: 10px">
            <!--     <el-form-item label="活动启用来源" prop="activity_object">
                    <el-select v-model="query.activity_object" clearable>
                        <el-option label="酒云" value="1" />
                        <el-option label="第三方平台" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="活动名称">
                    <el-input v-model="query.name" placeholder="请输入活动名称" />
                </el-form-item> -->
            <!--   <el-button type="success" @click="search" icon="el-icon-search" v-action="'rabbit_select'">查询
                    </el-button> -->
            <el-button
                type="primary"
                v-if="!isAll"
                @click="handleClick()"
                size="mini"
                >添加</el-button
            >
        </el-form>
        <el-card shadow="hover">
            <el-table
                stripe
                size="mini"
                border
                :data="DataList"
                fit
                highlight-current-row
                style="width: 100%"
            >
                <el-table-column
                    label="期数ID"
                    prop="period_id"
                    align="center"
                />
                <el-table-column label="马甲数量" prop="nums" align="center" />

                <el-table-column
                    label="开始时间"
                    prop="start_time"
                    align="center"
                    min-width="160"
                />
                <el-table-column
                    label="执行时长(分钟)"
                    prop="duration"
                    align="center"
                    width="120"
                />
                <el-table-column
                    label="是否重复"
                    prop="is_repeat"
                    align="center"
                    width="100"
                >
                    <template slot-scope="row">
                        {{ row.row.is_repeat == 1 ? "是" : "否" }}
                    </template>
                </el-table-column>

                <el-table-column
                    label="重复执行次数"
                    prop="repeat_nums"
                    align="center"
                    width="100"
                >
                    <template slot-scope="row">
                        {{ row.row.is_repeat == 1 ? row.row.repeat_nums : "-" }}
                    </template>
                </el-table-column>

                <el-table-column
                    label="重复间隔(天)"
                    prop="repeat_interval"
                    align="center"
                    width="100"
                >
                    <template slot-scope="row">
                        {{
                            row.row.is_repeat == 1
                                ? row.row.repeat_interval
                                : "-"
                        }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="马甲状态"
                    prop="status"
                    align="center"
                    width="120"
                >
                    <template slot-scope="row">
                        <div
                            class="status-container"
                            @click="showStatusDetail(row.row)"
                        >
                            <div class="status-row">
                                <span
                                    class="status-text"
                                    :class="getStatusTextClass(row.row.status)"
                                    >{{ row.row.status | statusFilter }}</span
                                >
                                <i
                                    class="status-icon"
                                    :class="getStatusIcon(row.row.status)"
                                ></i>
                            </div>
                            <div class="progress-row">
                                <div class="progress-bar-container">
                                    <div
                                        class="progress-bar"
                                        :style="{
                                            width:
                                                getProgressPercentage(
                                                    row.row.exec_total,
                                                    row.row.total
                                                ) + '%',
                                        }"
                                    ></div>
                                </div>
                                <span class="progress-text">
                                    {{ row.row.exec_total || 0 }} /
                                    {{ row.row.total || 0 }}
                                </span>
                            </div>
                            <el-button type="text" size="mini"
                                >查看详情</el-button
                            >
                        </div>
                    </template>
                </el-table-column>

                <el-table-column
                    label="操作人"
                    prop="operator"
                    width="100"
                    align="center"
                />
                <el-table-column
                    label="操作"
                    fixed="right"
                    align="center"
                    width="100"
                >
                    <template slot-scope="{ row }">
                        <el-button
                            type="danger"
                            v-if="row.status === 0"
                            size="mini"
                            @click="handleStop(row)"
                            >终止</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div class="pagination-block">
            <!-- 分页 -->
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <VestOp ref="VestOp" :parentObj="this"></VestOp>

        <!-- 马甲执行计划详情弹窗 -->
        <el-dialog
            title="马甲执行计划"
            :visible.sync="statusDetailVisible"
            width="60%"
            append-to-body
        >
            <div class="status-detail-container">
                <!-- 整体执行进度 -->
                <div class="overall-progress">
                    <div class="progress-header">
                        <span class="progress-title">整体执行进度</span>
                        <span class="progress-percentage"
                            >{{ getOverallProgressPercentage() }}%</span
                        >
                    </div>
                    <div class="progress-bar-large">
                        <div
                            class="progress-fill"
                            :style="{
                                width: getOverallProgressPercentage() + '%',
                            }"
                        ></div>
                    </div>
                    <div class="progress-info">
                        <span
                            >已完成 {{ vestProgressData.exec_total }} /
                            {{ vestProgressData.total }} 个执行时间点</span
                        >
                        <span class="remaining-count"
                            >剩余
                            {{
                                vestProgressData.total -
                                vestProgressData.exec_total
                            }}
                            个</span
                        >
                    </div>
                </div>

                <!-- 执行计划列表 -->
                <div class="execution-list">
                    <div class="list-header">
                        <span>执行时间</span>
                        <span>状态</span>
                        <span>马甲数量</span>
                    </div>
                    <div class="list-body">
                        <div
                            v-for="(item, index) in vestProgressData.progress"
                            :key="index"
                            class="execution-item"
                        >
                            <div class="execution-time">
                                <div class="date">
                                    {{ formatDateTime(item.trigger_time).date }}

                                    {{ formatDateTime(item.trigger_time).time }}
                                </div>
                            </div>
                            <div class="execution-status">
                                <span
                                    class="status-badge"
                                    :class="getStatusClass(item.status)"
                                >
                                    <i :class="getStatusIcon(item.status)"></i>
                                    {{ item.status }}
                                </span>
                            </div>
                            <div class="vest-count">
                                <i class="el-icon-user"></i>
                                {{ item.add }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>
    </el-dialog>
</template>

<script>
import VestOp from "./VestOp.vue";
export default {
    props: {
        isAll: {
            default: 0,
            type: Number,
        },
    },
    filters: {
        // 马甲状态：0-执行中 1-已结束 2-已终止
        statusFilter(val) {
            switch (val) {
                case 0:
                    return "执行中";
                case 1:
                    return "已结束";
                case 2:
                    return "已终止";
                default:
                    return "未知";
            }
        },
    },
    components: {
        VestOp,
    },
    data() {
        return {
            dialogVisible: false,
            btnVisible: false,
            DataList: [],
            query: {
                page: 1,
                limit: 10,
                buyer_review_status: "",
                periods_type: "",
                periods: "",
                title: "",
            },
            total: 0,
            rows: {},
            // 状态详情弹窗
            statusDetailVisible: false,
            currentStatusData: null,
            // 马甲进度详情数据
            vestProgressData: {
                total: 0,
                exec_total: 0,
                progress: [],
            },
        };
    },
    mounted() {},
    methods: {
        // 获取状态图标
        getStatusIcon(status) {
            // 如果是数字状态（马甲列表）
            if (typeof status === "number") {
                switch (status) {
                    case 0:
                        return "el-icon-loading"; // 执行中
                    case 1:
                        return "el-icon-circle-check"; // 已结束
                    case 2:
                        return "el-icon-circle-close"; // 已终止
                    default:
                        return "el-icon-question";
                }
            }
            // 如果是字符串状态（进度详情）
            switch (status) {
                case "已执行":
                    return "el-icon-check";
                case "待执行":
                    return "el-icon-time";
                case "已终止":
                    return "el-icon-close";
                default:
                    return "el-icon-question";
            }
        },
        // 获取状态样式类
        getStatusClass(status) {
            switch (status) {
                case "已执行":
                    return "completed";
                case "待执行":
                    return "pending";
                case "已终止":
                    return "terminated";
                default:
                    return "unknown";
            }
        },
        // 获取状态文字颜色类
        getStatusTextClass(status) {
            switch (status) {
                case 0:
                    return "status-executing"; // 执行中 - 蓝色
                case 1:
                    return "status-completed"; // 已结束 - 绿色
                case 2:
                    return "status-terminated"; // 已终止 - 红色
                default:
                    return "status-unknown";
            }
        },
        // 计算进度百分比
        getProgressPercentage(execTotal, total) {
            if (!total || total === 0) return 0;
            const percentage = Math.round((execTotal / total) * 100);
            return Math.min(percentage, 100); // 确保不超过100%
        },
        // 显示状态详情
        showStatusDetail(rowData) {
            this.currentStatusData = rowData;
            this.statusDetailVisible = true;
            // 调用API获取马甲进度详情
            this.getVestProgressDetail(rowData.id);
        },
        // 获取马甲进度详情
        getVestProgressDetail(vestId) {
            const data = { id: vestId };
            this.$request.article
                .getVestProgress(data)
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.vestProgressData = res.data.data;
                    }
                })
                .catch((error) => {
                    console.error("获取马甲进度详情失败:", error);
                    this.$message.error("获取马甲进度详情失败");
                });
        },
        // 获取整体进度百分比
        getOverallProgressPercentage() {
            if (this.vestProgressData.total === 0) return 0;
            return Math.round(
                (this.vestProgressData.exec_total /
                    this.vestProgressData.total) *
                    100
            );
        },
        // 获取已完成数量
        getCompletedCount() {
            return this.vestProgressData.progress.filter(
                (item) => item.status === "已执行"
            ).length;
        },
        // 获取总数量
        getTotalCount() {
            return this.vestProgressData.progress.length;
        },
        // 获取剩余数量
        getRemainingCount() {
            return this.getTotalCount() - this.getCompletedCount();
        },
        // 格式化时间显示
        formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return { date: "", time: "" };

            const targetDate = new Date(dateTimeStr);
            const today = new Date();

            // 重置时间为当天0点，用于日期比较
            const todayStart = new Date(
                today.getFullYear(),
                today.getMonth(),
                today.getDate()
            );
            const targetStart = new Date(
                targetDate.getFullYear(),
                targetDate.getMonth(),
                targetDate.getDate()
            );

            // 计算天数差
            const diffTime = targetStart.getTime() - todayStart.getTime();
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            // 格式化时间部分
            const hours = targetDate.getHours().toString().padStart(2, "0");
            const minutes = targetDate.getMinutes().toString().padStart(2, "0");
            const seconds = targetDate.getSeconds().toString().padStart(2, "0");
            const timeStr = `${hours}:${minutes}:${seconds}`;

            let dateStr = "";
            if (diffDays === 0) {
                dateStr = "今日";
            } else if (diffDays === 1) {
                dateStr = "明日";
            } else if (diffDays > 1) {
                dateStr = `${diffDays}天后`;
            } else {
                // 过去的日期，显示具体日期
                dateStr = targetDate.toLocaleDateString("zh-CN", {
                    month: "numeric",
                    day: "numeric",
                });
            }

            return {
                date: dateStr,
                time: timeStr,
            };
        },
        getData() {
            console.log(this.rows);
            const data = {
                period_id: this.rows.id,
                limit: this.query.limit,
                page: this.query.page,
            };
            this.$request.article.getVestLists(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.DataList = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        openForm(rows) {
            this.dialogVisible = true;
            this.rows = rows;
            this.getData();
        },
        handleClick(rows) {
            this.$nextTick(() => {
                this.$refs.VestOp.openForm(this.rows);
            });
        },
        handleStop(rows) {
            this.$confirm("你确定终止吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.$request.article
                        .stopVestOp({
                            vest_id: rows.id,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("终止成功");
                                this.getData();
                            }
                        });
                })
                .catch(() => {
                    //几点取消的提示
                });
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.article-layout {
    .article-main {
        margin: 10px 0;

        /deep/ .el-table .warning-row {
            background: oldlace;
        }

        /deep/ .el-table .danger-row {
            background: oldlace;
        }

        /deep/ .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}

// 马甲状态样式
.status-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
        background-color: #f5f7fa;
    }
}

.status-row {
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-text {
    font-size: 12px;
    font-weight: 500;

    &.status-executing {
        color: #409eff; // 执行中 - 蓝色
    }

    &.status-completed {
        color: #67c23a; // 已结束 - 绿色
    }

    &.status-terminated {
        color: #f56c6c; // 已终止 - 红色
    }

    &.status-unknown {
        color: #909399; // 未知 - 灰色
    }
}

.status-icon {
    font-size: 14px;

    &.el-icon-loading {
        color: #409eff;
        animation: rotating 2s linear infinite;
    }

    &.el-icon-circle-check {
        color: #67c23a;
    }

    &.el-icon-circle-close {
        color: #f56c6c;
    }

    &.el-icon-question {
        color: #909399;
    }
}

.progress-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 6px;
}

.progress-bar-container {
    width: 40px;
    height: 4px;
    background-color: #e4e7ed;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: #409eff;
    border-radius: 2px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 10px;
    color: #909399;
    background: #f5f7fa;
    padding: 1px 4px;
    border-radius: 8px;
    min-width: 30px;
    text-align: center;
    white-space: nowrap;
}

@keyframes rotating {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

// 状态详情弹窗样式
.status-detail-container {
    padding: 20px 0;
}

.overall-progress {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.progress-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
}

.progress-percentage {
    font-size: 24px;
    font-weight: bold;
    color: #409eff;
}

.progress-bar-large {
    width: 100%;
    height: 8px;
    background-color: #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
    border-radius: 4px;
    transition: width 0.6s ease;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #606266;
}

.remaining-count {
    color: #909399;
}

.execution-list {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #ebeef5;
}

.list-header {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    padding: 15px 20px;
    background: #f5f7fa;
    font-weight: 600;
    color: #303133;
    border-bottom: 1px solid #ebeef5;
}

.list-body {
    max-height: 400px;
    overflow-y: auto;
}

.execution-item {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f2f5;
    transition: background-color 0.2s ease;

    &:hover {
        background-color: #fafafa;
    }

    &:last-child {
        border-bottom: none;
    }
}

.execution-time {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.date {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
}

.time {
    font-size: 12px;
    color: #909399;
}

.execution-status {
    display: flex;
    align-items: center;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;

    &.completed {
        background: #f0f9eb;
        color: #67c23a;

        i {
            color: #67c23a;
        }
    }

    &.pending {
        background: #fdf6ec;
        color: #e6a23c;

        i {
            color: #e6a23c;
        }
    }

    &.terminated {
        background: #fef0f0;
        color: #f56c6c;

        i {
            color: #f56c6c;
        }
    }
}

.vest-count {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: #606266;

    i {
        color: #909399;
    }
}
</style>
