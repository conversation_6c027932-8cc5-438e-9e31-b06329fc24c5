<template>
    <el-dialog
        title="已购信息"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
        width="65%"
        append-to-body
    >
     <div><el-link   :underline="false" @click="showUserDetail(-1)">总人数：{{statistics.total_users}}，</el-link>
        <el-link  :underline="false" @click="showUserDetail(5)">沉默用户：{{statistics.silent_users}}，</el-link>
        <el-link  :underline="false" @click="showUserDetail(2)">新用户：{{statistics.new_users}}，</el-link>
        <el-link  :underline="false" @click="showUserDetail(3)">无效用户：{{statistics.invalid_users}}，</el-link>
        <el-link  :underline="false" @click="showUserDetail(1)"> 普通用户：{{statistics.default_users}}，</el-link>
        <el-link  :underline="false" @click="showUserDetail(4)">活跃用户：{{statistics.active_users}}</el-link>
        <el-popover
            placement="top-start"
            width="500"
            trigger="click"
          style="margin-left: 15px;"
        >
        <div>沉默等级：一级沉默用(3个月无有效下单)、二级沉默用(6个月无有效下单)、三级沉默用(一年以上无有效下单)(无触发节点)</div>
        <div>新用户：注册时间小于等于1个月(30天)，且无【有效订单】的用户；</div>
        <div>无效用户：注册时间大于1个月（30天），且无【有效订单】的用户(无触发节点)；</div>
        <div>活跃用户：本月内（30天）有效子订单大于5个的用户；</div>
         <div> 普通用户：（非以上用户）默认</div>
    <br>
       <div>有效订单：已发货订单；</div>
       <div>订单生成时，记录本单产生时用户属性；
        </div>
       <div>订单发货后更新用户属性；</div>
            <i
                class="el-icon-question"
                slot="reference"
            ></i>
        </el-popover>
    </div>
        <div style="margin-bottom: 10px">新购用户数：{{ new_user_nums }}</div>
        <el-card shadow="hover">
            <el-table
                stripe
                :data="DataList"
                border
                size="mini"
                fit
                highlight-current-row
                style="width: 100%"
            >
                <el-table-column
                    label="用户ID"
                    prop="uid"
                    align="center"
                    width="80px"
                />
                 <el-table-column
                    label="用户昵称"
                    prop="nickname"
                    align="center"
                    width="120"
                >
                <!-- <template slot-scope="scope">
                        {{ scope.row.nickname }}
                    </template> -->
            </el-table-column>
            <el-table-column
                    label="用户属性"
                    prop="user_attribute_name"
                    align="center"
                    width="100"
                >
                <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.user_attribute_name" :key="index">{{ item }}</div>
                   
                    </template>
            </el-table-column>
            <el-table-column
                    label="等级"
                    prop="user_level"
                    align="center"
                    width="120"
                >
                <template slot-scope="scope">
                       LV{{ scope.row.user_level }}
                    </template>
            </el-table-column>
                <el-table-column
                    label="购买份数"
                    prop="order_qty"
                    align="center"
                    width="80px"
                />
                <el-table-column
                    label="支付总金额"
                    prop="payment_amount"
                    align="center"
                    width="110px"
                />
                <el-table-column
                    label="收货城市"
                    prop="start_time"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        <div v-for="(v, i) in row.address_info" :key="i">
                            {{
                                v.province_name +
                                v.city_name +
                                v.district_name
                            }}
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div class="flex-layout">
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <DetailsOfSoldUsers
            ref="DetailsOfSoldUsersref"
            :parentObj="this"
            v-if="DetailsOfSoldUsersVisible"
        ></DetailsOfSoldUsers>
    </el-dialog>
</template>

<script>
import DetailsOfSoldUsers from "./DetailsOfSoldUsers.vue";
export default {
    components: {
        DetailsOfSoldUsers,
    },
    data() {
        return {
            dialogVisible: false,
            btnVisible: false,
            DataList: [],
            userText: {
                0: "普通用户",
                1: "新用户",
                2: "无效用户",
                3: "活跃用户",
                4: "一级沉默用户",
                5: "二级沉默用户",
                6: "三级沉默用户",
            },
            query: {
                page: 1,
                limit: 10,
                buyer_review_status: "",
                periods_type: "",
                periods: "",
                title: "",
            },
            statistics:{},
            total: 0,
            new_user_nums: 0,
            rows: {},
            DetailsOfSoldUsersVisible:false,
        };
    },
    mounted() {},
    methods: {
        getData() {
            let params = {
                type: 2,
                period: this.rows.id,
                periods_type: this.rows.periods_type,
                page: this.query.page,
                limit: this.query.limit,
            };
            this.$request.article
                .getSoldPurchasedOrderList(params)
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.DataList = res.data.data.list || [];
                        this.total = res.data.data.total;
                        this.new_user_nums = res.data.data.new_user_nums;
                        this.statistics = res.data.data.statistics;
                    }
                });
        },
        openForm(rows) {
            this.dialogVisible = true;
            this.rows = rows;
            this.query.page = 1;
            this.query.limit = 10;
            this.getData();
        },
        handleClick(rows) {
            this.$nextTick(() => {
                this.$refs.VestOp.openForm(this.rows);
            });
        },
        handleDel(row) {
            this.$confirm("你确定删除吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.$request.article
                        .articalDelete({
                            period: row.id,
                            periods_type: row.periods_type,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.page = 1;
                                this.getData();
                            }
                        });
                })
                .catch(() => {
                    //几点取消的提示
                });
        },
        showUserDetail(type){
            this.DetailsOfSoldUsersVisible = true;
                this.$nextTick(() => {
                    this.$refs.DetailsOfSoldUsersref.openForm(this.rows, type);
                });
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.flex-layout {
    text-align: center;
}
.article-layout {
    .article-main {
        margin: 10px 0;

        /deep/ .el-table .warning-row {
            background: oldlace;
        }

        /deep/ .el-table .danger-row {
            background: oldlace;
        }

        /deep/ .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
</style>
