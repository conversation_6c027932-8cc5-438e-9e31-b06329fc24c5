<template>
    <el-dialog
        title="库存信息"
        :visible.sync="dialogVisible"
        width="65%"
        :close-on-click-modal="false"
    >
        <div class="article-layout">
            <div class="article-main">
                <el-card>
                    <el-table
                        border
                        size="mini"
                        :data="tableData"
                        style="width: 100%"
                    >
                        <el-table-column
                            prop="id"
                            label="ID"
                            min-width="60"
                            show-overflow-tooltip
                            align="center"
                        />
                        <el-table-column
                            prop="product_name"
                            label="产品名称"
                            show-overflow-tooltip
                            min-width="220"
                            align="center"
                        />
                        <el-table-column
                            prop="short_code"
                            label="简码"
                            show-overflow-tooltip
                            min-width="120"
                            align="center"
                        />
                        <el-table-column
                            prop="bar_code"
                            label="条码"
                            show-overflow-tooltip
                            min-width="120"
                            align="center"
                        />
                        <el-table-column
                            prop="inventory"
                            label="库存"
                            min-width="80"
                            show-overflow-tooltip
                            align="center"
                        >
                            <template slot-scope="{ row }">
                                <el-popover
                                    placement="top-start"
                                    title="已售库存/已设置库存/目前剩余库存"
                                    width="200"
                                    trigger="hover"
                                >
                                    <div slot="reference">
                                        {{
                                            row.inventory_accum -
                                            row.inventory +
                                            "/" +
                                            row.inventory_accum +
                                            "/" +
                                            row.inventory
                                        }}
                                    </div>
                                </el-popover>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="inventory"
                            v-if="!isAll"
                            label="增减库存"
                            min-width="120"
                            align="center"
                        >
                            <template slot-scope="{ row }">
                                <div class="stoct_wrap">
                                    <div class="stock_btn">
                                        <span
                                            :class="
                                                row.btntype == 1
                                                    ? 'action_btn'
                                                    : ''
                                            "
                                            @click="changeType(1, row)"
                                            >增</span
                                        >
                                        <span
                                            :class="
                                                row.btntype == 2
                                                    ? 'action_btn'
                                                    : ''
                                            "
                                            @click="changeType(2, row)"
                                            >减</span
                                        >
                                    </div>
                                    <el-input
                                        v-model="row.nums"
                                        oninput="value=value.replace(/[^\d]/g,'')"
                                        @input="getStock(row)"
                                    />
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="inventory"
                            v-if="!isAll"
                            label="修改后剩余库存"
                            min-width="80"
                            align="center"
                        >
                            <template slot-scope="{ row }">
                                <el-input
                                    v-model="row.totleInventory"
                                    oninput="value=value.replace(/[^\d]/g,'')"
                                    @input="getStockByTotal(row)"
                                />
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="warehouse"
                            label="发货仓库"
                            min-width="120"
                            show-overflow-tooltip
                            align="center"
                        />
                        <el-table-column
                            label="操作"
                            fixed="right"
                            v-if="!isAll"
                            width="100"
                            align="center"
                        >
                            <template slot-scope="{ row }">
                                <el-button
                                    type="primary"
                                    size="mini"
                                    @click="handleEdit(row, 1)"
                                    >确定
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
            </div>
            <div
                class="article-main"
                v-if="tableData1.length > 0 && tableData1[0].unlimited === 0"
            >
                <el-card>
                    <el-table
                        border
                        size="mini"
                        :data="tableData1"
                        style="width: 100%"
                    >
                        <el-table-column
                            prop="id"
                            label="ID"
                            min-width="40"
                            align="center"
                        />
                        <el-table-column
                            prop="package_name"
                            label="套餐名称"
                            min-width="220"
                            align="center"
                        />
                        <el-table-column
                            prop="inventory"
                            label="库存"
                            min-width="80"
                            align="center"
                        >
                            <template slot-scope="{ row }">
                                <div v-if="row.inventory == -1">不限量</div>
                                <div v-else>
                                    {{
                                        row.inventory_accum -
                                        row.inventory +
                                        "/" +
                                        row.inventory_accum +
                                        "/" +
                                        row.inventory
                                    }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="inventory"
                            v-if="!isAll"
                            label="增减库存"
                            min-width="120"
                            align="center"
                        >
                            <template slot-scope="{ row }">
                                <div
                                    class="stoct_wrap"
                                    v-if="row.inventory != -1"
                                >
                                    <div class="stock_btn">
                                        <span
                                            :class="
                                                row.btntype == 1
                                                    ? 'action_btn'
                                                    : ''
                                            "
                                            @click="changeType(1, row)"
                                            >增</span
                                        >
                                        <span
                                            :class="
                                                row.btntype == 2
                                                    ? 'action_btn'
                                                    : ''
                                            "
                                            @click="changeType(2, row)"
                                            >减</span
                                        >
                                    </div>
                                    <el-input
                                        v-model="row.nums"
                                        oninput="value=value.replace(/[^\d]/g,'')"
                                        @input="getStock(row)"
                                    />
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="inventory"
                            v-if="!isAll"
                            label="修改后剩余库存"
                            min-width="80"
                            align="center"
                        >
                            <template
                                slot-scope="{ row }"
                                v-if="row.inventory != -1"
                            >
                                <el-input
                                    v-model="row.totleInventory"
                                    oninput="value=value.replace(/[^\d]/g,'')"
                                    @input="getStockByTotal(row)"
                                />
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="操作"
                            fixed="right"
                            v-if="!isAll"
                            width="100"
                            align="center"
                        >
                            <template slot-scope="{ row }">
                                <el-button
                                    type="primary"
                                    size="mini"
                                    @click="handleEdit(row, 2)"
                                    >确定
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
            </div>
            <!--  <div class="pagination-block">
            <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="query.page" :page-size="query.limit" :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper" :total="total">
            </el-pagination>
        </div> -->
        </div>
    </el-dialog>
</template>

<script>
function debounce(func, wait = 400) {
    let timeout;
    return function (event) {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            func.call(this, event);
        }, wait);
    };
}
export default {
    props: {
        parentObj: Object,
        isAll: {
            default: 0,
            type: Number,
        },
    },
    data() {
        return {
            dialogVisible: false,
            btnVisible: false,
            tableData: [],
            tableData1: [],
            query: {
                page: 1,
                limit: 10,
                buyer_review_status: "",
                periods_type: "",
                periods: "",
                title: "",
            },
            total: 0,
            rows: {},
            btntype: 1,
            btntype1: 1,
        };
    },
    computed: {},
    mounted() {},
    methods: {
        getStock: debounce(function (rows) {
            return (rows.totleInventory =
                rows.btntype == 1
                    ? Number(rows.nums) + rows.inventory
                    : rows.inventory - Number(rows.nums));
        }),
        getStockByTotal: debounce(function (rows) {
            console.log(rows, 333);
            if (rows.totleInventory > rows.inventory) {
                rows.nums = rows.totleInventory - rows.inventory;
                rows.btntype = 1;
            } else {
                rows.nums = rows.inventory - rows.totleInventory;
                rows.btntype = 2;
            }
        }),
        openForm(rows) {
            this.rows = rows;
            this.dialogVisible = true;
            this.getData();
            this.getPackData();
        },
        getData() {
            let params = {
                period: this.rows.id,
            };
            this.$request.article.inventoryList(params).then((res) => {
                if (res.data.error_code == 0) {
                    res.data.data.map((item) => {
                        item.flag = false;
                        item.nums = "";
                        item.totleInventory = "";
                        item.btntype = 1;
                    });
                    this.tableData = res.data.data || [];
                    // this.total = res.data.data.total;
                }
            });
        },
        getPackData() {
            let params = {
                period: this.rows.id,
                periods_type: this.rows.periods_type,
            };
            this.$request.article.packList(params).then((res) => {
                if (res.data.error_code == 0) {
                    res.data.data.map((item) => {
                        item.flag = false;
                        item.nums = "";
                        item.totleInventory = "";
                        item.btntype = 1;
                    });
                    this.tableData1 = res.data.data || [];
                    // this.total = res.data.data.total;
                }
            });
        },
        changeType(btntype, row) {
            row.btntype = btntype;
            if (row.nums) {
                this.getStock(row);
            }
        },
        getchannel(type) {
            console.log(type, 12323);
            let channel = "flash";
            switch (type) {
                case 0:
                    channel = "flash";
                    break;
                case 1:
                    channel = "second";
                    break;
                case 2:
                    channel = "cross";
                    break;
                case 3:
                    channel = "leftover";
                    break;
            }
            return channel;
        },

        async handleEdit(row, type) {
            if (
                row.periods_type == 2 &&
                !this.$route.meta.operations.includes("CROSS_INVENTORY_UPDATE")
            ) {
                this.$message.error("你没有操作权限");
                return;
            }
            if (row.periods_type == 2 && row.btntype == 1) {
                //验证跨境库存信息
                let data = {
                    goods_barcode: row.short_code,
                    warehouse_code: row.erp_id,
                    inventory_nums: row.totleInventory,
                };
                let res = await this.$request.article.verifyCrossStock(data);
                if (res.data.error_code != 0) {
                    // this.$message.error(res.data.error_msg);
                    return;
                }
            }
            console.log("----");
            if (!row.nums) {
                this.$message.error("请输入要修改的库存或者总库存！");
                return;
            }
            const period_id = row.period ? row.period : row.period_id;
            this.$request.article
                .queryPendingPayment({
                    period_id: period_id,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        var tipStr;
                        if (res.data.data.unpay_order) {
                            tipStr = "该期数存在待支付的订单，请注意不要超卖！";
                        } else {
                            tipStr = "你确定要修改库存吗?";
                        }
                        this.$confirm(tipStr, "提示", {
                            confirmButtonText: "确定",
                            cancelButtonText: "取消",
                            type: "warning",
                        })
                            .then(() => {
                                let params = {
                                    period_id: period_id,
                                    product_id: row.product_id,
                                    nums: Number(row.nums),
                                    action: row.btntype == 1 ? "inc" : "dec",
                                };
                                if (type == 2) {
                                    params = {
                                        set_id: row.id,
                                        channel: this.getchannel(
                                            this.rows.periods_type
                                        ),
                                        nums: Number(row.nums),
                                        action:
                                            row.btntype == 1 ? "inc" : "dec",
                                    };
                                    this.$request.article
                                        .updateInventory(params)
                                        .then((res) => {
                                            if (res.data.error_code == 0) {
                                                this.$message.success(
                                                    "操作成功！"
                                                );
                                                if (type == 1) {
                                                    this.getData();
                                                } else {
                                                    this.getPackData();
                                                }
                                            }
                                        });
                                } else {
                                    this.$request.article
                                        .updateInventory1(params)
                                        .then((res) => {
                                            if (res.data.error_code == 0) {
                                                this.$message.success(
                                                    "操作成功！"
                                                );
                                                if (type == 1) {
                                                    this.getData();
                                                } else {
                                                    this.getPackData();
                                                }
                                            }
                                        });
                                }
                            })
                            .catch(() => {
                                //几点取消的提示
                            });
                    }
                });
        },
        search() {
            this.query.page = 1;
            this.getOrderList();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getOrderList();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getOrderList();
        },
    },
};
</script>
<style>
.stoct_wrap .el-input__inner {
    padding-left: 55px !important;
}
</style>
<style lang="scss" scoped>
.stoct_wrap {
    .el-input {
    }

    .stock_btn {
        position: absolute;
        top: 17px;
        z-index: 1;

        .action_btn {
            color: #66b1ff;
        }

        > span {
            color: #cccccc;
            padding: 10px 5px;
            font-size: 14px;
            border-right: 1px solid #ddd;
            cursor: pointer;
        }
    }
}

.article-layout {
    .article-main {
        .stock_title {
            margin: 10px;
        }

        margin: 10px 0;

        /deep/ .el-table .warning-row {
            background: oldlace;
        }

        /deep/ .el-table .danger-row {
            background: oldlace;
        }

        /deep/ .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
</style>
