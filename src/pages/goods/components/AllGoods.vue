<template>
    <!-- 搜索区 -->
    <div class="order-layout">
        <!-- 全部商品列表（兔头除外） -->
        <div class="order-form fixed-search" v-show="showSearchArea">
            <el-card>
                <div class="search-header">
                    <div class="close-button" @click="toggleSearchArea">
                        <i class="el-icon-close"></i>
                    </div>
                </div>
                <div class="box_flex box_wrap">
                    <div>
                        <el-input
                            v-model="query.periods"
                            class="w-mini m-r-10"
                            placeholder="期数"
                            size="mini"
                            @keyup.enter.native="search"
                            clearable
                        >
                        </el-input>
                    </div>
                    <div>
                        <el-input
                            @keyup.enter.native="search"
                            v-model="query.title"
                            class="w-normal m-r-10"
                            placeholder="标题"
                            clearable
                            size="mini"
                        >
                        </el-input>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.product_category"
                            filterable
                            size="mini"
                            placeholder="类别"
                            clearable
                        >
                            <el-option
                                v-for="item in typeOptions"
                                :key="item.label"
                                :label="item.label"
                                :value="item.label"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-input
                            size="mini"
                            class="w-mini m-r-10"
                            v-model="query.short_code"
                            placeholder="简码"
                            @keyup.enter.native="search"
                            clearable
                        >
                        </el-input>
                    </div>

                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.product_type"
                            filterable
                            remote
                            clearable
                            :loading="loadings"
                            reserve-keyword
                            placeholder="产品类型"
                            size="mini"
                            :remote-method="remoteMethod"
                        >
                            <el-option
                                v-for="(item, index) in product_typeOptions"
                                :key="index"
                                :label="item.name"
                                :value="item.name"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.is_channel"
                            size="mini"
                            placeholder="是否渠道"
                            clearable
                        >
                            <el-option
                                v-for="item in is_channelOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.creator_id"
                            filterable
                            size="mini"
                            placeholder="文案制作人"
                            clearable
                        >
                            <el-option
                                v-for="item in options"
                                :key="item.id"
                                :label="item.realname"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.supplier_id"
                            filterable
                            clearable
                            placeholder="商家名称"
                            size="mini"
                        >
                            <el-option
                                v-for="(item, index) in merchantList"
                                :key="index"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-large m-r-10"
                            v-model="query.periods_type_arr"
                            filterable
                            multiple
                            clearable
                            placeholder="商品频道（可多选）"
                            size="mini"
                        >
                            <el-option
                                v-for="(item, index) in periodsTypeList"
                                :key="index"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.product_channel"
                            filterable
                            clearable
                            placeholder="商家产品渠道"
                            size="mini"
                        >
                            <el-option
                                v-for="(item, index) in productChannelOptions"
                                :key="index"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>

                    <!-- <div>
                        <el-select class="w-mini m-r-10" v-model="query.product_category" filterable size="mini"
                            placeholder="排序" clearable>
                            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div> -->
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.import_type"
                            filterable
                            size="mini"
                            placeholder="进口类型"
                            clearable
                        >
                            <el-option
                                v-for="item in import_typeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.onsale_review_status"
                            filterable
                            size="mini"
                            placeholder="状态"
                            clearable
                        >
                            <el-option
                                v-for="item in statusOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>

                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.buyer_id"
                            filterable
                            size="mini"
                            placeholder="采购人"
                            clearable
                        >
                            <el-option
                                v-for="item in buyerOptions"
                                :key="item.id"
                                :label="item.realname"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-input
                            size="mini"
                            class="w-mini m-r-10"
                            v-model="query.country"
                            placeholder="国家"
                            @keyup.enter.native="search"
                            clearable
                        >
                        </el-input>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.label_id"
                            filterable
                            multiple
                            collapse-tags
                            size="mini"
                            placeholder="商品标签"
                            clearable
                        >
                            <el-option
                                v-for="item in goodTagOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <el-date-picker
                        v-model="query.time"
                        size="mini"
                        type="datetimerange"
                        range-separator="-"
                        class="m-r-10"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="上架时间-开始日期"
                        end-placeholder="上架时间-结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                    <el-date-picker
                        v-model="query.time1"
                        size="mini"
                        type="datetimerange"
                        range-separator="-"
                        class="m-r-10"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="下架时间-开始日期"
                        end-placeholder="下架时间-结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                    <el-date-picker
                        v-model="query.time2"
                        size="mini"
                        type="datetimerange"
                        range-separator="-"
                        class="m-r-10"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="发货时间-开始日期"
                        end-placeholder="发货时间-结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                    <el-select
                        class="w-mini m-r-10"
                        v-model="query.sales_model"
                        filterable
                        size="mini"
                        placeholder="售卖模式"
                        clearable
                    >
                        <el-option
                            v-for="item in [
                                { value: 1, text: '代发' },
                                { value: 2, text: '订金' },
                                { value: 3, text: '预售' },
                            ]"
                            :key="item.value"
                            :label="item.text"
                            :value="item.value"
                        />
                    </el-select>
                    <el-select
                        v-model="query.seller_id"
                        placeholder="供应商"
                        size="mini"
                        clearable
                        filterable
                        remote
                        :remote-method="supplierRemoteMethod"
                        :loading="supplierRemoteMethodLoading"
                        class="m-r-10"
                    >
                        <el-option
                            v-for="item in supplierOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                    <el-checkbox v-model="isOnsale" class="m-r-10"
                        >是否在售</el-checkbox
                    >
                    <!-- <el-button @click="reset" size="mini">重置</el-button> -->
                    <div class="action-btn">
                        <el-button type="warning" size="mini" @click="search"
                            >查询</el-button
                        >
                        <!-- 批量操作 -->
                        <el-button
                            type="primary"
                            size="mini"
                            @click="handleBatchOperation"
                            >批量操作</el-button
                        >
                        <!-- <el-button type="success" size="mini">导出</el-button> -->
                    </div>
                </div>
            </el-card>
        </div>
        <!-- 批量操作按钮 -->
        <div v-if="isBatchSelecting" class="batch-operation-bar">
            <div class="batch-info">
                已选择
                <span class="selected-count">{{ selectedItems.length }}</span>
                个商品
            </div>
            <div class="batch-actions">
                <el-button type="primary" size="small" @click="selectAllItems"
                    >全选</el-button
                >
                <el-button type="warning" size="small" @click="deselectAllItems"
                    >取消全选</el-button
                >
                <el-button type="primary" size="small" @click="batchEditTags"
                    >批量编辑标签</el-button
                >
                <el-button
                    type="info"
                    size="small"
                    @click="cancelBatchSelection"
                    >取消</el-button
                >
            </div>
        </div>

        <!-- <el-empty></el-empty> -->
        <el-card
            v-for="(v, k) in tableData"
            :key="k"
            shadow="hover"
            :class="{
                'selectable-card': isBatchSelecting,
                'selected-card': v.isSelected,
            }"
        >
            <div class="good-card">
                <div class="checkbox-container" v-if="isBatchSelecting">
                    <el-checkbox
                        v-model="v.isSelected"
                        @change="(val) => handleCheckboxChange(v, val)"
                    ></el-checkbox>
                </div>
                <div class="good-cardinfo">
                    <div class="good-head">
                        <div class="good-tag">
                            <el-tag
                                v-if="v.is_deposit_period"
                                type="danger"
                                size="mini"
                                effect="dark"
                                >订金</el-tag
                            >
                            <el-tag type="primary" size="mini">{{
                                periods_typeTxt[v.periods_type]
                            }}</el-tag>
                            <el-tag type="primary" size="mini">
                                <span v-if="v.periods_type !== 9">{{
                                    v.import_type | import_typeFormat
                                }}</span>
                                <span v-if="v.periods_type === 9">{{
                                    v.product_channel | vmall_import_typeFormat
                                }}</span>
                            </el-tag>
                            <el-tag
                                type="primary"
                                size="mini"
                                v-if="v.is_presell"
                                >{{ v.is_presell ? "预售" : "" }}</el-tag
                            >
                            <el-tag
                                type="warning"
                                size="mini"
                                v-if="v.is_supplier_delivery"
                            >
                                {{ v.is_supplier_delivery ? "代发" : "" }}
                            </el-tag>
                            <el-tag
                                type="success"
                                size="mini"
                                v-if="v.is_channel"
                            >
                                {{ v.is_channel == 1 ? "渠道" : "" }}</el-tag
                            >
                        </div>
                        <div
                            v-if="
                                statusTxt(
                                    v.onsale_review_status,
                                    v.onsale_status
                                ) === '在售中'
                            "
                        >
                            <el-tag
                                size="mini"
                                type="danger"
                                effect="dark"
                                class="m-r-4"
                            >
                                在售
                            </el-tag>
                        </div>
                        <div class="good-title">
                            <el-link
                                :underline="false"
                                class="m-r-10"
                                @click="copy(v.id)"
                            >
                                {{ v.id }}
                            </el-link>
                            <el-link
                                v-if="v.encrypt_id"
                                :underline="false"
                                class="m-r-10"
                                @click="copy(`encrypt_id=${v.encrypt_id}`)"
                            >
                                encrypt_id
                            </el-link>
                            <el-link :underline="false">
                                <span @click="$viewPcGoods(v.id)">{{
                                    v.title
                                }}</span>
                            </el-link>
                            <el-tag
                                size="mini"
                                type="danger"
                                effect="dark"
                                class="m-l-4"
                                v-if="v.is_seckill"
                            >
                                秒杀
                            </el-tag>
                        </div>
                    </div>
                    <div class="good-content">
                        <div class="good-info">
                            <p>
                                国家：<el-link :underline="false">{{
                                    v.country.join(",")
                                        ? v.country.join(",")
                                        : "-"
                                }}</el-link>
                            </p>
                            <p>
                                类型：<el-link :underline="false">{{
                                    v.product_category.join(",")
                                        ? v.product_category.join(",")
                                        : "-"
                                }}</el-link>
                            </p>
                            <p>
                                容量：<el-link :underline="false">{{
                                    v.capacity
                                }}</el-link>
                            </p>
                            <p
                                :class="v.price > 0 ? 'p_blue' : ''"
                                style="vertical-align: middle"
                                @click="handlePrice(v)"
                            >
                                售价：<el-link
                                    :underline="false"
                                    v-if="v.price > 0"
                                    class="p_blue ellipsis"
                                    >{{ v.package_prices }}</el-link
                                >
                                <el-link :underline="false" v-else>-</el-link>
                            </p>
                            <div
                                style="
                                    display: flex;
                                    align-content: center;
                                    max-width: 240px;
                                "
                            >
                                <span style="display: block; min-width: 60px"
                                    >商品标签：</span
                                >
                                <div
                                    style="display: flex; flex-wrap: wrap"
                                    v-if="v.label_arr.length > 0"
                                >
                                    <el-tag
                                        type="primary"
                                        size="mini"
                                        v-for="item in v.label_arr"
                                        :key="item.id"
                                        style="
                                            margin-right: 5px;
                                            margin-bottom: 4px;
                                            cursor: pointer;
                                        "
                                        >{{ item.name }}
                                    </el-tag>
                                </div>
                                <div v-else>
                                    <el-link :underline="false" type="primary"
                                        >-</el-link
                                    >
                                </div>
                            </div>
                            <p>
                                收款单位：
                                <el-link :underline="false">{{
                                    v.payee_merchant_name || "-"
                                }}</el-link>
                            </p>
                        </div>
                        <!-- <el-link :underline="false"></el-link> -->
                        <div class="good-popularize">
                            <p>
                                文案：<el-link :underline="false">{{
                                    v.creator_name || "-"
                                }}</el-link>
                            </p>
                            <p>
                                采购：<el-link :underline="false">{{
                                    v.buyer_name || "-"
                                }}</el-link>
                            </p>
                            <p>
                                上架：<el-link :underline="false">{{
                                    v.operation_name || "-"
                                }}</el-link>
                            </p>
                            <p>
                                运营：<el-link :underline="false">{{
                                    v.operation_review_name || "-"
                                }}</el-link>
                            </p>
                             <p>
                                曝光方式：<el-link 
                                    :underline="false"
                                    v-for="(item, index) in v.exposure_method" 
                                    :key="index"
                                    :style="{ color: item.status === 1 ? '#409eff' : '#000000' }"
                                >
                                    {{ item.type }}{{ index < v.exposure_method.length - 1 ? '/' : '' }}
                                </el-link>
                            </p>
                        </div>
                        <div class="good-time">
                            <div>
                                <div class="p_blue">上架：</div>
                                <!-- @click="updateTime(v)" -->
                                <el-link class="p_blue" :underline="false"
                                    >{{ v.onsale_time | timeFormat
                                    }}<i
                                        class="el-icon-edit"
                                        v-if="
                                            (v.onsale_status == 0 &&
                                                v.onsale_review_status == 3) ||
                                            v.onsale_review_status == 4
                                        "
                                    ></i>
                                </el-link>
                            </div>
                            <div class="p_blue">
                                开售：
                                <el-link class="p_blue" :underline="false"
                                    >{{ v.sell_time | timeFormat
                                    }}<i
                                        class="el-icon-edit"
                                        v-if="
                                            (v.onsale_status == 0 &&
                                                v.onsale_review_status == 3) ||
                                            v.onsale_review_status == 4
                                        "
                                    ></i>
                                </el-link>
                            </div>
                            <div class="p_blue">
                                下架：
                                <el-link class="p_blue" :underline="false"
                                    >{{ v.sold_out_time | timeFormat
                                    }}<i
                                        class="el-icon-edit"
                                        v-if="
                                            (v.onsale_status == 0 &&
                                                v.onsale_review_status == 3) ||
                                            v.onsale_review_status == 4
                                        "
                                    ></i>
                                </el-link>
                            </div>
                            <div class="p_blue">
                                发货：
                                <el-link class="p_blue" :underline="false">
                                    {{ v.predict_shipment_time | timeFormat
                                    }}<i
                                        class="el-icon-edit"
                                        v-if="
                                            (v.onsale_status == 0 &&
                                                v.onsale_review_status == 3) ||
                                            v.onsale_review_status == 4
                                        "
                                    ></i>
                                </el-link>
                            </div>
                        </div>
                        <div class="good-stock">
                            <p class="p_blue" @click="updateStock(v)">
                                库存：<el-link
                                    :underline="false"
                                    class="p_blue"
                                >
                                    {{ v.inventory
                                    }}<i class="el-icon-edit"></i>
                                </el-link>
                            </p>
                            <el-popover
                                placement="top-start"
                                title="已售"
                                width="200"
                                trigger="click"
                                content="已售：显示数值为实际已售出瓶数"
                            >
                                <p class="p_blue" slot="reference">
                                    已售<i
                                        class="fas fa-question-circle"
                                    />：<el-link
                                        @click.stop="handleBuy(v, 1)"
                                        :underline="false"
                                        class="p_blue"
                                        style="min-width: 15px"
                                    >
                                        {{ v.saled_count }}
                                    </el-link>
                                </p>
                            </el-popover>
                            <el-popover
                                placement="top-start"
                                title="已购"
                                width="200"
                                trigger="click"
                                content="已购：购买人数/套餐数量"
                            >
                                <p slot="reference" class="p_blue">
                                    已购<i
                                        class="fas fa-question-circle"
                                    />：<el-link
                                        :underline="false"
                                        class="p_blue"
                                        @click.stop="handleBuy(v, 2)"
                                    >
                                        {{ v.purchased_person }}/{{
                                            v.purchased_number
                                        }}
                                    </el-link>
                                </p>
                            </el-popover>
                            <p>
                                前端：<el-link :underline="false"
                                    >{{ v.purchased + v.vest_purchased }}/{{
                                        v.limit_number
                                    }}
                                </el-link>
                            </p>
                            <p v-if="v.reservation_number > 0">
                                预约：<el-link :underline="false"
                                    >{{ v.reservation_number }}
                                </el-link>
                            </p>
                        </div>
                        <div class="good-comment">
                            <p class="p_blue" @click="handleVest(v)">
                                马甲：<el-link
                                    :underline="false"
                                    class="p_blue"
                                >
                                    {{ v.vest_count }}
                                </el-link>
                                <i
                                    class="el-icon-edit"
                                    style="cursor: pointer"
                                ></i>
                            </p>
                            <p>
                                评论：<el-link :underline="false">
                                    本{{ v.periods_comment_count }}/编{{
                                        v.virtual_comment_count
                                    }}/总{{ v.product_comment_count }}
                                </el-link>
                            </p>
                            <p>
                                浏览：<el-link :underline="false">{{
                                    v.pageviews
                                }}</el-link>
                            </p>
                            <p>
                                转化率：<el-link :underline="false">
                                    {{ v.rate }}‰</el-link
                                >
                            </p>
                            <p
                                style="
                                    font-size: 20px;
                                    color: #409eff;
                                    cursor: pointer;
                                "
                                @click="lineChart(v)"
                            >
                                <i class="el-icon-s-data"></i>
                            </p>
                        </div>
                        <div class="good-saleinfo">
                            <p>
                                套餐：<el-link :underline="false">{{
                                    v.periods_set_count
                                }}</el-link>
                            </p>
                            <div class="good-remark">
                                <p class="p_blue" @click="handleRemark(v)">
                                    备注:<i
                                        class="el-icon-edit"
                                        style="cursor: pointer"
                                    ></i>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="good-status">
                    <p
                        style="color: #409eff; cursor: pointer"
                        @click="handleStatusClick(v)"
                    >
                        {{ statusTxt(v.onsale_review_status, v.onsale_status) }}
                    </p>
                    <p
                        style="color: #409eff; cursor: pointer"
                        @click="showOperationRecord(v)"
                    >
                        操作记录
                    </p>
                    <p
                        style="
                            color: #409eff;
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                        "
                    >
                        <ShareLink :id="v.id" :encrypt-id="v.encrypt_id" />
                    </p>
                    <p
                        style="color: #409eff; cursor: pointer"
                        @click="subscriptionInventoryReminder(v)"
                    >
                        订阅库存提醒
                    </p>
                    <el-popover
                        placement="left-start"
                        width="200"
                        trigger="click"
                        @show="getMinipogramCode(v)"
                        style="margin-top: 10px; margin: 0 auto"
                        v-if="
                            v.onsale_review_status == 3 && v.onsale_status == 2
                        "
                    >
                        <img :src="minipogram_code" style="width: 180px" />
                        <p
                            slot="reference"
                            style="color: #409eff; cursor: pointer; margin: 0"
                        >
                            小程序码
                        </p>
                    </el-popover>
                </div>
            </div>
        </el-card>
        <div class="pagination-block" v-if="!isEdit">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>

        <remarkList ref="remarklist" :isAll="1"></remarkList>
        <logList ref="logList"></logList>
        <OperationRecord
            ref="OperationRecordref"
            :parentObj="this"
        ></OperationRecord>
        <packList ref="packList"></packList>
        <stockList ref="stocklist" :isAll="1" :parentObj="this"></stockList>
        <VestList ref="vestlist" :isAll="1" :parentObj="this"></VestList>
        <FlashVestList ref="flashVestList" :parentObj="this"></FlashVestList>
        <BuyList
            ref="buylist"
            :parentObj="this"
            v-if="dialogVisible2"
        ></BuyList>
        <SoldList
            ref="soldlist"
            :parentObj="this"
            v-if="dialogVisible3"
        ></SoldList>
        <!-- 折线图 -->
        <el-dialog
            title=""
            :visible.sync="lineChartVisible"
            width="70%"
            :before-close="lineChartHandleClose"
        >
            <LineChart v-if="lineChartVisible" :rowData="rowData" />
        </el-dialog>
        <RightSideMenu @menu-click="handleRightMenuClick" />
        <SubscriptionInventory ref="subscriptionInventory" />
        <BatchGoodTags
            ref="batchGoodTags"
            @batchUpdateTagsSuccess="batchUpdateTagsSuccess"
        />
    </div>
</template>
<script>
import copy from "copy-to-clipboard";
import goodsDetail from "../../article/GoodsDetail.vue";
import remarkList from "../../article/remarkList.vue";
import logList from "./LogList.vue";
import packList from "./packList.vue";
import stockList from "./stockList.vue";
import VestList from "./VestList.vue";
import BuyList from "./BuyList.vue";
import SoldList from "./SoldList.vue";
import LineChart from "./lineChart.vue";
import FlashVestList from "./flashVestList.vue";
import OperationRecord from "./OperationRecord.vue";
import ShareLink from "@/components/ShareLink.vue";
import RightSideMenu from "@/components/RightSideMenu.vue";
import SubscriptionInventory from "./SubscriptionInventory.vue";
import BatchGoodTags from "@/components/BatchGoodTags/index.vue";

export default {
    components: {
        remarkList,
        logList,
        packList,
        goodsDetail,
        stockList,
        VestList,
        BuyList,
        SoldList,
        LineChart,
        FlashVestList,
        OperationRecord,
        ShareLink,
        RightSideMenu,
        SubscriptionInventory,
        BatchGoodTags,
    },
    props: {
        status: {
            type: Number,
            default: 0,
        },
        periods_type: {
            type: Number,
            default: 0,
        },
    },

    data() {
        return {
            getOrderList: [],
            lineChartVisible: false,
            rowData: {},
            loadings: false,
            showSearchArea: localStorage.getItem("showSearchArea") !== "false",
            selectedItems: [],
            isBatchSelecting: false,
            query: {
                page: 1,
                limit: 10,
                periods: "",
                product_type: "",
                title: "",
                short_code: "",
                creator_id: "",
                product_category: "",
                country: "",
                import_type: "",
                is_channel: "",
                periods_type: "",
                product_channel: "",
                onsale_review_status: 3,
                periods_type_arr: [],
                supplier_id: "",
                buyer_review_status: 3,
                time: [],
                buyer_id: "",
                time1: [],
                time2: [],
                sales_model: "",
                seller_id: "",
                label_id: [],
            },
            buyerOptions: [],
            merchantList: [],
            is_channelOptions: [
                { value: 1, label: "是" },
                { value: 0, label: "否" },
            ],
            periods_typeTxt: {
                0: "闪购",
                1: "秒发",
                2: "跨境",
                3: "尾货",
                4: "兔头",
                9: "商家秒发",
            },
            periodsTypeList: [
                { value: 0, label: "闪购" },
                { value: 1, label: "秒发" },
                { value: 2, label: "跨境" },
                { value: 3, label: "尾货" },
                { value: 4, label: "兔头" },
                { value: 9, label: "商家秒发" },
            ],

            tableData: [],
            goodsData: {},
            payOrderTimes: [],
            product_typeOptions: [],
            total: 0,
            isEdit: false,
            activeName: "beshelved",
            editInput: true,
            dialogVisible: false,
            GoodsDetailVisible: false,
            dialogVisible1: false,
            dialogVisible3: false,
            dialogVisible2: false,
            entity: {
                reason: "",
            },
            productChannelOptions: [
                // {
                //     label: "平台",
                //     value: 1,
                // },
                {
                    label: "自有产品",
                    value: 2,
                },
                {
                    label: "酒云采购",
                    value: 3,
                },
            ],
            reasonOptions: [
                {
                    label: "酒瓶信息错误",
                    value: "酒瓶信息错误",
                },
                {
                    label: "年份错误",
                    value: "年份错误",
                },
                {
                    label: "详细资料错误",
                    value: "详细资料错误",
                },
                {
                    label: "采购人不符",
                    value: "采购人不符",
                },
                {
                    label: "其他",
                    value: "其他",
                },
            ],
            typeOptions: [
                {
                    label: "酒类",
                    value: 0,
                },
                {
                    label: "饮料",
                    value: 2,
                },
                {
                    label: "食品",
                    value: 3,
                },
                {
                    label: "物料",
                    value: 4,
                },
                {
                    label: "虚拟",
                    value: 6,
                },
                {
                    label: "酒具",
                    value: 7,
                },
                {
                    label: "其他",
                    value: 5,
                },
            ],
            import_typeOptions: [
                {
                    label: "自进口",
                    value: 0,
                },
                {
                    label: "地采",
                    value: 1,
                },
            ],
            statusOptions: [
                {
                    label: "待绑定",
                    value: 0,
                },
                {
                    label: "待审核",
                    value: 1,
                },
                // {
                //     label: "审批中",
                //     value: 2
                // },
                {
                    label: "已审核",
                    value: 3,
                },
                {
                    label: "待上架",
                    value: 5,
                },
                {
                    label: "待售中",
                    value: 6,
                },
                {
                    label: "在售中",
                    value: 7,
                },
                {
                    label: "已下架",
                    value: 8,
                },
            ],
            rules: {
                reason: [
                    {
                        required: true,
                        message: "请选择驳回原因",
                        trigger: "change",
                    },
                ],
            },
            options: [],
            auditTime: 0,
            goodsinfo: {},
            isOnsale: false,
            supplierRemoteMethodLoading: false,
            supplierOptions: [],
            goodTagOptions: [],
            minipogram_code: "",
            program_row: {},
        };
    },
    methods: {
        handleShare(v) {
            try {
                const userinfo = JSON.parse(localStorage.getItem("userinfo"));
                const uid = userinfo?.original_uid || "";

                if (!uid) {
                    this.$message.error(
                        "未能成功获取到您的用户ID，请退出中台重新登录。"
                    );
                    return;
                }

                const currentUrl = window.location.href.toLowerCase();
                const domain =
                    currentUrl.includes("127.0.0.1") ||
                    currentUrl.includes("localhost") ||
                    currentUrl.includes("test")
                        ? "https://test.wineyun.com"
                        : "https://www.vinehoo.com";

                const decryptUrl =
                    currentUrl.includes("127.0.0.1") ||
                    currentUrl.includes("localhost") ||
                    currentUrl.includes("test")
                        ? "https://test-wine.wineyun.com/go-wechat/wechat/v3/decrypt/channel"
                        : "https://callback.vinehoo.com/go-wechat/wechat/v3/decrypt/channel";

                const generateShareUrl = (id, auth = "") => {
                    let url = `${domain}/pages/goods-detail/goods-detail?id=${id}&source_platform=vinehoo&source_event=share&source_user=${uid}`;
                    if (auth) {
                        url += `&auth=${auth}`;
                    }
                    return url;
                };

                if (v.encrypt_id) {
                    fetch(`${decryptUrl}?id=${v.encrypt_id}`, {
                        method: "GET",
                    })
                        .then((response) => response.json())
                        .then((data) => {
                            if (data.error_code === 0 && data.data) {
                                const shareUrl = generateShareUrl(
                                    data.data.id,
                                    data.data.auth
                                );
                                copy(shareUrl);
                                this.$message.success("链接已复制到剪贴板");
                            } else {
                                this.$message.error("解密失败");
                            }
                        })
                        .catch((error) => {
                            console.error("Decryption error:", error);
                            this.$message.error("解密请求失败");
                        });
                } else {
                    const shareUrl = generateShareUrl(v.id);
                    copy(shareUrl);
                    this.$message.success("链接已复制到剪贴板");
                }
            } catch (error) {
                console.error("Share error:", error);
                this.$message.error("生成分享链接失败");
            }
        },
        lineChart(v) {
            this.rowData = v;
            this.lineChartVisible = true;
        },
        lineChartHandleClose() {
            this.lineChartVisible = false;
        },
        handlePrice(rows) {
            this.$nextTick(() => {
                this.$refs.packList.openForm(rows);
            });
        },
        handleStatusClick(rows) {
            this.$nextTick(() => {
                this.$refs.logList.openForm(rows);
            });
        },
        copy(data) {
            copy(data);
            this.$message.success("复制成功");
        },
        getPurchase() {
            this.$request.article
                .purchaseList({
                    type: 2,
                })
                .then((res) => {
                    this.buyerOptions = res.data.data.list;
                });
            this.$request.article
                .purchaseList({
                    type: 3,
                })
                .then((res) => {
                    this.options = res.data.data.list;
                });
        },
        async getMerchantList() {
            const data = {
                page: 1,
                limit: 999,
            };
            const res = await this.$request.article.getMerchantList(data);
            if (res.data.error_code === 0) {
                this.merchantList = res.data.data.list;
            }
        },

        // 获取商品标签选项
        getGoodTagOptions() {
            this.$request.recommendLabel
                .labelList({ page: 1, limit: 999, type: 2 })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.goodTagOptions = res.data.data.list;
                    }
                });
        },
        showOperationRecord(row) {
            console.log("1000---", row);
            this.$nextTick(() => {
                this.$refs.OperationRecordref.openForm(row.id);
            });
        },
        handleBuy(v, type) {
            if (type == 1) {
                this.dialogVisible3 = true;
                this.$nextTick(() => {
                    this.$refs.soldlist.openForm(v);
                });
            } else {
                this.dialogVisible2 = true;
                this.$nextTick(() => {
                    this.$refs.buylist.openForm(v);
                });
            }
        },
        viewGoods(item) {
            window.open(
                this.$BASE.PCDomain +
                    "/pages/goods-detail/goods-detail?id=" +
                    (item.encrypt_id || item.id)
            );
        },
        handleVest(v) {
            if (v.is_seckill == 1) {
                this.$nextTick(() => {
                    this.$refs.flashVestList.openForm(v);
                });
            } else {
                this.$nextTick(() => {
                    this.$refs.vestlist.openForm(v);
                });
            }
        },
        updateStock(row) {
            this.$refs.stocklist.openForm(row);
        },
        handleStatusClick(rows) {
            this.$nextTick(() => {
                this.$refs.logList.openForm(rows);
            });
        },
        statusTxt(operateStatus, status) {
            let statusTxt = "";
            if (operateStatus == 0) {
                statusTxt = "待绑定";
            } else if (operateStatus == 1) {
                statusTxt = "待审核";
            } else if (operateStatus == 3 && status == 0) {
                statusTxt = "待上架";
            } else if (operateStatus == 3 && status == 2) {
                statusTxt = "在售中";
            } else if (operateStatus == 3 && status == 1) {
                statusTxt = "待售中";
            } else if (operateStatus == 4) {
                statusTxt = "已驳回";
            } else if (status == 3 || status == 4) {
                statusTxt = "已下架";
            }
            return statusTxt;
        },
        HandleClick(rows) {
            this.$refs.goodsop.openForm(rows);
        },
        edit() {
            this.isEdit = true;
            this.activeName = "edit";
        },
        subscriptionInventoryReminder(row) {
            this.$nextTick(() => {
                this.$refs.subscriptionInventory.open(row);
            });
        },
        getData() {
            let onsale_review_status = this.query.onsale_review_status;
            let onsale_status = 0;
            const params = {
                periods: this.query.periods,
                country: this.query.country,
                creator_id: this.query.creator_id,
                title: this.query.title,
                product_type: this.query.product_type,
                buyer_id: this.query.buyer_id,
                supplier_id: this.query.supplier_id,
                periods_type_arr: this.query.periods_type_arr.join(","),
                buyer_review_status: 3,
                product_channel: this.query.product_channel,
                product_category: this.query.product_category,
                is_channel: this.query.is_channel,
                onsale_review_status: onsale_review_status,
                import_type: this.query.import_type,
                onsale_time_start:
                    (this.query.time && this.query.time[0]) || "",
                onsale_time_end: (this.query.time && this.query.time[1]) || "",
                sold_out_time_start:
                    (this.query.time1 && this.query.time1[0]) || "",
                sold_out_time_end:
                    (this.query.time1 && this.query.time1[1]) || "",
                predict_shipment_time_start:
                    (this.query.time2 && this.query.time2[0]) || "",
                predict_shipment_time_end:
                    (this.query.time2 && this.query.time2[1]) || "",
                periods_list: 1,
                page: this.query.page,
                limit: this.query.limit,
                short_code: this.query.short_code,
                sales_model: this.query.sales_model,
                seller_id: this.query.seller_id,
                label_id:
                    this.query.label_id.length > 0
                        ? this.query.label_id.join(",")
                        : "",
            };
            if (this.query.onsale_review_status > 3) {
                params.onsale_review_status = 3;
                if (this.query.onsale_review_status == 5) {
                    params.onsale_status = 0;
                } else if (this.query.onsale_review_status == 6) {
                    params.onsale_status = 1;
                } else if (this.query.onsale_review_status == 7) {
                    params.onsale_status = 2;
                } else if (this.query.onsale_review_status == 8) {
                    params.onsale_status = 3;
                }
            }
            if (this.isOnsale) {
                params.onsale_status = 2;
            }
            if (
                this.query.onsale_review_status == 0 ||
                this.query.onsale_review_status == 1
            ) {
                delete params.periods_list;
            }
            this.$request.article.articleList(params).then((res) => {
                if (res.data.error_code == 0) {
                    res.data.data.list.map((item) => {
                        item.onsale_time_visible = true;
                        item.sell_time_visible = true;
                        item.predict_shipment_time_visible = true;
                        item.sold_out_time_visible = true;
                        item.country = item.country;
                        item.product_category = item.product_category;
                    });
                    this.tableData = res.data.data.list || [];
                    this.total = res.data.data.total;
                    window.scrollTo({
                        top: 0,
                        behavior: "smooth",
                    });
                }
            });
        },
        // timeFormat(val) {
        //     if (val == '1970-01-01 08:00:00') {
        //         return "-"
        //     } else {
        //         return val
        //     }
        // },
        updateTime(rows, type) {
            let params = {
                period: rows.id,
                periods_type: rows.periods_type,
            };
            if (type == 1) {
                if (!rows.onsale_time) {
                    this.$message.error("请选择上架时间！");
                    return;
                }
                params.onsale_time = rows.onsale_time;
            } else if (type == 2) {
                if (!rows.sell_time) {
                    this.$message.error("请选择开售时间！");
                    return;
                }
                params.sell_time = rows.sell_time;
            } else if (type == 3) {
                if (!rows.sold_out_time) {
                    this.$message.error("请选择下架时间！");
                    return;
                }
                params.sold_out_time = rows.sold_out_time;
            } else if (type == 4) {
                if (!rows.predict_shipment_time) {
                    this.$message.error("请选择发货时间！");
                    return;
                }
                params.predict_shipment_time = rows.predict_shipment_time;
            }
            this.$request.article.goodsUpdate(params).then((res) => {
                if (res.data.error_code == 0) {
                    if (type == 1) {
                        rows.onsale_time_visible = !rows.onsale_time_visible;
                    } else if (type == 2) {
                        rows.sell_time_visible = !rows.sell_time_visible;
                    } else if (type == 3) {
                        rows.sold_out_time_visible =
                            !rows.sold_out_time_visible;
                    } else if (type == 4) {
                        rows.predict_shipment_time_visible =
                            !rows.predict_shipment_time_visible;
                    }
                }
            });
        },
        remoteMethod(query) {
            console.log(query);
            if (query.length >= 1) {
                this.loadings = true;
                setTimeout(() => {
                    let data = {
                        keywords: query,
                    };
                    this.$request.purchase
                        .getProductListForKeywords(data)
                        .then((res) => {
                            this.loadings = false;
                            if (res.data.error_code == 0) {
                                console.log(res.data);
                                this.product_typeOptions = res.data.data.list;
                            }
                        });
                }, 300);
            } else {
                this.goodsOptions = [];
            }
            console.log(this.goodsOptions);
        },
        HandleAudit(rows, review_status, reason) {
            this.GoodsDetailVisible = true;
            this.rows = rows;
            this.auditTime = 5;
            if (rows.periods_type == 0) {
                this.$request.article
                    .getFlashDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.imgList =
                                res.data.data.product_img.split(",");
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            } else if (rows.periods_type == 1) {
                this.$request.article
                    .getSecondDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.imgList =
                                res.data.data.product_img.split(",");
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            } else if (rows.periods_type == 2) {
                this.$request.article
                    .getCrossDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.imgList =
                                res.data.data.product_img.split(",");
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            } else if (rows.periods_type == 3) {
                this.$request.article
                    .getLeftoverDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.imgList =
                                res.data.data.product_img.split(",");
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            }

            this.countDown();
        },
        audit(rows, review_status, reason) {
            // if (review_status == 3 && !this.validateForm()) {
            //     return
            // }
            console.log(rows, 3333333);
            const params = {
                period: rows.id,
                onsale_review_status: review_status,
                reject_reason: reason,
                periods_type: rows.periods_type,
                version: this.goodsinfo.version,
            };
            this.$request.article.updateOnsale(params).then((res) => {
                if (res.data.error_code == 0) {
                    this.getData();
                    if (review_status == 4) {
                        this.dialogVisible = false;
                    }
                    this.GoodsDetailVisible = false;
                    this.dialogVisible1 = false;
                    this.$message.success("操作成功！");
                }
            });
        },
        HandleReject(rows) {
            this.rows = rows;
            this.entity.reason = "";
            this.dialogVisible = !this.dialogVisible;
        },
        rejects() {
            if (this.validateForm()) {
                this.audit(this.rows, 4, this.entity.reason);
            }
        },
        handleRemark(rows) {
            this.$nextTick(() => {
                this.$refs.remarklist.openForm(rows);
            });
        },
        onShelf(rows) {
            this.$confirm("你确定要上架吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.$request.article
                        .updateoffSale({
                            period: rows.id,
                            periods_type: rows.periods_type,
                            onsale_status: 2,
                            operation_id: "1",
                            operation_name: "操作人",
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.query.page = 1;
                                this.getData();
                                this.$message.success("操作成功");
                            }
                        });
                })
                .catch(() => {
                    //几点取消的提示
                });
        },
        offShelf(rows) {
            this.$prompt("请输入下架原因", "下架", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                inputPlaceholder: "请输入下架原因",
                inputValidator: (value) => {
                    if (!value) {
                        return "下架原因不能为空";
                    }
                    return true;
                },
            })
                .then(({ value }) => {
                    this.$request.article
                        .updateoffSale({
                            period: rows.id,
                            periods_type: rows.periods_type,
                            onsale_status: 3,
                            operation_id: "1",
                            operation_name: "操作人",
                            remark: value,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.query.page = 1;
                                this.getData();
                                this.$message.success("下架成功");
                            }
                        });
                })
                .catch(() => {
                    // 取消操作
                });
        },
        handleCopy() {},
        countDown() {
            this.auditTime = this.auditTime - 1;
            if (this.auditTime == 0) {
                return;
            }
            setTimeout(() => {
                this.countDown();
            }, 1000);
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        reset() {
            this.$confirm("您确定要重置所有筛选条件吗?", "重置操作", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.payOrderTimes = [];
                this.form = {
                    page: 1,
                };
                this.getData();
            });
        },

        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
        supplierRemoteMethod(query) {
            if (query !== "") {
                this.supplierRemoteMethodLoading = true;
                this.$request.article
                    .supplierList({ page: 1, limit: 99, keyword: query })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.supplierOptions = res.data.data.list.map(
                                (item) => ({
                                    value: item.id,
                                    label: item.supplier_name,
                                })
                            );
                            this.supplierRemoteMethodLoading = false;
                        }
                    });
            } else {
                this.supplierOptions = [];
            }
        },
        getMinipogramCode(params) {
            if (params.id != this.program_row.id) {
                this.$request.article
                    .getMinappQrcode({
                        scene: String(params.id),
                        page: "pages/goods-detail/goods-detail",
                    })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            console.warn(res.data.data);
                            this.minipogram_code =
                                "data:image/jpg;base64," + res.data.qrcode;
                            console.warn(this.minipogram_code);
                            this.program_row = params;
                        }
                    });
            }
        },
        handleRightMenuClick(menuItem) {
            if (menuItem === "realtime-sales") {
                const currentUrl = window.location.origin;
                const targetUrl = `${currentUrl}/data-analysis/InstantSalesStatistics`;
                window.open(targetUrl, "_blank");
            } else if (menuItem === "search-goods") {
                this.showSearchArea = true;
                localStorage.setItem("showSearchArea", "true");
            }
        },
        toggleSearchArea() {
            this.showSearchArea = false;
            localStorage.setItem("showSearchArea", "false");
        },

        // 批量操作相关方法
        handleBatchOperation() {
            // 检查是否有选中的商品
            if (this.tableData.length === 0) {
                this.$message.warning("当前没有可操作的商品");
                return;
            }

            // 弹出确认对话框
            this.$confirm("请选择要批量操作的商品", "批量操作", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "info",
            })
                .then(() => {
                    // 清空之前选择的商品
                    this.selectedItems = [];

                    // 为每个商品添加选择状态
                    this.tableData.forEach((item) => {
                        this.$set(item, "isSelected", false);
                    });

                    // 显示选择界面
                    this.$set(this, "isBatchSelecting", true);

                    // 显示批量操作按钮
                    this.$message.success(
                        '请选择要操作的商品，然后点击"批量编辑标签"按钮'
                    );
                })
                .catch(() => {
                    // 用户取消操作
                });
        },

        // 处理复选框变化
        handleCheckboxChange(item, isChecked) {
            // 更新选中的商品列表
            if (isChecked) {
                this.selectedItems.push({
                    id: item.id,
                    periods_type: item.periods_type,
                    label_arr: item.label_arr || [], // 包含标签信息
                });
            } else {
                const index = this.selectedItems.findIndex(
                    (selected) => selected.id === item.id
                );
                if (index !== -1) {
                    this.selectedItems.splice(index, 1);
                }
            }
        },

        // 全选
        selectAllItems() {
            this.tableData.forEach((item) => {
                if (!item.isSelected) {
                    this.$set(item, "isSelected", true);
                    // 避免重复添加
                    const exists = this.selectedItems.some(
                        (selected) => selected.id === item.id
                    );
                    if (!exists) {
                        this.selectedItems.push({
                            id: item.id,
                            periods_type: item.periods_type,
                            label_arr: item.label_arr || [], // 包含标签信息
                        });
                    }
                }
            });
            this.$message.success(`已选择全部 ${this.tableData.length} 个商品`);
        },

        // 取消全选
        deselectAllItems() {
            this.tableData.forEach((item) => {
                this.$set(item, "isSelected", false);
            });
            this.selectedItems = [];
            this.$message.success("已取消全部选择");
        },

        // 批量编辑标签
        batchEditTags() {
            if (this.selectedItems.length === 0) {
                this.$message.warning("请至少选择一个商品");
                return;
            }

            // 打开批量标签编辑弹窗
            this.$nextTick(() => {
                this.$refs.batchGoodTags.show(this.selectedItems);
            });
        },

        // 取消批量选择
        cancelBatchSelection() {
            // 清除选择状态
            this.tableData.forEach((item) => {
                this.$set(item, "isSelected", false);
            });

            // 清空选中的商品列表
            this.selectedItems = [];

            // 隐藏选择界面
            this.$set(this, "isBatchSelecting", false);
        },

        // 批量更新标签成功回调
        batchUpdateTagsSuccess() {
            // 刷新数据
            this.getData();

            // 清除选择状态
            this.cancelBatchSelection();
        },
    },
    filters: {
        vmall_import_typeFormat(val) {
            switch (val) {
                case 2:
                    return "自有产品";
                case 3:
                    return "酒云采购";
                default:
                    return "未知";
            }
        },
        import_typeFormat(val) {
            switch (val) {
                case 0:
                    return "自进口";
                case 1:
                    return "地采";
                case 2:
                    return "跨境";
                default:
                    return "未知";
            }
        },
        timeFormat(val) {
            if (val == "1970-01-01 08:00:00") {
                return "-";
            } else {
                return val;
            }
        },
    },
    mounted() {
        this.getPurchase();
        this.getData();
        this.getMerchantList();
        this.getGoodTagOptions();
    },
};
</script>
<style lang="scss" scoped>
.ellipsis {
    display: inline-block;
    text-align: left;
    width: 220px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    height: 22px;
    line-height: 22px;
}
.el-card {
    margin-bottom: 10px;
}

.good-card {
    display: flex;
    justify-content: space-between;

    .checkbox-container {
        display: flex;
        align-items: center;
        padding-right: 15px;
    }

    .good-cardinfo {
        width: 80%;

        .p_blue {
            color: #409eff;
            cursor: pointer;
        }

        .good-head {
            width: 100%;
            display: flex;
            justify-content: flex-start;
            white-space: nowrap;
            overflow: hidden;

            .good-tag {
                & > .el-tag {
                    margin-right: 4px;
                }
            }
        }

        .good-content {
            margin-top: 10px;
            display: flex;
            justify-content: space-between;

            .el-icon-edit {
                margin-left: 5px;
                color: #409eff;
                font-size: 14px;
            }

            p {
                margin-bottom: 0;
            }

            .good-time {
                width: 240px;

                > div {
                    display: flex;
                    align-items: center;
                }

                .times {
                    display: flex;

                    i {
                        cursor: pointer;
                        font-size: 20px;
                        color: #409eff;
                        padding: 2px 0 0 3px;
                    }
                }
            }

            .el-link {
                font-size: 12px;
            }

            .good-saleinfo {
                .good-remark {
                    display: flex;
                    align-items: center;
                    height: 30px;

                    & > p {
                        margin-bottom: 0;
                    }

                    & > .el-button {
                        margin-left: 20px;
                    }
                }
            }
        }
    }

    .good-status {
        width: 80px;
    }

    .opration {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 80px;

        & > .el-button {
            margin-left: 0;
            margin-top: 10px;
        }
    }
}

.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .table {
        margin-top: 10px;

        .f-12 {
            font-size: 12px;
        }

        .card {
            margin-bottom: 8px;

            .card-title {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;

            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }

                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}

.el-link--default {
    vertical-align: inherit !important;
}

.fixed-search {
    position: sticky;
    top: 51px;
    z-index: 1000;
    background: #fff;
    margin-bottom: 10px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.batch-operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #f0f9ff;
    border: 1px solid #d9ecff;
    border-radius: 4px;
    margin-bottom: 10px;

    .batch-info {
        font-size: 14px;

        .selected-count {
            color: #409eff;
            font-weight: bold;
            font-size: 16px;
        }
    }

    .batch-actions {
        display: flex;
        gap: 10px;
    }
}

.selectable-card {
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
        box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.2) !important;
        border-color: #c6e2ff;
    }
}

.selected-card {
    border: 2px solid #409eff !important;
    box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.2) !important;
}

.order-layout {
    padding-top: 10px;
}

.search-header {
    position: relative;
    height: 20px;
    margin-bottom: 10px;
}

.close-button {
    position: absolute;
    right: 0;
    top: 0;
    cursor: pointer;
    padding: 5px;

    i {
        font-size: 20px;
        color: #909399;
        transition: color 0.3s;

        &:hover {
            color: #409eff;
        }
    }
}
</style>
