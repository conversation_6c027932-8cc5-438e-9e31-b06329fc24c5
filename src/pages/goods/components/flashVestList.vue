<template>
    <el-dialog
        title="马甲列表"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
        width="65%"
        append-to-body
    >
        <el-form :inline="true" size="small" style="margin-bottom: 10px">
            <!--     <el-form-item label="活动启用来源" prop="activity_object">
                    <el-select v-model="query.activity_object" clearable>
                        <el-option label="酒云" value="1" />
                        <el-option label="第三方平台" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="活动名称">
                    <el-input v-model="query.name" placeholder="请输入活动名称" />
                </el-form-item> -->
            <!--   <el-button type="success" @click="search" icon="el-icon-search" v-action="'rabbit_select'">查询
                    </el-button> -->
            <el-button
                type="primary"
                v-if="!isAll"
                @click="handleClick()"
                size="mini"
                >添加</el-button
            >
        </el-form>
        <el-card shadow="hover">
            <el-table
                stripe
                size="mini"
                border
                :data="DataList"
                fit
                highlight-current-row
                style="width: 100%"
            >
                
                <el-table-column label="马甲数量" prop="num" align="center" />
                <el-table-column
                    label="执行方式"
                    prop="mode"
                    align="center"
                   
                >
                    <template slot-scope="row">
                        {{ row.row.mode | modeFilter }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="执行状态"
                    prop="status"
                    align="center"
                   
                >
                    <template slot-scope="row">
                        {{ row.row.status | statusFilter }}
                    </template>
                </el-table-column>

                <el-table-column
                    label="操作人"
                    prop="op_name"
                   
                    align="center"
                />
                <el-table-column
                    label="操作"
                    fixed="right"
                    align="center"
                    
                >
                    <template slot-scope="{ row }">
                        <el-button
                            type="danger"
                            v-if="row.status === 1"
                            size="mini"
                            @click="handleStop(row)"
                            >终止</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- <div class="pagination-block">
            
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div> -->
        <VestOp ref="VestOp" :parentObj="this"></VestOp>
    </el-dialog>
</template>

<script>
import VestOp from "./flashVestOp.vue";
export default {
    props: {
        isAll: {
            default: 0,
            type: Number,
        },
    },
    filters: {
        // 马甲状态：0-执行中 1-已结束 2-已终止
        statusFilter(val) {
            switch (val) {
                case 1:
                    return "待执行";
                case 2:
                    return "已执行";
                case 3:
                    return "已终止";
                default:
                    return "未知";
            }
        },
        modeFilter(val) {
            switch (val) {
                case 1:
                    return "售卖开始时";
                case 2:
                    return "立即执行";
                case 3:
                    return "产品售完时";
                default:
                    return "未知";
            }
        },
    },
    components: {
        VestOp,
    },
    data() {
        return {
            dialogVisible: false,
            btnVisible: false,
            DataList: [],
            query: {
                page: 1,
                limit: 10,
                periods: "",
                
            },
            total: 0,
            rows: {},
        };
    },
    mounted() {},
    methods: {
        getData() {
            console.log(this.rows);
            const data = {
                period: this.rows.id,
            };
            this.$request.article.getFlashVestLists(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.DataList = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        openForm(rows) {
            this.dialogVisible = true;
            this.rows = rows;
            this.getData();
        },
        handleClick(rows) {
            this.$nextTick(() => {
                this.$refs.VestOp.openForm(this.rows);
            });
        },
        handleStop(rows) {
            this.$confirm("你确定终止吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.$request.article
                        .flashStopVestOp({
                            id: rows.id,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("终止成功");
                                this.getData();
                            }
                        });
                })
                .catch(() => {
                    //几点取消的提示
                });
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.article-layout {
    .article-main {
        margin: 10px 0;

        /deep/ .el-table .warning-row {
            background: oldlace;
        }

        /deep/ .el-table .danger-row {
            background: oldlace;
        }

        /deep/ .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}
</style>
