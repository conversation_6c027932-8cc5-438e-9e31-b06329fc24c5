<template>
    <div>
        <div
            id="mycharts"
            ref="mycharts"
            style="width: 100%; height: 500px"
        ></div>
        <div class="dataView" @click="getDataView">
            <i class="el-icon-tickets"></i>
        </div>
        <el-dialog
            title="Data View"
            :visible.sync="dataViewVisible"
            width="50%"
            :before-close="handleClose"
            append-to-body
        >
            <el-card shadow="never">
                <el-table
                    :data="tableData"
                    border
                    style="width: 100%"
                    size="small"
                    :header-cell-style="{ 'text-align': 'center' }"
                    :cell-style="{ 'text-align': 'center' }"
                >
                    <el-table-column prop="time" label="时间" width="180">
                        <template slot-scope="scope">
                            {{ scope.row.time }}时
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="pageviews"
                        label="浏览量"
                        width="180"
                    >
                    </el-table-column>
                    <el-table-column prop="order_user" label="下单人数">
                    </el-table-column>
                </el-table>
            </el-card>
            <div style="text-align: center" class="m-t-10">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="query.page"
                    :page-size="query.limit"
                    :page-sizes="[10, 30, 50, 100, 200]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import moment from "moment";
import * as echarts from "echarts";
export default {
    props: ["rowData"],
    data() {
        return {
            dataViewVisible: false,
            tableData: [],
            mycharts: null,
            chartData: [],
            query: {
                page: 1,
                limit: 10,
            },
            total: 0,
        };
    },
    mounted() {
        console.log(this.rowData);
        // console.log(
        //     this.getdiffdate(
        //         this.rowData.onsale_time.split(" ")[0],
        //         this.rowData.sold_out_time.split(" ")[0]
        //     )
        // );
        this.getPageviewsByDays();
    },
    methods: {
        getDataView() {
            this.getPeriodPageviewsByHours();
            this.dataViewVisible = true;
        },
        async getPeriodPageviewsByHours() {
            let data = {
                st: this.rowData.onsale_time,
                et: this.rowData.sold_out_time,
                period: this.rowData.id,
                ...this.query,
            };
            let res = await this.$request.article.getPeriodPageviewsByHours(
                data
            );
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getPeriodPageviewsByHours();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.query.page = val;
            this.getPeriodPageviewsByHours();
        },
        handleClose() {
            this.dataViewVisible = false;
        },
        async getPageviewsByDays() {
            if (
                this.rowData.periods_type === 1 &&
                Date.now() > new Date(this.rowData.sold_out_time).getTime()
            ) {
                this.rowData.sold_out_time = moment().format(
                    "YYYY-MM-DD HH:mm:ss"
                );
            }
            let data = {
                st: this.rowData.onsale_time,
                et: this.rowData.sold_out_time,
                period: this.rowData.id,
            };
            let res = await this.$request.article.getPageviewsByDays(data);
            if (res.data.error_code == 0) {
                this.chartData = res.data.data;
                this.renderEchart();
            }
        },
        renderEchart() {
            if (!this.mycharts) {
                this.mycharts = echarts.init(this.$refs.mycharts);
            }
            const dateList = this.getdiffdate(
                this.rowData.onsale_time.split(" ")[0],
                this.rowData.sold_out_time.split(" ")[0]
            );
            const dataList = dateList
                .filter((date) => {
                    const now = Date.now();
                    const dateTime = new Date(date).getTime();
                    return dateTime < now;
                })
                .map((date) => {
                    const { order_user = 0, pageviews = 0 } =
                        this.chartData.find((item) => item.time === date) || {};
                    return { order_user, pageviews };
                });
            this.mycharts.setOption({
                // title: {
                //     text: "Temperature Change in the Coming Week",
                // },
                tooltip: {
                    trigger: "axis",
                },
                legend: {},
                color: ["#91cc75", "#5470c6"],
                toolbox: {
                    show: true,
                    feature: {
                        // dataZoom: {
                        //     yAxisIndex: "none",
                        // },
                        // dataView: { readOnly: false },
                        // magicType: { type: ["line", "bar"] },
                        restore: { show: true },
                        // saveAsImage: {},
                    },
                },
                xAxis: {
                    type: "category",
                    boundaryGap: false,
                    data: this.getdiffdate(
                        this.rowData.onsale_time.split(" ")[0],
                        this.rowData.sold_out_time.split(" ")[0]
                    ),
                },
                yAxis: {
                    type: "value",
                    // axisLabel: {
                    //     formatter: "{value} °C",
                    // },
                },
                series: [
                    {
                        name: "下单人数",
                        type: "line",
                        data: dataList.map((item) => item.order_user),
                        label: {
                            show: true,
                            position: "top",
                            formatter: (params) => {
                                return params.value;
                            },
                        },
                    },
                    {
                        name: "浏览量",
                        type: "line",
                        data: dataList.map((item) => item.pageviews),
                        label: {
                            show: true,
                            position: "top",
                            formatter: (params) => {
                                return params.value;
                            },
                        },
                    },
                ],
            });
            // 重置 option 并重新渲染
            this.mycharts.on("restore", () => {
                let data = {
                    st: this.rowData.onsale_time,
                    et: this.rowData.sold_out_time,
                    period: this.rowData.id,
                };
                this.$request.article.getPageviewsByDays(data).then((res) => {
                    if (res.data.error_code == 0) {
                        this.chartData = res.data.data;
                        this.mycharts.setOption({
                            series: [
                                {
                                    name: "下单人数",
                                    type: "line",
                                    data: this.chartData.map(
                                        (item) => item.order_user
                                    ),
                                    label: {
                                        show: true,
                                        position: "top",
                                        formatter: (params) => {
                                            return params.value;
                                        },
                                    },
                                },
                                {
                                    name: "浏览量",
                                    type: "line",
                                    data: this.chartData.map(
                                        (item) => item.pageviews
                                    ),
                                    label: {
                                        show: true,
                                        position: "top",
                                        formatter: (params) => {
                                            return params.value;
                                        },
                                    },
                                },
                            ],
                        });
                    }
                });
            });
        },
        //获取两个日期中间的日期
        getdiffdate(stime, etime) {
            //初始化日期列表，数组
            let diffdate = new Array();
            let i = 0;
            //开始日期小于等于结束日期,并循环
            while (stime <= etime) {
                diffdate[i] = stime;
                //获取开始日期时间戳
                let stime_ts = new Date(stime).getTime();
                //增加一天时间戳后的日期
                let next_date = stime_ts + 24 * 60 * 60 * 1000;
                //拼接年月日，这里的月份会返回（0-11），所以要+1
                let next_dates_y = new Date(next_date).getFullYear() + "-";
                let next_dates_m =
                    new Date(next_date).getMonth() + 1 < 10
                        ? "0" + (new Date(next_date).getMonth() + 1) + "-"
                        : new Date(next_date).getMonth() + 1 + "-";
                let next_dates_d =
                    new Date(next_date).getDate() < 10
                        ? "0" + new Date(next_date).getDate()
                        : new Date(next_date).getDate();
                stime = next_dates_y + next_dates_m + next_dates_d;
                //增加数组key
                i++;
            }
            return diffdate;
        },
    },
};
</script>

<style>
.dataView {
    font-size: 21px;
    position: absolute;
    top: 61px;
    right: 52px;
    cursor: pointer;
}
</style>
