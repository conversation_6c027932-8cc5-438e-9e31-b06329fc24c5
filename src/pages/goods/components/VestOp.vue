<template>
    <el-dialog
        title="基本信息"
        :visible.sync="dialogVisible"
        width="500px"
        append-to-body
        :close-on-click-modal="false"
    >
        <el-form ref="form" :model="entity" :rules="rules" label-width="120px">
            <el-form-item label="马甲数量" prop="number">
                <el-col :span="18">
                    <el-input
                        v-model="entity.number"
                        oninput="value=value.replace(/[^\d]/g,'')"
                        placeholder="请输入马甲数量"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="开始时间" prop="start_time">
                <el-col :span="18">
                    <el-date-picker
                        v-model="entity.start_time"
                        type="datetime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        placeholder="请输入开始时间"
                    >
                    </el-date-picker>
                </el-col>
            </el-form-item>
            <el-form-item label="执行时长" prop="duration">
                <el-col :span="18">
                    <el-input
                        v-model="entity.duration"
                        oninput="value=value.replace(/[^\d]/g,'')"
                        placeholder="请输入运行时长"
                        style="width: 150px"
                    /><span
                        style="
                            color: red;
                            display: inline-block;
                            width: 39px;
                            margin-left: 10px;
                        "
                        >分钟</span
                    >
                </el-col>
            </el-form-item>
            <div style="margin-left: 45px">
                <el-checkbox v-model="entity.is_repeat1">重复</el-checkbox>
            </div>
            <el-form-item
                label="执行次数"
                prop="count"
                v-if="entity.is_repeat1"
            >
                <el-col :span="18">
                    <el-input
                        v-model="entity.count"
                        oninput="value=value.replace(/[^\d]/g,'')"
                        placeholder="请输入执行次数"
                    />
                </el-col>
            </el-form-item>
            <el-form-item
                label="执行间隔"
                prop="interval"
                v-if="entity.is_repeat1"
            >
                <el-col :span="18">
                    <el-input
                        v-model="entity.interval"
                        oninput="value=value.replace(/[^\d]/g,'')"
                        placeholder="请输入执行间隔"
                        style="width: 150px"
                    />
                    <span style="color: red; width: 39px; margin-left: 10px"
                        >天</span
                    >
                </el-col>
            </el-form-item>
            <el-form-item label class="clearfix">
                <div style="float: right; margin-top: 20px">
                    <el-button type="primary" @click="submits">提交</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>

<script>
export default {
    props: {
        parentObj: Object,
    },
    data() {
        return {
            dialogVisible: false,
            tableData: [],
            entity: {
                goods_id: "",
                number: "",
                start_time: "",
                duration: "",
                is_repeat: false,
                is_repeat1: false,
                periods_type: "",
                count: "",
                interval: "",
            },
            rules: {
                count: [
                    {
                        required: true,
                        message: "请输入执行次数",
                        trigger: "blur",
                    },
                ],
                interval: [
                    {
                        required: true,
                        message: "请输入执行间隔",
                        trigger: "blur",
                    },
                ],
                duration: [
                    {
                        required: true,
                        message: "请输入执行时长",
                        trigger: "blur",
                    },
                ],
                start_time: [
                    {
                        required: true,
                        message: "请选择开始时间",
                        trigger: "change",
                    },
                ],
                number: [
                    {
                        required: true,
                        message: "请输入马甲数量",
                        trigger: "blur",
                    },
                ],
            },
            rows: {},
        };
    },
    mounted() {},
    methods: {
        submits() {
            if (!this.validateForm()) {
                return;
            }
            const data = {
                period_id: this.entity.goods_id,
                periods_type: this.entity.periods_type,
                nums: Number(this.entity.number),
                start_time: this.entity.start_time,
                duration: this.entity.duration,
                is_repeat: this.entity.is_repeat1 ? 1 : 0,
                repeat_nums: this.entity.count,
                repeat_interval: this.entity.is_repeat1,
            };
            this.$request.article.createVestOp(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.dialogVisible = false;
                    this.parentObj.getData();
                    this.$message.success("操作成功！");
                }
            });
        },
        openForm(rows) {
            this.entity = {
                goods_id: rows.id,
                number: "",
                start_time: "",
                duration: "",
                is_repeat: false,
                is_repeat1: false,
                periods_type: rows.periods_type,
                count: "",
                interval: "",
            };
            this.dialogVisible = true;
        },
        validateForm() {
            let flag = null;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped></style>
