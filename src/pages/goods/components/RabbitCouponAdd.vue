<template>
    <div>
        <el-form
            ref="form"
            :model="entity"
            :rules="rules"
            label-width="120px"
            v-if="dialogVisible"
        >
            <el-form-item label="优惠券ID" prop="coupon_id">
                <el-select
                    v-model="entity.coupon_id"
                    class="rabbitw"
                    clearable
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请输入关键词"
                    :remote-method="
                        (query) => {
                            searchKeyByWineryID(query);
                        }
                    "
                    :loading="loading"
                    @change="searchChange($event)"
                >
                    <el-option
                        v-for="item in couponList"
                        :key="item.id"
                        :label="item.id + '-' + item.coupon_name"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="库存" prop="inventory">
                <el-input
                    v-model="entity.inventory"
                    placeholder="请输入"
                    class="rabbitw"
                />
            </el-form-item>
            <el-form-item label="商品名称" prop="title">
                <el-input
                    v-model="entity.title"
                    placeholder="请输入"
                    class="rabbitw"
                />
            </el-form-item>
            <el-form-item label="一句话介绍" prop="brief">
                <el-input
                    v-model="entity.brief"
                    placeholder="请输入"
                    class="rabbitw"
                />
            </el-form-item>
            <el-form-item label="商品图片" prop="filelist1">
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :dir="dir"
                    :file-list="entity.filelist1"
                    :limit="1"
                    :multiple="true"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item label="优惠券价格">
                <div style="display: flex">
                    <el-input
                        v-model="entity.price"
                        :disabled="true"
                        placeholder="请输入"
                        class="rabbitw"
                    />
                    <!-- <span>{{isPrice?'元':'折'}}</span> -->
                </div>
            </el-form-item>
            <el-form-item label="兔头价格" prop="rabbit_price">
                <el-input
                    v-model="entity.rabbit_price"
                    placeholder="请输入"
                    class="rabbitw"
                />
            </el-form-item>
            <el-form-item label="详情图" prop="filelist2">
                <vos-oss
                    list-type="picture-card"
                    :readonly="false"
                    :showFileList="true"
                    :dir="dir"
                    :file-list="entity.filelist2"
                    :limit="4"
                    :multiple="true"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item label="商品详情" prop="detail">
                <Tinymce
                    ref="editor"
                    @singleValidate="singleValidate"
                    v-model.trim="entity.detail"
                    :height="300"
                />
            </el-form-item>
            <div class="box_flex" style="margin: 0 0 0 31px">
                <el-form-item
                    label="初始量"
                    label-width="80px"
                    prop="limit_number"
                >
                    <el-input
                        v-if="entity.id"
                        v-model="entity.invariant_number"
                        size="mini"
                        style="width: 160px; margin-left: 10px"
                        placeholder="初始量"
                    />
                    <el-input
                        v-else
                        v-model="entity.limit_number"
                        size="mini"
                        style="width: 160px; margin-left: 10px"
                        placeholder="初始量"
                    />
                </el-form-item>
                <el-form-item
                    label="临界值"
                    label-width="80px"
                    prop="critical_value"
                >
                    <el-input
                        v-model="entity.critical_value"
                        size="mini"
                        style="width: 160px; margin-left: 10px"
                        placeholder="临界值"
                    />
                </el-form-item>
                <el-form-item
                    label="自增量"
                    label-width="80px"
                    prop="incremental"
                >
                    <el-input
                        v-model="entity.incremental"
                        size="mini"
                        style="width: 160px; margin-left: 10px"
                        placeholder="自增量"
                    />
                </el-form-item>
                <!-- <el-form-item label-width="20px">
                    <el-button
                        type="primary"
                        style="margin-left: 34px;"
                        size="mini"
                        @click="limitBuy = !limitBuy"
                    >
                        {{ limitBuy ? "取消限购" : "设置限购" }}
                    </el-button>
                </el-form-item> -->
            </div>
            <div style="margin: 10px 33px" v-if="limitBuy">
                <div>
                    限购 每人<el-select
                        v-model="entity.quota_rule.quota_type"
                        size="mini"
                        style="width: 90px"
                        placeholder="请选择"
                    >
                        <el-option label="每次" value="1"> </el-option>
                        <el-option label="永久" value="0"> </el-option>
                    </el-select>
                    限购
                    <el-input
                        v-model="entity.quota_rule.quota_number"
                        size="mini"
                        style="width: 60px; margin-left: 5px"
                        placeholder="件数"
                    />件
                </div>
                <div style="margin: 5px">
                    特殊限购
                    <el-checkbox
                        @change="handleAddrChange"
                        v-model="entity.quota_rule.check_addr"
                        >地区
                    </el-checkbox>
                    <el-checkbox
                        @change="handleLevelChange"
                        v-model="entity.quota_rule.check_level"
                        >等级</el-checkbox
                    >
                    <el-checkbox
                        @change="handleTimeChange"
                        v-model="entity.quota_rule.check_time"
                        >注册时间</el-checkbox
                    >
                </div>
                <div style="display: flex">
                    <div
                        v-if="entity.quota_rule.check_addr"
                        style="width: 327px"
                    >
                        <el-checkbox-group v-model="entity.quota_rule.addrs">
                            <el-checkbox
                                :label="v.id"
                                v-for="(v, i) in cascaderOptions"
                                :key="i"
                            >
                                {{ v.name }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </div>
                    <div
                        style="margin: 0 10px"
                        v-if="entity.quota_rule.check_level"
                    >
                        等级
                        <el-input
                            v-model="entity.quota_rule.min_level"
                            size="mini"
                            style="width: 80px; margin-left: 5px"
                            placeholder="最低等级"
                        />-
                        <el-input
                            v-model="entity.quota_rule.max_level"
                            size="mini"
                            style="width: 80px; margin-left: 5px"
                            placeholder="最高等级"
                        />
                    </div>
                    <div v-if="entity.quota_rule.check_time">
                        注册时间<el-date-picker
                            v-model="entity.quota_rule.register_time"
                            size="mini"
                            value-format="yyyy-mm-dd"
                            style="margin-left: 5px"
                            type="date"
                            placeholder="选择日期"
                        >
                        </el-date-picker>
                    </div>
                </div>
            </div>
            <div class="box_flex">
                <el-form-item label="上架时间" prop="onsale_time">
                    <el-date-picker
                        v-model="entity.onsale_time"
                        @change="changeTime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="datetime"
                        placeholder="选择日期"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="开售时间" prop="sell_time">
                    <el-date-picker
                        v-model="entity.sell_time"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="datetime"
                        placeholder="选择日期"
                    >
                    </el-date-picker>
                </el-form-item>
            </div>
            <div class="box_flex">
                <el-form-item label="下架时间" prop="sold_out_time">
                    <el-date-picker
                        v-model="entity.sold_out_time"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="datetime"
                        placeholder="选择日期"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="发货时间" prop="predict_shipment_time">
                    <el-date-picker
                        v-model="entity.predict_shipment_time"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="datetime"
                        placeholder="选择日期"
                    >
                    </el-date-picker>
                </el-form-item>
            </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="$emit('closeGoodsOpDialog')">取 消</el-button>
            <el-button type="primary" @click="submits">保 存</el-button>
        </div>
    </div>
    <!-- </el-dialog> -->
</template>

<script>
import vosOss from "vos-oss";
import Tinymce from "@/components/Tinymce";
export default {
    components: {
        vosOss,
        Tinymce,
    },
    props: {
        parentObj: Object,
    },
    data() {
        return {
            dialogVisible: false,
            tableData: [],
            couponList: [],
            cascaderOptions: [],
            entity: {
                filelist1: [],
                filelist2: [],
                coupon_id: "",
                title: "",
                brief: "",
                banner_img: "",
                product_img: "",
                detail: "",
                price: "",
                rabbit_price: "",
                limit_number: "",
                critical_value: "",
                quota_rule: "",
                onsale_time: "",
                sell_time: "",
                sold_out_time: "",
            },
            limitBuy: false,
            rules: {
                coupon_id: [
                    {
                        required: true,
                        message: "请选择优惠券",
                        trigger: "change",
                    },
                ],
                title: [
                    {
                        required: true,
                        message: "请输入商品名称",
                        trigger: "blur",
                    },
                ],
                brief: [
                    {
                        required: true,
                        message: "请输入一句话介绍",
                        trigger: "blur",
                    },
                ],
                detail: [
                    {
                        required: true,
                        message: "请输入商品详情",
                        trigger: "blur",
                    },
                ],
                filelist1: [
                    {
                        required: true,
                        message: "请先上传图片",
                        trigger: "change",
                    },
                ],
                filelist2: [
                    {
                        required: true,
                        message: "请先上传图片",
                        trigger: "change",
                    },
                ],
                incremental: [
                    {
                        required: true,
                        message: "请输入自增量",
                        trigger: "blur",
                    },
                ],
                limit_number: [
                    {
                        required: true,
                        message: "请输入初始值",
                        trigger: "blur",
                    },
                ],
                critical_value: [
                    {
                        required: true,
                        message: "请输入临界值",
                        trigger: "blur",
                    },
                ],
                inventory: [
                    {
                        required: true,
                        message: "请输入库存",
                        trigger: "blur",
                    },
                ],
                rabbit_price: [
                    {
                        required: true,
                        message: "请输入售价",
                        trigger: "blur",
                    },
                    {
                        pattern:
                            /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
                        message: "请输入正确售价，可保留两位小数",
                    },
                ],
                onsale_time: [
                    {
                        required: true,
                        message: "请选择上架时间",
                        trigger: "change",
                    },
                ],
                sold_out_time: [
                    {
                        required: true,
                        message: "请选择下架时间",
                        trigger: "change",
                    },
                ],
                sell_time: [
                    {
                        required: true,
                        message: "请选择开售时间",
                        trigger: "change",
                    },
                ],
                predict_shipment_time: [
                    {
                        required: true,
                        message: "请选择发货时间",
                        trigger: "change",
                    },
                ],
            },
            rows: {},
            dir: "vinehoo/rabbit-coupon/",
            loading: false,
            isPrice: true,
        };
    },
    mounted() {},
    methods: {
        submits() {
            if (!this.validateForm()) {
                return;
            }
            this.entity.banner_img = this.entity.filelist1.join(",");
            this.entity.product_img = this.entity.filelist2.join(",");
            if (this.entity.id) {
                this.$request.article
                    .rabbitCouponUpdate(this.entity)
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.$emit("closeGoodsOpDialog");
                            this.$message.success("操作成功！");
                        }
                    });
            } else {
                this.$request.article
                    .rabbitCouponAdd(this.entity)
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.$emit("closeGoodsOpDialog");
                            this.$message.success("操作成功！");
                        }
                    });
            }
        },
        searchChange(val, catype, index) {
            this.entity.coupon_id = val;
            const coupons = this.couponList.find(
                (f) => f.id == this.entity.coupon_id
            );
            this.entity.price = coupons.coupon_face_value;
            this.isPrice =
                coupons.classify_id == 1 || coupons.classify_id == 2
                    ? true
                    : false;
        },
        async searchKeyByWineryID(keys, catype, index) {
            if (keys != "") {
                this.loading = true;
                this.$request.article
                    .getCoupon({
                        id: keys,
                    })
                    .then((res) => {
                        this.loading = false;
                        if (res.data.error_code == 0) {
                            this.couponList = res.data.data.list || [];
                        }
                    });
            }
        },
        //下架时间自动填充
        changeTime() {
            if (!this.entity.onsale_time) {
                this.entity.sold_out_time = "";
                return;
            }
            this.entity.sold_out_time = this.addDate(
                this.entity.onsale_time,
                7
            );
            this.entity.sell_time = this.addDate(this.entity.onsale_time, 0);
        },
        openForm(rows) {
            this.rows = rows;
            this.entity = {
                coupon_id: "",
                title: "",
                brief: "",
                banner_img: "",
                product_img: "",
                detail: "",
                price: "",
                rabbit_price: "",
                limit_number: "",
                critical_value: "",
                quota_rule: "",
                onsale_time: "",
                sell_time: "",
                sold_out_time: "",
                filelist1: [],
                filelist2: [],
                quota_rule: {
                    check_addr: false,
                    check_level: false,
                    check_time: false,
                    quota_type: "1",
                    quota_number: 9999,
                    register_time: "",
                    min_level: "",
                    max_level: "",
                    addrs: [],
                    district: "",
                    rank: "",
                },
            };
            this.dialogVisible = true;
            this.getAddress();
            if (rows) {
                this.$request.article
                    .getRabbitCouponDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.filelist1 = res.data.data.banner_img;
                            res.data.data.filelist2 = res.data.data.product_img;
                            if (res.data.data.quota_rule) {
                                res.data.data.quota_rule = JSON.parse(
                                    res.data.data.quota_rule
                                );
                                if (res.data.data.quota_rule) {
                                    if (
                                        res.data.data.quota_rule.check_addr ||
                                        res.data.data.quota_rule.check_level ||
                                        res.data.data.quota_rule.check_time
                                    ) {
                                        this.limitBuy = true;
                                    }
                                }
                            }
                            this.entity = res.data.data;
                        }
                    });
            }
        },
        //获取地区
        getAddress() {
            this.$request.article.getAddress().then((res) => {
                this.cascaderOptions = res.data.data.list;
            });
        },
        handleTimeChange() {
            if (!this.entity.quota_rule.check_time) {
                this.entity.quota_rule.register_time = "";
            }
        },
        //特殊限购
        handleLevelChange() {
            if (!this.entity.quota_rule.check_level) {
                this.entity.quota_rule.max_level = "";
                this.entity.quota_rule.min_level = "";
            }
        },
        handleAddrChange() {
            if (this.entity.quota_rule.check_addr) {
                // this.cascaderOptions.map(item => {
                //     this.entity.quota_rule.addrs.push(item.id)
                // })
            } else {
                this.entity.quota_rule.addrs = [];
            }
        },
        //添加时间
        addDate(date, days) {
            if (days == undefined) {
                days = 1;
            }
            var date = new Date(date);
            date.setDate(date.getDate() + days);
            var month = date.getMonth() + 1;
            var day = date.getDate();
            return (
                date.getFullYear() +
                "-" +
                this.getFormatDate(month) +
                "-" +
                this.getFormatDate(day) +
                " " +
                this.getFormatDate(date.getHours()) +
                ":" +
                this.getFormatDate(date.getMinutes()) +
                ":" +
                this.getFormatDate(date.getSeconds())
            );
        },
        // 日期月份/天的显示，如果是1位数，则在前面加上'0'
        getFormatDate(arg) {
            if (arg == undefined || arg == "") {
                return "00";
            }
            var re = arg + "";
            if (re.length < 2) {
                re = "0" + re;
            }
            return re;
        },
        singleValidate() {
            let flag = null;
            this.$refs["form"].validateField("detail", (valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
        validateForm() {
            let flag = null;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.rabbitw {
    width: 500px;
}
.dialog-footer {
    display: flex;
    justify-content: center;
}
</style>
