<template>
    <div class="order-layout">
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="待上架" name="beshelved">
                <div class="order-form">
                    <el-card>
                        <el-date-picker v-model="payOrderTimes" @change="payOrderTimesChange" size="mini"
                            type="datetimerange" range-separator="-" class="m-r-10" value-format="yyyy-MM-dd HH:mm:ss"
                            start-placeholder="上架时间-开始日期" end-placeholder="上架时间-结束日期"
                            :default-time="['00:00:00', '23:59:59']">
                        </el-date-picker>
                        <el-date-picker v-model="payOrderTimes" @change="payOrderTimesChange" size="mini"
                            type="datetimerange" range-separator="-" class="m-r-10" value-format="yyyy-MM-dd HH:mm:ss"
                            start-placeholder="下架时间-开始日期" end-placeholder="下架时间-结束日期"
                            :default-time="['00:00:00', '23:59:59']">
                        </el-date-picker>
                        <el-date-picker v-model="payOrderTimes" @change="payOrderTimesChange" size="mini"
                            type="datetimerange" range-separator="-" class="m-r-10" value-format="yyyy-MM-dd HH:mm:ss"
                            start-placeholder="发货时间-开始日期" end-placeholder="发货时间-结束日期"
                            :default-time="['00:00:00', '23:59:59']">
                        </el-date-picker>
                        <!-- <el-button @click="reset" size="mini">重置</el-button> -->
                        <div class="action-btn">
                            <el-button type="warning" size="mini" @click="search">查询</el-button>
                            <!-- <el-button type="success" size="mini">导出</el-button> -->
                        </div>
                    </el-card>
                </div>
                <!-- <el-empty></el-empty> -->
                <el-card v-for="(i, k) in 5" :key="k" shadow="hover">
                    <div class="good-card">
                        <div class="good-cardinfo">
                            <div class="good-head">
                                <div class="good-tag">
                                    <el-tag type="primary" size="mini">地采</el-tag>
                                    <el-tag type="primary" size="mini">预售</el-tag>
                                    <el-tag type="warning" size="mini">代发</el-tag>
                                    <el-tag type="success" size="mini">渠道</el-tag>
                                </div>
                                <div class="good-title">
                                    <el-link :underline="false">
                                        {{
                      "73640 [德国必喝酒庄|一 杯水果炸弹] Bassermann Jordan DEIDESHEIM Riesling Trocken 2017Jordan DEIDESHEIM Riesling Trocken 2017"
                    }}
                                    </el-link>
                                </div>
                            </div>
                            <div class="good-content">
                                <div class="good-info">
                                    <p>国家：<el-link :underline="false">意大利</el-link>
                                    </p>
                                    <p>类型：<el-link :underline="false">干红</el-link>
                                    </p>
                                    <p>容量：<el-link :underline="false">750ml</el-link>
                                    </p>
                                    <p>售价：<el-link :underline="false">88.0</el-link>
                                    </p>
                                </div>
                                <!-- <el-link :underline="false"></el-link> -->
                                <div class="good-popularize">
                                    <p>文案：<el-link :underline="false">吴思明</el-link>
                                    </p>
                                    <p>采购：<el-link :underline="false">夏振炎</el-link>
                                    </p>
                                    <p>运营：<el-link :underline="false">程洪洲</el-link>
                                    </p>
                                    <p>审核：<el-link :underline="false">朱成浩</el-link>
                                    </p>
                                </div>
                                <div class="good-time">
                                    <p>
                                        上架：<el-link :underline="false">2020-07-10 20：20：20</el-link>
                                    </p>
                                    <p>
                                        开售：<el-link :underline="false">2020-07-10 20：20：20</el-link>
                                    </p>
                                    <p>
                                        下架：<el-link :underline="false">2020-07-10 20：20：20</el-link>
                                    </p>
                                    <p>
                                        发货：<el-link :underline="false">2020-07-10 20：20：20</el-link>
                                    </p>
                                </div>
                                <div class="good-stock">
                                    <p>库存：<el-link :underline="false">24</el-link>
                                    </p>
                                    <p>已售：<el-link :underline="false">14</el-link>
                                    </p>
                                    <p>已购：<el-link :underline="false">7单/8份</el-link>
                                    </p>
                                    <p>前端：<el-link :underline="false">9/18</el-link>
                                    </p>
                                </div>
                                <div class="good-saleinfo">
                                    <p>套餐：<el-link :underline="false">2</el-link>
                                    </p>
                                    <div class="good-remark">
                                        <p>
                                            备注:<el-link :underline="false">时间，备注人</el-link>
                                        </p>
                                        <el-button type="primary" size="mini">查看</el-button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="good-status">
                            <p>{{ "待上架" }}</p>
                        </div>
                        <div class="opration">
                            <el-input v-model="num" size="mini"></el-input>
                            <el-button type="primary" size="mini" @click="edit">编辑</el-button>
                            <el-button type="warning" size="mini">达人说</el-button>
                            <el-button type="info" size="mini">待上架</el-button>
                        </div>
                    </div>
                </el-card>
            </el-tab-pane>
            <el-tab-pane label="在售中" name="onsale">
                <div class="order-form">
                    <el-card>
                        <el-date-picker v-model="payOrderTimes" @change="payOrderTimesChange" size="mini"
                            type="datetimerange" range-separator="-" class="m-r-10" value-format="yyyy-MM-dd HH:mm:ss"
                            start-placeholder="上架时间-开始日期" end-placeholder="上架时间-结束日期"
                            :default-time="['00:00:00', '23:59:59']">
                        </el-date-picker>
                        <el-date-picker v-model="payOrderTimes" @change="payOrderTimesChange" size="mini"
                            type="datetimerange" range-separator="-" class="m-r-10" value-format="yyyy-MM-dd HH:mm:ss"
                            start-placeholder="下架时间-开始日期" end-placeholder="下架时间-结束日期"
                            :default-time="['00:00:00', '23:59:59']">
                        </el-date-picker>
                        <el-date-picker v-model="payOrderTimes" @change="payOrderTimesChange" size="mini"
                            type="datetimerange" range-separator="-" class="m-r-10" value-format="yyyy-MM-dd HH:mm:ss"
                            start-placeholder="发货时间-开始日期" end-placeholder="发货时间-结束日期"
                            :default-time="['00:00:00', '23:59:59']">
                        </el-date-picker>
                        <!-- <el-button @click="reset" size="mini">重置</el-button> -->
                        <div class="action-btn">
                            <el-button type="warning" size="mini" @click="search">查询</el-button>
                            <!-- <el-button type="success" size="mini">导出</el-button> -->
                        </div>
                    </el-card>
                </div>
                <!-- <el-empty></el-empty> -->
                <el-card v-for="(i, k) in 5" :key="k" shadow="hover">
                    <div class="good-card">
                        <div class="good-cardinfo">
                            <div class="good-head">
                                <div class="good-tag">
                                    <el-tag type="primary" size="mini">地采</el-tag>
                                    <el-tag type="primary" size="mini">预售</el-tag>
                                    <el-tag type="warning" size="mini">代发</el-tag>
                                    <el-tag type="success" size="mini">渠道</el-tag>
                                </div>
                                <div class="good-title">
                                    <el-link :underline="false">
                                        {{
                      "73640 [德国必喝酒庄|一 杯水果炸弹] Bassermann Jordan DEIDESHEIM Riesling Trocken 2017Jordan DEIDESHEIM Riesling Trocken 2017"
                    }}
                                    </el-link>
                                </div>
                            </div>
                            <div class="good-content">
                                <div class="good-info">
                                    <p>国家：<el-link :underline="false">意大利</el-link>
                                    </p>
                                    <p>类型：<el-link :underline="false">干红</el-link>
                                    </p>
                                    <p>容量：<el-link :underline="false">750ml</el-link>
                                    </p>
                                    <p>售价：<el-link :underline="false">88.0</el-link>
                                    </p>
                                </div>
                                <!-- <el-link :underline="false"></el-link> -->
                                <div class="good-popularize">
                                    <p>文案：<el-link :underline="false">吴思明</el-link>
                                    </p>
                                    <p>采购：<el-link :underline="false">夏振炎</el-link>
                                    </p>
                                    <p>运营：<el-link :underline="false">程洪洲</el-link>
                                    </p>
                                    <p>审核：<el-link :underline="false">朱成浩</el-link>
                                    </p>
                                </div>
                                <div class="good-time">
                                    <p>
                                        上架：<el-link :underline="false" v-if="editInput">2020-07-10 20：20：20</el-link>
                                        <el-input placeholder="" size="normal" clearable v-else></el-input>

                                        <i class="el-icon-edit"></i>
                                    </p>
                                    <p>
                                        开售：<el-link :underline="false">2020-07-10 20：20：20</el-link>
                                        <i class="el-icon-edit"></i>
                                    </p>
                                    <p>
                                        下架：<el-link :underline="false">2020-07-10 20：20：20</el-link>
                                        <i class="el-icon-edit"></i>
                                    </p>
                                    <p>
                                        发货：<el-link :underline="false">2020-07-10 20：20：20</el-link>
                                        <i class="el-icon-edit"></i>
                                    </p>
                                </div>
                                <div class="good-stock">
                                    <p>库存：<el-link :underline="false">24</el-link>
                                    </p>
                                    <p>已售：<el-link :underline="false">14</el-link>
                                    </p>
                                    <p>已购：<el-link :underline="false">7单/8份</el-link>
                                    </p>
                                    <p>前端：<el-link :underline="false">9/18</el-link>
                                    </p>
                                </div>
                                <div class="good-saleinfo">
                                    <p>套餐：<el-link :underline="false">2</el-link>
                                    </p>
                                    <div class="good-remark">
                                        <p>
                                            备注:<el-link :underline="false">时间，备注人</el-link>
                                        </p>
                                        <el-button type="primary" size="mini">查看</el-button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="good-status">
                            <p>{{ "在售中" }}</p>
                        </div>
                        <div class="opration">
                            <el-input v-model="num" size="mini"></el-input>
                            <el-button type="primary" size="mini" @click="edit">编辑</el-button>
                            <el-button type="warning" size="mini">达人说</el-button>
                            <el-button type="info" size="mini">待上架</el-button>
                        </div>
                    </div>
                </el-card>
            </el-tab-pane>
            <el-tab-pane label="已下架" name="removed">
                <div class="order-form">
                    <el-card>
                        <el-date-picker v-model="payOrderTimes" @change="payOrderTimesChange" size="mini"
                            type="datetimerange" range-separator="-" class="m-r-10" value-format="yyyy-MM-dd HH:mm:ss"
                            start-placeholder="上架时间-开始日期" end-placeholder="上架时间-结束日期"
                            :default-time="['00:00:00', '23:59:59']">
                        </el-date-picker>
                        <el-date-picker v-model="payOrderTimes" @change="payOrderTimesChange" size="mini"
                            type="datetimerange" range-separator="-" class="m-r-10" value-format="yyyy-MM-dd HH:mm:ss"
                            start-placeholder="下架时间-开始日期" end-placeholder="下架时间-结束日期"
                            :default-time="['00:00:00', '23:59:59']">
                        </el-date-picker>
                        <el-date-picker v-model="payOrderTimes" @change="payOrderTimesChange" size="mini"
                            type="datetimerange" range-separator="-" class="m-r-10" value-format="yyyy-MM-dd HH:mm:ss"
                            start-placeholder="发货时间-开始日期" end-placeholder="发货时间-结束日期"
                            :default-time="['00:00:00', '23:59:59']">
                        </el-date-picker>
                        <!-- <el-button @click="reset" size="mini">重置</el-button> -->
                        <div class="action-btn">
                            <el-button type="warning" size="mini" @click="search">查询</el-button>
                            <!-- <el-button type="success" size="mini">导出</el-button> -->
                        </div>
                    </el-card>
                </div>
                <!-- <el-empty></el-empty> -->
                <el-card v-for="(i, k) in 5" :key="k" shadow="hover">
                    <div class="good-card">
                        <div class="good-cardinfo">
                            <div class="good-head">
                                <div class="good-tag">
                                    <el-tag type="primary" size="mini">地采</el-tag>
                                    <el-tag type="primary" size="mini">预售</el-tag>
                                    <el-tag type="warning" size="mini">代发</el-tag>
                                    <el-tag type="success" size="mini">渠道</el-tag>
                                </div>
                                <div class="good-title">
                                    <el-link :underline="false">
                                        {{
                      "73640 [德国必喝酒庄|一 杯水果炸弹] Bassermann Jordan DEIDESHEIM Riesling Trocken 2017Jordan DEIDESHEIM Riesling Trocken 2017"
                    }}
                                    </el-link>
                                </div>
                            </div>
                            <div class="good-content">
                                <div class="good-info">
                                    <p>国家：<el-link :underline="false">意大利</el-link>
                                    </p>
                                    <p>类型：<el-link :underline="false">干红</el-link>
                                    </p>
                                    <p>容量：<el-link :underline="false">750ml</el-link>
                                    </p>
                                    <p>售价：<el-link :underline="false">88.0</el-link>
                                    </p>
                                </div>
                                <!-- <el-link :underline="false"></el-link> -->
                                <div class="good-popularize">
                                    <p>文案：<el-link :underline="false">吴思明</el-link>
                                    </p>
                                    <p>采购：<el-link :underline="false">夏振炎</el-link>
                                    </p>
                                    <p>运营：<el-link :underline="false">程洪洲</el-link>
                                    </p>
                                    <p>审核：<el-link :underline="false">朱成浩</el-link>
                                    </p>
                                </div>
                                <div class="good-time">
                                    <p>
                                        上架：<el-link :underline="false">2020-07-10 20：20：20</el-link>
                                    </p>
                                    <p>
                                        开售：<el-link :underline="false">2020-07-10 20：20：20</el-link>
                                    </p>
                                    <p>
                                        下架：<el-link :underline="false">2020-07-10 20：20：20</el-link>
                                    </p>
                                    <p>
                                        发货：<el-link :underline="false">2020-07-10 20：20：20</el-link>
                                    </p>
                                </div>
                                <div class="good-stock">
                                    <p>库存：<el-link :underline="false">24</el-link>
                                    </p>
                                    <p>已售：<el-link :underline="false">14</el-link>
                                    </p>
                                    <p>已购：<el-link :underline="false">7单/8份</el-link>
                                    </p>
                                    <p>前端：<el-link :underline="false">9/18</el-link>
                                    </p>
                                </div>
                                <div class="good-saleinfo">
                                    <p>套餐：<el-link :underline="false">2</el-link>
                                    </p>
                                    <div class="good-remark">
                                        <p>
                                            备注:<el-link :underline="false">时间，备注人</el-link>
                                        </p>
                                        <el-button type="primary" size="mini">查看</el-button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="good-status">
                            <p>{{ "已下架" }}</p>
                        </div>
                        <div class="opration">
                            <el-input v-model="num" size="mini"></el-input>
                            <el-button type="primary" size="mini" @click="edit">编辑</el-button>
                            <el-button type="warning" size="mini">达人说</el-button>
                            <el-button type="info" size="mini">待上架</el-button>
                        </div>
                    </div>
                </el-card>
            </el-tab-pane>
            <el-tab-pane label="正在编辑中" name="edit" v-if="isEdit">
                <edit></edit>
            </el-tab-pane>
        </el-tabs>

        <div class="pagination-block" v-if="!isEdit">
            <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="form.page" :page-size="form.limit" :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper" :total="total">
            </el-pagination>
        </div>
    </div>
</template>
<script>
    export default {
        
        data() {
            return {
                getOrderList: [],
                form: {
                    page: 1,
                    limit: 10,
                },
                total: 0,
                isEdit: false,
                activeName: "beshelved",
                editInput: true
            };
        },
        methods: {
            handleClick(tab) {
                console.info(tab);
                if (tab.name != "edit") {
                    this.isEdit = false;
                }
            },
            edit() {
                this.isEdit = true;
                this.activeName = "edit";
            },
            getOrderList() {
                this.tableData = [121];
            },
            search() {
                this.getOrderList();
                this.form.page = 1;
            },
            reset() {
                this.$confirm("您确定要重置所有筛选条件吗?", "重置操作", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(() => {
                    this.payOrderTimes = [];
                    this.form = {
                        page: 1,
                    };
                    this.getOrderList();
                });
            },

            handleSizeChange(val) {
                this.form.page = 1;
                this.form.limit = val;
                this.getOrderList();
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                console.log(`当前页: ${val}`);
                this.form.page = val;
                this.getOrderList();
            },
        },
        mounted() {
            this.getOrderList();
        },
    };
</script>
<style lang="scss" scoped>
    .el-card {
        margin-bottom: 10px;
    }

    .good-card {
        display: flex;
        justify-content: space-between;

        .good-cardinfo {
            width: 80%;

            .good-head {
                width: 100%;
                display: flex;
                justify-content: space-between;
                white-space: nowrap;
                overflow: hidden;

                .good-tag {
                    &>.el-tag {
                        margin-right: 4px;
                    }
                }
            }

            .good-content {
                margin-top: 20px;
                display: flex;
                justify-content: space-between;

                .good-time {
                    width: 200px;

                    &>p {
                        i {
                            margin-left: 5px;
                            color: #409eff;
                            font-size: 14px;
                        }
                    }
                }

                .el-link {
                    font-size: 12px;
                }

                .good-saleinfo {
                    .good-remark {
                        display: flex;
                        align-items: center;
                        height: 30px;

                        &>p {
                            margin-bottom: 0;
                        }

                        &>.el-button {
                            margin-left: 20px;
                        }
                    }
                }
            }
        }

        .good-status {
            width: 50px;
        }

        .opration {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 80px;

            &>.el-button {
                margin-left: 0;
                margin-top: 10px;
            }
        }
    }

    .order-layout {
        .pagination-block {
            text-align: center;
            display: flex;
            justify-content: center;
        }

        .table {
            margin-top: 10px;

            .f-12 {
                font-size: 12px;
            }

            .card {
                margin-bottom: 8px;

                .card-title {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .m-l-8 {
                        margin-left: 10px;
                    }
                }
            }

            .order-main {
                display: flex;

                &>div {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                    display: -moz-box;
                    -moz-line-clamp: 1;
                    -moz-box-orient: vertical;
                    word-wrap: break-word;
                    word-break: break-all;
                    white-space: nowrap;
                    min-width: 200px;
                    margin-right: 10px;

                    color: #333;

                    &>div {
                        display: flex;
                    }

                    b {
                        line-height: 2;
                        opacity: 1;
                        display: inline-block;
                        font-weight: bold;
                    }

                    // width: 30;
                }
            }
        }
    }
</style>
