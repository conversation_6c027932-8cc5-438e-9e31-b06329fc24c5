<template>
    <el-dialog
        title="套餐信息"
        :visible.sync="dialogVisible"
        width="65%"
        :close-on-click-modal="false"
    >
        <div class="article-layout">
            <div class="article-main">
                <el-card>
                    <el-table
                        border
                        size="mini"
                        :data="tableData1"
                        style="width: 100%"
                    >
                        <el-table-column
                            prop="id"
                            label="ID"
                            min-width="40"
                            align="center"
                        />
                        <el-table-column
                            prop="package_name"
                            label="套餐名称"
                            min-width="120"
                            align="center"
                        />
                        <el-table-column
                            prop="costprice"
                            :label="is_online ? '简码' : '参考成本'"
                            min-width="120"
                            align="center"
                        >
                            <template slot-scope="{ row }">
                                <div v-for="(v, i) in row.product" :key="i">
                                    <span v-if="!is_online">
                                        {{ v.short_code + "/" + v.costprice }}
                                    </span>
                                    <span v-if="is_online">
                                        {{ v.short_code }}
                                    </span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="costprice"
                            label="包材费+运费"
                            min-width="120"
                            align="center"
                        >
                            <template slot-scope="{ row }">
                                <span>
                                    {{
                                        row.express_fee.package_materials_fee
                                    }}元 </span
                                >+
                                <span>
                                    {{ row.express_fee.express_fee }}元
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="price"
                            label="售价"
                            min-width="80"
                            align="center"
                        />
                        <el-table-column
                            prop="market_price"
                            label="市场价"
                            min-width="80"
                            align="center"
                        />
                        <el-table-column
                            prop="warehouse"
                            label="毛利(实际毛利)"
                            min-width="140"
                            align="center"
                            v-if="!is_online"
                        >
                            <template slot-scope="{ row }">
                                {{
                                    (row.price - row.totlecostprice).toFixed(2)
                                }}
                                ({{
                                    (
                                        row.price -
                                        row.totlecostprice -
                                        row.express_fee.package_materials_fee -
                                        row.express_fee.express_fee
                                    ).toFixed(2)
                                }})
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="warehouse"
                            label="毛利率(实际毛利率)"
                            min-width="140"
                            align="center"
                            v-if="!is_online"
                        >
                            <template slot-scope="{ row }">
                                {{
                                    (
                                        ((row.price - row.totlecostprice) /
                                            Number(row.price)) *
                                        100
                                    ).toFixed(2)
                                }}% ({{
                                    (
                                        ((row.price -
                                            row.totlecostprice -
                                            row.express_fee
                                                .package_materials_fee -
                                            row.express_fee.express_fee) /
                                            Number(row.price)) *
                                        100
                                    ).toFixed(2)
                                }}%)
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
            </div>
            <!--  <div class="pagination-block">
            <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="query.page" :page-size="query.limit" :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper" :total="total">
            </el-pagination>
        </div> -->
        </div>
    </el-dialog>
</template>

<script>
function debounce(func, wait = 400) {
    let timeout;
    return function (event) {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            func.call(this, event);
        }, wait);
    };
}
export default {
    props: {
        parentObj: Object,
    },
    data() {
        return {
            dialogVisible: false,
            btnVisible: false,
            tableData: [],
            tableData1: [],
            query: {
                page: 1,
                limit: 10,
                buyer_review_status: "",
                periods_type: "",
                periods: "",
                title: "",
            },
            total: 0,
            rows: {},
            btntype: 1,
            btntype1: 1,
            is_online: false,
        };
    },
    computed: {},
    mounted() {},
    methods: {
        openForm(rows, is_online) {
            this.is_online = is_online ? true : false;
            console.log(is_online);
            this.rows = rows;
            this.dialogVisible = true;
            this.getPackData();
        },
        getPackData() {
            let params = {
                period: this.rows.id,
                periods_type: this.rows.periods_type,
            };
            this.$request.article.productList(params).then((res) => {
                if (res.data.error_code == 0) {
                    res.data.data.map((item) => {
                        item.flag = false;
                        item.nums = "";
                        item.totleInventory = "";
                        item.totlecostprice = 0;
                        item.product.map((child, i) => {
                            item.totlecostprice =
                                Number(item.totlecostprice) +
                                Number(child.costprice * child.nums);
                        });
                    });
                    this.tableData1 = res.data.data;
                }
            });
        },
        changeType(btntype, type) {
            if (type == 1) {
                this.btntype = btntype;
            } else {
                this.btntype1 = btntype;
            }
        },
        getchannel(type) {
            console.log(type, 12323);
            let channel = "flash";
            switch (type) {
                case 0:
                    channel = "flash";
                    break;
                case 1:
                    channel = "second";
                    break;
                case 2:
                    channel = "cross";
                    break;
                case 3:
                    channel = "leftover";
                    break;
            }
            return channel;
        },
        handleEdit(row) {
            if (!row.nums) {
                this.$message.error("请输入要修改的库存或者总库存！");
                return;
            }
            this.$confirm("你确定要修改库存吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    let params = {
                        period_id: row.period,
                        product_id: row.product_id,
                        nums: Number(row.nums),
                        action: this.btntype == 1 ? "inc" : "dec",
                    };
                    this.$request.article
                        .updateInventory(params)
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("操作成功！");
                                this.getData();
                            }
                        });
                })
                .catch(() => {
                    //几点取消的提示
                });
        },
        search() {
            this.query.page = 1;
            this.getOrderList();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getOrderList();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getOrderList();
        },
    },
};
</script>
<style>
.stoct_wrap .el-input__inner {
    padding-left: 55px !important;
}
</style>
<style lang="scss" scoped>
.stoct_wrap {
    .el-input {
    }

    .stock_btn {
        position: absolute;
        top: 17px;
        z-index: 1;

        .action_btn {
            color: #66b1ff;
        }

        > span {
            color: #cccccc;
            padding: 10px 5px;
            font-size: 14px;
            border-right: 1px solid #ddd;
            cursor: pointer;
        }
    }
}

.article-layout {
    .article-main {
        .stock_title {
            margin: 10px;
        }

        margin: 10px 0;

        /deep/ .el-table .warning-row {
            background: oldlace;
        }

        /deep/ .el-table .danger-row {
            background: oldlace;
        }

        /deep/ .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
</style>
