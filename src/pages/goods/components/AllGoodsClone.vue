<template>
    <div class="order-layout">
        <!-- 全部商品列表（兔头除外） -->
        <div class="order-form">
            <el-card>
                <div class="box_flex box_wrap">
                    <div>
                        <el-input
                            v-model="query.periods"
                            class="w-mini m-r-10"
                            placeholder="期数"
                            size="mini"
                            @keyup.enter.native="search"
                            clearable
                        >
                        </el-input>
                    </div>
                    <div>
                        <el-input
                            @keyup.enter.native="search"
                            v-model="query.title"
                            class="w-normal m-r-10"
                            placeholder="标题"
                            clearable
                            size="mini"
                        >
                        </el-input>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.product_category"
                            filterable
                            size="mini"
                            placeholder="类别"
                            clearable
                        >
                            <el-option
                                v-for="item in typeOptions"
                                :key="item.label"
                                :label="item.label"
                                :value="item.label"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-input
                            size="mini"
                            class="w-mini m-r-10"
                            v-model="query.short_code"
                            placeholder="简码"
                            @keyup.enter.native="search"
                            clearable
                        >
                        </el-input>
                    </div>

                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.product_type"
                            filterable
                            remote
                            clearable
                            :loading="loadings"
                            reserve-keyword
                            placeholder="产品类型"
                            size="mini"
                            :remote-method="remoteMethod"
                        >
                            <el-option
                                v-for="(item, index) in product_typeOptions"
                                :key="index"
                                :label="item.name"
                                :value="item.name"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.creator_id"
                            filterable
                            size="mini"
                            placeholder="文案制作人"
                            clearable
                        >
                            <el-option
                                v-for="item in options"
                                :key="item.id"
                                :label="item.realname"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.supplier_id"
                            filterable
                            clearable
                            placeholder="商家名称"
                            size="mini"
                        >
                            <el-option
                                v-for="(item, index) in merchantList"
                                :key="index"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-large m-r-10"
                            v-model="query.periods_type_arr"
                            filterable
                            multiple
                            clearable
                            placeholder="商品频道（可多选）"
                            size="mini"
                        >
                            <el-option
                                v-for="(item, index) in periodsTypeList"
                                :key="index"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.product_channel"
                            filterable
                            clearable
                            placeholder="商家产品渠道"
                            size="mini"
                        >
                            <el-option
                                v-for="(item, index) in productChannelOptions"
                                :key="index"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>

                    <!-- <div>
                        <el-select class="w-mini m-r-10" v-model="query.product_category" filterable size="mini"
                            placeholder="排序" clearable>
                            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div> -->
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.import_type"
                            filterable
                            size="mini"
                            placeholder="进口类型"
                            clearable
                        >
                            <el-option
                                v-for="item in import_typeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.onsale_review_status"
                            filterable
                            size="mini"
                            placeholder="状态"
                            clearable
                        >
                            <el-option
                                v-for="item in statusOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>

                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.buyer_id"
                            filterable
                            size="mini"
                            placeholder="采购人"
                            clearable
                        >
                            <el-option
                                v-for="item in buyerOptions"
                                :key="item.id"
                                :label="item.realname"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-input
                            size="mini"
                            class="w-mini m-r-10"
                            v-model="query.country"
                            placeholder="国家"
                            @keyup.enter.native="search"
                            clearable
                        >
                        </el-input>
                    </div>
                    <el-date-picker
                        v-model="query.time"
                        size="mini"
                        type="datetimerange"
                        range-separator="-"
                        class="m-r-10"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="上架时间-开始日期"
                        end-placeholder="上架时间-结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                    <el-date-picker
                        v-model="query.time1"
                        size="mini"
                        type="datetimerange"
                        range-separator="-"
                        class="m-r-10"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="下架时间-开始日期"
                        end-placeholder="下架时间-结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                    <el-date-picker
                        v-model="query.time2"
                        size="mini"
                        type="datetimerange"
                        range-separator="-"
                        class="m-r-10"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="发货时间-开始日期"
                        end-placeholder="发货时间-结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                    <el-select
                        class="w-mini m-r-10"
                        v-model="query.sales_model"
                        filterable
                        size="mini"
                        placeholder="售卖模式"
                        clearable
                    >
                        <el-option
                            v-for="item in [
                                { value: 1, text: '代发' },
                                { value: 2, text: '订金' },
                                { value: 3, text: '预售' },
                            ]"
                            :key="item.value"
                            :label="item.text"
                            :value="item.value"
                        />
                    </el-select>
                    <!-- <el-button @click="reset" size="mini">重置</el-button> -->
                    <div class="action-btn">
                        <el-button type="warning" size="mini" @click="search"
                            >查询</el-button
                        >
                        <!-- <el-button type="success" size="mini">导出</el-button> -->
                    </div>
                </div>
            </el-card>
        </div>
        <!-- <el-empty></el-empty> -->
        <el-card v-for="(v, k) in tableData" :key="k" shadow="hover">
            <div class="good-card">
                <div class="good-cardinfo">
                    <div class="good-head">
                        <div class="good-tag">
                            <el-tag
                                v-if="v.is_deposit_period"
                                type="danger"
                                size="mini"
                                effect="dark"
                                >订金</el-tag
                            >
                            <el-tag type="primary" size="mini">{{
                                periods_typeTxt[v.periods_type]
                            }}</el-tag>
                            <el-tag type="primary" size="mini">
                                <span v-if="v.periods_type !== 9">{{
                                    v.import_type | import_typeFormat
                                }}</span>
                                <span v-if="v.periods_type === 9">{{
                                    v.product_channel | vmall_import_typeFormat
                                }}</span>
                            </el-tag>
                            <el-tag
                                type="primary"
                                size="mini"
                                v-if="v.is_presell"
                                >{{ v.is_presell ? "预售" : "" }}</el-tag
                            >
                            <el-tag
                                type="warning"
                                size="mini"
                                v-if="v.is_supplier_delivery"
                            >
                                {{ v.is_supplier_delivery ? "代发" : "" }}
                            </el-tag>
                            <el-tag
                                type="success"
                                size="mini"
                                v-if="v.is_channel"
                            >
                                {{ v.is_channel == 1 ? "渠道" : "" }}</el-tag
                            >
                        </div>
                        <div class="good-title">
                            <el-link
                                :underline="false"
                                class="m-r-10"
                                @click="copy(v.id)"
                            >
                                {{ v.id }}
                            </el-link>
                            <el-link
                                v-if="v.encrypt_id"
                                :underline="false"
                                class="m-r-10"
                                @click="copy(`encrypt_id=${v.encrypt_id}`)"
                            >
                                encrypt_id
                            </el-link>
                            <el-link :underline="false">
                                <span @click="$viewPcGoods(v.id)">{{
                                    v.title
                                }}</span>
                            </el-link>
                        </div>
                        <el-tag
                            v-if="v.is_support_reduction"
                            type="danger"
                            size="mini"
                            effect="dark"
                            style="margin-left: 5px"
                            >减</el-tag
                        >
                        <el-tag
                            v-if="v.is_support_coupon"
                            type="danger"
                            size="mini"
                            effect="dark"
                            style="margin-left: 5px"
                            >券</el-tag
                        >
                    </div>
                    <div class="good-content">
                        <div class="good-info">
                            <p>
                                国家：<el-link :underline="false">{{
                                    v.country.join(",")
                                        ? v.country.join(",")
                                        : "-"
                                }}</el-link>
                            </p>
                            <p>
                                类型：<el-link :underline="false">{{
                                    v.product_category.join(",")
                                        ? v.product_category.join(",")
                                        : "-"
                                }}</el-link>
                            </p>
                            <p>
                                容量：<el-link :underline="false">{{
                                    v.capacity
                                }}</el-link>
                            </p>
                            <p
                                :class="v.price > 0 ? 'p_blue' : ''"
                                style="vertical-align: middle"
                                @click="handlePrice(v)"
                            >
                                <el-link
                                    :underline="false"
                                    v-if="v.price > 0"
                                    class="p_blue ellipsis"
                                    >售价：{{ v.package_prices }}</el-link
                                >
                                <el-link :underline="false" v-else>-</el-link>
                            </p>
                            <p>
                                收款单位：
                                <el-link :underline="false">{{
                                    v.payee_merchant_name || "-"
                                }}</el-link>
                            </p>
                        </div>
                        <!-- <el-link :underline="false"></el-link> -->
                        <div class="good-popularize">
                            <p>
                                文案：<el-link :underline="false">{{
                                    v.creator_name || "-"
                                }}</el-link>
                            </p>
                            <p>
                                采购：<el-link :underline="false">{{
                                    v.buyer_name || "-"
                                }}</el-link>
                            </p>
                            <p>
                                上架：<el-link :underline="false">{{
                                    v.operation_name || "-"
                                }}</el-link>
                            </p>
                            <p>
                                运营：<el-link :underline="false">{{
                                    v.operation_review_name || "-"
                                }}</el-link>
                            </p>
                        </div>
                        <div class="good-time">
                            <div>
                                <div class="p_blue">上架：</div>
                                <!-- @click="updateTime(v)" -->
                                <el-link class="p_blue" :underline="false"
                                    >{{ v.onsale_time | timeFormat
                                    }}<i
                                        class="el-icon-edit"
                                        v-if="
                                            (v.onsale_status == 0 &&
                                                v.onsale_review_status == 3) ||
                                            v.onsale_review_status == 4
                                        "
                                    ></i>
                                </el-link>
                            </div>
                            <div class="p_blue">
                                开售：
                                <el-link class="p_blue" :underline="false"
                                    >{{ v.sell_time | timeFormat
                                    }}<i
                                        class="el-icon-edit"
                                        v-if="
                                            (v.onsale_status == 0 &&
                                                v.onsale_review_status == 3) ||
                                            v.onsale_review_status == 4
                                        "
                                    ></i>
                                </el-link>
                            </div>
                            <div class="p_blue">
                                下架：
                                <el-link class="p_blue" :underline="false"
                                    >{{ v.sold_out_time | timeFormat
                                    }}<i
                                        class="el-icon-edit"
                                        v-if="
                                            (v.onsale_status == 0 &&
                                                v.onsale_review_status == 3) ||
                                            v.onsale_review_status == 4
                                        "
                                    ></i>
                                </el-link>
                            </div>
                            <div class="p_blue">
                                发货：
                                <el-link class="p_blue" :underline="false">
                                    {{ v.predict_shipment_time | timeFormat
                                    }}<i
                                        class="el-icon-edit"
                                        v-if="
                                            (v.onsale_status == 0 &&
                                                v.onsale_review_status == 3) ||
                                            v.onsale_review_status == 4
                                        "
                                    ></i>
                                </el-link>
                            </div>
                        </div>
                        <div class="good-stock">
                            <p class="p_blue" @click="updateStock(v)">
                                库存：<el-link
                                    :underline="false"
                                    class="p_blue"
                                >
                                    {{ v.inventory
                                    }}<i class="el-icon-edit"></i>
                                </el-link>
                            </p>
                            <el-popover
                                placement="top-start"
                                title="已售"
                                width="200"
                                trigger="click"
                                content="已售：显示数值为实际已售出瓶数"
                            >
                                <p class="p_blue" slot="reference">
                                    已售<i
                                        class="fas fa-question-circle"
                                    />：<el-link
                                        @click.stop="handleBuy(v, 1)"
                                        :underline="false"
                                        class="p_blue"
                                    >
                                        {{ v.saled_count }}
                                    </el-link>
                                </p>
                            </el-popover>
                            <el-popover
                                placement="top-start"
                                title="已购"
                                width="200"
                                trigger="click"
                                content="已购：购买人数/套餐数量"
                            >
                                <p slot="reference" class="p_blue">
                                    已购<i
                                        class="fas fa-question-circle"
                                    />：<el-link
                                        :underline="false"
                                        class="p_blue"
                                        @click.stop="handleBuy(v, 2)"
                                    >
                                        {{ v.purchased_person }}/{{
                                            v.purchased_number
                                        }}
                                    </el-link>
                                </p>
                            </el-popover>
                            <p>
                                前端：<el-link :underline="false"
                                    >{{ v.purchased + v.vest_purchased }}/{{
                                        v.limit_number
                                    }}
                                </el-link>
                            </p>
                        </div>
                        <div class="good-saleinfo">
                            <!-- <p>
                                套餐：<el-link :underline="false">{{
                                    v.periods_set_count
                                }}</el-link>
                            </p> -->
                            <div class="good-remark">
                                <p class="p_blue" @click="handleRemark(v)">
                                    备注:<i
                                        class="el-icon-edit"
                                        style="cursor: pointer"
                                    ></i>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="good-status">
                    <!-- <p
                        style="color: #409eff; cursor: pointer"
                        @click="handleStatusClick(v)"
                    >
                        {{ statusTxt(v.onsale_review_status, v.onsale_status) }}
                    </p> -->
                    <el-button
                        type="text"
                        size="mini"
                        @click="handleStatusClick(v)"
                    >
                        {{
                            statusTxt(v.onsale_review_status, v.onsale_status)
                        }}</el-button
                    >
                    <div>
                        <el-button
                            type="text"
                            size="mini"
                            v-if="v.periods_type != 4 && v.periods_type != 5"
                            @click="handleCopy(v)"
                            >复制</el-button
                        >
                    </div>
                    <div>
                        <ShareLink :id="v.id" :encrypt-id="v.encrypt_id" />
                    </div>
                    <el-popover
                        placement="left-start"
                        width="200"
                        trigger="click"
                        @show="getMinipogramCode(v)"
                        style="margin-top: 10px; margin: 0 auto"
                        v-if="
                            v.onsale_review_status == 3 && v.onsale_status == 2
                        "
                    >
                        <img :src="minipogram_code" style="width: 180px" />
                        <div
                            slot="reference"
                            style="color: #409eff; cursor: pointer"
                        >
                            小程序码
                        </div>
                    </el-popover>
                </div>
            </div>
        </el-card>
        <div class="pagination-block" v-if="!isEdit">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <el-dialog
            :visible.sync="dialogVisibleCopy"
            title="提示"
            :close-on-click-modal="false"
            custom-class="dialogwid"
            width="500px"
        >
            <div
                style="
                    display: flex;
                    justify-content: center;
                    margin-bottom: 20px;
                    font-size: 16px;
                "
            >
                请选择要复制到的位置
            </div>
            <div v-if="copyStatus == 0">
                <div>
                    <el-button
                        style="margin-right: 10px"
                        type="primary"
                        size="mini"
                        @click="copys(1)"
                        >文案列表</el-button
                    >

                    <el-radio-group v-model="to_periods">
                        <el-radio
                            :label="v.value"
                            v-for="(v, i) in goodsType"
                            :key="i"
                        >
                            {{ v.label }}</el-radio
                        >
                    </el-radio-group>
                </div>

                <el-button
                    type="warning"
                    size="mini"
                    style="margin-right: 10px; margin-top: 20px"
                    @click="copys(2)"
                    >商品列表</el-button
                >
                <el-radio-group v-model="goodsCopyPeriodsType">
                    <!-- v-show="rows.periods_type === v.value" -->

                    <el-radio
                        v-show="isShowCopy(goodsCopyPeriodsType, v.value)"
                        :label="v.value"
                        v-for="(v, i) in goodsType"
                        :key="i"
                    >
                        {{ v.label }}</el-radio
                    >
                </el-radio-group>
            </div>
            <!-- <div
                v-if="copyStatus == 0"
                style="
                    display: flex;
                    justify-content: center;
                    margin-bottom: 20px;
                    font-size: 16px;
                "
            >
                <div
                    style="
                        display: flex;
                        justify-content: space-between;
                        width: 500px;
                    "
                >
                    <el-button type="primary" size="mini" @click="copys(1)"
                        >文案列表</el-button
                    >
                    <el-button type="primary" size="mini" @click="copys(2)"
                        >商品列表</el-button
                    >
                </div>
            </div> -->
        </el-dialog>
        <remarkList ref="remarklist" :isAll="1"></remarkList>
        <logList ref="logList"></logList>
        <packList ref="packList"></packList>
        <stockList ref="stocklist" :isAll="1" :parentObj="this"></stockList>
        <VestList ref="vestlist" :isAll="1" :parentObj="this"></VestList>
        <BuyList
            ref="buylist"
            :parentObj="this"
            v-if="dialogVisible2"
        ></BuyList>
        <SoldList
            ref="soldlist"
            :parentObj="this"
            v-if="dialogVisible3"
        ></SoldList>
        <ShareLink v-if="dialogVisible1"></ShareLink>
    </div>
</template>
<script>
import copy from "copy-to-clipboard";
import goodsDetail from "../../article/GoodsDetail.vue";
import remarkList from "../../article/remarkList.vue";
import logList from "./LogList.vue";
import packList from "./packList.vue";
import stockList from "./stockList.vue";
import VestList from "./VestList.vue";
import BuyList from "./BuyList.vue";
import SoldList from "./SoldList.vue";
import ShareLink from "@/components/ShareLink.vue";

export default {
    components: {
        remarkList,
        logList,
        packList,
        goodsDetail,
        stockList,
        VestList,
        BuyList,
        SoldList,
        ShareLink,
    },
    props: {
        status: {
            type: Number,
            default: 0,
        },
        periods_type: {
            type: Number,
            default: 0,
        },
    },

    data() {
        return {
            getOrderList: [],
            loadings: false,
            query: {
                page: 1,
                limit: 10,
                periods: "",
                product_type: "",
                title: "",
                short_code: "",
                creator_id: "",
                product_category: "",
                country: "",
                import_type: "",
                periods_type: "",
                product_channel: "",
                onsale_review_status: 3,
                periods_type_arr: [],
                supplier_id: "",
                buyer_review_status: 3,
                time: [],
                buyer_id: "",
                time1: [],
                time2: [],
                sales_model: "",
            },
            buyerOptions: [],
            merchantList: [],
            periods_typeTxt: {
                0: "闪购",
                1: "秒发",
                2: "跨境",
                3: "尾货",
                4: "兔头",
                9: "商家秒发",
            },
            periodsTypeList: [
                { value: 0, label: "闪购" },
                { value: 1, label: "秒发" },
                { value: 2, label: "跨境" },
                { value: 3, label: "尾货" },
                { value: 4, label: "兔头" },
                { value: 9, label: "商家秒发" },
            ],

            tableData: [],
            goodsData: {},
            payOrderTimes: [],
            product_typeOptions: [],
            total: 0,
            isEdit: false,
            activeName: "beshelved",
            editInput: true,
            dialogVisible: false,
            GoodsDetailVisible: false,
            dialogVisible1: false,
            dialogVisible3: false,
            dialogVisible2: false,
            dialogVisibleCopy: false,
            entity: {
                reason: "",
            },
            productChannelOptions: [
                // {
                //     label: "平台",
                //     value: 1,
                // },
                {
                    label: "自有产品",
                    value: 2,
                },
                {
                    label: "酒云采购",
                    value: 3,
                },
            ],
            reasonOptions: [
                {
                    label: "酒瓶信息错误",
                    value: "酒瓶信息错误",
                },
                {
                    label: "年份错误",
                    value: "年份错误",
                },
                {
                    label: "详细资料错误",
                    value: "详细资料错误",
                },
                {
                    label: "采购人不符",
                    value: "采购人不符",
                },
                {
                    label: "其他",
                    value: "其他",
                },
            ],
            typeOptions: [
                {
                    label: "酒类",
                    value: 0,
                },
                {
                    label: "饮料",
                    value: 2,
                },
                {
                    label: "食品",
                    value: 3,
                },
                {
                    label: "物料",
                    value: 4,
                },
                {
                    label: "虚拟",
                    value: 6,
                },
                {
                    label: "酒具",
                    value: 7,
                },
                {
                    label: "其他",
                    value: 5,
                },
            ],
            import_typeOptions: [
                {
                    label: "自进口",
                    value: 0,
                },
                {
                    label: "地采",
                    value: 1,
                },
            ],
            statusOptions: [
                {
                    label: "待绑定",
                    value: 0,
                },
                {
                    label: "待审核",
                    value: 1,
                },
                // {
                //     label: "审批中",
                //     value: 2
                // },
                {
                    label: "已审核",
                    value: 3,
                },
                {
                    label: "待上架",
                    value: 5,
                },
                {
                    label: "待售中",
                    value: 6,
                },
                {
                    label: "在售中",
                    value: 7,
                },
                {
                    label: "已下架",
                    value: 8,
                },
            ],
            rules: {
                reason: [
                    {
                        required: true,
                        message: "请选择驳回原因",
                        trigger: "change",
                    },
                ],
            },
            goodsType: [
                {
                    label: "闪购",
                    value: 0,
                },
                {
                    label: "秒发",
                    value: 1,
                },
                {
                    label: "跨境",
                    value: 2,
                },
                {
                    label: "尾货",
                    value: 3,
                },
            ],
            options: [],
            auditTime: 0,
            goodsinfo: {},
            copyRows: {},
            copy_type: 1,
            copyStatus: 0,
            goodsCopyPeriodsType: 0,
            to_periods: 0,
            minipogram_code: "",
            program_row: {},
        };
    },
    methods: {
        handlePrice(rows) {
            this.$nextTick(() => {
                this.$refs.packList.openForm(rows, true);
            });
        },
        handleStatusClick(rows) {
            this.$nextTick(() => {
                this.$refs.logList.openForm(rows);
            });
        },
        copy(data) {
            copy(data);
            this.$message.success("复制成功");
        },
        getPurchase() {
            this.$request.article
                .purchaseList({
                    type: 2,
                })
                .then((res) => {
                    this.buyerOptions = res.data.data.list;
                });
            this.$request.article
                .purchaseList({
                    type: 3,
                })
                .then((res) => {
                    this.options = res.data.data.list;
                });
        },
        async getMerchantList() {
            const data = {
                page: 1,
                limit: 999,
            };
            const res = await this.$request.article.getMerchantList(data);
            if (res.data.error_code === 0) {
                this.merchantList = res.data.data.list;
            }
        },
        handleBuy(v, type) {
            if (type == 1) {
                this.dialogVisible3 = true;
                this.$nextTick(() => {
                    this.$refs.soldlist.openForm(v);
                });
            } else {
                this.dialogVisible2 = true;
                this.$nextTick(() => {
                    this.$refs.buylist.openForm(v);
                });
            }
        },
        viewGoods(item) {
            window.open(
                this.$BASE.PCDomain +
                    "/pages/goods-detail/goods-detail?id=" +
                    (item.encrypt_id || item.id)
            );
        },
        handleVest(v) {
            this.$nextTick(() => {
                this.$refs.vestlist.openForm(v);
            });
        },
        updateStock(row) {
            this.$refs.stocklist.openForm(row);
        },
        handleStatusClick(rows) {
            this.$nextTick(() => {
                this.$refs.logList.openForm(rows);
            });
        },
        statusTxt(operateStatus, status) {
            let statusTxt = "";
            if (operateStatus == 0) {
                statusTxt = "待绑定";
            } else if (operateStatus == 1) {
                statusTxt = "待审核";
            } else if (operateStatus == 3 && status == 0) {
                statusTxt = "待上架";
            } else if (operateStatus == 3 && status == 2) {
                statusTxt = "在售中";
            } else if (operateStatus == 3 && status == 1) {
                statusTxt = "待售中";
            } else if (operateStatus == 4) {
                statusTxt = "已驳回";
            } else if (status == 3 || status == 4) {
                statusTxt = "已下架";
            }
            return statusTxt;
        },
        HandleClick(rows) {
            this.$refs.goodsop.openForm(rows);
        },
        edit() {
            this.isEdit = true;
            this.activeName = "edit";
        },
        getData() {
            let onsale_review_status = this.query.onsale_review_status;
            let onsale_status = 0;
            const params = {
                periods: this.query.periods,
                country: this.query.country,
                creator_id: this.query.creator_id,
                title: this.query.title,
                product_type: this.query.product_type,
                buyer_id: this.query.buyer_id,
                supplier_id: this.query.supplier_id,
                periods_type_arr: this.query.periods_type_arr.join(","),
                buyer_review_status: 3,
                product_channel: this.query.product_channel,
                product_category: this.query.product_category,
                onsale_review_status: onsale_review_status,
                import_type: this.query.import_type,
                onsale_time_start:
                    (this.query.time && this.query.time[0]) || "",
                onsale_time_end: (this.query.time && this.query.time[1]) || "",
                sold_out_time_start:
                    (this.query.time1 && this.query.time1[0]) || "",
                sold_out_time_end:
                    (this.query.time1 && this.query.time1[1]) || "",
                predict_shipment_time_start:
                    (this.query.time2 && this.query.time2[0]) || "",
                predict_shipment_time_end:
                    (this.query.time2 && this.query.time2[1]) || "",
                periods_list: 1,
                page: this.query.page,
                limit: this.query.limit,
                short_code: this.query.short_code,
                sales_model: this.query.sales_model,
            };
            if (this.query.onsale_review_status > 3) {
                params.onsale_review_status = 3;
                if (this.query.onsale_review_status == 5) {
                    params.onsale_status = 0;
                } else if (this.query.onsale_review_status == 6) {
                    params.onsale_status = 1;
                } else if (this.query.onsale_review_status == 7) {
                    params.onsale_status = 2;
                } else if (this.query.onsale_review_status == 8) {
                    params.onsale_status = 3;
                }
            }
            if (
                this.query.onsale_review_status == 0 ||
                this.query.onsale_review_status == 1
            ) {
                delete params.periods_list;
            }
            this.$request.article.articleList(params).then((res) => {
                if (res.data.error_code == 0) {
                    res.data.data.list.map((item) => {
                        item.onsale_time_visible = true;
                        item.sell_time_visible = true;
                        item.predict_shipment_time_visible = true;
                        item.sold_out_time_visible = true;
                        item.country = item.country;
                        item.product_category = item.product_category;
                        // item.onsale_time = this.timeFormat(item.onsale_time)
                        // item.sell_time = this.timeFormat(item.sell_time)
                        // item.sold_out_time = this.timeFormat(item.sold_out_time)
                        // item.predict_shipment_time = this.timeFormat(item.predict_shipment_time)
                    });
                    this.tableData = res.data.data.list || [];
                    this.total = res.data.data.total;
                }
            });
        },
        // timeFormat(val) {
        //     if (val == '1970-01-01 08:00:00') {
        //         return "-"
        //     } else {
        //         return val
        //     }
        // },
        updateTime(rows, type) {
            let params = {
                period: rows.id,
                periods_type: rows.periods_type,
            };
            if (type == 1) {
                if (!rows.onsale_time) {
                    this.$message.error("请选择上架时间！");
                    return;
                }
                params.onsale_time = rows.onsale_time;
            } else if (type == 2) {
                if (!rows.sell_time) {
                    this.$message.error("请选择开售时间！");
                    return;
                }
                params.sell_time = rows.sell_time;
            } else if (type == 3) {
                if (!rows.sold_out_time) {
                    this.$message.error("请选择下架时间！");
                    return;
                }
                params.sold_out_time = rows.sold_out_time;
            } else if (type == 4) {
                if (!rows.predict_shipment_time) {
                    this.$message.error("请选择发货时间！");
                    return;
                }
                params.predict_shipment_time = rows.predict_shipment_time;
            }
            this.$request.article.goodsUpdate(params).then((res) => {
                if (res.data.error_code == 0) {
                    if (type == 1) {
                        rows.onsale_time_visible = !rows.onsale_time_visible;
                    } else if (type == 2) {
                        rows.sell_time_visible = !rows.sell_time_visible;
                    } else if (type == 3) {
                        rows.sold_out_time_visible =
                            !rows.sold_out_time_visible;
                    } else if (type == 4) {
                        rows.predict_shipment_time_visible =
                            !rows.predict_shipment_time_visible;
                    }
                }
            });
        },
        remoteMethod(query) {
            console.log(query);
            if (query.length >= 1) {
                this.loadings = true;
                setTimeout(() => {
                    let data = {
                        keywords: query,
                    };
                    this.$request.purchase
                        .getProductListForKeywords(data)
                        .then((res) => {
                            this.loadings = false;
                            if (res.data.error_code == 0) {
                                console.log(res.data);
                                this.product_typeOptions = res.data.data.list;
                            }
                        });
                }, 300);
            } else {
                this.goodsOptions = [];
            }
            console.log(this.goodsOptions);
        },
        HandleAudit(rows, review_status, reason) {
            this.GoodsDetailVisible = true;
            this.rows = rows;
            this.auditTime = 5;
            if (rows.periods_type == 0) {
                this.$request.article
                    .getFlashDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.imgList =
                                res.data.data.product_img.split(",");
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            } else if (rows.periods_type == 1) {
                this.$request.article
                    .getSecondDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.imgList =
                                res.data.data.product_img.split(",");
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            } else if (rows.periods_type == 2) {
                this.$request.article
                    .getCrossDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.imgList =
                                res.data.data.product_img.split(",");
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            } else if (rows.periods_type == 3) {
                this.$request.article
                    .getLeftoverDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.imgList =
                                res.data.data.product_img.split(",");
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            }

            this.countDown();
        },
        audit(rows, review_status, reason) {
            // if (review_status == 3 && !this.validateForm()) {
            //     return
            // }
            console.log(rows, 3333333);
            const params = {
                period: rows.id,
                onsale_review_status: review_status,
                reject_reason: reason,
                periods_type: rows.periods_type,
                version: this.goodsinfo.version,
            };
            this.$request.article.updateOnsale(params).then((res) => {
                if (res.data.error_code == 0) {
                    this.getData();
                    if (review_status == 4) {
                        this.dialogVisible = false;
                    }
                    this.GoodsDetailVisible = false;
                    this.dialogVisible1 = false;
                    this.$message.success("操作成功！");
                }
            });
        },
        HandleReject(rows) {
            this.rows = rows;
            this.entity.reason = "";
            this.dialogVisible = !this.dialogVisible;
        },
        rejects() {
            if (this.validateForm()) {
                this.audit(this.rows, 4, this.entity.reason);
            }
        },
        handleRemark(rows) {
            this.$nextTick(() => {
                this.$refs.remarklist.openForm(rows);
            });
        },
        onShelf(rows) {
            this.$confirm("你确定要上架吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.$request.article
                        .updateoffSale({
                            period: rows.id,
                            periods_type: rows.periods_type,
                            onsale_status: 2,
                            operation_id: "1",
                            operation_name: "操作人",
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.query.page = 1;
                                this.getData();
                                this.$message.success("操作成功");
                            }
                        });
                })
                .catch(() => {
                    //几点取消的提示
                });
        },
        offShelf(rows) {
            this.$prompt("请输入下架原因", "下架", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                inputPlaceholder: "请输入下架原因",
                inputValidator: (value) => {
                    if (!value) {
                        return "下架原因不能为空";
                    }
                    if (value.trim().length < 3) {
                        return "下架原因至少需要3个字符";
                    }
                    return true;
                },
            })
                .then(({ value }) => {
                    this.$request.article
                        .updateoffSale({
                            period: rows.id,
                            periods_type: rows.periods_type,
                            onsale_status: 3,
                            operation_id: "1",
                            operation_name: "操作人",
                            remark: value,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.query.page = 1;
                                this.getData();
                                this.$message.success("下架成功");
                            }
                        });
                })
                .catch(() => {
                    // 取消操作
                });
        },
        // handleCopy() {},
        countDown() {
            this.auditTime = this.auditTime - 1;
            if (this.auditTime == 0) {
                return;
            }
            setTimeout(() => {
                this.countDown();
            }, 1000);
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        reset() {
            this.$confirm("您确定要重置所有筛选条件吗?", "重置操作", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.payOrderTimes = [];
                this.form = {
                    page: 1,
                };
                this.getData();
            });
        },
        handleCopy(rows) {
            this.copyStatus = 0;
            this.copyRows = rows;
            this.to_periods = this.copyRows.periods_type;
            this.dialogVisibleCopy = true;
        },
        copys(copy_type) {
            this.copy_type = copy_type;
            // this.copyStatus = 1;
            this.handleConfirm();
        },
        isShowCopy(period, value) {
            if (
                period == value ||
                (period === 0 && value === 3) ||
                (period === 3 && value === 0)
            ) {
                return true;
            } else {
                return false;
            }
        },
        handleConfirm() {
            this.$request.article
                .copyPeriod({
                    period: this.copyRows.id,
                    periods_type: this.copyRows.periods_type,
                    to_periods:
                        this.copy_type == 2
                            ? this.goodsCopyPeriodsType
                            : this.to_periods,
                    copy_type: this.copy_type,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        let url = "";
                        //跳转
                        if (this.copy_type == 2) {
                            if (this.goodsCopyPeriodsType == 0) {
                                url = "/flash?tab=wait";
                            } else if (this.goodsCopyPeriodsType == 1) {
                                url = "/second?tab=wait";
                            } else if (this.goodsCopyPeriodsType == 2) {
                                url = "/crossborder?tab=wait";
                            } else if (this.goodsCopyPeriodsType == 3) {
                                url = "/tailcargo?tab=wait";
                            }

                            this.$router.push(url);
                            this.$emit("targetPage", "wait");
                            // if (this.rows.periods_type != this.to_periods) {
                            //     this.$router.push(url);
                            // } else {
                            //     this.$emit("targetPage", "wait");
                            // }
                        } else {
                            this.$router.push("/article");
                        }
                        this.$message.success("操作成功！");
                    }
                });
        },
        closeCopyDialog() {
            // goodsCopyPeriodsType
            this.goodsCopyPeriodsType = 0;
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
        getMinipogramCode(params) {
            if (params.id != this.program_row.id) {
                this.$request.article
                    .getMinappQrcode({
                        scene: String(params.id),
                        page: "pages/goods-detail/goods-detail",
                    })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            console.warn(res.data.data);
                            this.minipogram_code =
                                "data:image/jpg;base64," + res.data.qrcode;
                            console.warn(this.minipogram_code);
                            this.program_row = params;
                        }
                    });
            }
        },
    },
    filters: {
        vmall_import_typeFormat(val) {
            switch (val) {
                case 2:
                    return "自有产品";
                case 3:
                    return "酒云采购";
                default:
                    return "未知";
            }
        },
        import_typeFormat(val) {
            switch (val) {
                case 0:
                    return "自进口";
                case 1:
                    return "地采";
                case 2:
                    return "跨境";
                default:
                    return "未知";
            }
        },
        timeFormat(val) {
            if (val == "1970-01-01 08:00:00") {
                return "-";
            } else {
                return val;
            }
        },
    },
    mounted() {
        this.getPurchase();
        this.getData();
        this.getMerchantList();
    },
};
</script>
<style lang="scss" scoped>
.ellipsis {
    display: inline-block;
    text-align: left;
    width: 220px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    height: 22px;
    line-height: 22px;
}
.el-card {
    margin-bottom: 10px;
}

.good-card {
    display: flex;
    justify-content: space-between;

    .good-cardinfo {
        width: 80%;

        .p_blue {
            color: #409eff;
            cursor: pointer;
        }

        .good-head {
            width: 100%;
            display: flex;
            justify-content: flex-start;
            white-space: nowrap;
            overflow: hidden;

            .good-tag {
                & > .el-tag {
                    margin-right: 4px;
                }
            }
        }

        .good-content {
            margin-top: 10px;
            display: flex;
            justify-content: space-between;

            .el-icon-edit {
                margin-left: 5px;
                color: #409eff;
                font-size: 14px;
            }

            p {
                margin-bottom: 0;
            }

            .good-time {
                width: 240px;

                > div {
                    display: flex;
                    align-items: center;
                }

                .times {
                    display: flex;

                    i {
                        cursor: pointer;
                        font-size: 20px;
                        color: #409eff;
                        padding: 2px 0 0 3px;
                    }
                }
            }

            .el-link {
                font-size: 12px;
            }

            .good-saleinfo {
                .good-remark {
                    display: flex;
                    align-items: center;
                    height: 30px;

                    & > p {
                        margin-bottom: 0;
                    }

                    & > .el-button {
                        margin-left: 20px;
                    }
                }
            }
        }
    }

    .good-status {
        width: 80px;
        text-align: center;

        .el-button {
            padding: 7px 0;
        }

        > div {
            margin-bottom: 8px;
            white-space: nowrap;
            min-width: 80px;
        }
    }

    .opration {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 80px;

        & > .el-button {
            margin-left: 0;
            margin-top: 10px;
        }
    }
}

.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .table {
        margin-top: 10px;

        .f-12 {
            font-size: 12px;
        }

        .card {
            margin-bottom: 8px;

            .card-title {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;

            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }

                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}

.el-link--default {
    vertical-align: inherit !important;
}
</style>
