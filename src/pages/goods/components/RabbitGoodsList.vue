<template>
    <div class="order-layout">
        <!-- 兔头商品列表 -->
        <div class="order-form">
            <el-card>
                <div class="box_flex box_wrap">
                    <div>
                        <el-input
                            v-model="query.periods"
                            class="w-mini m-r-10"
                            placeholder="期数"
                            @keyup.enter.native="search"
                            size="mini"
                            clearable
                        >
                        </el-input>
                    </div>
                    <div>
                        <el-input
                            v-model="query.title"
                            class="w-normal m-r-10"
                            placeholder="标题"
                            @keyup.enter.native="search"
                            clearable
                            size="mini"
                        >
                        </el-input>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.product_category"
                            filterable
                            size="mini"
                            placeholder="类别"
                            clearable
                        >
                            <el-option
                                v-for="item in typeOptions"
                                :key="item.label"
                                :label="item.label"
                                :value="item.label"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-input
                            size="mini"
                            class="w-mini m-r-10"
                            v-model="query.short_code"
                            placeholder="简码"
                            @keyup.enter.native="search"
                            clearable
                        >
                        </el-input>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.creator_id"
                            filterable
                            size="mini"
                            placeholder="文案制作人"
                            clearable
                        >
                            <el-option
                                v-for="item in options"
                                :key="item.id"
                                :label="item.realname"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.sort_rule"
                            filterable
                            size="mini"
                            placeholder="排序"
                            clearable
                        >
                            <el-option
                                v-for="item in sortOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.import_type"
                            filterable
                            size="mini"
                            placeholder="进口类型"
                            clearable
                        >
                            <el-option
                                v-for="item in import_typeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div v-if="status != 3">
                        <el-select
                            class="w-mini m-r-10"
                            v-model="query.onsale_review_status"
                            filterable
                            size="mini"
                            placeholder="状态"
                            clearable
                        >
                            <el-option
                                v-for="item in statusOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-input
                            size="mini"
                            class="w-mini m-r-10"
                            v-model="query.country"
                            placeholder="国家"
                            @keyup.enter.native="search"
                            clearable
                        >
                        </el-input>
                    </div>
                    <el-date-picker
                        v-model="query.time"
                        size="mini"
                        type="datetimerange"
                        range-separator="-"
                        class="m-r-10"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="上架时间-开始日期"
                        end-placeholder="上架时间-结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                    <el-date-picker
                        v-model="query.time1"
                        size="mini"
                        type="datetimerange"
                        range-separator="-"
                        class="m-r-10"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="下架时间-开始日期"
                        end-placeholder="下架时间-结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                    <el-date-picker
                        v-model="query.time2"
                        size="mini"
                        type="datetimerange"
                        range-separator="-"
                        class="m-r-10"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="发货时间-开始日期"
                        end-placeholder="发货时间-结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                    <!-- <el-button @click="reset" size="mini">重置</el-button> -->
                    <div class="action-btn">
                        <el-button type="warning" size="mini" @click="search"
                            >查询</el-button
                        >
                        <!-- <el-button type="success" size="mini">导出</el-button> -->
                    </div>
                </div>
            </el-card>
        </div>
        <!-- <el-empty></el-empty> -->
        <el-card v-for="(v, k) in tableData" :key="k" shadow="hover">
            <div class="good-card">
                <div class="good-cardinfo">
                    <div class="good-head">
                        <div class="good-tag">
                            <el-tag
                                type="primary"
                                size="mini"
                                v-if="
                                    v.periods_type == 4 || v.periods_type == 5
                                "
                            >
                                {{ v.periods_type == 4 ? "实物" : "优惠券" }}
                            </el-tag>
                            <!-- <el-tag type="primary" size="mini" v-if="v.is_presell">{{v.is_presell?'预售':''}}</el-tag>
                            <el-tag type="warning" size="mini" v-if="v.is_supplier_delivery">
                                {{v.is_supplier_delivery?'代发':''}}
                            </el-tag>
                            <el-tag type="success" size="mini" v-if="v.is_channel"> {{v.is_channel==1?'渠道':''}}</el-tag> -->
                        </div>
                        <div class="good-title">
                            <el-link :underline="false">
                                {{ v.id }}
                                {{ v.title }}
                            </el-link>
                            <i
                                class="el-icon-star-on"
                                :style="v.is_hot == 1 ? 'color:red;' : ''"
                                style="cursor: pointer; font-size: 18px"
                                @click="handleStars(v)"
                            ></i>
                        </div>
                    </div>
                    <div class="good-content">
                        <div class="good-info">
                            <p v-if="isShowTitle(v.periods_type)">
                                国家：<el-link :underline="false">{{
                                    v.country.join(",")
                                        ? v.country.join(",")
                                        : "-"
                                }}</el-link>
                            </p>
                            <p v-if="isShowTitle(v.periods_type)">
                                类型：<el-link :underline="false">{{
                                    v.product_category.join(",")
                                        ? v.product_category.join(",")
                                        : "-"
                                }}</el-link>
                            </p>
                            <p v-if="isShowTitle(v.periods_type)">
                                容量：<el-link :underline="false">{{
                                    v.capacity || "-"
                                }}</el-link>
                            </p>
                            <p
                                v-if="isShowTitle(v.periods_type)"
                                :class="v.price > 0 ? 'p_blue' : ''"
                                @click="handlePrice(v)"
                            >
                                <!-- 实物 -->
                                售价：<el-link
                                    :underline="false"
                                    class="p_blue"
                                    >{{ v.package_prices }}</el-link
                                >
                            </p>
                            <p v-if="!isShowTitle(v.periods_type)">
                                <!-- 优惠券 -->
                                售价：<el-link
                                    :underline="false"
                                    class="p_blue"
                                    >{{ v.rabbit_price }}</el-link
                                >
                            </p>
                        </div>
                        <!-- <el-link :underline="false"></el-link> -->
                        <div class="good-popularize">
                            <p v-if="isShowTitle(v.periods_type)">
                                文案：<el-link :underline="false">{{
                                    v.creator_name || "-"
                                }}</el-link>
                            </p>
                            <p v-if="!isShowTitle(v.periods_type)">
                                运营：<el-link :underline="false">{{
                                    v.creator_name || "-"
                                }}</el-link>
                            </p>
                            <p v-if="isShowTitle(v.periods_type)">
                                采购：<el-link :underline="false">{{
                                    v.buyer_name || "-"
                                }}</el-link>
                            </p>
                            <p v-if="isShowTitle(v.periods_type)">
                                运营：<el-link :underline="false">{{
                                    v.operation_name || "-"
                                }}</el-link>
                            </p>
                            <p v-if="isShowTitle(v.periods_type)">
                                审核：<el-link :underline="false">{{
                                    v.operation_review_name || "-"
                                }}</el-link>
                            </p>
                        </div>
                        <div class="good-time">
                            <div class="p_blue">
                                <div>上架：</div>
                                <el-link
                                    class="p_blue"
                                    :underline="false"
                                    @click="updateTime(v)"
                                    >{{ v.onsale_time | timeFormat
                                    }}<i
                                        class="el-icon-edit"
                                        v-if="
                                            (v.onsale_status == 0 &&
                                                v.onsale_review_status == 3) ||
                                            v.onsale_review_status == 4
                                        "
                                    ></i>
                                </el-link>
                            </div>
                            <div class="p_blue">
                                开售：
                                <el-link
                                    class="p_blue"
                                    :underline="false"
                                    @click="updateTime(v)"
                                    >{{ v.sell_time | timeFormat
                                    }}<i
                                        class="el-icon-edit p_blue"
                                        v-if="
                                            (v.onsale_status == 0 &&
                                                v.onsale_review_status == 3) ||
                                            v.onsale_review_status == 4
                                        "
                                    ></i>
                                </el-link>
                            </div>
                            <div class="p_blue">
                                下架：
                                <el-link
                                    class="p_blue"
                                    :underline="false"
                                    @click="updateTime(v)"
                                    >{{ v.sold_out_time | timeFormat
                                    }}<i
                                        class="el-icon-edit p_blue"
                                        v-if="
                                            (v.onsale_status == 0 &&
                                                v.onsale_review_status == 3) ||
                                            v.onsale_review_status == 4
                                        "
                                    ></i>
                                </el-link>
                            </div>
                            <div class="p_blue">
                                发货：
                                <el-link
                                    class="p_blue"
                                    :underline="false"
                                    @click="updateTime(v)"
                                >
                                    {{ v.predict_shipment_time | timeFormat
                                    }}<i
                                        class="el-icon-edit p_blue"
                                        v-if="
                                            (v.onsale_status == 0 &&
                                                v.onsale_review_status == 3) ||
                                            v.onsale_review_status == 4
                                        "
                                    ></i>
                                </el-link>
                            </div>
                        </div>
                        <div class="good-stock">
                            <p class="p_blue" @click="updateStock(v)">
                                库存：<el-link
                                    :underline="false"
                                    class="p_blue"
                                >
                                    {{ v.inventory
                                    }}<i
                                        v-if="isShowTitle(v.periods_type)"
                                        class="el-icon-edit"
                                    ></i>
                                </el-link>
                            </p>
                            <el-popover
                                placement="top-start"
                                title="已售"
                                width="200"
                                v-if="isShowTitle(v.periods_type)"
                                trigger="click"
                                content="已售：显示数值为实际已售出瓶数"
                            >
                                <p class="p_blue" slot="reference">
                                    已售<i
                                        class="fas fa-question-circle"
                                    />：<el-link
                                        @click.stop="handleBuy(v, 1)"
                                        :underline="false"
                                        class="p_blue"
                                    >
                                        {{ v.saled_count }}
                                    </el-link>
                                </p>
                            </el-popover>
                            <el-popover
                                placement="top-start"
                                title="已售"
                                width="200"
                                v-if="isShowTitle(v.periods_type)"
                                trigger="click"
                                content="已售：显示数值为实际已售出瓶数"
                            >
                                <p class="p_blue" slot="reference">
                                    已购<i
                                        class="fas fa-question-circle"
                                    />：<el-link
                                        @click.stop="handleBuy(v, 2)"
                                        :underline="false"
                                        class="p_blue"
                                    >
                                        {{ v.purchased_person }}/{{
                                            v.purchased_number
                                        }}
                                    </el-link>
                                </p>
                            </el-popover>

                            <p>
                                前端：<el-link :underline="false"
                                    >{{ v.purchased + v.vest_purchased }}/{{
                                        v.limit_number
                                    }}
                                </el-link>
                            </p>
                        </div>
                        <div class="good-comment">
                            <p
                                class="p_blue"
                                @click="handleVest(v)"
                                v-if="isShowTitle(v.periods_type)"
                            >
                                马甲：<el-link
                                    :underline="false"
                                    class="p_blue"
                                >
                                    {{ v.vest_count }}
                                </el-link>
                                <i
                                    class="el-icon-edit"
                                    style="cursor: pointer"
                                ></i>
                            </p>
                            <p v-if="isShowTitle(v.periods_type)">
                                评论：<el-link :underline="false">
                                    本{{ v.periods_comment_count }}/编{{
                                        v.virtual_comment_count
                                    }}/总{{ v.product_comment_count }}
                                </el-link>
                            </p>
                            <p v-if="isShowTitle(v.periods_type)">
                                浏览：<el-link :underline="false">{{
                                    v.pageviews
                                }}</el-link>
                            </p>
                            <p v-if="isShowTitle(v.periods_type)">
                                转化率：<el-link :underline="false">
                                    {{ v.rate }}‰</el-link
                                >
                            </p>
                        </div>
                        <div class="good-saleinfo">
                            <p v-if="isShowTitle(v.periods_type)">
                                套餐：<el-link :underline="false">{{
                                    v.periods_set_count
                                }}</el-link>
                            </p>
                            <div class="good-remark">
                                <p class="p_blue" @click="handleRemark(v)">
                                    备注:<i
                                        class="el-icon-edit"
                                        style="cursor: pointer"
                                    ></i>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="good-status">
                    <!--    <div v-if="v.onsale_review_status==4">
                        <el-popover placement="top-start" :open-delay="400" :title="v.content" width="200"
                            trigger="manual" :content="v.remark" v-model="v.visible">
                            <span slot="reference" style="color:#409EFF;cursor: pointer;"
                                @click="showRemark(v)">{{statusTxt(v.onsale_review_status,v.onsale_status) }}</span>
                        </el-popover>
                    </div> -->
                    <p
                        style="color: #409eff; cursor: pointer"
                        @click="handleStatusClick(v)"
                    >
                        {{ statusTxt(v.onsale_review_status, v.onsale_status) }}
                    </p>
                </div>
                <div class="opration">
                    <el-input
                        v-model="v.sort"
                        class="inp_center"
                        style="width: 80px; text-align: center"
                        @blur="updateSort(v)"
                        oninput="value=value.replace(/[^\d]/g,'')"
                    />
                    <!-- <el-input v-model="num" size="mini"></el-input> -->
                    <el-button
                        type="primary"
                        size="mini"
                        @click="HandleClick(v)"
                        >编辑
                        <!-- v-if="v.onsale_review_status != 1" -->
                    </el-button>
                    <el-button
                        type="primary"
                        size="mini"
                        @click="HandleAudit(v, 3, '')"
                        v-if="v.onsale_review_status == 1"
                    >
                        审核</el-button
                    >
                    <!-- <el-button type="warning" size="mini">达人说</el-button> -->
                    <el-button
                        type="info"
                        size="mini"
                        v-if="
                            v.onsale_review_status == 3 &&
                            v.onsale_status == 0 &&
                            v.onsale_verify_status == 0
                        "
                        @click="onShelf(v)"
                        >上架</el-button
                    >
                    <el-button
                        type="danger"
                        size="mini"
                        v-if="
                            v.onsale_review_status == 3 && v.onsale_status == 2
                        "
                        @click="offShelf(v)"
                        >下架</el-button
                    >
                    <el-button
                        type="danger"
                        size="mini"
                        v-if="
                            v.onsale_review_status == 3 &&
                            (v.onsale_status == 2 ||
                                v.onsale_status == 3 ||
                                v.onsale_status == 4) &&
                            v.periods_type != 4 &&
                            v.periods_type != 5
                        "
                        @click="handleCopy(v)"
                        >复制</el-button
                    >
                </div>
            </div>
        </el-card>
        <div class="pagination-block" v-if="!isEdit">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <el-dialog
            title="商品信息"
            :visible.sync="goodsOpenDialogStatus"
            :close-on-click-modal="false"
            :before-close="closeGoodsOpDialog"
            fullscreen
        >
            <RabbitGoodsOp
                @closeGoodsOpDialog="closeGoodsOpDialog"
                v-if="goodsOpenDialogStatus"
                ref="goodsop"
                :parentObj="this"
            ></RabbitGoodsOp>
        </el-dialog>
        <el-dialog
            title="驳回信息"
            :visible.sync="dialogVisible"
            width="30%"
            :close-on-click-modal="false"
        >
            <el-form
                label-width="100px"
                ref="form"
                :model="entity"
                :rules="rules"
            >
                <el-form-item label="驳回理由" prop="reason">
                    <el-select
                        class="w-mini m-r-10"
                        v-model="entity.reason"
                        filterable
                        size="mini"
                        placeholder="状态"
                    >
                        <el-option
                            v-for="item in reasonOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input
                        v-model="entity.remark"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入备注"
                    />
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="rejects()">确 定</el-button>
            </span>
        </el-dialog>
        <el-dialog
            :visible.sync="GoodsDetailVisible"
            :close-on-click-modal="false"
            title="商品详情浏览"
            custom-class="dialogwid"
            width="81%"
            destroy-on-close
        >
            <!-- <iframe v-if="GoodsDetailVisible" :src="ifreameurl" frameborder="0"
                style="width: 100%;height: 800px;"></iframe> -->
            <div>
                <goodsdetail
                    ref="goodsdetail"
                    :goodsinfo="goodsinfo"
                ></goodsdetail>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button
                    type="primary"
                    @click="agreeAudit(rows, 3, '')"
                    :disabled="auditTime > 0"
                >
                    通过{{ auditTime > 0 ? auditTime : "" }}
                </el-button>
                <el-button type="primary" @click="HandleReject(rows)"
                    >驳回</el-button
                >
            </div>
        </el-dialog>
        <el-dialog
            :visible.sync="dialogVisible1"
            :close-on-click-modal="false"
            title="提示"
            custom-class="dialogwid"
            width="30%"
        >
            <div
                style="
                    display: flex;
                    justify-content: center;
                    margin-bottom: 20px;
                    font-size: 16px;
                "
            >
                请选择要复制到的{{ copyStatus == 1 ? "频道" : "位置" }}
            </div>
            <div v-if="copyStatus == 1">
                <div
                    style="
                        display: flex;
                        justify-content: space-between;
                        padding-left: 110px;
                    "
                >
                    <el-radio-group v-model="to_periods">
                        <el-radio
                            :label="v.value"
                            v-for="(v, i) in goodsType"
                            :key="i"
                        >
                            {{ v.label }}</el-radio
                        >
                    </el-radio-group>
                </div>
                <div
                    style="
                        display: flex;
                        justify-content: center;
                        margin-bottom: 20px;
                        font-size: 16px;
                    "
                >
                    <div
                        style="
                            display: flex;
                            justify-content: space-between;
                            width: 500px;
                        "
                    >
                        <el-button
                            type="primary"
                            size="mini"
                            @click="handleBack()"
                            >返回</el-button
                        >
                        <el-button
                            type="primary"
                            size="mini"
                            @click="handleConfirm()"
                            >确认</el-button
                        >
                    </div>
                </div>
            </div>
            <div
                v-if="copyStatus == 0"
                style="
                    display: flex;
                    justify-content: center;
                    margin-bottom: 20px;
                    font-size: 16px;
                "
            >
                <div
                    style="
                        display: flex;
                        justify-content: space-between;
                        width: 500px;
                    "
                >
                    <el-button type="primary" size="mini" @click="copys(1)"
                        >文案列表</el-button
                    >
                    <el-button type="primary" size="mini" @click="copys(2)"
                        >商品列表</el-button
                    >
                </div>
            </div>
        </el-dialog>
        <remarkList ref="remarklist"></remarkList>
        <stockList ref="stocklist" :parentObj="this"></stockList>
        <VestList ref="vestlist" :parentObj="this"></VestList>
        <BuyList
            ref="buylist"
            :parentObj="this"
            v-if="dialogVisible2"
        ></BuyList>

        <SoldList
            ref="soldlist"
            :parentObj="this"
            v-if="dialogVisible3"
        ></SoldList>
        <logList ref="logList"></logList>
        <packList ref="packList"></packList>

        <updateTime ref="updateTime" :parentObj="this"></updateTime>
        <el-dialog
            title="兔头优惠券商品"
            :visible.sync="couponDialogVisible"
            :before-close="closeGoodsOpDialog"
            fullscreen
            :close-on-click-modal="false"
        >
            <RabbitCouponAdd
                @closeGoodsOpDialog="closeGoodsOpDialog"
                ref="rabbitcouponadd"
                v-if="couponDialogVisible"
                :parentObj="this"
            ></RabbitCouponAdd>
        </el-dialog>
    </div>
</template>
<script>
import RabbitGoodsOp from "./GoodsOp.vue";
import goodsdetail from "../../article/GoodsDetail.vue";
import remarkList from "../../article/remarkList.vue";
import stockList from "./stockList.vue";
import VestList from "./VestList.vue";
import BuyList from "./BuyList.vue";
import SoldList from "./SoldList.vue";
import logList from "./LogList.vue";
import packList from "./packList.vue";
import updateTime from "./updateTime.vue";
import RabbitCouponAdd from "./RabbitCouponAdd.vue";
export default {
    props: {
        status: {
            type: Number,
            default: 0,
        },
        periods_type: {
            type: Number,
            default: 0,
        },
    },
    filters: {
        timeFormat(val) {
            if (val == "1970-01-01 08:00:00") {
                return "-";
            } else {
                return val;
            }
        },
    },
    components: {
        RabbitGoodsOp,
        goodsdetail,
        remarkList,
        stockList,
        VestList,
        BuyList,
        SoldList,
        logList,
        packList,
        updateTime,
        RabbitCouponAdd,
    },
    data() {
        return {
            couponDialogVisible: false,
            goodsOpenDialogStatus: false,
            getOrderList: [],
            options: [],
            query: {
                page: 1,
                limit: 10,
                periods: "",
                short_code: "",
                creator_id: "",
                country: "",
                title: "",
                product_category: "",
                import_type: "",
                periods_type: "",
                onsale_review_status: "",
                time: [],
                time1: [],
                time2: [],
            },
            goodsType: [
                {
                    label: "闪购",
                    value: 0,
                },
                {
                    label: "秒发",
                    value: 1,
                },
                {
                    label: "跨境",
                    value: 2,
                },
                {
                    label: "尾货",
                    value: 3,
                },
            ],
            tableData: [],
            goodsData: {},
            payOrderTimes: [],
            total: 0,
            isEdit: false,
            activeName: "beshelved",
            editInput: true,
            dialogVisible: false,
            GoodsDetailVisible: false,
            dialogVisible1: false,
            dialogVisible2: false,
            dialogVisible3: false,
            entity: {
                reason: "",
                remark: "",
            },

            reasonOptions: [
                {
                    label: "酒瓶信息错误",
                    value: "酒瓶信息错误",
                },
                {
                    label: "年份错误",
                    value: "年份错误",
                },
                {
                    label: "详细资料错误",
                    value: "详细资料错误",
                },
                {
                    label: "采购人不符",
                    value: "采购人不符",
                },
                {
                    label: "其他",
                    value: "其他",
                },
            ],
            sortOption: [
                {
                    label: "排序值降序",
                    value: 8,
                },
                {
                    label: "排序值升序",
                    value: 7,
                },

                {
                    label: "转化率降序",
                    value: 6,
                },
                {
                    label: "转化率升序",
                    value: 5,
                },
                {
                    label: "销量降序",
                    value: 4,
                },
                {
                    label: "销量升序",
                    value: 3,
                },
                {
                    label: "库存值降序",
                    value: 10,
                },

                {
                    label: "库存值升序",
                    value: 9,
                },

                {
                    label: "价格降序",
                    value: 2,
                },
                {
                    label: "价格升序",
                    value: 1,
                },

                {
                    label: "下架时间降序",
                    value: 12,
                },
                {
                    label: "下架时间升序",
                    value: 11,
                },
                {
                    label: "上架时间降序",
                    value: 13,
                },
                {
                    label: "上架时间升序",
                    value: 14,
                },
            ],
            typeOptions: [
                {
                    label: "酒类",
                    value: 0,
                },
                {
                    label: "饮料",
                    value: 2,
                },
                {
                    label: "食品",
                    value: 3,
                },
                {
                    label: "物料",
                    value: 4,
                },
                {
                    label: "虚拟",
                    value: 6,
                },
                {
                    label: "酒具",
                    value: 7,
                },
                {
                    label: "其他",
                    value: 5,
                },
            ],
            import_typeOptions: [
                {
                    label: "自进口",
                    value: 0,
                },
                {
                    label: "地采",
                    value: 1,
                },
            ],
            statusOptions: [
                {
                    label: "待售中",
                    value: 1,
                },
                {
                    label: "在售中",
                    value: 2,
                },
            ],
            // statusOptions: [{
            //         label: "待绑定",
            //         value: 0
            //     },
            //     {
            //         label: "待审核",
            //         value: 1
            //     },
            //     {
            //         label: "审批中",
            //         value: 2
            //     },
            //     {
            //         label: "已审核",
            //         value: 3
            //     },
            //     {
            //         label: "已驳回",
            //         value: 4
            //     }
            // ],
            rules: {
                reason: [
                    {
                        required: true,
                        message: "请选择驳回原因",
                        trigger: "change",
                    },
                ],
            },
            auditTime: 0,
            goodsinfo: {},
            to_periods: 0,
            copyStatus: 0,
            copy_type: 1,
            rows: {},
        };
    },
    methods: {
        isShowTitle(type) {
            if (type === 4) {
                return true;
            } else {
                return false;
            }
        },

        handleBack() {
            this.copyStatus = 0;
            console.log(this.copyStatus, 232323);
        },
        handleConfirm() {
            this.$request.article
                .copyPeriod({
                    period: this.rows.id,
                    periods_type: this.rows.periods_type,
                    to_periods: this.to_periods,
                    copy_type: this.copy_type,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        let url = "";
                        //跳转
                        if (this.copy_type == 2) {
                            if (this.to_periods == 0) {
                                url = "/flash?tab=wait";
                            } else if (this.to_periods == 1) {
                                url = "/second?tab=wait";
                            } else if (this.to_periods == 2) {
                                url = "/crossborder?tab=wait";
                            } else if (this.to_periods == 3) {
                                url = "/tailcargo?tab=wait";
                            }
                            if (this.rows.periods_type != this.to_periods) {
                                this.$router.push(url);
                            } else {
                                this.$emit("targetPage", "wait");
                            }
                        } else {
                            this.$router.push("/article");
                        }
                        this.$message.success("操作成功！");
                    }
                });
        },
        handleStars(row) {
            this.$request.article
                .updateUncUsed({
                    period: row.id,
                    periods_type: row.periods_type,
                    is_hot: row.is_hot == 1 ? 0 : 1,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        row.is_hot = row.is_hot == 0 ? 1 : 0;
                        this.$message.success("操作成功！");
                    }
                });
        },
        copys(copy_type) {
            this.copy_type = copy_type;
            this.copyStatus = 1;
        },
        handlePrice(rows) {
            this.$nextTick(() => {
                this.$refs.packList.openForm(rows);
            });
        },
        handleStatusClick(rows) {
            this.$nextTick(() => {
                this.$refs.logList.openForm(rows);
            });
        },
        handleBuy(v, type) {
            if (type == 1) {
                this.dialogVisible3 = true;
                this.$nextTick(() => {
                    this.$refs.soldlist.openForm(v);
                });
            } else {
                this.dialogVisible2 = true;
                this.$nextTick(() => {
                    this.$refs.buylist.openForm(v);
                });
            }
        },

        handleVest(v) {
            this.$nextTick(() => {
                this.$refs.vestlist.openForm(v);
            });
        },
        showRemark(row) {
            if (!row.content) {
                this.$request.article
                    .getReviewLog({
                        period: row.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.$set(row, "visible", true);
                            this.$set(row, "content", res.data.data.describe);
                            this.$set(row, "remark", res.data.data.remark);
                        }
                    });
            } else {
                row.visible = !row.visible;
            }
        },
        statusTxt(operateStatus, status) {
            let statusTxt = "";
            if (operateStatus == 0) {
                statusTxt = "待绑定";
            } else if (operateStatus == 1) {
                statusTxt = "待审核";
            } else if (operateStatus == 3 && status == 0) {
                statusTxt = "待上架";
            } else if (operateStatus == 3 && status == 1) {
                statusTxt = "待售中";
            } else if (operateStatus == 3 && status == 2) {
                statusTxt = "在售中";
            } else if (operateStatus == 4) {
                statusTxt = "已驳回";
            }
            return statusTxt;
        },
        closeGoodsOpDialog() {
            this.couponDialogVisible = false;
            this.goodsOpenDialogStatus = false;
            this.getData();
        },
        HandleClick(rows) {
            if (rows.periods_type == 4) {
                this.goodsOpenDialogStatus = true;
                this.$nextTick(() => {
                    this.$refs.goodsop.openForm(rows);
                });
            } else {
                this.couponDialogVisible = true;
                this.$nextTick(() => {
                    this.$refs.rabbitcouponadd.openForm(rows);
                });
            }
        },
        edit() {
            this.isEdit = true;
            this.activeName = "edit";
        },
        updateStock(row) {
            if (row.periods_type == 4) {
                this.$refs.stocklist.openForm(row);
            }
        },
        getData() {
            console.log(this.query.time2, 333);
            const params = {
                periods: this.query.periods,
                title: this.query.title,
                product_category: this.query.product_category,
                country: this.query.country,
                rabbit: 1,
                import_type: this.query.import_type,
                onsale_status: this.status,
                short_code: this.query.short_code,
                creator_id: this.query.creator_id,
                onsale_time_start:
                    (this.query.time && this.query.time[0]) || "",
                onsale_time_end: (this.query.time && this.query.time[1]) || "",
                sold_out_time_start:
                    (this.query.time1 && this.query.time1[0]) || "",
                sold_out_time_end:
                    (this.query.time1 && this.query.time1[1]) || "",
                predict_shipment_time_start:
                    (this.query.time2 && this.query.time2[0]) || "",
                predict_shipment_time_end:
                    (this.query.time2 && this.query.time2[1]) || "",
                page: this.query.page,
                limit: this.query.limit,
            };
            if (this.query.sort_rule == 1) {
                params.sort_rule = JSON.stringify({
                    price: "asc",
                });
            } else if (this.query.sort_rule == 2) {
                params.sort_rule = JSON.stringify({
                    price: "desc",
                });
            } else if (this.query.sort_rule == 3) {
                params.sort_rule = JSON.stringify({
                    saled_count: "asc",
                });
            } else if (this.query.sort_rule == 4) {
                params.sort_rule = JSON.stringify({
                    saled_count: "desc",
                });
            } else if (this.query.sort_rule == 5) {
                params.sort_rule = JSON.stringify({
                    conversion_rate: "asc",
                });
            } else if (this.query.sort_rule == 6) {
                params.sort_rule = JSON.stringify({
                    conversion_rate: "desc",
                });
            } else if (this.query.sort_rule == 7) {
                params.sort_rule = JSON.stringify({
                    sort: "asc",
                });
            } else if (this.query.sort_rule == 8) {
                params.sort_rule = JSON.stringify({
                    sort: "desc",
                });
            } else if (this.query.sort_rule == 9) {
                params.sort_rule = JSON.stringify({
                    inventory: "asc",
                });
            } else if (this.query.sort_rule == 10) {
                params.sort_rule = JSON.stringify({
                    inventory: "desc",
                });
            } else if (this.query.sort_rule == 11) {
                params.sort_rule = JSON.stringify({
                    sold_out_time: "asc",
                });
            } else if (this.query.sort_rule == 12) {
                params.sort_rule = JSON.stringify({
                    sold_out_time: "desc",
                });
            } else if (this.query.sort_rule == 13) {
                params.sort_rule = JSON.stringify({
                    onsale_time: "desc",
                });
            } else if (this.query.sort_rule == 14) {
                params.sort_rule = JSON.stringify({
                    onsale_time: "asc",
                });
            }
            if (this.status == 2) {
                if (this.query.onsale_review_status) {
                    params.onsale_status = this.query.onsale_review_status;
                } else {
                    delete params.onsale_status;
                    params.onsale_status_t = 1;
                }
            }
            if (this.status == 0) {
                params.onsale_review_status = this.query.onsale_review_status;
            }

            this.$request.article.articleList(params).then((res) => {
                if (res.data.error_code == 0) {
                    res.data.data.list.map((item) => {
                        item.onsale_time_visible = true;
                        item.sell_time_visible = true;
                        item.predict_shipment_time_visible = true;
                        item.sold_out_time_visible = true;
                        // item.country = [...new Set(item.country)][0]
                        // item.product_category = [...new Set(item.product_category)].join("、")
                        // item.onsale_time = item.onsale_time
                        // item.sell_time = item.sell_time
                        // item.sold_out_time = this.timeFormat(item.sold_out_time)
                        // item.predict_shipment_time = this.timeFormat(item.predict_shipment_time)
                    });
                    this.tableData = res.data.data.list || [];
                    this.total = res.data.data.total;
                }
            });
        },
        updateSort(rows) {
            this.$request.article
                .updateUncUsed({
                    period: rows.id,
                    periods_type: rows.periods_type,
                    sort: rows.sort,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功！");
                    }
                });
        },
        // timeFormat(val) {
        //     if (val == '1970-01-01 08:00:00') {
        //         return "-"
        //     } else {
        //         return val
        //     }
        // },
        updateTime(rows, type) {
            console.log(11111);
            this.$nextTick(() => {
                this.$refs.updateTime.openForm(rows);
            });
            // let params = {
            //     period: rows.id,
            //     periods_type: rows.periods_type,
            // };
            // if(new Date(rows.sell_time).getTime()<=new Date(rows.onsale_time).getTime()){
            //     this.$message.error("开售时间必须大于上架时间！")
            //     return
            // }
            //  if(new Date(rows.sold_out_time).getTime()<=new Date(rows.sell_time).getTime()){
            //     this.$message.error("下架时间必须大于开售时间！")
            //     return
            // }
            // if (type == 1) {
            //     if (!rows.onsale_time) {
            //         this.$message.error("请选择上架时间！")
            //         return;
            //     }
            //     params.onsale_time = rows.onsale_time;
            // } else if (type == 2) {
            //     if (!rows.sell_time) {
            //         this.$message.error("请选择开售时间！")
            //         return;
            //     }
            //     params.sell_time = rows.sell_time;
            // } else if (type == 3) {
            //     if (!rows.sold_out_time) {
            //         this.$message.error("请选择下架时间！")
            //         return;
            //     }
            //     params.sold_out_time = rows.sold_out_time;
            // } else if (type == 4) {
            //     if (!rows.predict_shipment_time) {
            //         this.$message.error("请选择发货时间！")
            //         return;
            //     }
            //     params.predict_shipment_time = rows.predict_shipment_time;
            // }
            // this.$request.article.goodsUpdate(params).then(res => {
            //     if (res.data.error_code == 0) {
            //         if (type == 1) {
            //             rows.onsale_time_visible = !rows.onsale_time_visible;
            //         } else if (type == 2) {
            //             rows.sell_time_visible = !rows.sell_time_visible;
            //         } else if (type == 3) {
            //             rows.sold_out_time_visible = !rows.sold_out_time_visible;
            //         } else if (type == 4) {
            //             rows.predict_shipment_time_visible = !rows.predict_shipment_time_visible;
            //         }

            //     }

            // });
        },
        HandleAudit(rows, review_status, reason) {
            this.GoodsDetailVisible = true;
            this.goodsinfo = rows;
            this.rows = rows;
            this.auditTime = 5;
            if (rows.periods_type == 0) {
                this.$request.article
                    .getFlashDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            } else if (rows.periods_type == 1) {
                this.$request.article
                    .getSecondDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            } else if (rows.periods_type == 2) {
                this.$request.article
                    .getCrossDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            } else if (rows.periods_type == 3) {
                this.$request.article
                    .getLeftoverDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            } else if (rows.periods_type == 4) {
                this.$request.article
                    .rabbitDetail({
                        id: rows.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            res.data.data.periods_type = rows.periods_type;
                            res.data.data.imgList = res.data.data.product_img;
                            res.data.data.num = 1;
                            this.goodsinfo = Object.assign({}, res.data.data);
                            this.mainImg = this.goodsinfo.imgList[0];
                        }
                    });
            }

            this.countDown();
        },
        agreeAudit(rows, review_status) {
            if (review_status == 3) {
                this.$confirm("你确定审核通过吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        this.audit(rows, review_status);
                    })
                    .catch(() => {
                        //几点取消的提示
                    });
            }
        },
        audit(rows, review_status, reason) {
            // if (review_status == 3 && !this.validateForm()) {
            //     return
            // }
            console.log(rows, 3333333);

            const params = {
                period: rows.id,
                onsale_review_status: review_status,
                reject_reason: reason,
                remark: this.entity.remark,
                periods_type: rows.periods_type,
                version: this.goodsinfo.version,
            };
            this.$request.article.updateOnsale(params).then((res) => {
                if (res.data.error_code == 0) {
                    this.getData();
                    if (review_status == 4) {
                        this.dialogVisible = false;
                    }
                    this.GoodsDetailVisible = false;
                    this.$message.success("操作成功！");
                }
            });
        },
        HandleReject(rows) {
            this.rows = rows;
            this.entity.reason = "";
            this.entity.remark = "";
            this.dialogVisible = !this.dialogVisible;
        },
        rejects() {
            if (this.validateForm()) {
                this.audit(this.rows, 4, this.entity.reason);
            }
        },
        handleRemark(rows) {
            this.$nextTick(() => {
                this.$refs.remarklist.openForm(rows);
            });
        },
        judgeTime(time) {
            var strtime = time.replace("/-/g", "/"); //时间转换
            //时间
            var date1 = new Date(strtime);
            //现在时间
            var date2 = new Date();
            //判断时间是否过期
            return date1 < date2 ? true : false;
        },
        onShelf(rows) {
            this.$confirm("你确定要上架吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.$request.article
                        .updateoffSale({
                            period: rows.id,
                            periods_type: rows.periods_type,
                            onsale_status: 2,
                            operation_id: "1",
                            operation_name: "操作人",
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.query.page = 1;
                                this.getData();
                                this.$message.success("操作成功");
                            }
                        });
                })
                .catch(() => {
                    //几点取消的提示
                });
        },
        offShelf(rows) {
            this.$prompt("请输入下架原因", "下架", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                inputPlaceholder: "请输入下架原因",
                inputValidator: (value) => {
                    if (!value) {
                        return "下架原因不能为空";
                    }
                    if (value.trim().length < 3) {
                        return "下架原因至少需要3个字符";
                    }
                    return true;
                },
            })
                .then(({ value }) => {
                    this.$request.article
                        .updateoffSale({
                            period: rows.id,
                            periods_type: rows.periods_type,
                            onsale_status: 3,
                            operation_id: "1",
                            operation_name: "操作人",
                            remark: value,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.query.page = 1;
                                this.getData();
                                this.$message.success("下架成功");
                            }
                        });
                })
                .catch(() => {
                    // 取消操作
                });
        },
        handleCopy(rows) {
            this.copyStatus = 0;
            this.rows = rows;
            this.dialogVisible1 = true;
        },
        countDown() {
            this.auditTime = this.auditTime - 1;
            if (this.auditTime == 0) {
                return;
            }
            setTimeout(() => {
                this.countDown();
            }, 1000);
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        reset() {
            this.$confirm("您确定要重置所有筛选条件吗?", "重置操作", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.payOrderTimes = [];
                this.form = {
                    page: 1,
                };
                this.getData();
            });
        },

        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
        validateForm() {
            let flag = null;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
    },
    mounted() {
        this.$request.article
            .purchaseList({
                type: 3,
            })
            .then((res) => {
                this.options = res.data.data.list;
            });
        if (this.status == 0) {
            this.statusOptions = [
                {
                    label: "待绑定",
                    value: 0,
                },
                {
                    label: "待审核",
                    value: 1,
                },
                {
                    label: "待上架",
                    value: 3,
                },
                {
                    label: "已驳回",
                    value: 4,
                },
            ];
        }
        this.getData();
    },
};
</script>
<style>
.inp_center .el-input__inner {
    text-align: center;
}
</style>
<style lang="scss" scoped>
.el-card {
    margin-bottom: 10px;
}

.good-card {
    .el-button--mini,
    .el-button--mini.is-round {
        padding: 0;
        background-color: #ffffff;
        border-color: #ffffff;
        color: #66b1ff;
    }

    display: flex;
    justify-content: space-between;

    .good-cardinfo {
        width: 80%;

        .p_blue {
            color: #409eff;
            cursor: pointer;
        }

        .good-head {
            width: 100%;
            display: flex;
            justify-content: flex-start;
            white-space: nowrap;
            overflow: hidden;

            .good-tag {
                & > .el-tag {
                    margin-right: 4px;
                }
            }
        }

        .good-content {
            margin-top: 10px;
            display: flex;
            justify-content: space-between;

            .el-icon-edit {
                margin-left: 5px;
                color: #409eff;
                font-size: 14px;
            }

            p {
                margin-bottom: 0;
            }

            .good-time {
                width: 240px;

                > div {
                    display: flex;
                    align-items: center;
                }

                .times {
                    display: flex;

                    i {
                        cursor: pointer;
                        font-size: 20px;
                        color: #409eff;
                        padding: 2px 0 0 3px;
                    }
                }
            }

            .el-link {
                font-size: 12px;
            }

            .good-saleinfo {
                .good-remark {
                    display: flex;
                    align-items: center;
                    height: 30px;

                    & > p {
                        margin-bottom: 0;
                    }

                    & > .el-button {
                        margin-left: 20px;
                    }
                }
            }
        }
    }

    .good-status {
        width: 50px;
    }

    .opration {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 80px;

        & > .el-button {
            margin-left: 0;
            margin-top: 10px;
        }
    }
}

.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .table {
        margin-top: 10px;

        .f-12 {
            font-size: 12px;
        }

        .card {
            margin-bottom: 8px;

            .card-title {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;

            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                // white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }

                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}

.el-link--default {
    vertical-align: inherit !important;
}
</style>
