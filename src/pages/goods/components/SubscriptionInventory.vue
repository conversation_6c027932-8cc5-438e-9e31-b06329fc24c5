<template>
  <el-dialog
    title="订阅库存提醒"
    :visible.sync="dialogVisible"
    width="700px"
    :before-close="handleClose"
  >
    <div class="dialog-tip">商品订阅后，每日下午4点通过企微中台进行播报。</div>
    <div class="dialog-subtitle">如果要删除订阅请到"企业微信——工作台——库存变更订阅"中处理。</div>
    
    <div class="subscription-content">
      <div v-for="(item, index) in productList" :key="index" class="product-item">
        <div class="product-info">
          <div class="product-code">{{ item.short_code }}</div>
          <div class="product-name">{{ item.en_name }}</div>
          <div class="product-name-cn">{{ item.cn_name }}</div>
        </div>
        <el-button 
          type="success" 
          size="mini" 
          plain
          @click="handleSubscribe(item)"
          :disabled="item.subscribed"
        >
          订阅
        </el-button>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="handleClose" style="width: 100%">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'SubscriptionInventory',
  data() {
    return {
      dialogVisible: false,
      productList: [],
      currentProduct: null
    }
  },
  methods: {
    async open(product) {
      this.currentProduct = product
      this.dialogVisible = true
      await this.getProductInfo()
    },
    handleClose() {
      this.dialogVisible = false
      this.productList = []
    },
    async getProductInfo() {
        const res = await this.$request.article.getProductInfoByShortCode({
          short_code: this.currentProduct.short_code.join(',')
        })
        console.log('98833--', res.data);
        
        if (res.data.error_code === 0) {
            const dataDict = res.data.data;
            // 将对象转换为数组
            this.productList = Object.entries(dataDict).map(([shortCode, item]) => ({
              short_code: shortCode,
              cn_name: item.cn_product_name,
              en_name: item.en_product_name,
              subscribed: false
            }));
        }
    },
    async handleSubscribe(item) {
      try {
        const res = await this.$request.article.addInventorySubscription({
          short_code: item.short_code
        })
        if (res.data.error_code === 0) {
          this.$message.success('已成功订阅')
          item.subscribed = true
        }
      } catch (error) {
        console.error('订阅失败：', error)
        this.$message.error('订阅失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-tip {
  font-size: 14px;
  margin-bottom: 8px;
  color: #333;
  text-align: center;
}

.dialog-subtitle {
  font-size: 14px;
  margin-bottom: 20px;
  color: #666;
  text-align: center;
}

.subscription-content {
  max-height: 400px;
  overflow-y: auto;
}

.product-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #EBEEF5;

  &:last-child {
    border-bottom: none;
  }

  .product-info {
    flex: 1;
    
    .product-code {
      font-weight: bold;
      margin-bottom: 4px;
    }
    
    .product-name {
      color: #666;
      margin-bottom: 4px;
    }
    
    .product-name-cn {
      color: #666;
    }
  }

  .el-button {
    margin-left: 15px;
  }
}

:deep(.el-dialog__body) {
  padding-bottom: 0;
}

.dialog-footer {
  padding: 20px;
}
</style> 