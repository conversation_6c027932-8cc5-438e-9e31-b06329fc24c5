<template>
    <el-dialog
        title="已售信息"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible"
        width="65%"
        append-to-body
    >
    <!-- style="color: #409eff;" -->
    <!-- <div><el-link   :underline="false" @click="showUserDetail(-1)">总人数：{{statistics.total_users}}，</el-link>
        <el-link  :underline="false" @click="showUserDetail(5)">沉默用户：{{statistics.silent_users}}，</el-link>
        <el-link  :underline="false" @click="showUserDetail(2)">新用户：{{statistics.new_users}}，</el-link>
        <el-link  :underline="false" @click="showUserDetail(3)">无效用户：{{statistics.invalid_users}}，</el-link>
        <el-link  :underline="false" @click="showUserDetail(1)"> 普通用户：{{statistics.default_users}}，</el-link>
        <el-link  :underline="false" @click="showUserDetail(4)">活跃用户：{{statistics.active_users}}</el-link>
        <el-popover
            placement="top-start"
            width="500"
            trigger="click"
          style="margin-left: 15px;"
        >
        <div>沉默等级：一级沉默用(3个月无有效下单)、二级沉默用(6个月无有效下单)、三级沉默用(一年以上无有效下单)(无触发节点)</div>
        <div>新用户：注册时间小于等于1个月(30天)，且无【有效订单】的用户；</div>
        <div>无效用户：注册时间大于1个月（30天），且无【有效订单】的用户(无触发节点)；</div>
        <div>活跃用户：本月内（30天）有效子订单大于5个()的用户；</div>
         <div> 普通用户：（非以上用户）默认</div>
    <br>
       <div>有效订单：已发货订单；</div>
       <div>订单生成时，记录本单产生时用户属性；
        </div>
       <div>订单发货后更新用户属性；</div>
            <i
                class="el-icon-question"
                slot="reference"
            ></i>
        </el-popover>
    </div> -->
        <el-card shadow="hover">
            <el-table
                stripe
                :data="DataList"
                fit
                border
                size="mini"
                highlight-current-row
                style="width: 100%"
            >
                <!-- <el-table-column label="马甲ID" prop="id" align="center" /> -->
                <el-table-column
                    label="订单号"
                    prop="sub_order_no"
                    align="center"
                    width="190"
                    show-overflow-tooltip
                />
                <el-table-column
                    label="套餐名称"
                    prop="package_name"
                    align="center"
                    width="120px"
                    show-overflow-tooltip
                />
                <el-table-column
                    label="用户昵称"
                    prop="nickname"
                    align="center"
                    show-overflow-tooltip
                    width="210"
                >
                <!-- <template slot-scope="scope">
                        {{ scope.row.nickname }}<span style="color: red;">【{{userText[scope.row.user_attribute]}}】</span>
                    </template> -->
                </el-table-column>
                <el-table-column
                    label="购买时间"
                    prop="payment_time"
                    align="center"
                    width="160"
                >
                    <template slot-scope="row">
                        {{ row.row.payment_time | timeFormat }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="份数"
                    prop="order_qty"
                    align="center"
                    width="80px"
                />
                <el-table-column
                    label="实际支付"
                    prop="payment_amount"
                    align="center"
                    width="110px"
                />
                <el-table-column
                    label="订单状态"
                    prop="status"
                    align="center"
                    width="80px"
                >
                    <template slot-scope="{ row }">
                        {{ statusTxt[row.sub_order_status] }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="收货城市"
                    prop="stime"
                    align="center"
                    show-overflow-tooltip
                    min-width="120"
                >
                    <template slot-scope="{ row }">
                        {{
                            row.address_info[0].province_name +
                            row.address_info[0].city_name +
                            row.address_info[0].district_name +
                            row.address_info[0].address
                        }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作"
                    fixed="right"
                    width="110"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        <el-button
                            type="primary"
                            size="mini"
                            @click="handleClick(row)"
                            >查看详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 分页 -->
        <el-pagination
            class="flex-layout"
            style="margin-top: 10px"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            :page-size="query.limit"
            :current-page="query.page"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
        <el-dialog
            title="订单信息"
            :close-on-click-modal="false"
            :visible.sync="dialogVisible1"
            width="1200px"
            append-to-body
        >
            <div class="table" v-if="orderDetail.length">
                <el-card
                    class="card"
                    shadow="hover"
                    v-for="(item, index) in orderDetail"
                    :key="index"
                >
                    <div class="card-title">
                        <div>
                            <el-tag
                                size="mini"
                                effect="dark"
                                :type="
                                    middlewareFormat(
                                        'order_type_tag_type',
                                        item.order_type
                                    )
                                "
                            >
                                {{
                                    middlewareFormat(
                                        "order_type",
                                        item.order_type
                                    )
                                }}
                            </el-tag>
                            <el-tag
                                size="mini"
                                class="m-l-8"
                                :type="
                                    middlewareFormat(
                                        'order_status_tag_type',
                                        item.sub_order_status
                                    )
                                "
                                >{{
                                    middlewareFormat(
                                        "order_status",
                                        item.sub_order_status
                                    )
                                }}</el-tag
                            >
                            <el-tag
                                type="info"
                                @click="copy(item.sub_order_no)"
                                size="mini"
                                class="f-12 m-l-8"
                            >
                                {{ item.sub_order_no }}
                            </el-tag>
                            <el-link
                                type="warning"
                                :underline="false"
                                class="f-12 m-l-8"
                            >
                                {{ item.period }}期
                            </el-link>

                            <el-link
                                type="primary"
                                :underline="false"
                                class="f-12 m-l-8"
                            >
                                {{ item.title }}
                            </el-link>
                        </div>
                        <!-- <div>
                             <el-button slot="reference" size="mini" type="primary" @click="viewRouterHistory(item)">查看发货路由
                             </el-button>
                             <el-button size="mini" @click="viewRemarkList(item.sub_order_no)" class="m-l-8" type="warning">
                                 查看历史备注</el-button>
                                 <el-button size="mini" @click="createWorkOrder(item)" class="m-l-8" type="primary">
                                     创建工单</el-button>
                         </div> -->
                    </div>
                    <div class="order-main">
                        <div>
                            <div>
                                <b>套餐标题：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{ item.package_name }}
                                </el-link>
                            </div>
                            <div>
                                <b>用户昵称：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{ item.nickname }}
                                </el-link>
                            </div>
                            <div>
                                <b>下单时间：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                >
                                    {{ item.created_time | timeFormat }}
                                </el-link>
                            </div>

                            <div>
                                <b>支付时间：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                >
                                    {{ item.payment_time | timeFormat }}
                                </el-link>
                            </div>
                        </div>

                        <div>
                            <div>
                                <b>平台来源：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{
                                        middlewareFormat(
                                            "order_from",
                                            item.order_from
                                        )
                                    }}
                                </el-link>
                            </div>
                            <div>
                                <div>
                                    <b>主订单号：</b>
                                    <el-link
                                        type="info"
                                        :underline="false"
                                        class="f-12"
                                        @click="copy(item.main_order_no)"
                                    >
                                        {{ item.main_order_no }}
                                    </el-link>
                                </div>
                            </div>
                            <div>
                                <b>实际支付：</b>
                                <el-link
                                    v-if="item.order_type !== 4"
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{ item.payment_amount }}
                                </el-link>
                                <el-link
                                    v-if="item.order_type === 4"
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{ item.rabbit_payment_amount }}
                                </el-link>
                            </div>
                            <!-- <div>
                                 <b>订单金额：</b
                                 ><el-link
                                     type="info"
                                     :underline="false"
                                     class="f-12"
                                     >{{ item.main_order_money }}
                                 </el-link>
                             </div> -->
                            <div>
                                <b>购买数量：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{ item.order_qty }}
                                </el-link>
                            </div>
                        </div>
                        <div v-if="item.order_type !== 4">
                            <div>
                                <b>退款状态：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{
                                        middlewareFormat(
                                            "refund_status",
                                            item.refund_status
                                        )
                                    }}
                                </el-link>
                            </div>
                            <div>
                                <b>是否赠品：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                >
                                    {{
                                        middlewareFormat(
                                            "is_gift",
                                            item.is_gift
                                        )
                                    }}
                                </el-link>
                            </div>
                            <div>
                                <b>是否暂存：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                >
                                    {{ middlewareFormat("is_ts", item.is_ts) }}
                                </el-link>
                            </div>
                            <div>
                                <b>发票状态：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{
                                        middlewareFormat(
                                            "invoice_progress",
                                            item.invoice_progress
                                        )
                                    }}
                                </el-link>
                            </div>
                        </div>
                        <div>
                            <div>
                                <b>快递公司：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                >
                                    {{
                                        middlewareFormat(
                                            "express_type",
                                            item.express_type
                                        )
                                    }}
                                </el-link>
                            </div>
                            <div>
                                <b>快递单号：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{ item.express_number }}
                                </el-link>
                            </div>
                            <div>
                                <b>退货单号：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{ item.return_number }}
                                </el-link>
                            </div>

                            <div>
                                <b>快递费用：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{ item.express_fee }}
                                </el-link>
                            </div>
                        </div>
                        <div>
                            <div>
                                <b>收货人：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{ item.consignee_encrypt }}
                                </el-link>
                            </div>
                            <div>
                                <b>收货电话：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{ item.consignee_phone_encrypt }}
                                </el-link>
                            </div>
                            <div>
                                <b>收货地址：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{ item.address }}
                                </el-link>
                            </div>
                            <div>
                                <b>收货时间：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                >
                                    {{ item.goods_receipt_time | timeFormat }}
                                </el-link>
                            </div>
                        </div>
                        <div>
                            <div>
                                <b>T+推送状态：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{
                                        middlewareFormat(
                                            "push_t_status",
                                            item.push_t_status
                                        )
                                    }}
                                </el-link>
                            </div>
                            <div>
                                <b>萌牙推送状态：</b>
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{
                                        middlewareFormat(
                                            "push_wms_status",
                                            item.push_wms_status
                                        )
                                    }}
                                </el-link>
                            </div>
                        </div>
                    </div>
                </el-card>
            </div>
        </el-dialog>
        <DetailsOfSoldUsers
            ref="DetailsOfSoldUsersref"
            :parentObj="this"
            v-if="DetailsOfSoldUsersVisible"
        ></DetailsOfSoldUsers>
    </el-dialog>
    
</template>

<script>
import DetailsOfSoldUsers from "./DetailsOfSoldUsers.vue";
export default {
    components: {
        DetailsOfSoldUsers,
    },
    filters: {
        timeFormat(val) {
            if (val == "1970-01-01 08:00:00") {
                return "-";
            } else {
                return val;
            }
        },
    },
    data() {
        return {
            dialogVisible: false,
            dialogVisible1: false,
            btnVisible: false,
            DetailsOfSoldUsersVisible:false,
            DataList: [],
            orderDetail: [],
            statusTxt: {
                0: "待支付",
                1: "已支付",
                2: "已发货",
                3: "已完成",
                4: "已取消",
            },
            userText: {
                0: "普通用户",
                1: "新用户",
                2: "无效用户",
                3: "活跃用户",
                4: "一级沉默用户",
                5: "二级沉默用户",
                6: "三级沉默用户",
            },
            query: {
                page: 1,
                limit: 10,
                buyer_review_status: "",
                periods_type: "",
                periods: "",
                title: "",
            },
            total: 0,
            statistics:{},
            rows: {},
            invoice_progressOptions: [
                // 发票状态
            ],
            is_tsOptions: [
                // 是否暂存
            ],
            is_giftOptions: [
                // 是否赠品
            ],
            payment_methodOptions: [
                // 支付方式
            ],
            order_fromOptions: [
                // 客户端平台
            ],
            refund_statusOptions: [
                // 退款状态
            ],
            express_typeOptions: [
                // 快递方式
            ],
            order_statusOptions: [
                // 订单状态
            ],
            push_t_statusOptions: [
                // T+状态
            ],
            orders: {},
        };
    },
    mounted() {},
    methods: {
        getData() {
            let params = {
                type: 1,
                period: this.rows.id,
                periods_type: this.rows.periods_type,
                page: this.query.page,
                limit: this.query.limit,
            };
            this.$request.article
                .getSoldPurchasedOrderList(params)
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.DataList = res.data.data.list || [];
                        this.total = res.data.data.total;
                        this.statistics = res.data.data.statistics;
                    }
                });
        },
        openForm(rows) {
            this.dialogVisible = true;
            this.rows = rows;
            this.query.page = 1;
            this.query.limit = 10;
            this.getData();
        },
        handleClick(rows) {
            this.orders = rows;
            this.dialogVisible1 = true;
            this.getConfigList();
        },
        getOrderDetail() {
            console.log(11111);
            this.$request.article
                .getOrderDetail({
                    order_no: this.orders.sub_order_no,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.orderDetail = [];
                        this.orderDetail.push(res.data.data);
                        console.log(this.orderDetail, 3333333333333);
                    }
                });
        },
        showUserDetail(type){
            this.DetailsOfSoldUsersVisible = true;
                this.$nextTick(() => {
                    this.$refs.DetailsOfSoldUsersref.openForm(this.rows, type);
                });
        },
        async getConfigList() {
            const res = await this.$request.article.getConfigList();
            if (res.data.error_code == 0) {
                const data = res.data.data;
                this.invoice_progressOptions = data.invoice_progress;
                this.is_tsOptions = data.is_ts;
                this.is_giftOptions = data.is_gift;
                this.payment_methodOptions = data.payment_method;
                this.order_fromOptions = data.order_from;
                this.express_typeOptions = data.express_type;
                this.refund_statusOptions = data.refund_status;
                this.order_statusOptions = data.sub_order_status;
                this.push_t_statusOptions = data.push_t_status;
                this.push_wms_statusOptions = data.push_wms_status;
                this.order_typeOptions = data.order_type;
                this.getOrderDetail();
            }
        },
        middlewareFormat(type, val) {
            switch (type) {
                case "is_ts":
                    return this.is_tsOptions.find((i) => i.value == val)
                        ? this.is_tsOptions.find((i) => i.value == val).label
                        : "-";
                case "is_gift":
                    return this.is_giftOptions.find((i) => i.value == val)
                        ? this.is_giftOptions.find((i) => i.value == val).label
                        : "-";
                case "refund_status":
                    return this.refund_statusOptions.find((i) => i.value == val)
                        ? this.refund_statusOptions.find((i) => i.value == val)
                              .label
                        : "-";
                case "invoice_progress":
                    return this.invoice_progressOptions.find(
                        (i) => i.value == val
                    )
                        ? this.invoice_progressOptions.find(
                              (i) => i.value == val
                          ).label
                        : "-";
                case "order_from":
                    return this.order_fromOptions.find((i) => i.value == val)
                        ? this.order_fromOptions.find((i) => i.value == val)
                              .label
                        : "-";
                case "order_status":
                    return this.order_statusOptions.find((i) => i.value == val)
                        ? this.order_statusOptions.find((i) => i.value == val)
                              .label
                        : "-";
                case "express_type":
                    return this.express_typeOptions.find((i) => i.value == val)
                        ? this.express_typeOptions.find((i) => i.value == val)
                              .label
                        : "-";

                case "push_t_status":
                    return this.push_t_statusOptions.find((i) => i.value == val)
                        ? this.push_t_statusOptions.find((i) => i.value == val)
                              .label
                        : "-";
                case "push_wms_status":
                    return this.push_wms_statusOptions.find(
                        (i) => i.value == val
                    )
                        ? this.push_wms_statusOptions.find(
                              (i) => i.value == val
                          ).label
                        : "-";
                case "order_type":
                    return this.order_typeOptions.find((i) => i.value == val)
                        ? this.order_typeOptions.find((i) => i.value == val)
                              .label
                        : "-";
                case "order_type_tag_type":
                    switch (
                        this.order_typeOptions.find((i) => i.value == val).value
                    ) {
                        case 1:
                            return "danger";
                        case 2:
                            return "danger";
                        case 3:
                            return "danger";
                        case 4:
                            return "danger";
                        case 5:
                            return "info";
                        case 6:
                            return "info";
                        case 7:
                            return "";
                        case 8:
                            return "warning";
                        case 9:
                            return "";
                    }
                    break;
                case "order_status_tag_type":
                    switch (
                        this.order_statusOptions.find((i) => i.value == val)
                            .value
                    ) {
                        case 0:
                            return "info";
                        case 1:
                            return "";
                        case 2:
                            return "warning";
                        case 3:
                            return "success";
                        case 5:
                            return "danger";
                    }
                    break;
            }
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.flex-layout {
    text-align: center;
}
.table {
    margin-top: 10px;

    .f-12 {
        font-size: 12px;
    }

    .card {
        margin-bottom: 8px;

        .card-title {
            justify-content: space-between;
            display: flex;
            align-items: center;

            .m-l-8 {
                margin-left: 10px;
            }
        }
    }
}
.order-main {
    display: flex;
    overflow: scroll;
    & > div {
        // overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: -moz-box;
        -moz-line-clamp: 1;
        -moz-box-orient: vertical;
        word-wrap: break-word;
        word-break: break-all;
        white-space: nowrap;
        // min-width: 100px;
        margin-right: 10px;

        color: #333;

        & > div {
            display: flex;
            align-items: center;
        }
        b {
            line-height: 2;
            opacity: 1;
            display: inline-block;
            font-weight: bold;
        }

        // width: 30;
    }
}
.article-layout {
    .article-main {
        margin: 10px 0;

        /deep/ .el-table .warning-row {
            background: oldlace;
        }

        /deep/ .el-table .danger-row {
            background: oldlace;
        }

        /deep/ .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    
    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
</style>
