<template>
    <el-dialog
        title="汇总明细"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible"
        width="55%"
        append-to-body
    >
   
        <el-card shadow="hover">
            <el-table
                stripe
                :data="DataList"
                fit
                border
                size="mini"
                highlight-current-row
                style="width: 100%"
            >
                <!-- <el-table-column label="马甲ID" prop="id" align="center" /> -->
                <el-table-column
                    label="头像"
                    prop="avatar_image"
                    align="center"
        
                    width="120"
                >
                <template slot-scope="scope">
                    <el-image
                                    style="
                                        width:80px;
                                        height: 80px;
                                        border-radius: 50%;
                                    "
                                    :src="scope.row.avatar_image"
                                  
                                ></el-image>
                    </template>
                </el-table-column>
                <el-table-column
                    label="用户昵称"
                   
                    align="center"
        
                    width="240"
                >
                <template slot-scope="scope">
                   <div>{{ scope.row.nickname }}({{scope.row.uid  }})</div>
                </template>
                </el-table-column>
                <el-table-column
                    label="等级"
                    prop="user_level"
                    align="center"
                    width="120"
                >
                <template slot-scope="scope">
                       LV{{ scope.row.user_level }}
                    </template>
            </el-table-column>
                <el-table-column
                    label="注册日期"
                    prop="created_time"
                    align="center"
                    width="160"
                >
                    <template slot-scope="row">
                        {{ row.row.created_time | timeFormat }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="注册城市"
                    prop="reg_city"
                    align="center"
                    width="100px"
                />
               
                <el-table-column
                    label="用户属性"
                    prop="user_attribute_name"
                    align="center"
                    width="100"
                >
                <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.user_attribute_name" :key="index">{{ item }}</div>
                   
                    </template>
            </el-table-column>
                
            </el-table>
        </el-card>
        <!-- 分页 -->
        <el-pagination
            class="flex-layout"
            style="margin-top: 10px"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            :page-size="query.limit"
            :current-page="query.page"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
       
    </el-dialog>
</template>

<script>
export default {
    filters: {
        timeFormat(val) {
            if (val == "1970-01-01 08:00:00") {
                return "-";
            } else {
                return val;
            }
        },
    },
    data() {
        return {
            dialogVisible:false,
            DataList: [],
           
            userText: {
                0: "普通用户",
                1: "新用户",
                2: "无效用户",
                3: "活跃用户",
                4: "一级沉默用户",
                5: "二级沉默用户",
                6: "三级沉默用户",
            },
            query: {
                page: 1,
                limit: 10,
                type: "",
                periods_type: "",
                periods: "",
                sum_details: "",
            },
            total: 0,
            rows: {},
          
        };
    },
    mounted() {},
    methods: {
        getData() {
            let params = {
                type: 2,
                period: this.rows.id,
                periods_type: this.rows.periods_type,
                page: this.query.page,
                limit: this.query.limit,
                sum_details:this.query.sum_details,
            };
            this.$request.article
                .getSoldPurchasedOrderList(params)
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.DataList = res.data.data.list || [];
                        this.total = res.data.data.total;
                        
                    }
                });
        },
        openForm(rows, type) {
            this.dialogVisible = true;
           this.query.sum_details = type;
            this.rows = rows;
            this.query.page = 1;
            this.query.limit = 10;
            this.getData();
        },
       
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.flex-layout {
    text-align: center;
}
.table {
    margin-top: 10px;

    .f-12 {
        font-size: 12px;
    }

    .card {
        margin-bottom: 8px;

        .card-title {
            justify-content: space-between;
            display: flex;
            align-items: center;

            .m-l-8 {
                margin-left: 10px;
            }
        }
    }
}
.order-main {
    display: flex;
    overflow: scroll;
    & > div {
        // overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: -moz-box;
        -moz-line-clamp: 1;
        -moz-box-orient: vertical;
        word-wrap: break-word;
        word-break: break-all;
        white-space: nowrap;
        // min-width: 100px;
        margin-right: 10px;

        color: #333;

        & > div {
            display: flex;
            align-items: center;
        }
        b {
            line-height: 2;
            opacity: 1;
            display: inline-block;
            font-weight: bold;
        }

        // width: 30;
    }
}
.article-layout {
    .article-main {
        margin: 10px 0;

        /deep/ .el-table .warning-row {
            background: oldlace;
        }

        /deep/ .el-table .danger-row {
            background: oldlace;
        }

        /deep/ .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
</style>
