<template>
        <el-form ref="labelAlignForm" :model="entity" :rules="rules" label-width="118px"
            style="overflow-y: auto">
            <!-- 商品信息 -->
            <el-card class="box-card" shadow="hover" style="margin: 10px 0 10px">
                <div slot="header" class="clearfix">
                    <span>基本信息</span><!-- ({{nums}}) -->
                </div>
                <div class="text item">
                    <el-row :gutter="10">
                        <el-col :md="10" :lg="10" :xl="10">
                            <el-form-item label="商品标题" prop="title">
                                <el-input v-model="entity.title" placeholder="请输入商品标题" />
                            </el-form-item>
                        </el-col>
                        <el-col :md="10" :lg="10" :xl="10" :offset="2">
                            <el-form-item label="一句话介绍" prop="brief">
                                <el-input v-model="entity.brief" placeholder="请输入商品副标题" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :md="7" :lg="7" :xl="7">
                            <el-form-item label="上传题图" class="upload" prop="filelist1" style="margin-bottom: 30px;">
                                <vos-oss @on-error="onError" list-type="picture-card" :showFileList="true"
                                    @on-success="onSuccess($event,1)" :limit="1" :dir="dir"
                                    :file-list="entity.filelist1" @on-remove="onRemove($event,1)">
                                    <i slot="default" class="el-icon-plus"></i>
                                </vos-oss>
                                <!-- <SingleImage ref="singleupload" v-model="entity.banner_img"></SingleImage> -->
                            </el-form-item>
                        </el-col>
                        <el-col :md="6" :lg="6" :xl="6">
                            <div style="width:100%;height:200px">
                                <el-form-item label="上传题图(竖)" prop="filelist2" v-if="rows.periods_type==1">
                                    <br />
                                    <div style="display: flex;">
                                        <vos-oss @on-error="onError" list-type="picture-card" :showFileList="true"
                                            @on-success="onSuccess($event,2)" :limit="8" :dir="dir"
                                            :file-list="entity.filelist2" @on-remove="onRemove($event,2)">
                                            <i slot="default" class="el-icon-plus"></i>
                                        </vos-oss>
                                    </div>
                                </el-form-item>
                            </div>
                        </el-col>
                        <el-col :md="11" :lg="11" :xl="11">
                            <el-form-item :label="rows.periods_type==1? '上传产品图(方)': '上传产品图'" prop="filelist3">
                                <div>(最多支持8张)</div>
                                <vos-oss @on-error="onError" list-type="picture-card" :showFileList="true"
                                    @on-success="onSuccess($event,3)" :dir="dir" :file-list="entity.filelist3"
                                    :limit="8" :multiple="true" @on-remove="onRemove($event,3)">
                                    <i slot="default" class="el-icon-plus"></i>
                                </vos-oss>
                                <!-- <UploadImage ref="UploadImage" v-model="entity.product_img1"></UploadImage> -->
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-form-item label="视频封面图">
                           <vos-oss list-type="picture-card" :showFileList="true" :dir="dir"
                                    :file-list="coverList" :limit="1" :multiple="true">
                                    <i slot="default" class="el-icon-plus"></i>
                                </vos-oss>
                    </el-form-item>
                    <el-form-item label="视频上传">
                        <vos-vod @uploadSuccess="uploadSuccess" :playurl.sync="entity.video"></vos-vod>
                    </el-form-item>
                    <el-form-item label="商品详情" prop="detail">
                        <Tinymce ref="editor" @singleValidate="singleValidate" v-model.trim="entity.detail"
                            :height="300" />
                    </el-form-item>
                    <el-form-item label="采购人" prop="description">
                        <el-select v-model="entity.buyers" placeholder="请选择">
                            <el-option v-for="item in purchaseOptions" :key="item.id" :label="item.realname"
                                :value="item" :value-key="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <el-form-item label class="clearfix">
                    <div style="float: right;margin-top: 20px;">
                        <el-button type="primary" @click="submits">保存</el-button>
                    </div>
                </el-form-item>
            </el-card>
            <!-- 产品信息 -->
            <!--  <el-card class="box-card" shadow="hover" style="margin: 0 0 10px">
                <div slot="header" class="clearfix">
                    <span>产品信息</span>
                </div>
                <div v-for="(v,i) in entity.product_list" :key="i">
                    <el-row :gutter="10">
                        <el-col :md="10" :lg="10" :xl="10">
                            <el-form-item label="酒庄">
                                <el-input v-model="v.chateau_name" :disabled="product_disab"></el-input>

                            </el-form-item>
                        </el-col>
                        <el-col :md="10" :lg="10" :xl="10">
                            <el-form-item label="产区">
                                <el-input v-model="v.producing_area_name" :disabled="product_disab"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :md="10" :lg="10" :xl="10">
                            <el-form-item label="国家">
                                <el-input v-model="v.country_name" :disabled="product_disab"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :md="10" :lg="10" :xl="10">
                            <el-form-item label="葡萄">
                                <el-input v-model="v.grape_name" :disabled="product_disab"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :md="6" :lg="6" :xl="6">
                            <el-form-item label="类型">
                                <el-input v-model="v.product_type_name" :disabled="product_disab"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :md="6" :lg="6" :xl="6" :offset="1">
                            <el-form-item label="关键词">
                                <template v-if="v.product_keywords_id.length>0">
                                    <span type="info" v-for="(v,index) in v.product_keywords_id" :key="index"
                                        :underline="false" style="margin-right: 5px;">
                                        {{v.name}}
                                    </span>
                                </template>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :md="10" :lg="10" :xl="10">
                            <el-form-item label="残糖">
                                <el-input v-model="v.residual_sugar" :disabled="product_disab" placeholder="请输入残糖" />
                            </el-form-item>
                        </el-col>
                        <el-col :md="10" :lg="10" :xl="10" :offset="2">
                            <el-form-item label="酒精度">
                                <el-input v-model="v.alcohol" :disabled="product_disab" placeholder="请输入酒精度" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :md="10" :lg="10" :xl="10">
                            <el-form-item label="酿造工艺">
                                <el-input v-model="v.brewing" :disabled="product_disab" placeholder="请输入酿造工艺" />
                            </el-form-item>
                        </el-col>
                        <el-col :md="10" :lg="10" :xl="10" :offset="2">
                            <el-form-item label="Tasting Notes">
                                <el-input v-model="v.tasting_notes" :disabled="product_disab"
                                    placeholder="请输入Tasting Notes" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :md="10" :lg="10" :xl="10">
                            <el-form-item label="评分">
                                <el-input v-model="v.score" :disabled="product_disab" placeholder="请输入评分" />
                            </el-form-item>
                        </el-col>
                        <el-col :md="10" :lg="10" :xl="10" :offset="2">
                            <el-form-item label="获奖">
                                <el-input v-model="v.prize" :disabled="product_disab" placeholder="请输入获奖" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :md="10" :lg="10" :xl="10">
                            <el-form-item label="饮用建议">
                                <el-input v-model="v.drinking_suggestion" :disabled="product_disab"
                                    placeholder="请输入饮用建议" />
                            </el-form-item>
                        </el-col>
                    </el-row>

                </div>
               
            </el-card> -->
                <div style="display: flex;align-items: center;">
                    <span style="font-weight: bold;margin-right: 20px;">采购信息</span>
                    <el-button type="primary" size="mini" @click="purchaseDialog=!purchaseDialog">编辑</el-button>
                </div>
                <!-- 采购信息 -->
                <div style="display: flex;flex-wrap: wrap;margin-top: 20px;" v-if="purList.length>0">
                    <div v-for="(item,i) in purList" :key="i"
                        style="min-width: 300px;border: 1px solid #ddd;padding: 10px;margin-right: 10px;margin-bottom: 10px;">
                        <div class="purchase_list">
                            <label>英文名</label>
                            <span>{{item.en_product_name}}</span>
                        </div>
                        <div class="purchase_list">
                            <label>品名</label>
                            <span>{{item.cn_product_name}}</span>
                        </div>
                        <div class="purchase_list">
                            <label>简码</label>
                            <span>{{item.short_code}}</span>
                        </div>
                        <div class="purchase_list">
                            <label>发货仓库</label>
                            <span>{{item.warehouse&&typeof item.warehouse ==='object' ? item.warehouse.fictitious_name :item.warehouse}}</span>
                        </div>
                    </div>
                </div>
                <div style="display: flex;align-items: center;margin: 20px 0;">
                    <span style="font-weight: bold;margin-right: 20px;">套餐信息</span>
                    <el-button type="primary" size="mini" @click="packEdit()  ">编辑</el-button>
                </div>
                <!-- 套餐信息 -->
                <div style="display: flex;flex-wrap: wrap;margin-top: 20px;"
                    v-if="entity.salesList&&entity.salesList[0].package_name!=''">
                    <div v-for="(v,i) in entity.salesList" :key="i"
                        style="margin: 0px 20px 20px 0;display: flex;justify-content: space-between;border: 1px solid #ddd;padding: 7px 15px;width: 300px;">
                        <span>{{v.package_name}}</span>
                        <span>{{v.price}}</span>
                        <span style="text-decoration: line-through;color: silver;">{{v.market_price}}</span>
                    </div>
                </div>
                <div style="display: flex;align-items: center;">
                    <span style="font-weight: bold;margin-right: 20px;">商品信息</span>
                    <el-button type="primary" size="mini" @click="goodsEdit">编辑</el-button>
                </div>
                <div style="display: flex;flex-wrap: wrap;margin-top: 20px;" v-if="entity.onsale_time">
                    <div class="box_flex">
                        <el-form-item label="上架时间" label-width="70px">
                            <el-date-picker v-model="entity.onsale_time" :disabled="true"
                                value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="开售时间">
                            <el-date-picker v-model="entity.sell_time" :disabled="true"
                                value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                    </div>
                    <div class="box_flex">
                        <el-form-item label="下架时间" label-width="70px">
                            <el-date-picker v-model="entity.sold_out_time" :disabled="true"
                                value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="发货时间">
                            <el-date-picker v-model="entity.predict_shipment_time" :disabled="true"
                                value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                    </div>
                </div>
                </div>
            </el-card>

            <el-dialog title="采购信息" :visible.sync="purchaseDialog" append-to-body destroy-on-close width="70%"
                :close-on-click-modal="false">
                <div v-if="purchaseDialog">
                
                <div  class="pur_rwap" style="justify-content: space-between;">
                    <div class="pur_rwap">
                        <div class="pur_inline">
                            <label>进口类型</label>
                            <el-select v-model="entity.import_type" @change="updatePurchase" style="width: 110px;">
                                <el-option label="自进口" :value="0" />
                                <el-option label="地采" :value="1" />
                                <!-- <el-option label="跨境" value="28" /> -->
                            </el-select>
                        </div>
                        <div class="pur_inline">
                            <label>供应商</label>
                            <el-input v-model="entity.supplier" @blur="updatePurchase" style="width: 110px;"
                                placeholder="请输入" />
                        </div>
                        <!-- <div class="pur_inline">
                            <label>采购人</label>
                            <el-select clearable v-model="entity.buyers1" :disabled="true" value-key="id"
                                style="width: 110px;">
                                <el-option v-for="(item,i) in purchaseOptions" :key="i" :label="item.realname"
                                    :value="item" />
                            </el-select>
                        </div> -->
                        <div class="pur_inline" v-if="rows.periods_type!=2&&rows.periods_type!=3">
                            <label>是否代发</label>
                            <!--  <el-switch style="display: block;margin-top: 8px;" @change="updatePurchase"
                                    v-model="entity.is_supplier_delivery" active-color="#13ce66" inactive-color="#ff4949">
                                </el-switch> -->
                            <el-radio-group style="padding-top: 12px;" v-model="entity.is_supplier_delivery"
                                @change="updatePurchase">
                                <el-radio v-for="(item,key) in operations" :key="key" :label="item.label">
                                    {{ item.value }}
                                </el-radio>
                            </el-radio-group>
                        </div>
                        <div class="pur_inline" v-if="rows.periods_type!=3&&rows.periods_type!=1">
                            <label>是否预售</label>
                            <!-- <el-switch style="display: block;margin-top: 8px;" @change="updatePurchase"
                                    v-model="entity.is_presell" active-color="#13ce66" inactive-color="#ff4949">
                                </el-switch> -->
                            <el-radio-group style="padding-top: 12px;" v-model="entity.is_presell"
                                @change="updatePurchase">
                                <el-radio v-for="(item,key) in operations" :key="key" :label="item.label">
                                    {{ item.value }}
                                </el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div>
                        <el-button size="small" type="primary" @click="adds">继续添加
                        </el-button>
                    </div>
                </div>
                    <div v-for="(v,index) in entity.purchaseList"  :key="index" style="margin-top: 15px;">
                    <el-row>
                        <el-col :md="8" :lg="8" :xl="8">
                            <el-form-item label="简码" :prop="'purchaseList.'+index+'.short_code'"
                                :rules="rules.short_code">
                                <el-input v-model="v.short_code" placeholder="请输入简码" />
                            </el-form-item>
                        </el-col>
                        <el-col :md="2" :lg="2" :xl="2">
                            <el-button style="margin-left: 20px;margin-top: 6px;" type="primary" size="mini"
                                @click="searchCode(v.short_code)">搜索
                            </el-button>
                        </el-col>
                        <el-col :md="2" :lg="2" :xl="2">
                            <el-button v-if="entity.purchaseList&&entity.purchaseList.length>1" size="mini"
                                type="danger" style="margin-left:20px;margin-top: 6px;" icon="el-icon-delete"
                                @click="del(index)">删除
                            </el-button>
                        </el-col>
                    </el-row>
                    <div v-if="v.purchase.length>0" v-for="(item,i) in v.purchase"
                        :class="item.checked?'purchaseActive':'purchase'" :key="i">
                        <!-- <div>
                                <el-row :gutter="10">
                                    <el-col :md="2" :lg="2" :xl="2">
                                        <el-checkbox v-model="item.checked" @change="checkedStore(item,v.purchase)"
                                            style="margin: 63px 10px;">
                                        </el-checkbox>
                                    </el-col>
                                </el-row>
                            </div> -->
                        <div style="padding-left: 20px;">
                            <div class="pur_rwap">
                                <div class="pur_inline">
                                    <label>英文名</label>
                                    <span>
                                        <div style="width: 800px;"> {{item.en_product_name}}<i
                                                @click="copyName(item.en_product_name)"
                                                style="font-size: 18px;color: #409eff;cursor: pointer;margin-left: 5px;"
                                                class="el-icon-document-copy"></i></div>
                                    </span>
                                </div>
                            </div>
                            <div class="pur_rwap">
                                <div class="pur_inline">
                                    <label>品名</label>
                                    <span> {{item.cn_product_name}}
                                        <i @click="copyName(item.cn_product_name)"
                                            style="font-size: 18px;color: #409eff;cursor: pointer;margin-left: 5px;"
                                            class="el-icon-document-copy"></i></span>
                                </div>
                            </div>
                            <div class="pur_rwap">
                                <div class="pur_inline">
                                    <label>国家</label>
                                    <span> {{item.country_name}}</span>
                                </div>
                                <div class="pur_inline">
                                    <label>容量</label>
                                    <span> {{item.capacity}}</span>
                                </div>
                                <div class="pur_inline">
                                    <label>年份</label>
                                    <span> {{item.grape_picking_years}}</span>
                                </div>
                                <div class="pur_inline">
                                    <label>简码</label>
                                    <span> {{item.short_code}}</span>
                                </div>
                            </div>
                            <div class="pur_rwap">
                                <div class="pur_inline">
                                    <label>类型</label>
                                    <span> {{item.product_type_name}}</span>
                                </div>
                                <div class="pur_inline">
                                    <label>参考成本</label>
                                    <span style="display: flex;">
                                        {{item.costprice1}}

                                    </span>
                                </div>
                                <div class="pur_inline">
                                    <label>发货仓库</label>
                                    <span>
                                        <el-form-item label-width="0px"
                                            :prop="'purchaseList.'+index+'.purchase.'+i+'.warehouse'"
                                            :rules="rules.warehouse">
                                            <el-select v-model="item.warehouse" filterable size="mini" value-key="fictitious_id" style="width:400px">
                                                <el-option v-for="(v,i) in sendWarehouseList" :key="v.id"
                                                    :label="v.fictitious_name" :value="v" />
                                            </el-select>
                                        </el-form-item>
                                    </span>
                                </div>
                            </div>
                            <div class="pur_rwap">
                                <div class="pur_inline">
                                    <div style="display: flex;">
                                        <label style="padding-top: 10px;">仓库信息</label>
                                        <div style="margin:0 10px;">
                                            <div v-show="item.warehouseList&&item.warehouseList.length>0"
                                                v-for="(v,i) in item.warehouseList" style="display: flex;"
                                                :key="v.fictitious_id">
                                                <div style="min-width: 160px;"> {{v.fictitious_name}}</div>
                                                <label style="display: inline-block;width: 40px;">库存</label>
                                                <span> {{v.goods_count}}</span>
                                            </div>
                                            <div v-show="item.warehouseList&&item.warehouseList.length==0"
                                                style="margin-top: 10px;">
                                                暂无仓库信息
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="pur_inline">
                                        <label>售卖库存</label>
                                        <span>
                                            <el-form-item label-width="0px"
                                                :prop="'purchaseList.'+index+'.purchase.'+i+'.inventory'"
                                                :rules="rules.inventory">
                                                <el-input v-model="item.inventory" size="mini" placeholder="售卖库存" />
                                            </el-form-item>
                                        </span>
                                    </div>
                                    <div class="pur_inline">
                                        <label>本期成本</label>
                                        <span>
                                            <el-form-item label-width="0px"
                                                :prop="'purchaseList.'+index+'.purchase.'+i+'.costprice'"
                                                :rules="rules.costprice">
                                                <el-input v-model="item.costprice" size="mini" style="width: 100px;"
                                                    placeholder="本期成本" />
                                            </el-form-item>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <span slot="footer" class="dialog-footer" style="display:flex;justify-content:flex-end;">
                    <el-button type="primary" @click="handleNext(1,false)">下一步</el-button>
                    <el-button type="primary" @click="handleNext(1,true)">保存</el-button>
                </span>
                </div>
            </el-dialog>
            <el-dialog title="套餐信息" :visible.sync="packDialog" append-to-body destroy-on-close width="70%"
                :close-on-click-modal="false">
                <div v-if="packDialog">
                    <div style="text-align: right;margin-bottom: 20px;">
                        <el-button size="small" type="primary" @click="addSales">继续添加
                        </el-button>
                    </div>
                    <div v-for="(v,index) in entity.salesList" :key="index" class="sale_list_wrap">
                        <div style="display: flex;">
                            <el-form-item label="套餐名称" :prop="'salesList.'+index+'.package_name'"
                                :rules="rules.package_name" label-width="100px">
                                <el-select v-model="v.package_name" :disabled="v.is_onsale" placeholder="请选择"
                                    @change="packageChange(v)">
                                    <el-option v-for="item in parckageOptions" :key="item.value" :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <div v-if="v.package_name=='其他'||v.is_mystery_box==1">
                                <el-form-item label="自定义名称" :prop="'salesList.'+index+'.package_name1'"
                                    :rules="rules.package_name1" label-width="100px">
                                    <el-input v-model="v.package_name1" :disabled="v.is_onsale" @blur="changePack(v)"
                                        style="width: 200px;" placeholder="自定义名称" />
                                </el-form-item>
                            </div>
                            <div style="margin: 8px 0 0 30px;"
                                v-if="v.package_name!='其他'&&v.is_mystery_box!=1&&v.package_name!='单支套餐'">
                                <el-checkbox v-model="v.same" :disabled="v.is_onsale" @change="sameChange(v)">
                                    套餐内不相同</el-checkbox>
                            </div>
                            <div>
                                <el-button :disabled="v.is_onsale"
                                    v-if="entity.salesList&&entity.salesList.length>1&&v.isDel" size="mini"
                                    type="danger" style="margin-left:20px;margin-top: 6px;" icon="el-icon-delete"
                                    @click="delSales(index)">删除</el-button>
                            </div>
                        </div>
                        <div style="display: flex;">
                        
                            <el-form-item label="售价" :prop="'salesList.'+index+'.price'" :rules="rules.price"
                                label-width="60px">
                                <el-input v-model="v.price" :disabled="v.is_onsale" placeholder="售价" />
                            </el-form-item>
                            <el-form-item label="市场价" :prop="'salesList.'+index+'.market_price'"
                                :rules="rules.market_price" label-width="80px">
                                <el-input v-model="v.market_price" :disabled="v.is_onsale" placeholder="市场价" />
                            </el-form-item>
                        </div>
                        <!-- 套餐内设置 -->
                        <div v-if="v.package_name" style="margin-left: 30px;position: relative;"
                            v-for="(child,j) in v.numList" :key="j">
                            <div class="box_flex">
                                <div>
                                    <div class="sale_addInfo sale_addInfo_p" @click="handleCodeVisible(v,child) "
                                        v-if="!child.visible">
                                        <i class="el-icon-circle-plus-outline"></i>
                                    </div>
                                    <!-- 选择框 -->
                                    <div class="btn_visib_wrap sale_visi_wrap" v-show="child.codeVisible">
                                        <div class="btn_visib_content">
                                            <div class="code_close" style="margin-top: 4px;"
                                                @click="handleCodeVisible(v,child)">
                                                <i class="el-icon-close"></i>
                                            </div>
                                            <div class="code_content">
                                                <el-select v-if="v.is_mystery_box==1" v-model="child.productList"
                                                    multiple collapse-tags style="width: 600px;"
                                                    @change="productChange(child)"
                                                    @visible-change="visibleChange($event,child,v)" placeholder="请选择"
                                                    value-key="id">
                                                    <el-option v-for="(j,i) in purList" :key="j.id"
                                                        :label="j.en_product_name" :value="j">
                                                    </el-option>
                                                </el-select>
                                                <div v-else v-for="(j,i) in purList" class="code_wrap" :key="i"
                                                    @click="confirmPur(j,child,v)">
                                                    {{j.en_product_name}}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 选择之后的数据 -->
                                    <div class="sale_addInfo  " v-if="child.visible&&child.productList.length>0"
                                        @click="handleCodeVisible(v,child)">
                                        <div v-for="(p,k) in child.productList"
                                            :style="k>0?'border-top: 1px solid #ddd;':''" style="padding: 10px;" :key="k">
                                            <div class="pur_list_rwap">
                                                <div class="pur_item">
                                                    <label>英文名</label>
                                                    <span> {{p.en_product_name}}</span>
                                                    <span v-if="child.isGift"
                                                        style="color: #ebb563;margin-left: 20px;">赠品</span>
                                                </div>
                                            </div>
                                            <div class="pur_list_rwap">
                                                <div class="pur_item">
                                                    <label>品名</label>
                                                    <span> {{p.cn_product_name}}</span>
                                                </div>
                                            </div>
                                            <div class="pur_list_rwap">
                                                <div class="pur_item">
                                                    <label>容量</label>
                                                    <span> {{p.capacity}}</span>
                                                </div>
                                                <div class="pur_item">
                                                    <label>年份</label>
                                                    <span> {{p.grape_picking_years}}</span>
                                                </div>
                                                <div class="pur_item">
                                                    <label>简码</label>
                                                    <span> {{p.short_code}}</span>
                                                </div>
                                                <!-- <div class="pur_item">
                                                 <label>匹配度</label>
                                                 <span> {{child.product.short_code}}</span>
                                             </div> -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div style="margin-left: 20px;">
                                    <div v-if="v.numList.length>1&&child.isDel&&j>0" style="padding-bottom: 30px;">
                                        <el-button :disabled="v.is_onsale" type="danger" style="margin-left: 5px;"
                                            size="mini" @click="delProduct(v,j)">删除
                                        </el-button>
                                    </div>
                                    <div v-if="j==0&&(v.package_name=='其他'||v.is_mystery_box==1)"
                                        style="padding-bottom: 30px;">
                                        <el-button type="primary" :disabled="v.is_onsale" style="margin-left: 5px;"
                                            size="mini" @click="addProduct(v)">添加
                                        </el-button>
                                    </div>
                                    <el-form-item label="数量" label-width="60px"
                                        :prop="'salesList.'+index+'.numList.'+j+'.nums'"
                                        :rules="numberRules">
                                        <el-input-number v-model.number="child.nums" :min="0"  :disabled="child.disabled" label="数量"></el-input-number>
                                    </el-form-item>
                                </div>

                            </div>
                        </div>
                        <div class="box_flex" style="line-height: 40px;margin-left: 25px;" v-if="v.visible">
                            <el-checkbox v-model="v.is_hidden">隐藏套餐</el-checkbox>
                            <el-checkbox v-if="rows.periods_type!=2" v-model="v.is_original_package"
                                :disabled="v.is_onsale">原箱发货
                            </el-checkbox>


                        </div>
                        <div class="box_flex" style="line-height: 40px;margin-left: 25px;" v-if="v.visible">
                            <div style="margin-left: 20px;">
                                赠送优惠券
                                <el-popover placement="top" width="200" trigger="click" content="此设置会在用户确认收货后发放优惠券一张">
                                    <i class="el-icon-question" slot="reference"></i>
                                </el-popover>
                                <el-input v-model="v.coupons_id" :disabled="v.is_onsale" size="mini"
                                    style="width: 160px;margin-left: 10px;" placeholder="多个请用·分开" />
                            </div>
                            <div style="margin-left: 20px;display: flex;" v-if="rows.periods_type!=2">
                                优惠减免 <el-popover placement="top" width="200" trigger="click"
                                    content="此设置会在最终计算金额时优先在套餐售价上扣减,最大设置不能超过售价的50%(百分之五十)">
                                    <i class="el-icon-question" slot="reference"></i>
                                </el-popover>
                                <el-form-item :prop="'salesList.'+index+'.preferential_reduction'"
                                    :rules="rules.preferential_reduction" label-width="0">
                                    <el-input v-model="v.preferential_reduction" :disabled="v.is_onsale" size="mini"
                                        style="width: 160px;margin-left: 10px;" placeholder="请输入优惠减免" />
                                </el-form-item>

                            </div>
                            <!-- 是否不限量 -->
                           <!-- <div style="margin-left: 20px;">
                                <el-checkbox v-model="v.unlimited" :disabled="v.is_onsale" @change="changeUnlimit">是否不限量
                                </el-checkbox>
                            </div>
                            <div style="margin-left: 20px;" v-if="!v.unlimited">
                                套餐库存
                                <el-input v-model="v.inventory" :disabled="v.is_onsale" size="mini"
                                    style="width: 160px;margin-left: 10px;" placeholder="请输入套餐库存" />
                            </div> -->
                            <div style="margin-left: 20px;" v-if="rows.periods_type!=2">
                                <!-- <el-checkbox v-model="v.is_gift">是否赠品</el-checkbox> -->
                                <el-button type="warning" style="margin-left: 34px;" size="mini"
                                    @click="addProduct(v,true)">添加赠品
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
                <span slot="footer" class="dialog-footer" style="display:flex;justify-content:flex-end;">
                    <el-button type="primary" @click="handleNext(2,false)">下一步</el-button>
                    <el-button type="primary" @click="handleNext(2,true)">保存</el-button>
                </span>
            </el-dialog>
            <el-dialog title="商品信息" :visible.sync="goodsDialog" append-to-body destroy-on-close width="70%"
                :close-on-click-modal="false">
                <div v-if="goodsDialog">
                    <div class="box_flex" style="margin: 0 0 0 31px;">
                        <el-form-item label="初始量" label-width="80px" prop="limit_number">
                            <el-input v-model="entity.limit_number" size="mini" style="width: 160px;margin-left: 10px;"
                                placeholder="初始量" />
                        </el-form-item>
                        <el-form-item label="临界值" label-width="80px" prop="critical_value">
                            <el-input v-model="entity.critical_value" size="mini"
                                style="width: 160px;margin-left: 10px;" placeholder="临界值" />
                        </el-form-item>
                        <el-form-item label="自增量" label-width="80px" prop="incremental">
                            <el-input v-model="entity.incremental" size="mini" style="width: 160px;margin-left: 10px;"
                                placeholder="自增量" />
                        </el-form-item>
                    </div>
                    <div class="box_flex" style="margin: 0 0 15px 31px;">
                        <el-checkbox v-model="entity.is_channel">渠道销售</el-checkbox>
                        <el-checkbox v-model="entity.is_hidden_price">隐藏价格</el-checkbox>
                        <el-checkbox v-model="entity.is_support_ts">支持暂存</el-checkbox>
                        <el-checkbox v-model="entity.is_cold_chain">温控包裹</el-checkbox>
                        <el-checkbox v-model="entity.sellout_sold_out">售完不下架</el-checkbox>
                        <el-checkbox v-model="entity.is_sold_out_lock">下架后锁定</el-checkbox>
                        <el-checkbox v-if="rows.periods_type!=2" v-model="entity.is_parcel_insurance">保价</el-checkbox>
                        <el-checkbox v-if="rows.periods_type!=2" v-model="entity.is_support_reduction">支持满减
                        </el-checkbox>
                        <el-checkbox v-if="rows.periods_type!=2" v-model="entity.is_support_coupon">支持优惠券</el-checkbox>
                        <el-button type="primary" style="margin-left: 34px;" size="mini" @click="limitBuy=!limitBuy">
                            {{limitBuy?'取消限购':'设置限购'}}
                        </el-button>
                    </div>
                    <div style="margin: 10px 33px;" v-if="limitBuy">
                        <div> 限购 每人<el-select v-model="entity.quota_rule.quota_type" size="mini" style="width: 90px;"
                                placeholder="请选择">
                                <el-option label="每次" value="1">
                                </el-option>
                                <el-option label="永久" value="0">
                                </el-option>
                            </el-select>
                            限购
                            <el-input v-model="entity.quota_rule.quota_number" size="mini"
                                style="width: 60px;margin-left: 5px;" placeholder="件数" />件
                        </div>
                         <div style="margin: 5px;">
                    特殊限购
                    <el-checkbox
                        @change="handleAddrChange"
                        v-model="entity.quota_rule.check_addr"
                        >地区
                    </el-checkbox>
                    <el-checkbox
                        @change="handleLevelChange"
                        v-model="entity.quota_rule.check_level"
                        >等级</el-checkbox
                    >
                    <el-checkbox
                        @change="handleTimeChange"
                        v-model="entity.quota_rule.check_time"
                        >注册时间</el-checkbox
                    >
                </div>
                        <div style="display: flex;">
                            <div v-if="entity.quota_rule.check_addr" style="width: 327px;">
                                <el-checkbox-group v-model="entity.quota_rule.addrs">
                                    <el-checkbox :label="v.id" v-for="(v,i) in cascaderOptions" :key="v.name">
                                        {{v.name}}
                                    </el-checkbox>
                                </el-checkbox-group>
                            </div>
                            <div style="margin: 0 10px;" v-if="entity.quota_rule.check_level">
                                等级
                                <el-input v-model="entity.quota_rule.min_level" size="mini"
                                    style="width: 80px;margin-left: 5px;" placeholder="最低等级" />-
                                <el-input v-model="entity.quota_rule.max_level" size="mini"
                                    style="width: 80px;margin-left: 5px;" placeholder="最高等级" />
                            </div>
                            <div v-if="entity.quota_rule.check_time">
                                注册时间<el-date-picker v-model="entity.quota_rule.register_time" size="mini"
                                    value-format="yyyy-mm-dd" style="margin-left: 5px;" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </div>
                        </div>
                    </div>
                    <!--     <el-button type="primary" v-if="showNext2Btn" style="margin-left: 34px;" size="mini"
                        @click="handleNext(3)">下一步
                    </el-button> -->
                    <div class="box_flex">
                        <el-form-item label="上架时间" prop="onsale_time">
                            <el-date-picker v-model="entity.onsale_time" @change="changeTime"
                                value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="开售时间" prop="sell_time">
                            <el-date-picker v-model="entity.sell_time" value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetime" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                    </div>
                    <div class="box_flex">
                        <el-form-item label="下架时间" prop="sold_out_time">
                            <el-date-picker v-model="entity.sold_out_time" value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetime" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="发货时间" prop="predict_shipment_time">
                            <el-date-picker v-model="entity.predict_shipment_time" value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetime" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                    </div>

                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="handleNext(3)">保存</el-button>
                </span>
            </el-dialog>
        </el-form>
</template>

<script>
    import SingleImage from "@/components/UploadImage/SingleImage";
    import UploadImage from "@/components/UploadImage/UploadImage";
    import Tinymce from "@/components/Tinymce";
    import vosOss from "vos-oss"
    import vosVod from "vos-vod"
    export default {
        props: {
            parentObj: Object,
        },
        components: {
            SingleImage,
            Tinymce,
            UploadImage,
            vosOss,
            vosVod
        },
        data() {
            const validataprice = (rule, value, callback) => {
                console.log(rule, value, 4444)
                let reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/
                if (value) {
                    if (!reg.test(value)) {
                        callback(new Error('请输入正确的金额，可保留两位小数'))
                    } else if (value) {
                        let index = rule.field.split('.')[1];
                        if (this.entity.salesList[index].price * 0.5 < Number(value)) {
                            callback(new Error('输入金额不能大于售价50%'))
                        } else {
                            callback()
                        }
                    } else {
                        callback()
                    }
                } else {
                    callback()
                }

            }
            return {
                numberRules: [
                    {
                        required: true,
                        message: "请输入",
                        trigger: "blur",
                    },
                    {
                        validator(rule, value, callback) {
                            if (
                                Number.isInteger(Number(value)) &&
                                Number(value) > 0
                            ) {
                                callback();
                            } else {
                                callback(new Error("请输入正整数"));
                            }
                        },
                        trigger: "blur",
                    },
                ],
                dialogVisible: false,
                dialogVisible1: false,
                dialogVisible2: false,
                GoodsDetailVisible: false,
                imgVisible: false,
                purchaseDialog: false,
                packDialog: false,
                goodsDialog: false,
                title: "闪购介绍",
                coverList:[],
                type: 0,
                options: [{
                        value: "选项1",
                        label: "黄金糕"
                    },

                ],
                sendWarehouseList: [],
                cascaderOptions: [], //地区
                buyerList: [],
                entity: {
                    title: "",
                    brief: "",
                    banner_img: "",
                    product_img: "",
                    detail: '',
                    filelist1: [],
                    filelist2: [],
                    filelist3: [],
                    product_id: [],
                    purchaseList: [{
                        short_code: '1105000000003',
                        purchase: [],
                    }],
                    creator_name: 'test',
                    salesList: [{
                        package_name: ''
                    }],
                    is_channel: 0,
                    is_hidden_price: false,
                    sellout_sold_out: 1,
                    video: '',
                    quota_rule: {

                    },
                },
                purchaseOptions: [],
                limitBuy: false, //限购
                entity1: {
                    reason: ""
                },
                dir: "vinehoo/goods-images/",
                parckageOptions: [{
                        value: "单支",
                        label: "单支"
                    },
                    {
                        value: "双支套装",
                        label: "双支套装"
                    }, {
                        value: "三支套装",
                        label: "三支套装"
                    }, {
                        value: "四支套装",
                        label: "四支套装"
                    }, {
                        value: "五支套装",
                        label: "五支套装"
                    }, {
                        value: "六支套装",
                        label: "六支套装"
                    }, {
                        value: "单支礼盒装",
                        label: "单支礼盒装"
                    }, {
                        value: "六支原箱",
                        label: "六支原箱"
                    }, 
                    {
                        value: "十二支装",
                        label: "十二支装",
                    },
                    {
                        value: "十二支原箱",
                        label: "十二支原箱"
                    },
                    {
                        value: "十二支原箱（无冰袋）",
                        label: "十二支原箱（无冰袋）"
                    }, 
                    {
                        value: "二十四支原箱",
                        label: "二十四支原箱"
                    }, 
                    {
                        value: "二十四支原箱（无冰袋）",
                        label: "二十四支原箱（无冰袋）"
                    }, {
                        value: "盲盒",
                        label: "盲盒"
                    }, {
                        value: "其他",
                        label: "其他"
                    },
                ],
                loading: false,
                ids: [{
                    name: "酒庄id",
                    mode: "chateau_id"
                }],
                operations: [{
                    value: "是",
                    label: 1
                }, {
                    value: "否",
                    label: 0
                }],
                rules: {
                    incremental: [{
                        required: true,
                        message: "请输入自增量",
                        trigger: "blur"
                    }],
                    limit_number: [{
                        required: true,
                        message: "请输入初始值",
                        trigger: "blur"
                    }],
                    critical_value: [{
                        required: true,
                        message: "请输入临界值",
                        trigger: "blur"
                    }],
                    title: [{
                        required: true,
                        message: "请输入商品标题",
                        trigger: "blur"
                    }],
                    brief: [{
                        required: true,
                        message: "请输入商品副标题",
                        trigger: "blur"
                    }],
                    detail: [{
                        required: true,
                        message: "请输入商品详情",
                        trigger: "blur"
                    }],
                    banner_img: [{
                        required: true,
                        message: "请先上传图片",
                        trigger: "change"
                    }],
                    product_img1: [{
                        required: true,
                        // validator: validateimg,
                        message: "请先上传图片",
                        trigger: "change"
                    }],
                    short_code: [{
                        required: true,
                        message: "请输入简码",
                        trigger: "blur"
                    }],
                    package_name: [{
                        required: true,
                        message: "请选择套餐名称",
                        trigger: "change"
                    }],
                    package_name1: [{
                        required: true,
                        message: "请输入套餐名称",
                        trigger: "blur"
                    }],
                    price: [{
                        required: true,
                        message: "请输入售价",
                        trigger: "blur"
                    }, {
                        pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
                        message: "请输入正确售价，可保留两位小数",
                    }],
                    market_price: [{
                        required: true,
                        message: "请输入市场价",
                        trigger: "blur"
                    }, {
                        pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
                        message: "请输入正确市场价，可保留两位小数",
                    }],
                    costprice: [{
                        required: true,
                        message: "请输入本期成本价",
                        trigger: "blur"
                    }, {
                        pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
                        message: "请输入正确本期成本价，可保留两位小数",
                    }],
                    preferential_reduction: [{
                        required: true,
                        validator: validataprice,
                        trigger: "blur"
                    }],
                    reason: [{
                        required: true,
                        message: "请选择驳回理由",
                        trigger: "blur"
                    }],
                    onsale_time: [{
                        required: true,
                        message: "请选择上架时间",
                        trigger: "change"
                    }],
                    sold_out_time: [{
                        required: true,
                        message: "请选择下架时间",
                        trigger: "change"
                    }],
                    sell_time: [{
                        required: true,
                        message: "请选择开售时间",
                        trigger: "change"
                    }],
                    predict_shipment_time: [{
                        required: true,
                        message: "请选择发货时间",
                        trigger: "change"
                    }],
                    tasting_notes: [{
                        required: true,
                        message: "请输入Tasting Notes",
                        trigger: "blur"
                    }],
                    warehouse: [{
                        required: true,
                        message: "请选择发货仓库",
                        trigger: "change"
                    }],
                    inventory: [{
                        required: true,
                        message: "请输入售卖库存",
                        trigger: "blur"
                    }],
                },
                loading: false,
                disab: false,
                salesVisible: false,
                purcharseVisible: true,
                nextBtn: true,
                purList: [],
                numList: [],
                showNext: false,
                showNext1: false,
                showNext2: false,
                showNext2Btn: false,
                rows: {},
                sendWarehouseList: [],
                nextBtn1: false,
                purchases: {
                    period: "",
                    periods_type: '',
                    import_type: '',
                    supplier: '',
                    buyer_id: '',
                    buyer_name: '',
                    is_supplier_delivery: '',
                    is_presell: '',

                },
                product_disab: true,
            };
        },
        created() {},
        mounted() {
            console.log(this.entity, 2434)
        },
        methods: {
            changeUnlimit(checked) {
                this.entity.salesList.map(item => {
                    item.inventory = '';
                    item.unlimited = checked;
                })
            },
            handleCodeVisible(salesList, child) {
                console.log(child, 22222222)
                // 是否显示选择套餐下拉
                if (!salesList.is_onsale) {
                    child.codeVisible = !child.codeVisible
                }
            },
            goodsEdit() {
                if (this.entity.salesList && this.entity.salesList[0].package_name == '') {
                    this.$message.error("请先编辑套餐信息！")
                    return
                }
                this.goodsDialog = !this.goodsDialog
            },

            packEdit() {
                if (this.purList.length == 0) {
                    this.$message.error("请先编辑采购信息！")
                    return
                }
                this.getDetails(this.rows);
                console.log(1111112)
                this.packDialog = !this.packDialog

            },
            uploadSuccess(data) {
                this.entity.video = data.url
            },
            //验证套餐名称不能和下拉列表名称一样
            changePack(v) {
                let index1 = this.parckageOptions.findIndex(f => f.value == v.package_name1);
                if (index1 > -1) {
                    v.package_name1 = '';
                    this.$message.error("请选择对应套餐！");

                }

            },
            updatePurchase() {
                let parms = {
                    period: this.rows.id,
                    periods_type: this.rows.periods_type,
                    import_type: this.entity.import_type,
                    buyer_id: this.entity.buyers1.buyer_id,
                    buyer_name: this.entity.buyers1.buyer_name,
                    is_supplier_delivery: this.entity.is_supplier_delivery,
                    is_presell: this.entity.is_presell,
                    supplier: this.entity.supplier,
                }
                this.$request.article.updatePurchaseInfo(parms).then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功")
                    }
                });
            },
            //下架时间自动填充
            changeTime() {
                console.log(222)
                if (!this.entity.onsale_time) {
                    this.entity.sold_out_time = '';
                    return
                }
                let day = this.rows.periods_type == 0 ? 7 : 14;
                if (this.rows.periods_type == 0 || this.rows.periods_type == 2) {
                    this.entity.sold_out_time = this.addDate(this.entity.onsale_time, day)
                }
                this.entity.sell_time = this.addDate(this.entity.onsale_time, 0)
                console.log(this.entity, 333)
            },
            //添加产品
            addProduct(rows, isGift) {
                let data = {
                    nums: 1,
                    visible: false,
                    codeVisible: false,
                    disabled: false,
                    isCheck: false,
                    isDel: true,
                    productList: [],
                    isGift: isGift ? true : false,
                };
                rows.numList.push(data)
            },
               handleTimeChange() {
            if (!this.entity.quota_rule.check_time) {
                this.entity.quota_rule.register_time = "";
            }
        },
        //特殊限购
        handleLevelChange() {
            if (!this.entity.quota_rule.check_level) {
                this.entity.quota_rule.max_level = "";
                this.entity.quota_rule.min_level = "";
                this.entity.quota_rule.rank= ""
            }
        },
        handleAddrChange() {
            if (this.entity.quota_rule.check_addr) {
                // this.cascaderOptions.map(item => {
                //     this.entity.quota_rule.addrs.push(item.id)
                // })
            } else {
                this.entity.quota_rule.district = ""
                this.entity.quota_rule.addrs = [];
            }
        },
            //删除产品
            delProduct(rows, i) {
                rows.numList.splice(i, 1)
            },
            HandleSave() {
                if (!this.validateForm()) {
                    return;
                }
                if (new Date(this.entity.sell_time).getTime() < new Date(this.entity.onsale_time).getTime()) {
                    this.$message.error("开售时间必须大于上架时间！")
                    return
                }
                if (new Date(this.entity.sold_out_time).getTime() <= new Date(this.entity.sell_time).getTime()) {
                    this.$message.error("下架时间必须大于开售时间！")
                    return
                }

                let params = {
                    period: this.entity.id,
                    periods_type: this.rows.periods_type,
                    is_channel: this.entity.is_channel ? 1 : 0,
                    is_hidden_price: this.entity.is_hidden_price ? 1 : 0,
                    sellout_sold_out: this.entity.sellout_sold_out ? 1 : 0,
                    onsale_time: this.entity.onsale_time,
                    sold_out_time: this.entity.sold_out_time,
                    sell_time: this.entity.sell_time,
                    predict_shipment_time: this.entity.predict_shipment_time,
                }
                if (this.entity.onsale_review_status < 1) {
                    params.onsale_review_status = 1;
                }
                this.$request.article.goodsUpdate(params).then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功")
                        this.dialogVisible = false;
                    }
                });

            },
            handleNext(step, isSave) {
                if (step == 1) {
                    if (!this.validateForm()) {
                        return;
                    }
                    let list1 = [];
                    let list2 = [];

                    let paramsList = []
                    let product_category = [];
                    let keywords=[];
                    let country = []
                    let capacity = [];
                    this.purList = [];
                    let productcategoryname = []
                    let short_code = []
                    this.entity.product_id = [];
                    //组装库存数据
                    this.entity.purchaseList.map(item => {
                        list1.push(item.short_code)
                        item.purchase.map(child => {
                             child.product_keywords_id.map(keyword=>{
                                keywords.push(keyword.name)
                            
                            })
                            this.entity.product_id.push(child.id)
                            this.purList.push(child)
                            product_category.push(child.product_type_name)
                            country.push(child.country_name)
                            capacity.push(child.capacity)
                            productcategoryname.push(child.productcategoryname)
                            short_code.push(child.short_code)
                            paramsList.push({
                                period: this.entity.id,
                                periods_type: this.rows.periods_type,
                                product_id: child.id,
                                erp_id: child.warehouse.erp_id,
                                short_code: child.short_code,
                                inventory: child.inventory,
                                capacity: child.capacity,
                                warehouse: child.warehouse.fictitious_name,
                                warehouse_id: child.warehouse.fictitious_id,
                                product_name: child.cn_product_name,
                                costprice: child.costprice,
                                en_product_name:child.en_product_name,
                                bar_code:child.bar_code,
                            })
                        })
                    })
                    if (paramsList.length == 0) {
                        this.$message.error("请先搜索简码！")
                        return
                    }
                    this.entity.product_list.map(item => {
                        list2.push(item.short_code)
                    })
                    let flag = list1.length == list2.length && list1.every(a => list2.some(b => a == b)) && list2.every(
                        a1 => list1.some(b1 => a1 == b1));
                    let flag1 = false;
                    if (!flag) {
                        // this.$message.error("绑定的条码和文案绑定的有差异！")
                        this.$confirm('绑定的简码和文案绑定的有差异，确认继续吗?', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(() => {
                            let params = {
                                list: paramsList,
                                periods_product: {
                                    product_category: product_category.join(","),
                                    country: country.join(","),
                                    capacity: capacity.join(","),
                                    keywords:keywords.join(','),
                                    productcategoryname:productcategoryname.join(','),
                                    short_code:short_code.join(',')
                                }
                            }
                            this.$request.article.createStock(params).then(res => {
                                if (res.data.error_code == 0) {
                                    // this.purcharseVisible = false;
                                    // this.salesVisible = true;
                                    this.purchaseDialog = false;
                                    if (!isSave) {
                                        //显示套餐信息
                                        this.packDialog = true;
                                    }
                                    if (!this.entity.salesList[0].package_name) {
                                        this.entity.salesList[0].numList = [{
                                            product_id: 0,
                                            nums: 1,
                                            visible: false,
                                            codeVisible: false,
                                            unlimited: true,
                                            disabled: true,
                                            isCheck: false,
                                            productList: [],
                                        }]
                                    }
                                }
                            });
                        }).catch(() => {});
                        return;
                    }

                    let params = {
                        list: paramsList,
                        periods_product: {
                            product_category: product_category.join(","),
                            country: country.join(","),
                            capacity: capacity.join(","),
                            productcategoryname:productcategoryname.join(','),
                            keywords:keywords.join(','),
                            short_code:short_code.join(',')
                        }
                    }
                    this.$request.article.createStock(params).then(res => {
                        if (res.data.error_code == 0) {
                            this.purchaseDialog = false;
                            if (!isSave) {
                                //显示套餐信息
                                this.packDialog = true;
                            }
                            // this.purcharseVisible = false;
                            // this.salesVisible = true;
                            if (!this.entity.salesList[0].package_name) {
                                this.entity.salesList[0].numList = [{
                                    product_id: 0,
                                    nums: 1,
                                    visible: false,
                                    codeVisible: false,

                                    disabled: true,
                                    isCheck: false,
                                    productList: [],
                                }]
                            }

                        }
                    });

                } else if (step == 2) {
                    // 添加套餐
                    if (!this.validateForm()) {
                        return;
                    }
                    let list = [];
                    let isPackageName = false;
                    let isCheckProduct = false;
                    let salesList = JSON.parse(JSON.stringify(this.entity.salesList))
                    // let isInventory1 = 1
                    // let isInventory2 = 1
                    salesList.map(item => {
                        // if (item.inventory) {
                            // isInventory1 = 2
                        // } else {
                            // isInventory2 = 2
                        // }
                        item.periods_type = this.rows.periods_type;
                        item.is_mystery_box = item.package_name == '盲盒' ? 1 : 0
                        item.package_name = item.package_name == '其他' || item.package_name == '盲盒' ? item
                            .package_name1 : item.package_name
                        list = [];
                        item.numList.map(child => {
                            let product_id = [];
                            if (item.is_mystery_box == 1) {
                                child.productList.map(v => {
                                    product_id.push(v.id)
                                })
                            }
                            list.push({
                                product_id: item.is_mystery_box == 1 ? product_id : child
                                    .productList[0].id,
                                nums: child.nums,
                                isGift: child.isGift ? 1 : 0
                            });
                            if (!child.isCheck) {
                                isCheckProduct = true;
                            }
                        })
                        item.associated_products = JSON.stringify(list)
                    })
                    // if (isInventory1 == 2 && isInventory2 == 2 && !salesList[0].unlimited) {
                    //     this.$message.error('套餐限量设置不一致');
                    //     return
                    // }
                    if (isPackageName) {
                        this.$message.error("请选择套餐名称")
                        return;
                    }
                    if (isCheckProduct) {
                        this.$message.error("请选择产品")
                        return;
                    }
                    this.entity.periods_type = this.rows.periods_type;
                    this.entity.period = this.entity.id;
                    // this.entity.quota_rule = {
                    //     check_addr: false,
                    //     check_level: false,
                    //     check_time: false,
                    //     register_time: '',
                    //     min_level: '',
                    //     max_level: '',
                    //     addrs: [],
                    // }
                    // let type = this.rows.onsale_status == 3 ? 2 : 1;
                    salesList.map(item => {
                        item.operator_id = '1';
                    })
                    this.$request.article.packageAdd(salesList).then(res => {
                        if (res.data.error_code == 0) {
                            this.getDetails(this.rows)
                            this.packDialog = false;
                            if (!isSave) {
                                this.goodsDialog = true;
                            }
                            // this.showNext = false;
                            // this.showNext2 = true;
                            // this.showNext2Btn = true;
                        }
                    });
                } else {
                    if (!this.validateForm()) {
                        return;
                    }
                    let type = 2
                     if (this.limitBuy ) {
                        if(this.entity.quota_rule.check_level){
                            //等级限购
                            if (this.entity.quota_rule.min_level < 0 || this.entity.quota_rule.min_level > 20 || this.entity
                            .quota_rule.min_level == '') {
                            this.$message.error("请输入大于0少于20的最低等级")
                            return;
                            }
                            if (this.entity.quota_rule.max_level > 20 || this.entity.quota_rule.max_level == '') {
                                this.$message.error("请输入少于或等于20的最高等级")
                                return;
                            }
                        }
                        if(this.entity.quota_rule.check_addr){
                            //地址限购
                            if (this.entity.quota_rule.addrs.length == 0) {
                                this.$message.error("请选择地区")
                                return;
                            }
                        }
                        if(this.entity.quota_rule.check_time){
                            //时间限购
                            if (this.entity.quota_rule.register_time == '') {
                                this.$message.error("请选择注册时间")
                                return;
                            }
                        }
                    }
                    if (new Date(this.entity.sell_time).getTime() < new Date(this.entity.onsale_time).getTime()) {
                        this.$message.error("开售时间必须大于上架时间！")
                        return
                    }
                    if (new Date(this.entity.sold_out_time).getTime() <= new Date(this.entity.sell_time).getTime()) {
                        this.$message.error("下架时间必须大于开售时间！")
                        return
                    }
                    //修改商品信息
                    if (this.entity.quota_rule.check_addr) {
                        this.entity.quota_rule.district = this.entity.quota_rule.addrs.join(',')
                    }
                    if (this.entity.quota_rule.check_level) {
                        this.entity.quota_rule.rank = this.entity.quota_rule.min_level + ',' + this.entity.quota_rule
                            .max_level
                    }

                    // this.entity.is_channel=this.entity.is_channel?1:0;
                    // this.entity.is_hidden_price=this.entity.is_hidden_price?1:0;
                    // this.entity.is_support_ts=this.entity.is_support_ts?1:0;
                    // this.entity.sellout_sold_out=this.entity.sellout_sold_out?1:0;
                    // this.entity.is_sold_out_lock=this.entity.is_sold_out_lock?1:0;
                    // this.entity.is_support_reduction=this.entity.is_support_reduction?1:0;
                    // this.entity.is_support_coupon=this.entity.is_support_coupon?1:0;
                    // this.entity.is_parcel_insurance=this.entity.is_parcel_insurance?1:0;
                    let params = {
                        quota_rule: JSON.stringify(this.entity.quota_rule),
                        limit_number: this.entity.limit_number,
                        critical_value: this.entity.critical_value,
                        incremental: this.entity.incremental,
                        is_channel: this.entity.is_channel,
                        is_support_ts: this.entity.is_support_ts,
                        is_hidden_price: this.entity.is_hidden_price,
                        sellout_sold_out: this.entity.sellout_sold_out,
                        is_sold_out_lock: this.entity.is_sold_out_lock,
                        is_support_reduction: this.entity.is_support_reduction,
                        is_support_coupon: this.entity.is_support_coupon,
                        is_parcel_insurance: this.entity.is_parcel_insurance,
                        periods_type: this.rows.periods_type,
                        period: this.entity.id,
                        is_channel: this.entity.is_channel ? 1 : 0,
                        is_hidden_price: this.entity.is_hidden_price ? 1 : 0,
                        sellout_sold_out: this.entity.sellout_sold_out ? 1 : 0,
                        onsale_time: this.entity.onsale_time,
                        sold_out_time: this.entity.sold_out_time,
                        sell_time: this.entity.sell_time,
                        predict_shipment_time: this.entity.predict_shipment_time,
                        is_cold_chain:this.entity.is_cold_chain
                    }
                    if (this.entity.onsale_review_status == 0 || this.entity.onsale_review_status == 4) {
                        params.onsale_review_status = 1;
                    }
                    this.$request.article.goodsUpdate(params, type).then(res => {
                        if (res.data.error_code == 0) {
                            this.goodsDialog = false;
                            this.dialogVisible = false;
                            this.parentObj.getData();
                            this.$message.success("操作成功！")
                            // this.showNext2Btn = false;
                            // this.showNext1 = true;
                        }
                    });
                }
            },
            onSuccess(url, type) {
                if (type == 1) {
                    this.entity.filelist1.push(url.file);
                } else if (type == 2) {
                    this.entity.filelist2.push(url.file);
                } else if (type == 3) {
                    this.entity.filelist3.push(url.file);
                }
            },
            onRemove(event, type) {
                if (type == 1) {
                    this.entity.filelist1 = event.list
                } else if (type == 2) {
                    this.entity.filelist2 = event.list
                } else if (type == 3) {
                    this.entity.filelist3 = event.list
                }
            },
            onError(a, b) {
                console.warn(a, b);
                this.$message.error("上传失败，请重新上传");
            },
            submits() {
                if (this.validateForm()) {
                    if (this.entity.purchaseList.length == 0) {
                        this.$Message.error("请先搜索简码!");
                        return;
                    }
                    // this.entity.purchaseList.map(item => {
                    //     if (item.purchase.length > 1) {
                    //         item.purchase.map(child => {
                    //             if (child.checked) {
                    //                 this.entity.product_id.push(child.id)
                    //             }
                    //         })
                    //     } else {
                    //         this.entity.product_id.push(item.purchase[0].id)
                    //     }
                    // })
                    if (!this.coverList.length !==  !this.entity.video) {
                    this.$Message.error("请完善视频信息");
                    return;
                }
                  
                //   let product_info_list = []
                //  this.entity.purchaseList.map(pitem => {
                //      console.log(pitem)
                //      if(pitem.purchase){
                //         pitem.purchase.map(item=>{
                //             let obj = {
                //                 tasting_notes: item.tasting_notes,
                //                 score: item.score,
                //                 prize: item.prize,
                //                 drinking_suggestion: item.drinking_suggestion,
                //                 short_code: item.short_code,
                //                 product_id: item.id,
                //             };
                //             product_info_list.push(obj);
                //         })
                //      }
                //  })
                    
                 const data = {
                    product_info:product_info_list,
                    id: this.entity.id,
                    title:this.entity.title,
                    brief:this.entity.brief,
                    banner_img: this.entity.filelist1.join(","),
                    product_img:this.entity.filelist3.join(","),
                    detail:this.entity.detail,
                    product_id:this.entity.product_id.join(","),
                    video_cover:this.coverList.join(','),
                    video:this.entity.video,
                }
                    if (this.entity.id > 0) {
                        let type = 2;
                        if (this.rows.periods_type == 0) {
                            this.$request.article.flashAdd(data, type).then(res => {
                                this.articalCallback(res)
                            });
                        } else if (this.rows.periods_type == 1) {
                            this.$request.article.secondAdd(data, type).then(res => {
                                this.articalCallback(res)
                            });
                        } else if (this.rows.periods_type == 2) {
                            this.$request.article.crossAdd(data, type).then(res => {
                                this.articalCallback(res)
                            });
                        } else if (this.rows.periods_type == 3) {
                            this.$request.article.leftoverAdd(data, type).then(res => {
                                this.articalCallback(res)
                            });
                        }else if (this.rows.periods_type == 4) {
                            this.$request.article.rabbitAdd(data, type).then(res => {
                                this.articalCallback(res)
                            });
                        }
                    } else {
                        if (this.rows.periods_type == 0) {
                            this.$request.article.flashAdd(data).then(res => {
                                this.articalCallback(res)
                            });
                        } else if (this.rows.periods_type == 1) {
                            this.$request.article.secondAdd(data).then(res => {
                                this.articalCallback(res)
                            });
                        } else if (this.rows.periods_type == 2) {
                            this.$request.article.crossAdd(data).then(res => {
                                this.articalCallback(res)
                            });
                        } else if (this.rows.periods_type == 3) {
                            this.$request.article.leftoverAdd(data).then(res => {
                                this.articalCallback(res)
                            });
                        }else if (this.rows.periods_type == 4) {
                            this.$request.article.rabbitAdd(data).then(res => {
                                this.articalCallback(res)
                            });
                        }
                    }
                }
            },
            articalCallback(res) {
                if (res.data.error_code == 0) {
                    this.$emit('closeGoodsOpDialog')
                    this.$message.success("操作成功")
                } 
            },
            //简码选择
            checkedStore(row, purchase) {
                purchase.map(item => {
                    if (row.fictitious_name == item.fictitious_name) {
                        this.$set(item, 'checked', row.checked)
                    } else {
                        item.checked = false;
                    }
                })
                this.nextBtn = false;
            },
            getWarehouseList() {
                //获取发货仓库信息
                console.log(11221)
                this.$request.article.warehouseList({
                    channel_types: this.rows.periods_type
                }).then(res1 => {
                    if (res1.error_code == 0) {
                        this.sendWarehouseList = res1.data
                        console.log(this.sendWarehouseList, 444)
                    }
                });
                //获取仓库信息
                this.entity.purchaseList.map(item => {
                    item.purchase.map(v => {

                        this.$request.article.getWarehouse({
                            bra_code: v.short_code,
                        }).then(res2 => {
                            this.$set(v, 'warehouseList', res2.data)
                        });
                    })
                })
            },
            //详情数据
            openForm(row) {
                this.rows = row;
                this.dialogVisible = true;
                this.dialogVisible2 = true;
                // this.type = type;
                this.disab = false;
                this.nextBtn1 = false;
                this.purList = [];
                this.getAddress();
                this.getPurchase();
                console.log(row, 22)
                this.getDetails(row);

            },
            getDetails(row) {
                if (row) {
                    this.remoteMethod(row.supplier)
                    if (this.rows.periods_type == 4) {
                        this.$request.article.rabbitDetail({
                            id: row.id
                        }).then(res => {
                            if (res.data.error_code == 0) {
                                this.disab = res.data.data.buyer_review_status == 2 ? true : false;
                                res.data.data.is_cold_chain = res.data.data.is_cold_chain == 1 ? true : false;
                                // 供应商回显
                                res.data.data.suppliers = {
                                    supplier_name: res.data.data.supplier,
                                    id: res.data.data.supplier_id
                                }
                                // res.data.data.is_supplier_delivery = res.data.data.is_supplier_delivery == 1 ? true :
                                //     false;
                                // res.data.data.is_presell = res.data.data.is_presell == 1 ? true : false;
                                //回显采购信息
                                res.data.data.purchaseList = [];
                                if (res.data.data.product_list.length == 0 || !res.data.data.product_list[0].warehouse) {
                                    res.data.data.purchaseList.push({
                                        short_code: "",
                                        purchase: []
                                    })
                                } else {
                                    res.data.data.product_list.map(item => {
                                        res.data.data.purchaseList.push({
                                            purchase: [item],
                                            short_code: item.short_code
                                        });
                                    })

                                }

                                if (res.data.data.quota_rule) {
                                    res.data.data.quota_rule = JSON.parse(res.data.data.quota_rule)
                                    if (res.data.data.quota_rule) {
                                        if (res.data.data.quota_rule.check_addr || res.data.data.quota_rule.check_level ||
                                            res.data.data.quota_rule.check_time) {
                                            this.limitBuy = true;
                                        }
                                    }
                                } else {
                                    res.data.data.quota_rule = {
                                        check_addr: false,
                                        check_level: false,
                                        check_time: false,
                                        quota_type: "1",
                                        quota_number: this.rows.periods_type == 2 ? 1 : 9999,
                                        register_time: '',
                                        min_level: '',
                                        max_level: '',
                                        addrs: [],
                                        district: '',
                                        rank: '',
                                    }

                                }
                                res.data.data.product_id = [res.data.data.product_id];

                                //套餐保存后回显
                                if (row.onsale_status == 3 || res.data.data.package.length > 0) {
                                    this.purList = res.data.data.product_list;
                                    this.purcharseVisible = false;
                                    this.salesVisible = true;
                                    let datas = {};
                                    res.data.data.salesList = [];
                                    //套餐模拟数据
                                    res.data.data.package.map((item, index) => {
                                        item.is_onsale = item.is_onsale == 1 ? true : false;
                                        item.unlimited = item.unlimited == 1 ? true : false;
                                        item.is_original_package = item.is_original_package == 1 ?
                                            true : false;
                                        
                                        item.is_hidden = item.is_hidden == 1 ? true : false;

                                        //验证套餐是否是其他或者盲盒
                                        let index1 = this.parckageOptions.findIndex(f => f.value == item
                                            .package_name);
                                        if (item.package_name != '其他' && index1 < 0 && item
                                            .is_mystery_box == 0) {
                                            item.package_name1 = item.package_name;
                                            item.package_name = '其他';
                                        }
                                        if (item.is_mystery_box == 1) {
                                            item.package_name1 = item.package_name;
                                            item.package_name = '盲盒';
                                        }
                                        //采购信息和数量
                                        let lists = JSON.parse(item.associated_products)
                                        let isSame = [];
                                        lists.map((child, i) => {
                                            if (child.isGift == 0) {
                                                isSame.push(child)
                                            }
                                            //验证 产品 是否石盲盒
                                            console.log(typeof child.product_id, 333333)
                                            if (typeof child.product_id == "object") {
                                                let productList = []
                                                child.product_id.map(item => {
                                                    let products = res.data.data.product_list
                                                        .find(f =>
                                                            f.id == item);
                                                    productList.push(products);
                                                })
                                                child.productList = productList
                                            } else {
                                                let productList = []
                                                let products = res.data.data.product_list.find(f =>
                                                    f.id == child.product_id);
                                                productList.push(products)
                                                // child.productList = [res.data.data.product_list[i]];
                                                child.productList = productList
                                            }
                                            child.visible = true;
                                            child.isCheck = true;
                                            child.disabled = true;
                                            child.codeVisible = false;
                                        });
                                        datas = {
                                            same: (item.package_name != '其他' && item.package_name !=
                                                '盲盒' && isSame.length > 1) ? true : false, //套餐内不相同
                                            other: false,
                                            numList: lists,
                                            visible: true,
                                            unlimited: true,
                                        }
                                        res.data.data.salesList.push(Object.assign(datas, item));
                                    })
                                    this.showNext = false;
                                    this.showNext1 = false;
                                    this.showNext2 = true;
                                    this.showNext2Btn = true;
                                } else if (res.data.data.product_list[0].inventory) {
                                    // 库存保存后回显
                                    res.data.data.salesList = [{
                                        period_id: res.data.data.id,
                                        periods_type: "",
                                        package_name: '',
                                        package_name1: '',
                                        price: '',
                                        market_price: "",
                                        associated_products: "",
                                        limit_number: '',
                                        inventory: '',
                                        is_hidden: false,
                                        is_original_package: 0,
                                        coupons_id: '',
                                        is_cold_chain: 0,
                                        invoice_progress: 0,
                                        same: false,
                                        other: false,
                                        visible: false,
                                        unlimited: true,
                                        numList: [],
                                    }];
                                    this.purList = res.data.data.product_list;
                                    this.purcharseVisible = false;
                                    this.salesVisible = true;
                                    this.showNext = true;
                                    this.showNext1 = false;
                                    this.showNext2 = false;
                                } else {
                                    res.data.data.salesList = [{
                                        period_id: res.data.data.id,
                                        periods_type: "",
                                        package_name: '',
                                        package_name1: '',
                                        price: '',
                                        market_price: "",
                                        associated_products: "",
                                        limit_number: '',
                                        inventory: '',
                                        is_hidden: false,
                                        is_original_package: 0,
                                        coupons_id: '',
                                        is_cold_chain: 0,
                                        invoice_progress: 0,
                                        same: false,
                                        other: false,
                                        visible: false,
                                        unlimited: true,
                                        numList: [],
                                    }];
                                    this.purcharseVisible = true;
                                    this.salesVisible = false;
                                    this.showNext = true;
                                    this.showNext1 = false;
                                    this.showNext2 = false;
                                }

                                res.data.data.filelist1 = [];
                                res.data.data.filelist1 = res.data.data.banner_img;
                                res.data.data.filelist3 = res.data.data.product_img
                                res.data.data.video_cover ? this.coverList =  res.data.data.video_cover.split(',') : this.coverList = [];
                                res.data.data.buyers = {
                                    id: res.data.data.buyer_id,
                                    realname: res.data.data.buyer_name
                                }
                                res.data.data.buyers1 = {
                                    id: res.data.data.buyer_id,
                                    realname: res.data.data.buyer_name
                                }
                                this.entity = Object.assign({}, res.data.data);
                                this.clearDetial();
                                //获取仓库信息
                                this.getWarehouseList();

                                console.log(this.entity, 2333)
                                this.entity.is_channel = this.entity.is_channel == 1 ? true : false;
                                this.entity.is_hidden_price = this.entity.is_hidden_price == 1 ? true : false;
                                this.entity.sellout_sold_out = this.entity.sellout_sold_out == 1 ? true : false;
                                this.entity.is_support_ts = this.entity.is_support_ts ? true : false;
                                this.entity.is_sold_out_lock = this.entity.is_sold_out_lock ? true : false;
                                this.entity.is_support_reduction = this.entity.is_support_reduction ? true :
                                    false;
                                this.entity.is_support_coupon = this.entity.is_support_coupon ? true : false;
                                this.entity.is_parcel_insurance = this.entity.is_parcel_insurance ? true :
                                    false;

                            }
                        });
                    }

                }
            },
            clearDetial() {
                this.$refs.editor.setContent(this.entity.detail)
                setTimeout(() => {
                    this.singleValidate();
                }, 0)
            },
            //简码搜索
            async searchCode(code) {
                if (code == "") {
                    this.$message.error("请输入简码");
                    return
                }
                var i = 0;
                this.entity.purchaseList.map(item => {
                    if (item.short_code == code) {
                        i++;
                    }
                })
                if (i >= 2) {
                    this.$message.error("当前ERP编码已存在");
                    return;
                }
                var index = this.entity.purchaseList.findIndex(f => f.short_code == code);
                this.entity.purchaseList[index].purchase = [];
                this.$request.article.getProducts({
                    short_code: code,
                }).then(res => {
                    if (res.data.error_code == '0') {
                        if (res.data.data.length > 0) {
                            var isHas = false;
                            let isWarehouse = true;
                            this.nextBtn1 = true;
                            //获取发货仓库信息
                            this.$request.article.warehouseList({}).then(res1 => {
                                this.sendWarehouseList = res1.data || [];
                            });
                            //获取仓库信息
                            this.$request.article.getWarehouse({
                                bra_code: code,
                            }).then(res2 => {
                                res.data.data.map(v => {
                                    this.$set(v, 'warehouseList', res2.data)
                                })
                            });
                            res.data.data.map(item => {
                                item.costprice1 = item.costprice;
                            })
                            this.entity.purchaseList[index].purchase = res.data.data
                            console.log(this.entity.purchaseList, 2222)
                            // res.data.data.forEach((item) => {
                            //     isHas = false;
                            //     if (item.warehouse&&item.warehouse.length == 0) {
                            //         isWarehouse = false;
                            //     }
                            //     this.entity.purchaseList.forEach((child, i) => {
                            //         if (item.bar_code == child.bar_code) {
                            //             isHas = true;
                            //             index = i;
                            //         }
                            //     });
                            //     if (isHas) {
                            //         item.warehouse.map(child => {
                            //             let datas = {
                            //                 goods_count: child.goods_count,
                            //                 fake_count: child.fake_count,
                            //                 fictitious_name: child.fictitious_name,
                            //             };
                            //             this.entity.purchaseList[index].purchase.push(Object
                            //                 .assign(datas, item));
                            //         })
                            //     }
                            // })
                            // if (!isWarehouse) {
                            //     this.entity.purchaseList = [{
                            //         bar_code: '',
                            //         purchase: [],
                            //     }];
                            //     this.$message.error("无仓库信息,请先绑定仓库信息！");
                            // }
                        } else {
                            this.$message.error("暂无数据！");
                        }
                    }
                });
            },
            //添加套餐数量
            packageChange(rows, status) {
                let type = rows.package_name;
                console.log(rows, 344444444)
                this.$set(rows, 'is_mystery_box', 0)
                let len = 1;
                console.log(len, 2222)
                if (type == '单支') {
                    len = 1
                    this.updateSalesList(1, rows)
                } else if (type == '双支套装') {
                    len = 2
                    this.updateSalesList(2, rows)
                } else if (type == '三支套装') {
                    len = 3
                    this.updateSalesList(3, rows)
                } else if (type == '四支套装') {
                    len = 4
                    this.updateSalesList(4, rows)
                } else if (type == '五支套装') {
                    len = 5;
                    this.updateSalesList(5, rows)
                } else if (type == '六支套装') {
                    len = 6
                    this.updateSalesList(6, rows)
                } else if (type == '单支礼盒装') {
                    len = 1
                    this.updateSalesList(1, rows)
                } else if (type == '六支原箱') {
                    len = 6
                    this.updateSalesList(6, rows)
                } else if (type == "十二支装") {
                    len = 12;
                    this.updateSalesList(12, rows);
                } else if (type == '十二支原箱') {
                    len = 12
                    this.updateSalesList(12, rows)
                } else if (type == '十二支原箱（无冰袋）') {
                    len = 12
                    this.updateSalesList(12, rows)
                    rows.is_original_package = true
                } 
                else if (type == '二十四支原箱') {
                    len = 24
                    this.updateSalesList(24, rows)
                    rows.is_original_package = true
                } 
                else if (type == '二十四支原箱（无冰袋）') {
                    len = 24
                    this.updateSalesList(24, rows)
                    rows.is_original_package = true
                } else if (type == '其他' || type == '盲盒') {
                    rows.is_mystery_box = type == '盲盒' ? 1 : 0
                    rows.numList = [];
                    let data = {
                        nums: 0,
                        visible: false,
                        codeVisible: false,
                        disabled: false,
                        isCheck: false,
                        productList: [],
                    };
                    rows.numList.push(data)
                }
                if (status == 2) {
                    return len;
                }
            },
            updateSalesList(length, rows) {
                rows.numList = [];
                let data = {
                    nums: 1,
                    visible: false,
                    codeVisible: false,
                    disabled: true,
                    isCheck: false,
                    productList: [],
                };
                if (!rows.same) {
                    rows.numList.push(data);
                    rows.numList[0].nums = length;
                    return;
                }
                for (let i = 0; i < length; i++) {
                    let data1 = Object.assign({}, data)
                    rows.numList.push(data1)
                }

            },
            otherChange(rows) {
                this.show = true;
                this.$refs.labelAlignForm.clearValidate();
                if (rows.other) {
                    rows.numList = [{
                        nums: 1,
                        visible: false,
                        codeVisible: false,
                        disabled: false,
                        isCheck: false,
                        productList: [],
                    }]
                    rows.same = false;
                }
            },
            //套餐内不同
            sameChange(rows) {
                this.$refs.labelAlignForm.clearValidate();
                if (!rows.same) {
                    rows.numList = [{
                        nums: this.packageChange(rows, 2),
                        visible: false,
                        codeVisible: false,
                        disabled: true,
                        isCheck: false,
                        productList: [],
                    }]
                } else {
                    this.packageChange(rows);
                    rows.other = false;
                }
                this.show = true;
            },
            //采购信息确认
            confirmPur(data, rows, entity) {
                console.log(this.entity.salesList, 2323)
                if (entity.package_name == '盲盒') {
                    rows.productList = data;
                } else if (entity.package_name == '其他' || entity.same) {
                    rows.productList = [data];
                } else {
                    rows.productList = [data];
                }
                rows.codeVisible = !rows.codeVisible;
                rows.visible = true;
                entity.visible = true; //显示信息
                // let unlimited=this.entity.salesList[this.entity.salesList.length-2].unlimited?this.entity.salesList[this.entity.salesList.length-1].unlimited:true;
                // this.$set(entity,'unlimited',unlimited)
                let flag = true;
                this.entity.salesList.map(item => {
                    item.numList.map(child => {
                        if (!child.visible) {
                            flag = false;
                        }
                        child.isCheck = true;
                    })
                })
                if (flag) {
                    this.showNext = true;
                }
            },
            visibleChange(flag, rows, entity) {
                if (!flag) {
                    console.log(rows, entity, 222)
                    this.confirmPur(rows.productList, rows, entity);
                }
            },
            productChange(rows) {
                if (rows.productList && rows.productList.length == 0) {
                    rows.visible = false;
                }
            },
            //采购人
            getPurchase() {
                this.$request.article.purchaseList({
                    type: 2
                }).then(res => {
                    this.purchaseOptions = res.data.data.list
                });
            },
            //添加时间
            addDate(date, days) {
                if (days == undefined) {
                    days = 1;
                }
                var date = new Date(date);
                date.setDate(date.getDate() + days);
                var month = date.getMonth() + 1;
                var day = date.getDate();
                return date.getFullYear() + '-' + this.getFormatDate(month) + '-' + this.getFormatDate(day) + " " + this
                    .getFormatDate(date.getHours()) + ":" + this.getFormatDate(date.getMinutes()) + ":" + this
                    .getFormatDate(date.getSeconds());
            },
            // 日期月份/天的显示，如果是1位数，则在前面加上'0'
            getFormatDate(arg) {
                if (arg == undefined || arg == '') {
                    return "00";
                }
                var re = arg + '';
                if (re.length < 2) {
                    re = '0' + re;
                }
                return re;
            },
            //获取地区
            getAddress() {
                this.$request.article.getAddress().then(res => {
                    this.cascaderOptions = res.data.data.list
                });
            },

            copyName(val) {
                var input = document.createElement("input"); // 直接构建input
                input.value = val; // 设置内容
                document.body.appendChild(input); // 添加临时实例
                input.select(); // 选择实例内容
                document.execCommand("Copy"); // 执行复制
                document.body.removeChild(input); // 删除临时实例
                this.$message.success("复制成功！")
            },
            addSales() {
                const data = {
                    isDel: true,
                    period_id: this.entity.id,
                    periods_type: "",
                    package_name: '',
                    package_name1: '',
                    price: '',
                    market_price: "",
                    associated_products: "",
                    limit_number: '',
                    inventory: '',
                    is_hidden: '',
                    is_original_package: 0,
                    coupons_id: '',
                    is_cold_chain: 0,
                    invoice_progress: 0,
                    same: false,
                    other: false,
                    unlimited: this.entity.salesList[this.entity.salesList.length - 1].unlimited,
                    numList: [{
                        product_id: [],
                        nums: 1,
                        visible: false,
                        codeVisible: false,
                        disabled: false,
                        isCheck: false,
                        productList: [],
                    }],
                };
                this.entity.salesList.push(data);
                this.showNext = true;
                this.showNext1 = false;
                this.showNext2 = false;
            },
            delSales(index) {
                this.entity.salesList.splice(index, 1);
            },
            // 继续添加
            adds() {
                const datas = {
                    short_code: '',
                    purchase: [],
                };
                this.entity.purchaseList.push(datas);
                this.nums++;

            },
            // 删除
            del(index) {
                this.entity.purchaseList.splice(index, 1);
                this.nums--;
            },
            singleValidate() {
                let flag = null;
                this.$refs["labelAlignForm"].validateField("detail", valid => {
                    if (valid) {
                        flag = true;
                    } else {
                        flag = false;
                    }
                });
                return flag;
            },
            // 表单验证
            validateForm() {
                let flag = null;
                this.$refs["labelAlignForm"].validate(valid => {
                    if (valid) {
                        flag = true;
                    } else {
                        flag = false;
                    }
                });
                return flag;
            },
            colorChange(val, index, type) {}
        }
    };
</script>
<style>
    .purchase_list {
        display: flex;
    }

    .purchase_list>label {
        width: 60px;
        text-align: right;
        margin-right: 10px;
        font-weight: bold;
    }

    .purchase_list>span {
        width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
    }

    .purchase {
        border: 1px solid #CCCCCC;
        padding: 15px 0 0px;
        margin-bottom: 15px;
        display: flex;
    }

    .purchaseActive {
        border: 1px solid #00ffe2;
        padding: 15px 0 0px;
        margin-bottom: 15px;
        display: flex;
    }

    .pur_rwap {
        display: flex;
        /* margin-bottom: 20px; */
    }

    .pur_inline {
        display: flex;
        margin-right: 10px;
    }

    .pur_inline>label {
        line-height: 39px;
        margin-right: 10px;
    }

    .pur_inline>span {
        line-height: 39px;
        min-width: 80px;
    }

    .sale_addInfo {
        border: 1px solid #CCCCCC;
        width: 650px;
        min-height: 98px;
        cursor: pointer;
        text-align: center;
        position: relative;
        margin-bottom: 20px;
    }

    .sale_addInfo>i {
        font-size: 40px;
    }

    .sale_visi_wrap {
        width: 650px;
        top: 100px;
    }

    .sale_visi_wrap::before {
        left: 319px !important;
    }

    .sale_addInfo_p {
        padding-top: 27px;
    }

    .pur_list_rwap {
        display: flex;
    }

    .pur_list_rwap .pur_item {
        height: 28px;
        line-height: 28px;
    }

    .pur_list_rwap .pur_item>label {
        margin-right: 10px;
        text-align: right;
        display: inline-block;
        min-width: 45px;
    }

    .sale_list_wrap {
        border: 1px solid #CCCCCC;
        padding: 10px;
        margin-bottom: 10px;
    }
</style>
