<template>
    <el-dialog
        title="基本信息"
        :visible.sync="dialogVisible"
        width="500px"
        append-to-body
        :close-on-click-modal="false"
    >
        <el-form ref="form" :model="entity" :rules="rules" label-width="120px">
            <el-form-item label="马甲数量" prop="num">
                <el-col :span="18">
                    <el-input
                        v-model="entity.num"
                        class="w-large"
                        oninput="value=value.replace(/[^\d]/g,'')"
                        placeholder="请输入马甲数量"
                    />
                </el-col>
            </el-form-item>
           
            <el-form-item label="执行方式" prop="mode">
                <el-select
                        class="w-large m-r-10"
                        v-model="entity.mode"
                        filterable
                       
                        placeholder="请选择执行方式"
                        clearable
                    >
                        <el-option
                            v-for="item in channelOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
            </el-form-item>
           
            
            <el-form-item label class="clearfix">
                <div style="float: right; margin-top: 20px">
                    <el-button type="primary" @click="submits">提交</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>

<script>
export default {
    props: {
        parentObj: Object,
    },
    data() {
        return {
            dialogVisible: false,
            tableData: [],
            entity: {
                period: "",
                num: "",
                mode: "",
            },
            channelOptions: [
                {
                    label: "售卖开始时",
                    value: 1,
                },
                {
                    label: "立即执行",
                    value: 2,
                },
            ],
            rules: {
                num: [
                    {
                        required: true,
                        message: "请输入马甲数量",
                        trigger: "blur",
                    },
                ],
                mode: [
                    {
                        required: true,
                        message: "请选择执行方式",
                        trigger: "blur",
                    },
                ],
            },
            rows: {},
        };
    },
    mounted() {},
    methods: {
        submits() {
            if (!this.validateForm()) {
                return;
            }
            const data = {
                period: this.entity.period,
                mode: Number(this.entity.mode),
                num: Number(this.entity.num),
               
            };
            this.$request.article.addFlashVestOp(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.dialogVisible = false;
                    this.parentObj.getData();
                    this.$message.success("操作成功！");
                }
            });
        },
        openForm(rows) {
            this.entity = {
                period: rows.id,
                num: "",
                mode: "",
            };
            this.dialogVisible = true;
        },
        validateForm() {
            let flag = null;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped></style>
