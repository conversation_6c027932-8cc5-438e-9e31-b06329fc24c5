<template>
    <div>
        <!-- <div style="">你确定要上架吗?</div> -->
        <div v-if="rowData.gdwj_status && rowData.gdwj_status.length">
         <div>关单卫检状态 </div>
         <div> 
            <span v-for="(item, index) in rowData.gdwj_status" :key="index">
                {{item.short_code }}：{{toStatusText[item.status] }}
            </span>
         </div>
        </div>
        <div v-if="grossLow.length">
         <div>以下套餐毛利率低于10%</div>
         <div> 
            <span v-for="(item, index) in grossLow" :key="index">
                {{item.package_name }}<span v-if="index!=grossLow.length-1">、</span>
            </span>
         </div>
        </div>
        <el-card style="margin-top: 10px;" v-if="rowData.card.length || rowData.column.length">
            <el-row>
            <el-col :span="12">
                <div  v-if="rowData.card.length" > 
                    <div>卡片</div>
                <el-checkbox-group v-model="chooseCard" class="box_cloumn">
                            <el-checkbox
                                :label="v.id"
                                v-for="(v, i) in rowData.card"
                                :key="i"
                                @change="(checked) => handleCardChange(checked, v)"
                            >
                               <div>{{ v.name }}</div> 
                               <el-checkbox-group v-model="v.filter_id" :max="1" @change="(value) => handleCardFilterChange(value, v)">
                                <el-checkbox :label="item.id"
                                v-for="(item) in v.filter" 
                                 :key="item.id"
                                >{{ item.name }}</el-checkbox>
                               </el-checkbox-group>
                            </el-checkbox>
                </el-checkbox-group>
                </div>
                
            </el-col>
            <el-col :span="12">
                <div  v-if="rowData.column.length" >
                    <div>栏目</div>
                <el-checkbox-group v-model="chooseColumn" class="box_cloumn">
                            <el-checkbox
                                :label="v.id"
                                v-for="(v, i) in rowData.column"
                                :key="i"
                                @change="(checked) => handleColumnChange(checked, v)"
                            >
                              <div> {{ v.name }}</div> 
                              <el-checkbox-group v-model="v.filter_id" :max="1" @change="(value) => handleFilterChange(value, v)">
                                <el-checkbox :label="item.id"
                                v-for="(item) in v.filter" 
                                 :key="item.id"
                                >{{ item.name }}</el-checkbox>
                               </el-checkbox-group>
                            </el-checkbox>
                        </el-checkbox-group>
                </div>
                
            </el-col>
            </el-row>
            <div>
                <el-input
                        v-model="title"
                        autocomplete="off"
                        placeholder="请输入主标题"
                        maxlength="10"
                        show-word-limit
                        style="width:50%;margin-right:20px"
                    ></el-input>
            </div>
        </el-card>
       
    </div>

</template>

<script>
export default {
    props: ["row"],
    data() {
        return {
           rowData:{
            gdwj_status:[],
            gross_margin:[],
            card:[],
            column:[],
           },
           toStatusText : {
            0: "未审批",
            1: "审批中",
            2: "审批通过",
            3: "审批驳回",
           },
           grossLow:[],
           chooseCard:[],
           chooseColumn:[],
           title:""
        };
    },
    mounted() {
        this.relodData();
    },
    methods: {
        async relodData() {
          
         
            const { id, periods_type } = this.row;
            const res = await this.$request.article.getHealthInspectStatus({
                id,
                periods_type,
            });
            if (res.data.error_code == 0) {
                this.rowData = res.data.data;
                if (this.rowData.gross_margin && this.rowData.gross_margin.length) {
                    this.grossLow = this.rowData.gross_margin.filter(
                        (item) => item.gross_margin < 0.1
                    );
                   
                }
                this.chooseCard = this.rowData.card.map(item => {
                    return item.id
                });
                this.chooseColumn = this.rowData.column.map(item => {
                    return item.id
                });
                this.rowData.card =  this.rowData.card.map(item => ({ 
                    ...item, 
                    filter_id: item.filter && item.filter.length ? [item.filter[0].id] : [] 
                }));
                this.rowData.column =  this.rowData.column.map(item => ({ 
                    ...item, 
                    filter_id: item.filter && item.filter.length ? [item.filter[0].id] : [] 
                }));
            }
           
        },
       getData(){
        const chooseCard = this.chooseCard;
        const chooseColumn = this.chooseColumn;
        const title = this.title;
        const card_filter = this.rowData.card.flatMap(item => item.filter_id);
        const column_filter	 = this.rowData.column.flatMap(item => item.filter_id);
        return { chooseCard, chooseColumn, title,card_filter, column_filter };
       },
       handleColumnChange(checked, column) {
        if (checked) {
            if (column.filter && column.filter.length && !column.filter_id.length) {
                column.filter_id = [column.filter[0].id];
            }
        } else {
            column.filter_id = [];
        }
    },
    handleFilterChange(value, column) {
        if (value.length === 0) {
            const index = this.chooseColumn.indexOf(column.id);
            if (index > -1) {
                this.chooseColumn.splice(index, 1);
            }
        } else {
            if (!this.chooseColumn.includes(column.id)) {
                this.chooseColumn.push(column.id);
            }
        }
    },
    handleCardChange(checked, card) {
        if (checked) {
            if (card.filter && card.filter.length && !card.filter_id.length) {
                card.filter_id = [card.filter[0].id];
            }
        } else {
            card.filter_id = [];
        }
    },
    handleCardFilterChange(value, card) {
        if (value.length === 0) {
            const index = this.chooseCard.indexOf(card.id);
            if (index > -1) {
                this.chooseCard.splice(index, 1);
            }
        } else {
            if (!this.chooseCard.includes(card.id)) {
                this.chooseCard.push(card.id);
            }
        }
    }
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.flex-layout {
    text-align: center;
}
.article-layout {
    .article-main {
        margin: 10px 0;

        /deep/ .el-table .warning-row {
            background: oldlace;
        }

        /deep/ .el-table .danger-row {
            background: oldlace;
        }

        /deep/ .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
.box_cloumn{
    display: flex;
    flex-direction: column;
    // flex-wrap: wrap;
}
// .el-checkbox {
//   white-space: normal; /* 让文字进行正常的换行 */
// //   display: block; /* 让checkbox占满整个行，避免文字被截断 */
// }

// .el-checkbox__label {
//   white-space: normal; /* 同样适用于 label */
// }
::v-deep .el-checkbox__label {
			vertical-align: text-top;
			white-space: normal;
			word-break: break-all;
			width: 95%;
		}



</style>
