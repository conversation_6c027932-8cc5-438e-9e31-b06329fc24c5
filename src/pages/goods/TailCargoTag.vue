<template>
  <div>
      <el-tabs v-model="activeName" >
        <el-tab-pane v-for="(item, key) in commonData" :key="key" :label="item.label" :name="item.name">
            <goodslist :status="item.status"  @targetPage="changeTab"  :periods_type="3" v-if="activeName==item.name"></goodslist>
        </el-tab-pane>
      </el-tabs>
  </div>
</template>

<script>
 import goodslist from "./components/GoodsList.vue"
  export default {
    components: {
      goodslist,
    },
    data() {
      return {
        activeName: "",
        id: "0",
        commonData: [{
            label: "待上架",
            name: "wait",
            status:0,
          },
          {
            label: "在售中",
            name: "sale",
            status:2,
          },
          {
            label: "已下架",
            name: "downshelf",
            status:3,
          },
        ]
      };
    },
    watch: {
      activeName(val) {
        this.$router.push(`${this.$route.path}?tab=${val}`); //?tab=${val}
      }
    },
    created() {
      const tab = this.$route.query.tab;
      this.activeName="wait"
    },
    methods: {
      changeTab(data, flag) {
        this.activeName = data;
      }
    }
  };
</script>

<style lang="scss">
</style>
