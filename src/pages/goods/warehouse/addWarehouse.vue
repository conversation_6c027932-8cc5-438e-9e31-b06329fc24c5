<template>
    <el-form ref="form" :model="query" :rules="rules" label-width="120px">
        <el-form-item label="实体仓" prop="physical_id">
            <el-select
                v-model="query.physical_id"
                clearable
                filterable
                @change="physicalChange"
                class="w-large"
                placeholder="请选择实体仓"
            >
                <el-option
                    v-for="(item, index) in physicalOptions"
                    :key="index"
                    :label="
                        item.type === 1
                            ? '萌牙仓' + '-' + item.name
                            : '京东仓' + '-' + item.name
                    "
                    :value="item.id"
                >
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="虚拟仓" prop="virtual_id">
            <el-select
                v-model="query.virtual_id"
                @change="virtualChange"
                clearable
                class="w-large"
                filterable
                placeholder="请选择虚拟仓"
            >
                <el-option
                    v-for="(item, index) in virtualOptions"
                    :key="index"
                    :label="item.fictitious_id + '-' + item.fictitious_name"
                    :value="item.fictitious_id"
                >
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="所属公司" prop="company_ids">
            <el-select
                v-model="query.company_ids"
                clearable
                style="width: 280px;"
                multiple
                placeholder="请选择所属公司"
            >
            <el-option
                    v-for="(item) in payee_merchant_options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="可用频道" prop="channel_types">
            <el-select
                v-model="query.channel_types"
                clearable
                multiple
                class="w-large"
                placeholder="请选择可用频道"
            >
                <el-option
                    v-for="(item, index) in options"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="ERP仓库ID" prop="erp_id">
            <el-input
                v-model="query.erp_id"
                clearable
                class="w-large"
                placeholder="请输入ERP仓库ID"
            >
            </el-input>
        </el-form-item>

        <div class="flex-center">
            <el-button type="primary" @click="submits">提交</el-button>
            <el-button @click="close">取消</el-button>
        </div>
    </el-form>
</template>

<script>
export default {
    data() {
        return {
            payee_merchant_options: [
                { label: "重庆云酒佰酿电子商务有限公司", value: 1 },
                { label: "佰酿云酒（重庆）科技有限公司", value: 2},
                { label: "渝中区微醺酒业商行", value: 5},
                { label: "海南一花一世界科技有限公司", value: 10 },
            ],
            physicalOptions: [],
            virtualOptions: [],
            query: {
                physical_id: "",
                physical_name: "",
                erp_id: "",
                channel_types: [],
                virtual_id:"",
                company_ids:[],
                virtual_name: "",
            },
            options: [
                {
                    value: 0,
                    label: "闪购",
                },
                {
                    value: 1,
                    label: "秒发",
                },
                {
                    value: 2,
                    label: "跨境",
                },
                {
                    value: 3,
                    label: "尾货",
                },
                {
                    value: 4,
                    label: "兔头",
                },
            ],
            rules: {
                erp_id: [
                    {
                        required: true,
                        message: "请输入ERP仓库ID",
                        trigger: "blur",
                    },
                ],
                virtual_id: [
                    {
                        required: true,
                        message: "请选择虚拟仓",
                        trigger: "blur",
                    },
                ],
                physical_id: [
                    {
                        required: true,
                        message: "请选择实体仓",
                        trigger: "blur",
                    },
                ],
                company_ids: [
                    {
                        required: true,
                        message: "请选择所属公司",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    mounted() {
        this.getPhysicalOptions();
    },
    methods: {
        getPhysicalOptions() {
            let data = {
                limit: 100,
                page: 1,
            };
            this.$request.purchase.getRealWarehouseList(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.physicalOptions = res.data.data.list;
                }
            });
        },
        close() {
            this.$emit("close");
        },
        async submits() {
            if (!this.validateForm()) {
                return;
            }
            const data = {
                ...this.query,
            };
            const res = await this.$request.purchase.createVirtualWarehouse(
                data
            );
            if (res.data.error_code == 0) {
                this.$message.success("添加成功");
                this.$emit("close");
            }
        },
        virtualChange(val) {
            if (val) {
                this.query.virtual_name = this.virtualOptions.find(
                    (item) => item.fictitious_id == val
                ).fictitious_name;
            } else {
                this.query.virtual_name = "";
            }
        },
        
        physicalChange(val) {
            this.virtualOptions = [];
            this.query.virtual_id = "";
            this.query.erp_id = "";
            this.query.channel_types = [];
            this.query.virtual_name = "";
            if (val) {
                this.query.physical_name = this.physicalOptions.find(
                    (item) => item.id === val
                ).name;
                this.getVirtualOptions(val);
            } else {
                this.query.physical_name = "";
            }
        },
        async getVirtualOptions(warehouse_id) {
            const data = {
                warehouse_id,
            };
            const res = await this.$request.purchase.getWithoutWarehouseList(
                data
            );
            if (res.data.error_code == 0) {
                this.virtualOptions = res.data.data.list;
            }
        },
        validateForm() {
            let flag = null;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.flex-center {
    display: flex;
    justify-content: center;
}
</style>
