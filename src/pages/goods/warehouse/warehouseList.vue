<template>
    <div>
        <el-card shadow="hover">
            <el-input
                clearable
                size="mini"
                @keyup.enter.native="search"
                class="m-r-10"
                style="width: 180px"
                v-model="query.virtual_name"
                placeholder="虚拟仓库名称"
            />
            <el-select
                v-model="query.physical_id"
                size="mini"
                class="m-r-10"
                filterable
                clearable
                placeholder="请选择实体仓"
            >
                <el-option
                    v-for="item in physicalOptions"
                    :key="item.id"
                    :label="
                        item.type === 1
                            ? '萌牙仓' + '-' + item.name
                            : '京东仓' + '-' + item.name
                    "
                    :value="item.id"
                >
                </el-option>
            </el-select>
            <el-button type="success" @click="search" size="mini"
                >查询
            </el-button>
            <el-button type="primary" size="mini" @click="dialogVisible = true"
                >添加</el-button
            >
        </el-card>
        <el-card shadow="hover">
            <el-table
                size="mini"
                border
                stripe
                :data="DataList"
                style="width: 100%"
            >
                <el-table-column
                    label="ID"
                    prop="id"
                    width="80"
                    align="center"
                />
                <el-table-column
                    label="虚拟仓名称"
                    align="center"
                    prop="virtual_name"
                    min-width="100"
                   
                />
                <el-table-column
                    label="虚拟仓ID"
                    align="center"
                    prop="virtual_id"
                    width="100"
                />
                <el-table-column
                    label="ERP仓库ID"
                    prop="erp_id"
                    align="center"
                    width="100"
                />
                <el-table-column
                    label="实体仓名称"
                    prop="physical_name"
                    min-width="200"
                    show-overflow-tooltip
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    label="所属公司"
                    prop="company_ids_name"
                    min-width="150"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    label="可用频道"
                    prop="channel_types_name"
                    width="220"
                   
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    label="操作"
                    fixed="right"
                    align="center"
                    width="80"
                >
                    <template slot-scope="{ row }">
                        <el-button
                            type="text"
                            size="mini"
                            @click="handleClick(row)"
                            >编辑</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 分页 -->
        <div class="pagination-block">
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            title="添加虚拟仓"
            :visible.sync="dialogVisible"
            center
            :before-close="close"
            :close-on-click-modal="false"
            width="40%"
        >
            <warehouseOp v-if="dialogVisible" @close="close"></warehouseOp>
        </el-dialog>
        <el-dialog
            title="编辑虚拟仓"
            :visible.sync="editDialogVisible"
            center
            :before-close="close"
            :close-on-click-modal="false"
            width="40%"
        >
            <editRealWareHouse
                :detail="detail"
                v-if="editDialogVisible"
                @close="close"
            ></editRealWareHouse>
        </el-dialog>
    </div>
</template>

<script>
import editRealWareHouse from "./editWarehouse.vue";
import warehouseOp from "./addWarehouse.vue";
export default {
    components: {
        warehouseOp,
        editRealWareHouse,
    },
    data() {
        return {
            physicalOptions: [],
            dialogVisible: false,
            editDialogVisible: false,
            DataList: [],
            query: {
                page: 1,
                limit: 10,
                virtual_name: "",
                physical_id: "",
                
            },
            detail: {},
            total: 0,
        };
    },
    mounted() {
        this.getPhysicalOptions();
        this.getData();
    },
    methods: {
        getPhysicalOptions() {
            let data = {
                limit: 100,
                page: 1,
            };
            this.$request.purchase.getRealWarehouseList(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.physicalOptions = res.data.data.list;
                }
            });
        },
        getData() {
            
            const data = {
                ...this.query,
            };
            
            
            this.$request.purchase.getWarehouseList(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.DataList = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        handleClick(row) {
            this.detail = row;
            this.editDialogVisible = true;
        },
        close() {
            this.dialogVisible = false;
            this.editDialogVisible = false;
            this.getData();
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.article-main {
    margin: 10px 0;

    /deep/ .el-table .warning-row {
        background: oldlace;
    }

    /deep/ .el-table .danger-row {
        background: oldlace;
    }

    /deep/ .el-table .success-row {
        background: #f0f9eb;
    }
}

.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}

.article-form {
    & > div {
        display: inline-block;
    }
}
</style>
