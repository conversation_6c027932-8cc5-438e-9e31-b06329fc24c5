<template>
    <div>
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
            <el-form-item label="实体仓类型" prop="type">
                <el-radio-group v-model="form.type">
                    <el-radio disabled :label="1">萌牙</el-radio>
                    <el-radio disabled :label="2">京东</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="实体仓名称" prop="name">
                <el-input
                    v-model="form.name"
                    placeholder="请输入实体仓库名称"
                />
            </el-form-item>
            <el-form-item
                label="实体仓编码"
                prop="coding"
                v-if="form.type === 1"
            >
                <el-input
                    v-model="form.coding"
                    placeholder="请输入实体仓编码"
                />
            </el-form-item>
            <el-form-item
                label="联系人姓名"
                prop="contact_name"
                v-if="form.type === 1"
            >
                <el-input
                    v-model="form.contact_name"
                    placeholder="请输入联系人姓名"
                />
            </el-form-item>
            <el-form-item
                label="联系电话"
                prop="contact_phone"
                v-if="form.type === 1"
            >
                <el-input
                    v-model="form.contact_phone"
                    placeholder="请输入联系电话"
                />
            </el-form-item>
            <el-form-item
                label="仓库详细地址"
                prop="storhouse_address"
                v-if="form.type === 1"
            >
                <el-input
                    type="textarea"
                    v-model="form.storhouse_address"
                    placeholder="请输入仓库详细地址"
                />
            </el-form-item>
            <el-form-item label="AppKey" prop="appkey" v-if="form.type === 2">
                <el-input v-model="form.appkey" placeholder="请输入appkey" />
            </el-form-item>
            <el-form-item
                label="AppSecret"
                prop="appsecret"
                v-if="form.type === 2"
            >
                <el-input
                    v-model="form.appsecret"
                    placeholder="请输入appsecret"
                />
            </el-form-item>
            <el-form-item
                label="事业部编码"
                prop="business_department_coding"
                v-if="form.type === 2"
            >
                <el-input
                    v-model="form.business_department_coding"
                    placeholder="请输入事业部编码"
                />
            </el-form-item>
            <el-form-item
                label="供应商编码"
                prop="supplier_coding"
                v-if="form.type === 2"
            >
                <el-input
                    v-model="form.supplier_coding"
                    placeholder="请输入供应商编码"
                />
            </el-form-item>
            <el-form-item
                label="ISV来源编号"
                prop="isv_source_coding"
                v-if="form.type === 2"
            >
                <el-input
                    v-model="form.isv_source_coding"
                    placeholder="请输入ISV来源编号"
                />
            </el-form-item>
            <el-form-item
                label="店铺编号"
                prop="shop_coding"
                v-if="form.type === 2"
            >
                <el-input
                    v-model="form.shop_coding"
                    placeholder="请输入店铺编号"
                />
            </el-form-item>

            <div class="flex-center">
                <el-button type="primary" @click="submits">提交</el-button>
                <el-button @click="close">取消</el-button>
            </div>
        </el-form>
    </div>
</template>
<script>
export default {
    props: ["detail"],
    data() {
        return {
            form: {
                type: 1,
                name: "",
                appsecret: "",
                business_department_coding: "",
                coding: "",
                contact_name: "",
                shop_coding: "",
                storhouse_address: "",
                contact_phone: "",
                appkey: "",
                isv_source_coding: "",
                supplier_coding: "",
            },
            rules: {
                shop_coding: [
                    {
                        required: true,
                        message: "请输入店铺编号",
                        trigger: "blur",
                    },
                ],
                isv_source_coding: [
                    {
                        required: true,
                        message: "请输入ISV来源编号",
                        trigger: "blur",
                    },
                ],
                supplier_coding: [
                    {
                        required: true,
                        message: "请输入供应商编码",
                        trigger: "blur",
                    },
                ],
                business_department_coding: [
                    {
                        required: true,
                        message: "请输入事业部编码",
                        trigger: "blur",
                    },
                ],
                appsecret: [
                    {
                        required: true,
                        message: "请输入appsecret",
                        trigger: "blur",
                    },
                ],
                appkey: [
                    {
                        required: true,
                        message: "请输入appkey",
                        trigger: "blur",
                    },
                ],
                storhouse_address: [
                    {
                        required: true,
                        message: "请输入仓库详细地址",
                        trigger: "change",
                    },
                ],
                contact_phone: [
                    {
                        required: true,
                        message: "请输入联系电话",
                        trigger: "change",
                    },
                ],
                contact_name: [
                    {
                        required: true,
                        message: "请输入联系人姓名",
                        trigger: "change",
                    },
                ],
                type: [
                    {
                        required: true,
                        message: "请选择仓库类型",
                        trigger: "change",
                    },
                ],
                name: [
                    {
                        required: true,
                        message: "请输入仓库名称",
                        trigger: "blur",
                    },
                ],
                coding: [
                    {
                        required: true,
                        message: "请输入仓库编码",
                        trigger: "blur",
                    },
                ],
                coding: [
                    {
                        required: true,
                        message: "请输入仓库编码",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    mounted() {
        this.form = {
            ...this.detail,
        };
    },
    methods: {
        async submits() {
            if (!this.validateForm()) {
                return;
            }
            const data = {
                ...this.form,
            };
            delete data["created_time"];
            const res = await this.$request.purchase.updateRealWarehouse(data);
            if (res.data.error_code == 0) {
                this.$message.success("编辑成功");
                this.close();
            }
        },
        close() {
            this.$emit("close");
        },
        validateForm() {
            let flag = null;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
    },
};
</script>
<style lang="scss" scoped>
.flex-center {
    display: flex;
    justify-content: center;
}
</style>
