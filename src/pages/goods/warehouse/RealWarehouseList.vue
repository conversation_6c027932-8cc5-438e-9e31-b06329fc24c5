<template>
    <div>
        <el-card shadow="hover">
            <el-input
                size="mini"
                @keyup.enter.native="search"
                class="m-r-10"
                style="width: 180px"
                v-model="query.name"
                placeholder="实体仓库名称"
                clearable
            />
            <el-button type="success" @click="search" size="mini"
                >查询
            </el-button>
            <el-button type="primary" @click="dialogVisible = true" size="mini"
                >添加</el-button
            >
        </el-card>
        <el-card style="margin-top: 10px" shadow="hover">
            <el-table
                size="mini"
                border
                stripe
                :data="DataList"
                fit
                highlight-current-row
                style="width: 100%"
            >
                <el-table-column
                    width="100"
                    label="ID"
                    prop="id"
                    align="center"
                />
                <el-table-column
                    label="实体仓名称"
                    align="center"
                    prop="name"
                    show-overflow-tooltip
                    min-width="240"
                />
                <el-table-column
                    label="类型"
                    prop="type"
                    align="center"
                    width="140"
                >
                    <template slot-scope="row">
                        {{ row.row.type === 1 ? "萌牙" : "京东" }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作"
                    fixed="right"
                    align="center"
                    width="80"
                >
                    <template slot-scope="{ row }">
                        <el-button
                            type="text"
                            size="mini"
                            @click="handleClick(row)"
                            >编辑</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 分页 -->
        <div class="pagination-block">
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            title="添加实体仓库"
            :visible.sync="dialogVisible"
            center
            :close-on-click-modal="false"
            :before-close="close"
            width="40%"
        >
            <RealWarehouseAdd
                @close="close"
                v-if="dialogVisible"
            ></RealWarehouseAdd>
        </el-dialog>
        <el-dialog
            title="编辑实体仓库"
            :visible.sync="editDialogVisible"
            center
            :before-close="close"
            :close-on-click-modal="false"
            width="40%"
        >
            <RealWarehouseEdit
                :detail="detail"
                @close="close"
                v-if="editDialogVisible"
            ></RealWarehouseEdit>
        </el-dialog>
    </div>
</template>

<script>
import RealWarehouseAdd from "./addRealWareHouse.vue";
import RealWarehouseEdit from "./editRealWareHouse.vue";

export default {
    components: {
        RealWarehouseEdit,
        RealWarehouseAdd,
    },
    data() {
        return {
            detail: {},
            editDialogVisible: false,
            dialogVisible: false,
            DataList: [],
            query: {
                page: 1,
                limit: 10,
                name: "",
            },

            total: 0,
        };
    },
    mounted() {
        this.getRealWareHouseList();
    },
    methods: {
        handleClick(row) {
            this.detail = row;
            this.editDialogVisible = true;
        },
        getRealWareHouseList() {
            let data = {
                ...this.query,
            };
            this.$request.purchase.getRealWarehouseList(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.DataList = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        close() {
            this.editDialogVisible = false;
            this.dialogVisible = false;
            this.getRealWareHouseList();
        },
        search() {
            this.query.page = 1;
            this.getRealWareHouseList();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getRealWareHouseList();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getRealWareHouseList();
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.article-main {
    margin: 10px 0;

    /deep/ .el-table .warning-row {
        background: oldlace;
    }

    /deep/ .el-table .danger-row {
        background: oldlace;
    }

    /deep/ .el-table .success-row {
        background: #f0f9eb;
    }
}

.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}

.article-form {
    & > div {
        display: inline-block;
    }
}
</style>
