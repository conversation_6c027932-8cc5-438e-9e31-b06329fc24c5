<template>
    <div>
        <el-form
            :model="form"
            ref="form"
            :rules="rules"
            label-width="140px"
            :inline="true"
            size="normal"
        >
            <div>
                <el-form-item
                    label="展示的频道:"
                    prop="use_channel"
                    :rules="rules.use_channel"
                >
                    <el-select
                        style="width: 300px"
                        v-model="use_channel"
                        placeholder="请选择展示的频道"
                        clearable
                        multiple
                        @change="changeChannel"
                    >
                        <el-option
                            v-for="item in periodTypeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="选择套餐:" :rules="rules.channel">
                    <div
                        v-for="(item, key) in package_list"
                        :key="key"
                        class="package_container"
                        @click="selectPackage(item)"
                    >
                        <div
                            class="package_content"
                            :class="
                                form.package_id === item.id ? 'active_item' : ''
                            "
                        >
                            <el-form
                                :rules="package_rules"
                                label-width="120px"
                                :inline="true"
                                size="normal"
                            >
                                <div>
                                    <el-form-item
                                        label="套餐名称"
                                        :rules="package_rules.package_name"
                                    >
                                        <el-input
                                            v-model="item.package_name"
                                            placeholder="套餐名称"
                                            clearable
                                            disabled
                                        ></el-input>
                                    </el-form-item>
                                </div>
                                <div>
                                    <el-form-item
                                        label="售价"
                                        :rules="package_rules.price"
                                    >
                                        <el-input
                                            v-model="item.price"
                                            placeholder="售价"
                                            clearable
                                            disabled
                                        ></el-input>
                                    </el-form-item>
                                    <el-form-item
                                        label="市场价"
                                        :rules="package_rules.market_price"
                                    >
                                        <el-input
                                            v-model="item.market_price"
                                            placeholder="市场价"
                                            clearable
                                            disabled
                                        ></el-input>
                                    </el-form-item>
                                </div>
                                <div>
                                    <div
                                        class="package_product_info"
                                        v-for="(p_item, key) in item.product"
                                        :key="key"
                                    >
                                        <div class="package_product_info_item">
                                            <span class="item_labe"
                                                >英文名</span
                                            >
                                            <span class="single-line">
                                                {{
                                                    p_item.en_product_name ||
                                                    "-"
                                                }}</span
                                            >
                                        </div>
                                        <div class="package_product_info_item">
                                            <span class="item_labe">品名</span>
                                            <span class="single-line">
                                                {{
                                                    p_item.cn_product_name ||
                                                    "-"
                                                }}</span
                                            >
                                        </div>
                                        <div
                                            class="package_product_info_item"
                                            style="display: flex"
                                        >
                                            <div style="margin-right: 20px">
                                                <span>容量</span>
                                                <span>
                                                    {{
                                                        p_item.capacity || "-"
                                                    }}</span
                                                >
                                            </div>
                                            <div style="margin-right: 20px">
                                                <span>年份</span>
                                                <span>
                                                    {{
                                                        p_item.products_years ||
                                                        "-"
                                                    }}</span
                                                >
                                            </div>
                                            <div style="margin-right: 20px">
                                                <span>简码</span>
                                                <span>
                                                    {{
                                                        p_item.short_code || "-"
                                                    }}</span
                                                >
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    style="margin-left: 50px; margin-top: 10px"
                                >
                                    <el-checkbox
                                        disabled
                                        v-model="item.is_hidden"
                                        :active-value="1"
                                        :inactive-value="0"
                                        >隐藏套餐</el-checkbox
                                    >
                                    <el-checkbox
                                        disabled
                                        v-model="item.is_original_package"
                                        :active-value="1"
                                        :inactive-value="0"
                                        >原箱发货</el-checkbox
                                    >
                                    <el-checkbox
                                        disabled
                                        v-model="item.force_original_package"
                                        :active-value="1"
                                        :inactive-value="0"
                                        >强制原箱</el-checkbox
                                    >
                                </div>
                                <div>
                                    <el-form-item
                                        label="赠送优惠券"
                                        size="normal"
                                    >
                                        <el-input
                                            v-model="item.coupon_name"
                                            placeholder=""
                                            size="normal"
                                            disabled
                                        ></el-input>
                                    </el-form-item>
                                    <el-form-item
                                        label="优惠减免"
                                        size="normal"
                                    >
                                        <el-input
                                            v-model="
                                                item.preferential_reduction
                                            "
                                            placeholder="优惠减免"
                                            size="normal"
                                            disabled
                                        ></el-input>
                                    </el-form-item>
                                    <el-checkbox
                                        disabled
                                        v-model="item.unlimited"
                                        :active-value="1"
                                        :inactive-value="0"
                                        >不限量</el-checkbox
                                    >
                                </div>
                            </el-form>
                        </div>
                        <el-radio
                            v-model="form.package_id"
                            :label="item.id"
                            :disabled="
                                item.coupons_id ||
                                item.preferential_reduction != '0.00'
                            "
                            @change="selectPackage(item)"
                        >
                            <span></span>
                        </el-radio>
                    </div>
                </el-form-item>
            </div>
            <div>
                <el-form-item
                    label="顺手价"
                    prop="package_price"
                    :rules="rules.package_price"
                >
                    <el-input-number
                        :precision="2"
                        v-model="form.package_price"
                        placeholder="顺手价"
                        :controls="false"
                    ></el-input-number>
                </el-form-item>
                <el-form-item label="有效期" prop="time" :rules="rules.time">
                    <el-date-picker
                        v-model="time"
                        type="datetimerange"
                        size="normal"
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        clearable
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="限购数" prop="limit">
                    <el-input-number
                        v-model="form.limit"
                        placeholder="限购数"
                        disabled
                        :controls="false"
                    ></el-input-number>
                </el-form-item>
                <el-form-item label="商品标题" prop="ap_title">
                    <el-input
                        v-model="form.ap_title"
                        placeholder="商品标题"
                        clearable
                    ></el-input>
                    <div style="font-size: 12px; color: #909399">
                        （ 商品标题不可超过11个字）
                    </div>
                </el-form-item>
                <el-form-item label="" prop="is_force_merge">
                    <el-checkbox v-model="form.is_force_merge" :true-label="1" :false-label="0">强制合并发货</el-checkbox>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="选择产品图" :rules="rules.product_img">
                    <div
                        v-for="(item, key) in product_image"
                        :key="key"
                        class="product_image"
                        @click="selectProductImage(item)"
                    >
                        <el-image :src="item" fit="fill"> </el-image>
                        <div
                            class="is-selected-img"
                            v-if="item === form.ap_product_img"
                        >
                            <i class="el-icon-check"></i>
                        </div>
                    </div>
                </el-form-item>
            </div>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "Vue2CommoditiesGoodsConfig",

    data() {
        return {
            use_channel: [],
            form: {
                period: "",
                package_id: "",
                package_price: 0,
                limit: 1,
                ap_title: "",
                use_flash: 0,
                use_second: 0,
                use_leftover: 0,
                ap_product_img: "",
                is_force_merge:false,
            },
            rules: {
                use_channel: [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            if (this.use_channel.length === 0) {
                                callback(new Error("请选择展示的频道"));
                            } else {
                                callback();
                            }
                        },
                    },
                ],
                channel: [
                    {
                        required: true,
                    },
                ],
                package_price: [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            //【顺手价】必须小于等于所选套餐里的售价，若所填数字大于售价，弹出提示语【加购价须小于等于售价】
                            if (
                                this.form.package_price >
                                this.package_info.price
                            ) {
                                callback(new Error("顺手价须小于等于售价"));
                            } else {
                                callback();
                            }
                        },
                    },
                ],
                time: [
                    // 【有效期】的开始时间不得小于上架时间，结束时间不得大于下架时间，必须在上架时间和下架时间区间范围内
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            if (this.time.length === 0) {
                                callback(new Error("请选择有效期"));
                            } else {
                                if (this.time[0] < this.good_info.onsale_time) {
                                    callback(
                                        new Error("开始时间不得小于上架时间")
                                    );
                                } else if (
                                    this.time[1] > this.good_info.sold_out_time
                                ) {
                                    callback(
                                        new Error("结束时间不得大于下架时间")
                                    );
                                } else {
                                    callback();
                                }
                            }
                        },
                    },
                ],
                limit: [
                    {
                        // 【限购数】：是指该款加购商品可下单的份数的上限（此上限肯定小于库存数）
                        required: true,
                        validator: (rule, value, callback) => {
                            if (value > this.package_info.inventory) {
                                // callback(new Error("限购数不得大于库存数"));
                                callback();
                            } else {
                                callback();
                            }
                        },
                    },
                ],
                ap_title: [
                    {
                        // 商品标题大于11个字时，不可提交，弹出提示框【顺手买1件的商品标题不得超过11个字，请重新填写】，修改后的标题不会同步更新到其他系统，只用于加购商品的展示 ,不能为空
                        required: true,
                        validator: (rule, value, callback) => {
                            if (value) {
                                if (value.length > 11) {
                                    callback(
                                        new Error(
                                            "顺手买1件的商品标题不得超过11个字，请重新填写"
                                        )
                                    );
                                } else {
                                    callback();
                                }
                            } else {
                                callback(new Error("商品标题不能为空"));
                            }
                        },
                    },
                ],
                product_img: [
                    {
                        required: true,
                    },
                ],
            },
            package_rules: {
                package_name: [
                    {
                        required: true,
                    },
                ],
                price: [
                    {
                        required: true,
                    },
                ],
                market_price: [
                    {
                        required: true,
                    },
                ],
            },
            periodTypeOptions: [
                {
                    label: "闪购",
                    value: "use_flash",
                },
                {
                    label: "秒发",
                    value: "use_second",
                },
                // {
                //     label: "尾货",
                //     value: "use_leftover",
                // },
                // {
                //     label: "全部",
                //     value: "use_all",
                // },
            ],
            package_list: [],
            good_info: {},
            product_list: [],
            package_info: {},
            time: [],
            product_image: [],
            is_edit: false,
        };
    },

    mounted() {},
    computed: {},
    methods: {
        disableSelectPackage(row) {
            // 当该套餐有优惠券和优惠减免时，该套餐不可勾选 。
        },
        async getCommoditiesDetail(periods) {
            const res = await this.$request.added.getCommoditiesDetail({
                periods: periods,
            });
            if (res.data.error_code === 0) {
                this.good_info = res.data.data.list[0];
                this.product_image = res.data.data.list[0].product_img_arr;
                return Promise.resolve(res.data.data.list[0]);
            }
        },
        getCouponDetail(coupon_id) {
            this.$request.added.getCouponDetail({ coupon_id }).then((res) => {
                if (res.data.error_code === 0) {
                    return res.data.data.coupon_name;
                }
            });
        },

        addPeriods() {
            if (this.form.package_id === "") {
                this.$message({
                    message: "请选择套餐",
                    type: "error",
                });
                return false;
            }
            if (this.form.ap_product_img === "") {
                this.$message({
                    message: "请选择产品图",
                    type: "error",
                });
                return false;
            }
            this.$refs.form.validate((valid) => {
                if (valid) {
                    const domainRemoved = this.form.ap_product_img.replace(
                        /^https?:\/\/[^/]+/,
                        ""
                    );
                    let reduced_price =
                        (Number(this.package_info.price) * 100 -
                            Number(this.form.package_price) * 100) /
                        100;
                    let data = {
                        ...this.form,
                        ap_vp_st: this.time[0],
                        ap_vp_et: this.time[1],
                        is_ap: 1,
                        package_id: this.package_info.id,
                        package_name: this.package_info.package_name,
                        reduced_price: reduced_price,
                        predict_shipment_time:
                            this.good_info.predict_shipment_time,
                        ap_product_img: domainRemoved,
                    };
                    console.log("shissisisi", this.form);
                    let method = this.is_edit ? "updateAP" : "addPeriods";
                    this.$request.added[method](data).then((res) => {
                        if (res.data.error_code === 0) {
                            this.$emit("addPeriodsSuccess");
                            this.$message({
                                message: "添加成功",
                                type: "success",
                            });
                        }
                    });
                }
            });
        },
        getEdit(row) {
            this.is_edit = true;
            let use_channel_arr = ["use_flash", "use_second", "use_leftover"];
            use_channel_arr.map((item) => {
                if (row[item] === 1) {
                    this.use_channel.push(item);
                }
            });
            this.form = {
                package_id: row.package_id,
                package_price: row.package_price,
                limit: row.limit,
                ap_title: row.ap_title,
                use_flash: row.use_flash,
                use_second: row.use_second,
                use_leftover: row.use_leftover,
                ap_product_img: row.ap_product_img,
                is_force_merge:row.is_force_merge,
            };
            console.log("shissisisi", this.form);
            this.form.id = row.id;
            this.time = [row.ap_vp_st, row.ap_vp_et];
            this.form.period = row.period;

            this.getPackageDetail(row.period);
        },
        selectPackage(item) {
            if (item.coupons_id || item.preferential_reduction != "0.00") {
                this.$message({
                    message: "该套餐不可勾选",
                    type: "error",
                });
                return false;
            }
            this.form.package_id = item.id;
            this.package_info = { ...item };
        },
        changeChannel() {
            this.periodTypeOptions.map((item) => {
                if (this.use_channel.includes(item.value)) {
                    this.form[item.value] = 1;
                } else {
                    this.form[item.value] = 0;
                }
            });
        },
        async getPackageDetail(periods) {
            if (!periods) {
                this.$message.error("未获取到期数，请重试");
                return;
            }
            this.form.period = periods;
            const res = await this.getCommoditiesDetail(periods);
            if (res) {
                this.form.periods_type = res.periods_type;
                const package_list = await this.$request.added.getPackageDetail(
                    {
                        period: res.id,
                        periods_type: res.periods_type,
                        product: 1,
                    }
                );
                if (package_list.data.error_code === 0) {
                    this.package_list = package_list.data.data;
                    this.package_list.map((item) => {
                        if (item.coupons_id) {
                            item.coupon_name = this.getCouponDetail(
                                item.coupons_id
                            );
                        }
                    });
                    if (this.package_list.length === 1) {
                        this.form.package_id = this.package_list[0].id;
                        this.package_info = { ...this.package_list[0] };
                    }
                    if (this.form.package_id && this.is_edit) {
                        let current_package = this.package_list.find(
                            (item) => item.id === this.form.package_id
                        );
                        this.package_info = { ...current_package };
                    }
                }
            }
        },
        selectProductImage(item) {
            this.form.ap_product_img = item;
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep .package_container {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .package_content {
        padding: 12px 12px;
        border: 1px solid #ccc;
        height: 100%;
        border-radius: 4px;
        width: 900px;
        margin-right: 50px;
        .package_product_info {
            padding: 12px;
            border: 1px solid #ccc;
            width: 597px;
            margin-left: 50px;
            margin-top: 20px;

            .package_product_info_item {
                margin-bottom: 10px;
                line-height: 20px;
                display: flex;
                align-items: center;
                .item_labe {
                    width: 50px;
                    display: inline-block;
                    text-align: left;
                    margin-right: 10px;
                }
            }
        }
    }
}
.single-line {
    display: inline-block;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.active_item {
    border: 1px solid #409eff !important;
}
.product_image {
    width: 100px;
    height: 100px;
    margin-right: 10px;
    margin-bottom: 10px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    cursor: pointer;
}
.is-selected-img {
    position: absolute;
    right: -17px;
    top: -7px;
    width: 46px;
    height: 26px;
    background: #13ce66;
    text-align: center;
    transform: rotate(45deg);
    box-shadow: 0 1px 1px #ccc;
    & > i {
        transform: rotate(-45deg);
    }
}
</style>
