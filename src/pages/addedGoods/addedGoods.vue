<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-form :inline="true" size="mini">
                    <el-form-item label="">
                        <el-input
                            v-model="query.period"
                            placeholder="期数"
                        ></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-input
                            v-model="query.ap_title"
                            placeholder="标题"
                            clearable
                        ></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-select
                            v-model="use_channel"
                            placeholder="频道（可多选）"
                            clearable
                            multiple
                            @change="changeChannel"
                        >
                            <el-option
                                v-for="item in periodTypeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-select
                            v-model="query.is_ap"
                            clearable
                            @change="search"
                        >
                            <el-option label="启用" :value="1"> </el-option>
                            <el-option label="禁用" :value="0"> </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" size="mini" @click="search"
                            >查询</el-button
                        >
                        <el-button type="success" size="mini" @click="add"
                            >新增</el-button
                        >
                    </el-form-item>
                </el-form>
            </el-card>
        </div>
        <el-card shadow="hover" class="table-card" :body-style="{ padding: '0px' }">
            <el-table
                :data="tableData"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                style="width: 100%"
            >
                <el-table-column label="期数" width="150">
                    <template slot-scope="scope">
                        <span>{{ scope.row.period }}</span>
                        <el-tag 
                            :type="scope.row.is_ap == 1 ? 'success' : 'danger'"
                            size="mini"
                            style="margin-left: 5px"
                        >
                            {{ scope.row.is_ap == 1 ? '启用' : '禁用' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="商品标题" min-width="200">
                    <template slot-scope="scope">
                        <span style="display: block; text-align: left">{{ scope.row.ap_title }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="展示频道" width="150">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.use_flash && scope.row.use_second && scope.row.use_leftover">全部</el-tag>
                        <div v-else>
                            <el-tag v-if="scope.row.use_flash" type="success" size="mini" style="margin-right: 4px">闪购</el-tag>
                            <el-tag v-if="scope.row.use_second" type="warning" size="mini" style="margin-right: 4px">秒发</el-tag>
                            <el-tag v-if="scope.row.use_leftover" type="info" size="mini">尾货</el-tag>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="套装名称" min-width="200">
                    <template slot-scope="scope">
                        <span>{{ scope.row.package_name }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="顺手价" width="100">
                    <template slot-scope="scope">
                        <span>{{ scope.row.package_price }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="限购数" width="100">
                    <template slot-scope="scope">
                        <span>{{ scope.row.limit }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="有效期" width="380">
                    <template slot-scope="scope">
                        <div style="white-space: nowrap">
                            <span>{{ scope.row.ap_vp_st }}</span>
                            <span> ~ </span>
                            <span>{{ scope.row.ap_vp_et }}</span>
                            <el-tag 
                                :type="isExpired(scope.row.ap_vp_et) ? 'danger' : 'success'"
                                size="mini"
                                style="margin-left: 5px"
                            >
                                {{ isExpired(scope.row.ap_vp_et) ? '已过期' : '有效' }}
                            </el-tag>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="排序" width="100">
                    <template slot-scope="{ row }">
                        <el-input
                            v-model="row.sort"
                            size="mini"
                            oninput="value=value.replace(/[^\d]/g,'')"
                            @blur="handleUpdateSort(row)"
                        />
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right">
                    <template slot-scope="scope">
                        <div style="text-align: left">
                            <el-button
                                type="text"
                                size="mini"
                                @click="handleEdit(scope.row)"
                            >
                                配置
                            </el-button>
                            <el-button
                                type="text"
                                size="mini"
                                @click="handleStatusClick(scope.row)"
                            >
                                {{ scope.row.is_ap == 1 ? "禁用" : "启用" }}
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <el-dialog
            :visible.sync="select_visible"
            width="30%"
            @close="clsoeSelectDia"
            title="选择期数"
            custom-class="period-select-dialog"
            center
        >
            <div class="period-select-form">
                <el-form label-width="120px" :inline="false" size="normal">
                    <el-form-item
                        label="请选择期数"
                        :rules="[
                            {
                                required: true,
                            },
                        ]"
                    >
                        <el-select
                            style="width: 100%"
                            v-model="select_period"
                            placeholder="请输入期数"
                            filterable
                            remote
                            :remote-method="remoteMethod"
                            class="period-select"
                        >
                            <el-option
                                v-for="item in goodsList"
                                :key="item.id"
                                :label="item.title"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>

            <span slot="footer" class="dialog-footer">
                <el-button @click="select_visible = false">取消</el-button>
                <el-button type="primary" @click="comfirmSelectGoods">确定</el-button>
            </span>
        </el-dialog>

        <el-dialog
            title="配置页面"
            :visible.sync="add_goods_visible"
            :close-on-click-modal="false"
            width="80%"
            custom-class="goods-config-dialog"
            center
        >
            <div class="goods-config-container">
                <goodsConfig
                    class="goodsConfig"
                    v-if="add_goods_visible"
                    ref="goodsConfig"
                    @addPeriodsSuccess="addPeriodsSuccess"
                ></goodsConfig>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="add_goods_visible = false">取消</el-button>
                <el-button type="primary" @click="comfirmAddGoods">确定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import goodsConfig from "./goodsConfig.vue";
export default {
    components: {
        goodsConfig,
    },
    props: {
        status: {
            type: Number,
            default: 0,
        },
        periods_type: {
            type: Number,
            default: 0,
        },
    },

    data() {
        return {
            use_channel: [],
            query: {
                period: "",
                is_ap: 1,
                is_vp: "",
                ap_title: "",
                use_flash: "",
                use_leftover: "",
                use_second: "",
            },
            goodsList: [],
            tableData: [],
            total: 0,
            add_goods_visible: false,
            select_visible: false,
            select_period: "",
            periodTypeOptions: [
                {
                    label: "闪购",
                    value: "use_flash",
                },
                {
                    label: "秒发",
                    value: "use_second",
                },
                // {
                //     label: "尾货",
                //     value: "use_leftover",
                // },
                // {
                //     label: "全部",
                //     value: "use_all",
                // },
            ],
        };
    },
    methods: {
        add() {
            this.select_visible = true;
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        comfirmAddGoods() {
            this.$refs.goodsConfig.addPeriods();
        },
        handleStatusClick(rows) {
            this.$nextTick(() => {
                this.$refs.logList.openForm(rows);
            });
        },
        edit() {
            this.isEdit = true;
            this.activeName = "edit";
        },
        getData() {
            this.$request.added
                .getAddPurchase({
                    period: this.query.period,
                    is_ap: this.query.is_ap,
                    is_vp: this.query.is_vp,
                    ap_title: this.query.ap_title,
                    use_flash: this.query.use_flash,
                    use_leftover: this.query.use_leftover,
                    use_second: this.query.use_second,
                })
                .then((res) => {
                    console.log(res.data);
                    if (res.data.error_code == 0) {
                        this.tableData = res.data.data;
                    }
                });
        },
        clsoeSelectDia() {},
        changeChannel() {
            this.periodTypeOptions.map((item) => {
                if (this.use_channel.length) {
                    if (this.use_channel.includes(item.value)) {
                        this.query[item.value] = 1;
                    } else {
                        this.query[item.value] = "";
                    }
                } else {
                    this.query[item.value] = "";
                }
            });
            this.search();
        },
        comfirmSelectGoods() {
            if (this.select_period.length === 0) {
                this.$message.error("请选择期数");
                return;
            }
            this.add_goods_visible = true;
            this.$nextTick(() => {
                this.$refs.goodsConfig.getPackageDetail(this.select_period);
                this.select_period = "";
                this.goodsList = [];
            });
            this.select_visible = false;
        },
        async remoteMethod(query) {
            if (query !== "") {
                const res = await this.$request.added.getCommoditiesDetail({
                    periods: query,
                    onsale_status: 2,
                });
                if (res.data.error_code === 0) {
                    this.goodsList = res.data.data.list;
                }
            }
        },
        addPeriodsSuccess() {
            this.add_goods_visible = false;
            this.getData();
        },
        handleEdit(row) {
            this.add_goods_visible = true;
            this.$nextTick(() => {
                this.$refs.goodsConfig.getEdit(row);
            });
        },
        handleStatusClick(row) {
            this.$request.added
                .updateAP({
                    id: row.id,
                    is_ap: row.is_ap == 1 ? 0 : 1,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.$message.success("操作成功");
                        this.getData();
                    }
                });
        },
        handleUpdateSort({ id, sort }) {
            console.log(id, sort, 6666);
            if (!sort) return this.$message.error("排序值必须大于0！");
            this.$request.added.updateSort({ id, sort }).then((res) => {
                if (res.data.error_code == 0) {
                    this.$message.success("操作成功");
                    this.getData();
                }
            });
        },
        isExpired(endDate) {
            const currentDate = new Date();
            const endDateTime = new Date(endDate);
            return endDateTime < currentDate;
        },
    },
    mounted() {
        this.getData();
    },
};
</script>
<style lang="scss" scoped>
.ellipsis {
    display: inline-block;
    text-align: left;
    width: 220px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    height: 22px;
    line-height: 22px;
}
.el-card {
    margin-bottom: 10px;
}

.good-card {
    display: flex;
    justify-content: space-between;

    .good-cardinfo {
        width: 80%;

        .p_blue {
            color: #409eff;
            cursor: pointer;
        }

        .good-head {
            width: 100%;
            display: flex;
            justify-content: flex-start;
            white-space: nowrap;
            overflow: hidden;

            .good-tag {
                & > .el-tag {
                    margin-right: 4px;
                }
            }
        }

        .good-content {
            margin-top: 10px;
            display: flex;
            justify-content: space-between;

            .el-icon-edit {
                margin-left: 5px;
                color: #409eff;
                font-size: 14px;
            }

            p {
                margin-bottom: 0;
            }

            .good-time {
                width: 240px;

                > div {
                    display: flex;
                    align-items: center;
                }

                .times {
                    display: flex;

                    i {
                        cursor: pointer;
                        font-size: 20px;
                        color: #409eff;
                        padding: 2px 0 0 3px;
                    }
                }
            }

            .el-link {
                font-size: 12px;
            }

            .good-saleinfo {
                .good-remark {
                    display: flex;
                    align-items: center;
                    height: 30px;

                    & > p {
                        margin-bottom: 0;
                    }

                    & > .el-button {
                        margin-left: 20px;
                    }
                }
            }
        }
    }

    .good-status {
        width: 50px;
    }

    .opration {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 80px;

        & > .el-button {
            margin-left: 0;
            margin-top: 10px;
        }
    }
}

.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .table {
        margin-top: 10px;

        .f-12 {
            font-size: 12px;
        }

        .card {
            margin-bottom: 8px;

            .card-title {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;

            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }

                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}

.el-link--default {
    vertical-align: inherit !important;
}
.goodsConfig {
    height: calc(100vh - 200px);
    overflow-y: auto;
}

.table-card {
    border: none;
    
    :deep(.el-card__body) {
        padding: 0;
    }
}

.period-select-dialog {
    .el-dialog__body {
        padding: 10px 20px;
    }
    
    .period-select-form {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        
        .el-form {
            margin: 0 auto;
            max-width: 400px;
            
            .el-form-item__label {
                padding-right: 12px;
                font-size: 14px;
            }
        }
        
        .el-form-item {
            margin-bottom: 0;
            
            &__label {
                font-weight: 500;
                color: #606266;
            }
        }
        
        .period-select {
            .el-input__inner {
                height: 36px;
                line-height: 36px;
                border-radius: 4px;
                border: 1px solid #dcdfe6;
                font-size: 14px;
                
                &:hover, &:focus {
                    border-color: #409EFF;
                }
            }
        }
    }
}

.dialog-footer {
    text-align: center;
    padding-top: 20px;
    
    .el-button {
        min-width: 100px;
        padding: 10px 20px;
        margin: 0 10px;
        
        &--primary {
            background-color: #409EFF;
            border-color: #409EFF;
            
            &:hover {
                background-color: #66b1ff;
                border-color: #66b1ff;
            }
        }
    }
}

.goods-config-dialog {
    /deep/ .el-dialog {
        margin: 0 auto !important;
        max-width: 1200px;
        height: 90vh;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }

    /deep/ .el-dialog__body {
        padding: 0;
        height: calc(100% - 110px);
    }

    .goods-config-container {
        width: 100%;
        height: 100%;
        padding: 20px;
        background-color: #fff;
        border-radius: 8px;
    }

    .goodsConfig {
        height: 100%;
        overflow-y: auto;
        width: 100%;
    }

    .dialog-footer {
        background-color: #fff;
        padding: 15px 0;
        text-align: center;
        box-shadow: 0 -2px 12px 0 rgba(0, 0, 0, 0.05);
    }
}
</style>
