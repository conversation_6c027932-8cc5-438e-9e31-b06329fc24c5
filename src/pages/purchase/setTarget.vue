<template>
    <div>
        <!-- 高级查询 -->
        <el-form :inline="true" size="small">
            <el-form-item>
                <el-date-picker
                    v-model="query.month"
                    :clearable="false"
                    type="month"
                    value-format="yyyy-MM"
                    placeholder="选择月"
                >
                </el-date-picker>
            </el-form-item>

            <el-button type="warning" size="mini" @click="search"
                >查询</el-button
            >
            <el-button
                type="success"
                size="mini"
                @click="addTargetDialogStatus = true"
                >新增</el-button
            >
        </el-form>

        <el-card shadow="hover">
            <el-table stripe border size="mini" :data="DataList" fit>
                <el-table-column label="采购组" align="center">
                    <template slot-scope="row">
                        {{ row.row.group_attr | groupNameFormat }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="人员"
                    align="center"
                    show-overflow-tooltip
                    prop="buyer"
                />
                <el-table-column prop="product_type" align="center" label="统计类型"> 
                    <template slot-scope="{ row }">
                       {{ row.product_type | toTypeText }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="销售目标"
                    align="center"
                    prop="sale_target"
                >
                </el-table-column>
                <el-table-column
                    label="毛利率目标"
                    align="center"
                    prop="gross_profit_target"
                >
                    <template slot-scope="row">
                        {{ row.row.gross_profit_target }}%
                    </template>
                </el-table-column>
                <el-table-column label="月份" align="center" prop="month">
                </el-table-column>
            </el-table>
        </el-card>
        <el-dialog
            :visible.sync="addTargetDialogStatus"
            title="添加销售目标"
            width="40%"
            center
            :close-on-click-modal="false"
        >
            <el-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleForm"
                label-width="100px"
                class="demo-ruleForm"
            >
                <el-form-item label="采购员" prop="buyer_id">
                    <el-select
                        v-model="ruleForm.buyer_id"
                        filterable
                        placeholder="请选择采购员"
                    >
                        <el-option
                            v-for="(item, index) in purchasePeople"
                            :key="index"
                            :label="item.realname"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="统计类型" prop="product_type">
                    <el-select
                        v-model="ruleForm.product_type"
                        filterable
                        placeholder="请选择统计类型"
                    >
                        <el-option
                            v-for="(item, index) in typeList"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="销售目标" prop="sale_target">
                    <el-input-number
                        v-model="ruleForm.sale_target"
                        :precision="0"
                        :min="0"
                    ></el-input-number>
                </el-form-item>
                <el-form-item label="毛利率目标" prop="gross_profit_target">
                    <el-input-number
                        v-model="ruleForm.gross_profit_target"
                        :precision="0"
                        :min="0"
                        :max="100"
                    ></el-input-number>
                    %
                </el-form-item>
                <el-form-item label="月份" prop="month">
                    <el-date-picker
                        v-model="ruleForm.month"
                        type="month"
                        value-format="yyyy-MM"
                        placeholder="选择月"
                        :clearable="false"
                    >
                    </el-date-picker>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleForm')"
                        >立即创建</el-button
                    >
                    <el-button @click="resetForm('ruleForm')">重置</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
export default {
    components: {},
    
    filters: {
        groupNameFormat(val) {
            switch (val) {
                case "0":
                    return "A组";
                case "1":
                    return "B组";
                case "2":
                    return "C组";
                case "3":
                    return "D组";
                case "4":
                    return "E组";
                case "5":
                    return "F组";
                case "6":
                    return "L组";
                default:
                    return "-";
            }
        },

        toTypeText(input) {
            return (
                {
                    0: "所有",
                    1: "白酒类",
                    2: "食品类",
                    3: "酒类（非白酒）",
                }[input] || ""
            );
        },
    },
    data() {
        return {
            ruleForm: {
                buyer_id: "",
                buyer: "",
                gross_profit_target: 0,
                sale_target: 0,
                month: "",
                product_type:""
            },
            rules: {
                gross_profit_target: [
                    {
                        required: true,
                        message: "请输入毛利率目标",
                        trigger: "change",
                    },
                ],
                month: [
                    {
                        required: true,
                        message: "请选择时间",
                        trigger: "change",
                    },
                ],
                sale_target: [
                    {
                        required: true,
                        message: "请输入销售目标",
                        trigger: "change",
                    },
                ],
                buyer_id: [
                    {
                        required: true,
                        message: "请选择采购员",
                        trigger: "change",
                    },
                ],
                product_type: [
                    {
                        required: true,
                        message: "请选择统计类型",
                        trigger: "change",
                    },
                ],
            },
            addTargetDialogStatus: false,
            query: {
                month: "",
            },
            entranceOptions: [],
            channelOptions: [
                {
                    label: "闪购",
                    value: 0,
                },
                {
                    label: "秒发",
                    value: 1,
                },
                {
                    label: "跨境",
                    value: 2,
                },
                {
                    label: "尾货",
                    value: 3,
                },
            ],
            typeList: [
                {
                    label: "所有",
                    value: 0,
                },
                {
                    label: "白酒类",
                    value: 1,
                },
                {
                    label: "食品类",
                    value: 2,
                },
                {
                    label: "酒类（非白酒）",
                    value: 3,
                },
            ],
            purchasePeople: [],
            DataList: [],
        };
    },
    mounted() {
        const Y = new Date().getFullYear();
        const M = new Date().getMonth() + 1;
        this.query.month = Y + "-" + M;
        this.getSetTarget();
        this.purchaseList();
        this.ruleForm.month = Y + "-" + M;
    },
    methods: {
        findRealName() {
            return this.purchasePeople.find(
                (item) => item.id == this.ruleForm.buyer_id
            ).realname;
        },
        // 获取数据
        async getSetTarget() {
            const data = {
                month: this.query.month,
            };
            const res = await this.$request.purchase.getSetTarget(data);
            if (res.data.error_code === 0) {
                this.DataList = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        async purchaseList() {
            const data = {
                type: 2,
            };
            const res = await this.$request.article.purchaseList(data);
            if (res.data.error_code == 0) {
                this.purchasePeople = res.data.data.list;
            }
        },
        search() {
            this.currentPage = 1;
            this.getSetTarget();
        },
        submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    const data = {
                        ...this.ruleForm,
                        buyer: this.findRealName(),
                    };
                    console.log(data);
                    const res = await this.$request.purchase.addTarget(data);
                    if (res.data.error_code == 0) {
                        this.$message.success("添加成功");
                        this.addTargetDialogStatus = false;
                        this.getSetTarget();
                        this.resetForm("ruleForm");
                    }
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
    },
};
</script>

<style lang="scss" scoped>
.page-center {
    text-align: center;
}
.footer-dialog {
    display: flex;
    margin-top: 10px;
    justify-content: center;
}
</style>
