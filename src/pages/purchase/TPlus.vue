<template>
    <div>
        <el-card shadow="hover">
            <!-- 高级查询 -->
            <el-input
                v-model="code"
                class="w-large m-r-10"
                size="mini"
                @keyup.enter.native="search"
                placeholder="请输入进货单编码"
            />
            <el-button type="warning" size="mini" @click="search"
                >查询</el-button
            >
            <el-button
                type="success"
                :disabled="multipleSelection.length == 0"
                size="mini"
                @click="push"
                >手动推送</el-button
            >
        </el-card>
        <el-card shadow="hover">
            <el-table
                @selection-change="handleSelectionChange"
                stripe
                size="mini"
                border
                :data="DataList"
                style="width: 100%"
            >
                <el-table-column type="selection" width="40"> </el-table-column>
                <el-table-column
                    label="进货编码"
                    align="center"
                    prop="code"
                    width="180"
                >
                    <template slot-scope="{ row }">
                        <el-link
                            style="font-size: 12px"
                            @click="getDetail(row)"
                            >{{ row.code }}</el-link
                        >
                    </template>
                </el-table-column>
                <el-table-column
                    label="汇率"
                    align="center"
                    prop="exchangeRate"
                    width="160"
                />
                <el-table-column
                    label="币种名称"
                    align="center"
                    prop="name"
                    width="90"
                />
                <el-table-column
                    label="状态"
                    align="center"
                    prop="voucherStateName"
                    width="80"
                />
                <el-table-column
                    label="出单时间"
                    align="center"
                    prop="createtime"
                    width="170"
                />
                <el-table-column
                    label="制单人"
                    align="center"
                    prop="maker"
                    width="80"
                />
                <el-table-column
                    label="备注"
                    align="center"
                    prop="memo"
                    show-overflow-tooltip
                    min-width="200"
                />
                <el-table-column
                    label="采购类型"
                    align="center"
                    prop="pubuserdefnvc1"
                    width="100"
                />
                <el-table-column
                    label="来源单据编号"
                    align="center"
                    prop="sourceVoucherCode"
                    width="170"
                />
                <el-table-column
                    label="预计到货时间"
                    align="center"
                    prop="acceptDate"
                    width="170"
                />
                <el-table-column
                    label="单据日期"
                    align="center"
                    prop="voucherdate"
                    width="170"
                />
                <el-table-column
                    label="制单时间"
                    align="center"
                    prop="madedate"
                    width="170"
                />
                <el-table-column
                    label="往来单位"
                    align="center"
                    prop="partnername"
                    show-overflow-tooltip
                    width="200"
                />
                <el-table-column
                    label="仓库名称"
                    align="center"
                    prop="warehousename"
                    show-overflow-tooltip
                    width="180"
                />
            </el-table>
        </el-card>
        <!-- 分页 -->
        <div class="page-center">
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="pageSize"
                :current-page="currentPage"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            :visible.sync="detailDialogStatus"
            width="966px"
            center
            :close-on-click-modal="false"
        >
            <detail v-if="detailDialogStatus" :detailData="detailData"></detail>
        </el-dialog>
    </div>
</template>

<script>
import detail from "./TPlusDetail.vue";
export default {
    components: { detail },
    data() {
        return {
            multipleSelection: [],
            code: "",
            DataList: [],
            detailData: {},
            currentPage: 1, // 当前页
            detailDialogStatus: false,
            pageSize: 10, // 每页条数
            total: 0, // 总条数
        };
    },
    mounted() {
        this.getTplusOrder();
    },
    methods: {
        getDetail(row) {
            console.log(row.code);
            const data = {
                code: row.code,
            };
            this.$request.purchase.getDetail(data).then((res) => {
                console.log(res);
                if (res.data.error_code == 0) {
                    this.detailData = res.data.data;
                    this.detailDialogStatus = true;
                }
            });
        },
        async push() {
            let codes = [];
            this.multipleSelection.map((item) => {
                codes.push(item.code);
            });
            const data = {
                codes: codes.join(","),
            };
            const res = await this.$request.purchase.pushOrder(data);
            if (res.data.error_code == 0) {
                this.$message.success("推送成功");
                this.getTplusOrder();
            }
        },
        // 获取数据
        async getTplusOrder() {
            const data = {
                code: this.code,
                page: this.currentPage,
                limit: this.pageSize,
            };
            const res = await this.$request.purchase.getTplusOrder(data);
            if (res.data.error_code === 0) {
                this.DataList = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        search() {
            this.currentPage = 1;
            this.getTplusOrder();
        },
        // 改变每页条数
        handleSizeChange(val) {
            this.currentPage = 1;
            this.pageSize = val;
            this.getTplusOrder();
        },
        // 改变当前页
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getTplusOrder();
        },
    },
};
</script>

<style lang="scss" scoped>
.page-center {
    text-align: center;
}
</style>
