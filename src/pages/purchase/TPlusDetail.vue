<template>
    <vue-easy-print tableShow ref="easyPrint">
        <div class="detail-layout">
            <div class="print-btn" :class="ignore ? 'ignore' : ''">
                <el-button type="primary" @click="printPage">打印</el-button>
            </div>
            <div class="header-layout">
                <div class="title"><b>重庆云酒佰酿电子商务有限公司</b></div>
                <div class="desc">
                    <b>
                        {{ detailData.purchase_type }}
                    </b>
                </div>
            </div>
            <div class="content-layout">
                <div>
                    <div>
                        <span class="label">单号：</span
                        ><span class="value">{{ detailData.code }}</span>
                    </div>
                    <div>
                        <span class="label">申请人： </span>
                        <span class="value"> {{ detailData.maker }} </span>
                    </div>
                    <div>
                        <span class="label">仓库： </span>
                        <span class="value"> {{ detailData.warehouse }} </span>
                    </div>
                </div>
                <div>
                    <div>
                        <span class="label">供应商：</span
                        ><span class="value">{{ detailData.supplier }}</span>
                    </div>
                    <div>
                        <span class="label">单据时间： </span>
                        <span class="value">
                            {{ detailData.voucher_date }}
                        </span>
                    </div>
                    <div>
                        <span class="label">预计到货时间： </span>
                        <span class="value">
                            {{ detailData.accept_date }}
                        </span>
                    </div>
                </div>
            </div>
            <el-table
                size="mini"
                border
                :data="detailData.list"
                style="width: 100%"
            >
                <el-table-column
                    label="简码"
                    align="center"
                    prop="short_code"
                    width="130"
                >
                </el-table-column>
                <el-table-column
                    label="存货名称"
                    align="center"
                    prop="product_name"
                    width="140"
                >
                </el-table-column>
                <el-table-column
                    label="英文名称"
                    align="center"
                    prop="product_en_name"
                    min-width="140"
                >
                </el-table-column>
                <el-table-column
                    label="年份"
                    align="center"
                    prop="year"
                    width="70"
                >
                </el-table-column>
                <el-table-column
                    label="规格"
                    align="center"
                    prop="capacity"
                    width="70"
                >
                </el-table-column>
                <el-table-column
                    label="主计量"
                    align="center"
                    prop="unit"
                    width="70"
                >
                </el-table-column>
                <el-table-column
                    label="数量"
                    align="center"
                    prop="quantity"
                    width="70"
                >
                </el-table-column>
                <el-table-column
                    label="含税单价"
                    align="center"
                    prop="price"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    label="含税金额"
                    align="center"
                    prop="total_price"
                    width="120"
                >
                </el-table-column>
            </el-table>
            <div class="footer-layout">
                <div>
                    <span class="label">收货地址和联系人：</span>
                    <span v-show="ignore">{{ detailData.address }}</span>
                    <el-input
                        v-show="!ignore"
                        type="textarea"
                        :autosize="{ minRows: 1 }"
                        placeholder="请输入内容"
                        v-model="detailData.address"
                    >
                    </el-input>
                </div>
                <div>
                    <span class="label">收货质量：</span>
                    <span v-show="ignore">{{ detailData.quality }}</span>
                    <el-input
                        v-show="!ignore"
                        type="textarea"
                        :autosize="{ minRows: 1 }"
                        placeholder="请输入内容"
                        v-model="detailData.quality"
                    >
                    </el-input>
                </div>

                <div :class="ignore ? 'ignore' : ''">
                    <span class="label">备注：</span
                    ><span class="value">
                        {{ detailData.remark }}
                    </span>
                </div>
            </div>
        </div>
    </vue-easy-print>
</template>

<script>
import vueEasyPrint from "vue-easy-print";
export default {
    components: {
        vueEasyPrint,
    },
    data() {
        return {
            ignore: false,
        };
    },

    props: ["detailData"],
    methods: {
        printPage() {
            this.ignore = true;
            setTimeout(() => {
                this.ignore = false;
                this.$refs.easyPrint.print();
            }, 200);
        },
    },
};
</script>

<style scoped lang="scss">
.ignore {
    opacity: 0;
}
.detail-layout {
    .print-btn {
        float: right;
    }
    b {
        font-size: 20px;
        font-weight: bold;
        color: #333;
    }
    .header-layout {
        text-align: center;
    }
    .footer-layout {
        & > div {
            margin-top: 10px;

            align-items: center;
            display: flex;
        }
        .label {
            font-size: 14px;
            font-weight: bold;
            // margin-top: 10px;
            color: #555;
        }
        .value {
            font-size: 14px;
            font-weight: 500;
        }
    }
    .content-layout {
        display: flex;
        justify-content: space-around;
        margin-bottom: 10px;
        .label {
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }
        .value {
            font-size: 14px;
            font-weight: 500;
        }
    }
}
</style>
