<template>
    <div>
        <el-card shadow="hover">
            <!-- 高级查询 -->
            <el-form :inline="true" size="small">
                <el-form-item>
                    <el-input
                        v-model="query.period_id"
                        oninput="value=value.replace(/[^0-9]/g,'')"
                        placeholder="请输入期数"
                        @blur="change"
                        @keyup.enter.native="search"
                        ref="ele"
                    />
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="query.title"
                        placeholder="请输入商品名称"
                        @keyup.enter.native="search"
                    />
                </el-form-item>

                <el-form-item>
                    <el-select
                        v-model="query.payee_merchant"
                        clearable
                        size="small"
                        placeholder="请选择收款公司"
                        class="filter-item"
                    >
                        <el-option
                            v-for="(item, index) in companyOptions"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-input
                        v-model="query.bar_code"
                        placeholder="请输入条码"
                        @keyup.enter.native="search"
                    />
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="query.short_code"
                        placeholder="请输入简码"
                        @keyup.enter.native="search"
                    />
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="query.period_type"
                        clearable
                        size="small"
                        placeholder="请选择销售频道"
                        class="filter-item"
                    >
                        <el-option
                            v-for="(item, index) in channelOptions"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-select
                        v-model="query.import_type"
                        clearable
                        size="small"
                        placeholder="请选择采购类型"
                        class="filter-item"
                    >
                        <el-option
                            v-for="(item, index) in entranceOptions"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        class="w-mini"
                        v-model="query.buyer_id"
                        filterable
                        placeholder="采购人"
                        clearable
                    >
                        <el-option
                            v-for="item in buyerOptions"
                            :key="item.id"
                            :label="item.realname"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="query.sort"
                        clearable
                        size="small"
                        placeholder="请选择排序"
                        class="filter-item"
                    >
                        <el-option
                            v-for="(item, index) in sortList"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="query.operation_review_name"
                        placeholder="审核人"
                        @keyup.enter.native="search"
                    />
                </el-form-item>

                <el-form-item>
                    <el-input
                        v-model="query.supplier"
                        clearable
                        @keyup.enter.native="search"
                        placeholder="请输入供应商"
                    />
                </el-form-item>
                <!-- <el-form-item>
                    <el-input
                        v-model="query.winery_name"
                        clearable
                        @keyup.enter.native="search"
                        placeholder="请输入酒庄"
                    />
                </el-form-item> -->
                <el-form-item prop="$chateau_id">
                    <el-select
                        v-model="query.$chateau_id"
                        filterable
                        clearable
                        remote
                        reserve-keyword
                        placeholder="请输入酒庄"
                        :remote-method="getWineryList"
                        :loading="loading"
                        @change="handleWineryChange"
                        @clear="query.winery_name = ''"
                    >
                        <el-option
                            v-for="item in WineryOptions"
                            :key="item.id"
                            :label="item.winery_name_cn + item.winery_name_en"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-date-picker
                    v-model="shipment_time"
                    size="mini"
                    @change="shipment_timeChange"
                    type="datetimerange"
                    range-separator="-"
                    class="m-r-10"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="发货时间-开始日期"
                    end-placeholder="发货时间-结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                >
                </el-date-picker>
                <el-date-picker
                    v-model="start_time"
                    size="mini"
                    @change="start_timeChange"
                    type="datetimerange"
                    range-separator="-"
                    class="m-r-10"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="开售时间-开始日期"
                    end-placeholder="开售时间-结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                >
                </el-date-picker>
                <el-date-picker
                    v-model="end_time"
                    size="mini"
                    @change="end_timeChange"
                    type="datetimerange"
                    range-separator="-"
                    class="m-r-10"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="下架时间-开始日期"
                    end-placeholder="下架时间-结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                >
                </el-date-picker>
                <el-form-item>
                    <el-button type="warning" size="mini" @click="search"
                        >查询</el-button
                    >
                    <el-button
                        type="primary"
                        size="mini"
                        @click="purchaseListSalesStatistics"
                        >销量统计</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover">
            <el-table
                stripe
                size="mini"
                :data="DataList"
                border
                cell-class-name="flo"
                fit
                highlight-current-row
                style="width: 100%"
            >
                <el-table-column type="expand">
                    <template slot-scope="row">
                        <el-table
                            size="mini"
                            border
                            :data="row.row.package"
                            style="width: 100%"
                        >
                            <el-table-column
                                type="expand"
                                v-if="row.row.package.length"
                            >
                                <template slot-scope="prows">
                                    <el-table
                                        size="mini"
                                        border
                                        :data="prows.row.products"
                                    >
                                        <el-table-column
                                            label="产品ID"
                                            width="100"
                                            align="center"
                                            prop="product_id"
                                        />
                                        <el-table-column
                                            label="产品名称"
                                            align="center"
                                            min-width="140"
                                            prop="product_name"
                                        />
                                        <el-table-column
                                            label="简码"
                                            width="120"
                                            align="center"
                                            prop="short_code"
                                        />
                                        <el-table-column
                                            label="条码"
                                            width="120"
                                            align="center"
                                            prop="bar_code"
                                        />
                                        <el-table-column
                                            label="酒庄"
                                            width="120"
                                            align="center"
                                            prop="winery_name"
                                        />
                                        <el-table-column
                                            label="京东EMG码"
                                            width="140"
                                            align="center"
                                            prop="jd_eclp_code"
                                        />
                                        <el-table-column
                                            label="参考成本"
                                            width="160"
                                            prop="cost_price"
                                            align="center"
                                        >
                                            <template slot-scope="c_row">
                                                <el-input-number
                                                    size="mini"
                                                    :precision="2"
                                                    disabled
                                                    :min="0"
                                                    v-model="
                                                        c_row.row.cost_price
                                                    "
                                                    @change="
                                                        cost_priceChange(
                                                            $event,
                                                            c_row.row,
                                                            row.row
                                                        )
                                                    "
                                                ></el-input-number>
                                            </template>
                                        </el-table-column>
                                        <el-table-column
                                            label="已售/已退款/发货后退款(瓶)/售后中瓶数"
                                            width="250"
                                            prop="number"
                                            align="center"
                                        />
                                        <el-table-column
                                            label="汇总"
                                            width="100"
                                            align="center"
                                            prop="total_number"
                                        />
                                        <el-table-column
                                            label="销售额"
                                            width="100"
                                            align="center"
                                            prop="sales_amount"
                                        />
                                        <el-table-column
                                            label="下单人数"
                                            width="100"
                                            align="center"
                                            prop="people_number"
                                        />
                                        <el-table-column
                                            label="整箱成本"
                                            width="90"
                                            prop="cost_container"
                                            align="center"
                                        />
                                        <el-table-column
                                            label="容量"
                                            prop="capacity"
                                            align="center"
                                            width="80"
                                        />
                                        <el-table-column
                                            label="库存"
                                            width="80"
                                            align="center"
                                        >
                                            <template slot-scope="rows">
                                                <el-button
                                                    size="mini"
                                                    @click="showStock(rows.row)"
                                                    type="success"
                                                    >查询</el-button
                                                >
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="套餐ID"
                                align="center"
                                prop="package_id"
                                width="200"
                            />
                            <el-table-column
                                label="套餐名称"
                                align="center"
                                prop="package_name"
                                width="200"
                                show-overflow-tooltip
                            />
                            <el-table-column
                                label="售价"
                                align="center"
                                prop="price"
                                width="200"
                            />
                            <el-table-column
                                label="市场价"
                                align="center"
                                width="200"
                                prop="market_price"
                            />
                            <el-table-column
                                label="已售份数"
                                width="200"
                                align="center"
                                prop="inventory"
                            />
                        </el-table>
                    </template>
                </el-table-column>
                <el-table-column
                    label="ID"
                    align="center"
                    prop="period_id"
                    width="80"
                >
                    <template slot-scope="scope">
                        <div>
                            {{ scope.row.period_id }}
                        </div>
                        <el-tag
                            v-if="scope.row.is_deposit_period"
                            type="danger"
                            size="mini"
                            effect="dark"
                            >订金</el-tag
                        >
                    </template>
                </el-table-column>
                <el-table-column
                    label="频道"
                    align="center"
                    prop="period_type"
                    width="70"
                >
                    <template slot-scope="row">
                        {{ row.row.period_type | period_typeFormat }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="商品名称"
                    align="center"
                    prop="title"
                    min-width="180"
                >
                    <template slot-scope="row">
                        <span
                            @click="$viewPcGoods(row.row.period_id)"
                            class="link-span"
                        >
                            {{ row.row.title }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="收款公司"
                    align="center"
                    prop="payee_merchant_name"
                    min-width="100"
                >
                    <!-- <template slot-scope="row">
                        <span>
                            {{ companyText[row.row.payee_merchant] }}
                        </span>
                    </template> -->
                </el-table-column>
                <el-table-column
                    label="是否代发"
                    align="center"
                    prop="uid"
                    width="70"
                >
                    <template slot-scope="row">
                        {{ row.row.is_supplier_delivery ? "是" : "否" }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="时间（售卖、发货）"
                    align="center"
                    prop="title"
                    width="220"
                >
                    <template slot-scope="row">
                        <div>上架时间：{{ row.row.onsale_time }}</div>
                        <div>开售时间：{{ row.row.start_time }}</div>
                        <div>结束时间：{{ row.row.end_time }}</div>
                        <div>发货时间：{{ row.row.shipment_time }}</div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="相关负责人"
                    align="center"
                    prop="title"
                    width="140"
                >
                    <template slot-scope="row">
                        <div>上架：{{ row.row.operation_name }}</div>
                        <div>采购：{{ row.row.buyer_name }}</div>
                        <div>文案：{{ row.row.creator_name }}</div>
                        <div>运营：{{ row.row.operation_review_name }}</div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="采购类型"
                    align="center"
                    prop="title"
                    width="110"
                >
                    <template slot-scope="row">
                        <el-select
                            @change="changeType(row.row)"
                            v-model="row.row.import_type"
                            size="small"
                            placeholder="进口类型"
                            class="filter-item"
                        >
                            <el-option
                                v-for="(item, index) in entranceOptions"
                                :key="index"
                                :disabled="
                                    (item.value == 2 &&
                                        row.row.period_type != 2) ||
                                    (item.value != 2 &&
                                        row.row.period_type == 2)
                                "
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column
                    label="供应商"
                    align="center"
                    prop="title"
                    width="170"
                >
                    <template slot-scope="row">
                        <div>
                            {{ row.row.supplier ? row.row.supplier : "-" }}
                        </div>
                        <el-button
                            size="mini"
                            @click="editSupplier(row.row)"
                            type="warning"
                        >
                            编辑
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="80">
                    <template slot-scope="scope">
                        <el-button
                            size="mini"
                            @click="getRemarkList(scope.row)"
                            type="text"
                        >
                            查看备注
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 分页 -->
        <div class="page-center">
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="pageSize"
                :current-page="currentPage"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            :visible.sync="editSupplierDialogStatus"
            title="修改供应商"
            :close-on-click-modal="false"
            width="300px"
            center
            :before-close="editSupplierHandleClose"
        >
            <el-select
                v-model="supplierData"
                filterable
                remote
                reserve-keyword
                placeholder="请输入供应商"
                :remote-method="remoteMethod"
                value-key="id"
                :loading="loading"
            >
                <el-option
                    v-for="(item, index) in supplierOpration"
                    :key="index"
                    :label="item.supplier_name"
                    :value="item"
                >
                </el-option>
            </el-select>
            <div style="margin-top: 10px">
                <el-button type="primary" @click="saveChangeSupplier"
                    >修改</el-button
                >
                <el-button @click="editSupplierHandleClose">取消</el-button>
            </div>
        </el-dialog>
        <el-dialog
            :visible.sync="getRemarkDialogStatus"
            title="备注信息"
            width="50%"
            :close-on-click-modal="false"
            center
        >
            <remarkList v-if="getRemarkDialogStatus" :rows="rows"></remarkList>
        </el-dialog>
        <el-dialog
            :visible.sync="getStockDialogStatus"
            title="库存信息"
            :close-on-click-modal="false"
            width="50%"
            center
        >
            <stock v-if="getStockDialogStatus" :stockList="stockList"></stock>
        </el-dialog>
        <el-dialog
            :visible.sync="salesStatisticsVisible"
            title="销量统计"
            :close-on-click-modal="false"
            width="70%"
            center
        >
            <el-row type="flex" justify="end" style="margin-bottom: 10px">
                <el-button type="primary" @click="onExport">导出</el-button>
            </el-row>
            <SalesStatisticsTable :info="salesStatisticsInfo" />
        </el-dialog>
    </div>
</template>

<script>
import remarkList from "./common/remarkList.vue";
import stock from "./common/stock.vue";
import SalesStatisticsTable from "./common/salesStatisticsTable.vue";
export default {
    components: {
        remarkList,
        stock,
        SalesStatisticsTable,
    },
    filters: {
        period_typeFormat(val) {
            switch (val) {
                case 0:
                    return "闪购";
                case 1:
                    return "秒发";
                case 2:
                    return "跨境";
                case 3:
                    return "尾货";
                case 4:
                    return "兔头商品";
                case 5:
                    return "兔头优惠券";
                default:
                    return "未知";
            }
        },
    },
    data() {
        return {
            stockList: [],
            getStockDialogStatus: false,
            rows: {},
            getRemarkDialogStatus: false,
            supplierData: {},
            companyText: {
                1: "重庆云酒佰酿电子商务有限公司 ",
                2: "佰酿云酒（重庆）科技有限公司",
            },
            companyOptions: [
                {
                    value: 1,
                    label: "重庆云酒佰酿电子商务有限公司 ",
                },
                {
                    value: 2,
                    label: "佰酿云酒（重庆）科技有限公司",
                },
                {
                    value: 5,
                    label: "渝中区微醺酒业商行",
                },
                {
                    value: 10,
                    label: "海南一花一世界科技有限公司",
                },
            ],
            sortList: [
                {
                    label: "期数",
                    value: "period_id",
                },
                {
                    label: "开售时间",
                    value: "start_time",
                },
                {
                    label: "结束时间",
                    value: "end_time",
                },
                {
                    label: "发货时间",
                    value: "shipment_time",
                },
                { label: "上架时间", value: "onsale_time" },
            ],
            entranceOptions: [
                {
                    label: "自进口",
                    value: 0,
                },
                {
                    label: "地采",
                    value: 1,
                },
                { label: "跨境", value: 2 },
            ],
            channelOptions: [
                {
                    label: "闪购",
                    value: 0,
                },
                {
                    label: "秒发",
                    value: 1,
                },
                {
                    label: "跨境",
                    value: 2,
                },
                {
                    label: "尾货",
                    value: 3,
                },
            ],
            rowKeys: [],
            currentPage: 1, // 当前页
            loading: false,
            pageSize: 10, // 每页条数
            total: 0, // 总条数
            supplierOpration: [],
            end_time: [],
            start_time: [],
            buyerOptions: [],
            shipment_time: [],
            query: {
                title: "",
                buyer_id: "",
                import_type: "",
                period_type: "",
                start_time_s: "",
                operation_review_name: "",
                start_time_e: "",
                short_code: "",
                sort: "",
                bar_code: "",
                end_time_s: "",
                end_time_e: "",
                supplier: "",
                winery_name: "",
                $chateau_id: "",
                shipment_time_e: "",
                shipment_time_s: "",
                period_id: "",
                payee_merchant: "",
            },
            DataList: [],
            rowSupplier: {},
            editSupplierDialogStatus: false,

            WineryOptions: [], //酒庄列表
            salesStatisticsVisible: false,
            salesStatisticsInfo: {}, //销售统计数据
        };
    },
    mounted() {
        this.getPurchase();
        this.getPurchaseList();
    },

    methods: {
        change(e) {
            this.query.period_id = e.target.value;
        },
        start_timeChange(val) {
            if (val) {
                this.query.start_time_s = val[0];
                this.query.start_time_e = val[1];
            } else {
                this.query.start_time_s = "";
                this.query.start_time_e = "";
            }
        },
        end_timeChange(val) {
            if (val) {
                this.query.end_time_s = val[0];
                this.query.end_time_e = val[1];
            } else {
                this.query.end_time_s = "";
                this.query.end_time_e = "";
            }
        },

        shipment_timeChange(val) {
            if (val) {
                this.query.shipment_time_s = val[0];
                this.query.shipment_time_e = val[1];
            } else {
                this.query.shipment_time_s = "";
                this.query.shipment_time_e = "";
            }
        },
        viewGoods(item) {
            window.open(
                this.$BASE.PCDomain +
                    "/pages/goods-detail/goods-detail?id=" +
                    (item.encrypt_id || item.period_id)
            );
        },
        async showStock(row) {
            const data = {
                bar_code: row.bar_code,
                short_code: row.short_code,
                product_name: row.product_name,
                jd_eclp_code: row.jd_eclp_code ? row.jd_eclp_code : "",
            };
            const res = await this.$request.purchase.getStockList(data);
            if (res.data.error_code == 0) {
                this.stockList = res.data.data;
                this.getStockDialogStatus = true;
            }
        },
        async saveChangeSupplier() {
            const data = {
                period: this.rowSupplier.period_id,
                periods_type: this.rowSupplier.period_type,
                import_type: this.rowSupplier.import_type,
                supplier_id: this.supplierData.id,
                supplier: this.supplierData.supplier_name,
            };
            const res = await this.$request.article.updateUncUsed(data);
            if (res.data.error_code == 0) {
                this.editSupplierHandleClose();
                this.$message.success("操作成功");
            }
        },
        remoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.article
                    .supplierList({
                        page: 1,
                        limit: 20,
                        keyword: query,
                    })
                    .then((res) => {
                        this.loading = false;
                        this.supplierOpration = res.data.data.list;
                    });
            } else {
                this.supplierOpration = [];
            }
        },
        editSupplierHandleClose() {
            this.editSupplierDialogStatus = false;
            this.getPurchaseList();
        },
        editSupplier(row) {
            this.supplierData = {};
            this.rowSupplier = row;
            this.editSupplierDialogStatus = true;
        },
        async cost_priceChange(value, row, mrows) {
            const period = mrows.period_id;
            const product_id = row.product_id;
            const costprice = value;
            const data = {
                period,
                product_id,
                costprice,
            };
            const res =
                await this.$request.purchase.updatePeriodsProductInventory(
                    data
                );
            if (res.data.error_code === 0) {
                this.$message.success("修改成功");
                // this.getPurchaseList();
            }
        },

        getPurchase() {
            this.$request.article
                .purchaseList({
                    type: 2,
                })
                .then((res) => {
                    this.buyerOptions = res.data.data.list;
                });
        },
        async changeType(row) {
            // 修改状态
            console.log(row);
            const data = {
                period: row.period_id,
                periods_type: row.period_type,
                import_type: row.import_type,
            };
            const res = await this.$request.article.updateUncUsed(data);
            if (res.data.error_code == 0) {
                this.$message.success("操作成功");
            }
            this.getPurchaseList();
        },
        // 获取数据
        async getPurchaseList() {
            const data = {
                page: this.currentPage,
                limit: this.pageSize,
                ...this.query,
            };
            console.log(data.period_id);
            const res = await this.$request.purchase.getPurchaseList(data);
            if (res.data.error_code === 0) {
                this.DataList = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        async purchaseListSalesStatistics() {
            const { short_code, winery_name } = this.query;
            if (short_code || winery_name) {
                const data = {
                    short_code,
                    winery_name,
                };
                this.$request.purchase
                    .purchaseListSalesStatistics(data)
                    .then((res) => {
                        console.log(res);
                        this.salesStatisticsInfo = res?.data?.data || {};
                        this.salesStatisticsVisible = true;
                    });
            } else {
                this.$message.error("请输入简码或酒庄后查看销量统计!");
            }
        },
        onExport() {
            const { short_code, winery_name } = this.query;
            this.$request.purchase
                .exportPurchaseSaleStats({
                    short_code,
                    winery_name,
                })
                .then((res) => {
                    const { error_code, error_msg } = res.data;
                    if (error_code == 0) {
                        this.$message.success(error_msg);
                    }
                });
        },
        //酒庄
        getWineryList(keyword) {
            if (keyword) {
                // if (this.query.$chateau_id) {
                //     this.query.$chateau_id = parseInt(this.query.$chateau_id);
                // }
                this.$request.product
                    .getWineryList({
                        keyword,
                        page: 1,
                        limit: 10,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.WineryOptions = res.data.data.list;
                        }
                    });
            }
        },
        handleWineryChange(id) {
            const info = this.WineryOptions.find((item) => item.id === id);
            if (info) {
                const { winery_name_cn, winery_name_en } = info;
                // this.query.winery_name = winery_name_cn + winery_name_en;
                this.query.winery_name = winery_name_cn;
            }
        },

        search() {
            this.currentPage = 1;
            this.getPurchaseList();
        },
        getRemarkList(row) {
            this.rows = row;
            this.getRemarkDialogStatus = true;
        },
        // 改变每页条数
        handleSizeChange(val) {
            this.currentPage = 1;
            this.pageSize = val;
            this.getPurchaseList();
        },
        // 改变当前页
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getPurchaseList();
        },
    },
};
</script>

<style lang="scss" scoped>
.page-center {
    text-align: center;
}
.link-span {
    cursor: pointer;
}
</style>
