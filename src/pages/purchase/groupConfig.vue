<template>
    <div>
        <!-- <el-card shadow="hover">
            <el-input
                v-model="query.realname"
                class="w-mini m-r-10"
                size="mini"
                @keyup.enter.native="search"
                placeholder="请输入人员名称"
            />
            <el-select
                class="m-r-10 w-mini"
                v-model="query.group_attr"
                size="mini"
                clearable
                placeholder="请选择分组"
            >
                <el-option
                    v-for="(item, index) in groupList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <el-button type="warning" size="mini" @click="search"
                >查询</el-button
            >
        </el-card> -->
        <el-card shadow="hover">
            <el-table stripe border size="mini" :data="tableData" fit>
                <el-table-column label="采购组" align="center">
                    <template slot-scope="row">
                        <!-- <el-select
                            v-if="editSaleId === row.row.id"
                            v-model="row.row.label"
                            size="small"
                            placeholder="请选择销售频道"
                            class="filter-item"
                        >
                            <el-option
                                v-for="(item, index) in groupList"
                                :key="index"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select> -->
                        <div>
                            {{ row.row.label | groupNameFormat }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="人员"
                    align="center"
                    show-overflow-tooltip
                    prop="realname"
                />
                <el-table-column
                    label="展示名"
                    align="center"
                    show-overflow-tooltip
                    prop="display_name"
                />
                <el-table-column
                    label="介绍"
                    align="center"
                    show-overflow-tooltip
                    prop="introduction"
                />
                <el-table-column label="操作" align="center">
                    <template slot-scope="row">
                       
                        <el-button
                            type="text"
                            :disabled="editSaleId ? true : false"
                            size="mini"
                            @click="editConfig(row.row)"
                            >编辑</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <!-- <div class="page-center">
                <el-pagination
                    style="margin-top: 10px"
                    :total="total"
                    layout="total, sizes, prev, pager, next, jumper"
                    :page-size="pageSize"
                    :current-page="currentPage"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div> -->
        </el-card>
        <!-- 编辑弹窗 -->
        <el-dialog :visible.sync="editDialogVisible" title="编辑人员信息" width="500px" @close="resetEditForm">
            <el-form  size="mini" :model="editForm" label-width="90px">
                <el-form-item label="真实姓名" prop="">
                    <el-input v-model="editForm.realname" disabled></el-input>
                </el-form-item>
                <el-form-item label="展示名" prop="display_name">
                    <el-input v-model="editForm.display_name"></el-input>
                </el-form-item>
                <el-form-item label="分组" prop="label">
                    <el-select v-model="editForm.label" placeholder="请选择分组">
                        <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="头像" >
                    <vos-oss
                    v-if="editDialogVisible"
                            ref="vosOssRef1"
                            list-type="picture-card"
                            :showFileList="true"
                            :dir="dir"
                            :file-list="fileList"
                            :limit="1"
                            
                        >
                            <i class="el-icon-plus" />
                        </vos-oss>
                        <span style="font-size: 12px; color: #999;">请上传正方形图片</span>
                </el-form-item>
                <el-form-item label="介绍" prop="introduction">
                    <el-input v-model="editForm.introduction" type="textarea" :rows="3" placeholder="请输入介绍"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="footer-dialog">
                <el-button @click="editDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleEditSave">保存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss,
    },
    filters: {
        groupNameFormat(val) {
            switch (val) {
                case "0":
                    return "A组";
                case "1":
                    return "B组";
                case "2":
                    return "C组";
                case "3":
                    return "D组";
                case "4":
                    return "E组";
                case "5":
                    return "F组";
                case "6":
                    return "L组";
                default:
                    return "-";
            }
        },
    },
    data() {
        return {
            dir: "vinehoo/goods-images/",
            fileList:[],
            total: 0,
            pageSize: 10,
            currentPage: 1,
            query: {},
            groupList: [
                {
                    label: "A组",
                    value: "0",
                },
                {
                    label: "B组",
                    value: "1",
                },
                {
                    label: "C组",
                    value: "2",
                },
                {
                    label: "D组",
                    value: "3",
                },
                {
                    label: "E组",
                    value: "4",
                },
                {
                    label: "F组",
                    value: "5",
                },
                {
                    label: "L组",
                    value: "6",
                },
            ],
            tableData: [],
            editSaleId: false,
            editDialogVisible: false,
            editForm: {
                id: '',
                realname: '',
                display_name: '',
                label: '',
                image: '',
                introduction: ''
            },
            rules: {
              
            display_name: [
                {
                    required: true,
                    message: "请输入展示名",
                },
            ],
            
            introduction: [
                {
                    required: true,
                    message: "请输入介绍",
                },
            ],
            label: [
                {
                    required: true,
                    message: "请选择分组",
                    trigger: "change",
                },
            ],
        },
        };
    },
    mounted() {
        this.saleGroupConfigList();
    },
    methods: {
        editConfig(row) {
            this.editForm = {
                id: row.id,
                realname: row.realname,
                display_name: row.display_name || '',
                label: row.label,
                image: row.image || '',
                introduction: row.introduction || ''
            };
            console.log();
            if(row.image){
                this.fileList = row.image.split(',');
            }
           
            console.log('ddddd---------', this.fileList);
            
            this.editDialogVisible = true;
        },
        handleEditSave() {
            const data = {
                id: this.editForm.id,
                label: this.editForm.label,
                image: this.fileList.join(','),
                introduction: this.editForm.introduction,
                display_name: this.editForm.display_name
            };
            this.$request.purchase.updateAdminLabel(data).then(res => {
                if (res.data.error_code === 0) {
                    this.editDialogVisible = false;
                    this.saleGroupConfigList();
                    this.$message.success('操作成功');
                }
            });
        },
        resetEditForm() {
            this.editForm = {
                id: '',
                realname: '',
                display_name: '',
                label: '',
                image: '',
                introduction: ''
            };
            this.fileList=[];
        },
        async saveConfig(row) {
            const data = {
                id: this.editSaleId,
                label: row.label,
            };

            const res = await this.$request.purchase.updateAdminLabel(data);
            if (res.data.error_code === 0) {
                this.editSaleId = false;
                this.saleGroupConfigList();
                this.$message.success("操作成功");
            }
        },
        async saleGroupConfigList() {
            const data = {
                type: 2,
            };
            const res = await this.$request.purchase.saleGroupConfigList(data);
            if (res.data.error_code === 0) {
                this.tableData = res.data.data.list;
            }
        },
        // search() {
        //     this.currentPage = 1;
        //     this.saleGroupConfigList();
        // },

        // // 改变每页条数
        // handleSizeChange(val) {
        //     this.currentPage = 1;
        //     this.pageSize = val;
        //     this.saleGroupConfigList();
        // },
        // // 改变当前页
        // handleCurrentChange(val) {
        //     this.currentPage = val;
        //     this.saleGroupConfigList();
        // },
    },
};
</script>

<style lang="scss" scoped>
.page-center {
    text-align: center;
}
.footer-dialog {
    display: flex;
    margin-top: 10px;
    justify-content: center;
}
</style>
