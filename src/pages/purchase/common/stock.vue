<template>
    <div>
        <div style="display: flex; flex-direction: row-reverse; color: red; margin-bottom: 5px; font-size: 20px;">总数：{{totalNum}}</div>
        <el-card shadow="hover">
            <el-table
                stripe
                border
                size="mini"
                :data="stockList"
                fit
               
            >
                <el-table-column
                    label="产品名称"
                    align="center"
                    show-overflow-tooltip
                    prop="product_name"
                    min-width="170"
                />
                <el-table-column
                    label="仓库"
                    align="center"
                    prop="warehouse_name"
                    width="240"
                >
                </el-table-column>
                <el-table-column
                    label="数量"
                    align="center"
                    prop="number"
                    width="100"
                >
                </el-table-column>
            </el-table>
        </el-card>
    </div>
</template>

<script>
export default {
    props: ["stockList"],
    data() {
        return {
            totalNum:0
        };
    },
    methods: {},
    mounted(){
        
        this.totalNum=this.stockList.reduce((sum, obj) => sum + obj.number, 0);
    }
};
</script>

<style lang="scss" scoped>
.page-center {
    text-align: center;
}
.footer-dialog {
    display: flex;
    margin-top: 10px;
    justify-content: center;
}
</style>
