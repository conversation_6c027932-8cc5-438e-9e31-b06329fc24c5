<template>
    <div class="table">
        <el-card shadow="hover">
            <el-table
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                :data="info.list"
                height="500"
                :summary-method="getSummaries"
                show-summary
            >
                <el-table-column label="序号" type="index" width="50" />
                <el-table-column label="简码" prop="short_code" />
                <el-table-column label="酒庄" prop="winery_name" />
                <el-table-column label="期数" prop="period_id" />
                <el-table-column
                    label="品名"
                    prop="product_name"
                    min-width="200"
                />
                <el-table-column label="时间" min-width="200">
                    <template slot-scope="{ row }">
                        <div>上架：{{ row.onsale_time }}</div>
                        <div>下架：{{ row.end_time }}</div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="订单数"
                    prop="order_count"
                    min-width="60"
                />
                <el-table-column label="销售额">
                    <template slot-scope="{ row }">
                        ¥{{ row.sales_amount }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="已订货"
                    prop="order_goods_count"
                    min-width="60"
                />
                <el-table-column
                    label="已售瓶数"
                    prop="bottle_count"
                    min-width="60"
                />
                <el-table-column
                    label="下单人数"
                    prop="people_number"
                    min-width="60"
                />
            </el-table>
        </el-card>
    </div>
</template>

<script>
export default {
    props: {
        info: {
            type: Object,
            default: () => ({}),
        },
    },
    data: () => ({}),
    methods: {
        getSummaries(param) {
            const {
                total_orders,
                total_sales_amount,
                total_bottle_count,
                total_people_number,
                total_order_goods_count
            } = this.info;
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = "合计";
                    return;
                }

                if ([6, 7,8, 9, 10].includes(index)) {
                    if (index === 6) {
                        sums[index] = total_orders;
                    } else if (index === 7) {
                        sums[index] = `¥${total_sales_amount}`;
                    } else if (index === 8) {
                        sums[index] = total_order_goods_count;
                    } else if (index === 9) {
                        sums[index] = total_bottle_count;
                    } else if (index === 10) {
                        sums[index] = total_people_number;
                    }
                }
            });

            return sums;
        },
    },
};
</script>

<style lang="scss" scoped>
.table {
    /deep/ .el-table > .el-table__footer-wrapper > table > tbody > tr > td {
        text-align: center;
    }
    /deep/ .el-table td.el-table__cell {
        border-top: none;
    }
}
</style>
