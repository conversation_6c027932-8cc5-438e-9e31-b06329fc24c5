<template>
  <div>
    <!-- 高级查询 -->
    <div class="top-bar">
      <div>
        <el-input
          v-model="params.remark"
          class="w-normal m-r-10"
          clearable
          size="mini"
          @keyup.enter.native="search"
          placeholder="请输入内容关键词"
        ></el-input>
        <el-select
          v-model="choooseType"
          clearable
          size="small"
          @change="changeType"
          class="w-normal m-r-10"
          placeholder="请选择筛选方式"
        >
          <el-option
            v-for="(i, k) in typeList"
            :key="k"
            :label="i.name"
            :value="i.id"
          >
          </el-option>
        </el-select>
        <el-select
          v-model="params.operator"
          class="w-normal m-r-10"
          size="small"
          placeholder="请选择"
          filterable
          clearable
        >
          <el-option
            v-for="i in chooseList"
            :key="i.operator"
            :label="i.operator_name"
            :value="i.operator"
          >
          </el-option>
        </el-select>
        <el-button type="primary" size="mini" @click="search"
                    >查询</el-button
                >
      </div>
      <div>
        <el-button
        type="success"
        style="margin-bottom: 10px"
        size="mini"
        @click="addRemarkDialogStatus = true"
        >添加备注</el-button
      >
      </div>
      
    </div>

    <el-card shadow="hover">
      <el-table stripe border size="mini" :data="DataList" fit style="600px">
        <el-table-column
          label="内容"
          align="center"
          show-overflow-tooltip
          prop="remark"
          min-width="200"
        />
        <el-table-column
          label="备注人"
          align="center"
          prop="operator_name"
          width="120"
        >
        </el-table-column>
        <el-table-column
          label="备注时间"
          align="center"
          prop="created_time"
          width="160"
        >
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 分页 -->
    <div class="page-center">
      <el-pagination
        style="margin-top: 10px"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        :page-size="pageSize"
        :current-page="currentPage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      :visible.sync="addRemarkDialogStatus"
      title="添加备注"
      append-to-body
      :close-on-click-modal="false"
      width="30%"
      center
      :before-close="addRemarkHandleClose"
    >
      <el-input
        type="textarea"
        placeholder="请输入备注内容"
        v-model="remark"
        maxlength="120"
        rows="10"
        show-word-limit
      >
      </el-input>

      <div class="footer-dialog">
        <el-button type="primary" @click="addRemark">确定</el-button>
        <el-button @click="addRemarkHandleClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  components: {},
  props: ["rows"],
  data() {
    return {
      currentPage: 1, // 当前页
      pageSize: 50, // 每页条数
      total: 0, // 总条数
      DataList: [],
      remark: "",
      addRemarkDialogStatus: false,
      params: {
        remark: "",
        operator: "",
      },
      typeList:[{id:1,name:"按部门筛选"}, {id:2,name:"按备注人筛选"}],
      departmentList:[{operator:14,operator_name:"运营"}, {operator:15,operator_name:"采购执行"}],
      peopleList:[],
      chooseList:[],
      choooseType:"",
    };
  },
  mounted() {
    this.getremarkUserList();
    this.getPurchaseRemarkList();
  },
  methods: {
    async addRemark() {
      const data = {
        period: this.rows.period_id,
        periods_type: this.rows.period_type,
        remark: this.remark,
      };
      const res = await this.$request.article.createRemark(data);
      if (res.data.error_code == 0) {
        this.$message.success("添加成功");
        this.addRemarkHandleClose();
      }
    },
    // 获取数据
    async getPurchaseRemarkList() {
      var data = {
        page: this.currentPage,
        period: this.rows.period_id,
        limit: this.pageSize,
        remark: this.params.remark,
      };
      if(this.choooseType === 1) {
        data.department = this.params.operator;
      } else if(this.choooseType === 2) {
        data.operator = this.params.operator;
      } 
    
      const res = await this.$request.article.remarkList(data);
      if (res.data.error_code === 0) {
        this.DataList = res.data.data.list;
        this.total = res.data.data.total;
      }
    },
    async getremarkUserList() {
      const data = {period: this.rows.period_id,};
      const res = await this.$request.article.remarkUserList(data);
      if (res.data.error_code === 0) {
        this.peopleList = res.data.data.list;
      }
    },
    changeType(){
      this.params.operator="";
      if( this.choooseType===1) {
        this.chooseList = this.departmentList
      } else if(this.choooseType===2) {
        this.chooseList = this.peopleList;
      } else {
        this.chooseList = [];
      }
        
    },
    search() {
      this.currentPage = 1;
      this.getPurchaseRemarkList();
    },
    addRemarkHandleClose() {
      this.remark = "";
      this.addRemarkDialogStatus = false;
      this.getPurchaseRemarkList();
    },
    // 改变每页条数
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.getPurchaseRemarkList();
    },
    // 改变当前页
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getPurchaseRemarkList();
    },
  },
};
</script>

<style lang="scss" scoped>
.page-center {
  text-align: center;
}
.footer-dialog {
  display: flex;
  margin-top: 10px;
  justify-content: center;
}
.top-bar {
  display: flex;
  justify-content: space-between;
}
</style>
