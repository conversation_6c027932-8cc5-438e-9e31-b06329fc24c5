<template>
    <div>
        <el-card :body-style="{ padding: '5px' }" shadow="hover">
            <el-form :inline="true" size="mini">
                <el-form-item>
                    <el-date-picker
                        v-model="date"
                        :clearable="false"
                        size="mini"
                        type="month"
                        value-format="yyyy-MM"
                        placeholder="选择月"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button
                        size="mini"
                        @click="getStatistical"
                        type="warning"
                    >
                        查询
                    </el-button>
                    <el-button size="mini" @click="exportFile" type="danger">
                        导出
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <el-card
            :body-style="{ padding: '5px' }"
            shadow="hover"
            style="margin-top: 10px"
        >
            <el-table
                v-for="(data, index) in statisticalList"
                :key="index"
                style="margin-bottom: 30px"
                :data="data"
                border
                size="mini"
                :span-method="(params) => objectSpanMethod(params, data)"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column prop="group_attr" label="">
                    <template slot-scope="{ row }">
                        <div>{{ row.group_name }}</div>
                        <div>{{ row.group_leader }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="buyer" label="采销"> </el-table-column>
                <el-table-column prop="product_type" label="统计类型"> 
                    <template slot-scope="{ row }">
                       {{ row.product_type | toTypeText }}
                    </template>
                </el-table-column>
                <el-table-column prop="sale_target" label="销售目标">
                </el-table-column>
                <el-table-column prop="gross_profit_target" label="毛利率目标">
                    <template slot-scope="{ row }">
                        {{ row.gross_profit_target }}%
                    </template>
                </el-table-column>
                <el-table-column prop="total_amount" label="实际销量">
                </el-table-column>
                <el-table-column prop="gross_profit_margin" label="实际毛利率">
                    <template slot-scope="{ row }">
                        {{ row.gross_profit_margin }}%
                    </template>
                </el-table-column>
                <el-table-column prop="completion_rate" label="达标率">
                    <template slot-scope="{ row }">
                        {{ row.completion_rate }}%
                    </template>
                </el-table-column>
                <el-table-column
                    prop="profit_attainment_rate"
                    label="利润达标率"
                    width="100px"
                >
                    <template slot-scope="{ row }">
                        {{ row.profit_attainment_rate }}%
                    </template>
                </el-table-column>
                <el-table-column prop="place_collect_amount" label="地采">
                </el-table-column>
                <el-table-column prop="gross_profit_margin_place_collect" label="地采毛利率">
                    <template slot-scope="{ row }">
                        {{ row.gross_profit_margin_place_collect }}%
                    </template>
                 </el-table-column>
                <el-table-column prop="import_amount" label="自进口">
                </el-table-column>
                <el-table-column prop="gross_profit_margin_import" label="自进口毛利率"> 
                    <template slot-scope="{ row }">
                        {{ row.gross_profit_margin_import }}%
                    </template>
                </el-table-column>
                <el-table-column prop="cross_order_amount" label="跨境">
                </el-table-column>
                <el-table-column prop="gross_profit_margin_cross" label="跨境毛利率">
                    <template slot-scope="{ row }">
                        {{ row.gross_profit_margin_cross }}%
                    </template> </el-table-column>
                <el-table-column prop="second_order_amount" label="秒发">
                </el-table-column>
                <el-table-column prop="gross_profit_margin_second" label="秒发毛利率"> 
                    <template slot-scope="{ row }">
                        {{ row.gross_profit_margin_second }}%
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
    </div>
</template>
<script>
export default {
    data() {
        return {
            date: "",
            spanArr: [], //用于存放每一行记录的合并数,
            pos: 0, //用于记录当前行的位置
            statisticalList: [],
            // 分组 0-5 对应 A-E
            groupList: ["A", "B", "C", "D", "E", "F"],
        };
    },
    mounted() {
        const Y = new Date().getFullYear();
        const M = new Date().getMonth() + 1;
        this.date = Y + "-" + M;
        this.getStatistical();
    },
    filters: {
        toTypeText(input) {
            return (
                {
                    0: "所有",
                    1: "白酒类",
                    2: "食品类",
                    3: "酒类（非白酒）",
                }[input] || ""
            );
        },
    },
    methods: {
        exportFile() {
            window.open(
                ` https://callback.vinehoo.com/purchase-management/purchase/v3/sale/target/statistics?month=${this.date}&export=1`
            );
        },
        objectSpanMethod({ row, column, rowIndex, columnIndex }, data) {
            if (columnIndex === 0) {
                if (rowIndex === 0) {
                    return [data.length, 1];
                } else {
                    return [0, 0];
                }
            }
        },
        async getStatistical() {
            let data = {
                month: this.date,
            };
            const res = await this.$request.purchase.getStatistical(data);
            if (res.data.error_code == 0) {
                res.data.data.list.forEach((item) => {
                    item.group_attr = `${item.group_attr}`;
                });
                res.data.data.list.sort((a, b) => {
                    return a.group_attr - b.group_attr;
                });
                const list = res.data.data.list.reduce((prev, curr) => {
                    const [lastList] = prev.slice(-1);
                    if (lastList && lastList[0].group_attr == curr.group_attr) {
                        lastList.push(curr);
                        return prev;
                    } else {
                        return [...prev, [curr]];
                    }
                }, []);
                this.statisticalList = list;
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.search-layout {
    margin-bottom: 10px;
}
.card-title-b {
    font-size: 16px;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}
.table-layout {
    & > div {
        display: flex;

        .el-card {
            margin-right: 3%;
            margin-bottom: 10px;
            width: 47%;
        }
    }
}
</style>
