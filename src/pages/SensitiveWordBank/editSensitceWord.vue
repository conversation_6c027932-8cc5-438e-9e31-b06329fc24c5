<template>
    <div>
        <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            label-width="130px"
            class="demo-ruleForm"
        >
            <el-form-item label="极限词" prop="content">
                <el-input
                    placeholder="请输入极限词 "
                    v-model="ruleForm.content"
                    clearable
                ></el-input>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="close">取 消</el-button>
            <el-button type="primary" @click="submitForm('ruleForm')"
                >确 定</el-button
            >
        </span>
    </div>
</template>
<script>
export default {
    props: ["editData"],
    data() {
        return {
            ruleForm: {
                // id: "",
                // type: "",
                // level: "",
                // content: ""
            },
            rules: {
                type: [
                    {
                        required: true,
                        message: "请选择敏感业务类型",
                        trigger: "blur",
                    },
                ],
                level: [
                    {
                        required: true,
                        message: "请选择所属词库",
                        trigger: "blur",
                    },
                ],
                content: [
                    {
                        required: true,
                        message: "请输入极限词",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    mounted() {
        this.ruleForm = this.editData;
    },
    methods: {
        close() {
            this.$emit("close");
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    console.log(this.ruleForm);
                    const data = {
                        id: this.ruleForm.id,
                        type: this.ruleForm.type,
                        level: this.ruleForm.level,
                        content: this.ruleForm.content,
                    };
                    this.$request.mostwords.editWord(data).then((res) => {
                        if (res.data.error_code == 0) {
                            this.$message.success("编辑极限词成功");
                            this.close();
                        }
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.dialog-footer {
    display: flex;
    justify-content: center;
}
</style>
