<template>
    <div class="user-layout">
        <div class="user-form">
            <el-card>
                <el-input
                    class="w-normal m-r-10"
                    size="mini"
                    clearable
                    @keyup.enter.native="search"
                    v-model="form.content"
                    maxlength="16"
                    placeholder="极限词"
                ></el-input>

                <el-button @click="reset" size="mini">重置</el-button>

                <div class="action-btn">
                    <el-button type="warning" size="mini" @click="search"
                        >查询</el-button
                    >
                    <el-button
                        type="success"
                        size="mini"
                        @click="addDialogVisible = true"
                        >新增极限词</el-button
                    >
                    <el-button
                        :disabled="multipleSelection.length == 0"
                        type="danger"
                        size="mini"
                        @click="deleteWordMore"
                        >批量删除</el-button
                    >
                </div>
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    :row-class-name="tableRowClassName"
                    @selection-change="handleSelectionChange"
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column type="selection" fixed="left" width="55">
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="极限词内容"
                        prop="content"
                        min-width="220"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="created_time"
                        align="center"
                        label="添加时间"
                        min-width="170"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="admin_name"
                        align="center"
                        label="添加人"
                        width="120"
                    >
                    </el-table-column>

                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        width="160"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                @click="editWord(row.row)"
                                type="primary"
                                size="mini"
                                >编辑</el-button
                            >
                            <el-button
                                @click="deleteWord([row.row.id])"
                                size="mini"
                                type="danger"
                                >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>

        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="form.page"
                :page-size="form.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <div>
            <el-dialog
                title="编辑极限词"
                :visible.sync="editDialogVisible"
                width="40%"
                :before-close="handleClose"
            >
                <edit
                    @close="handleClose"
                    :editData="editData"
                    v-if="editDialogVisible"
                ></edit>
            </el-dialog>
        </div>

        <div>
            <el-dialog
                title="新增极限词"
                :visible.sync="addDialogVisible"
                width="40%"
                :before-close="handleClose"
            >
                <add @close="handleClose" v-if="addDialogVisible"></add>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import add from "./addSensitiveWord.vue";
import edit from "./editSensitceWord.vue";
export default {
    components: {
        add,
        edit,
    },

    data() {
        return {
            multipleSelection: [],
            editData: {},
            editDialogVisible: false,
            addDialogVisible: false,
            tableData: [],
            levelOptions: [
                { label: "红线词库", value: 1 },
                { label: "极限词库", value: 2 },
                { label: "危险词库", value: 3 },
            ],
            typeOptions: [
                { label: "全部", value: 1 },
                { label: "用户昵称", value: 2 },
                { label: "发布内容", value: 3 },
            ],
            form: {
                page: 1,
                limit: 10,
                content: "",
                type: 4,
            },
            total: 0,
        };
    },
    methods: {
        deleteWordMore() {
            const arr = [];
            this.multipleSelection.map((item) => {
                arr.push(item.id);
            });
            this.deleteWord(arr);
        },
        async deleteWord(id) {
            this.$confirm("此操作将不可逆, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(async () => {
                    const data = {
                        id,
                    };
                    const res = await this.$request.mostwords.deleteWord(data);
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功");
                        this.getWordList();
                    }
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        editWord(row) {
            this.editData = row;
            this.editDialogVisible = true;
        },
        handleClose() {
            this.addDialogVisible = false;
            this.editDialogVisible = false;
            this.form.page = 1;
            this.getWordList();
        },
        getWordList() {
            let data = {
                ...this.form,
            };
            this.$request.mostwords.getWordList(data).then((res) => {
                if (res.data.error_code == 0) {
                    console.log(res.data);
                    this.total = res.data.data.total;
                    this.tableData = res.data.data.list;
                }
            });
        },
        search() {
            this.form.page = 1;
            this.getWordList();
        },
        reset() {
            this.$confirm("您确定要重置所有筛选条件吗?", "重置操作", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.form.page = 1;
                this.form.content = "";
                this.getWordList();
            });
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        handleSizeChange(val) {
            this.form.page = 1;
            this.form.limit = val;
            this.getWordList();
            console.log(`每页 ${val} 条`);
        },
        tableRowClassName({ row }) {
            if (row.level === 1) {
                return "danger-row";
            } else if (row.level === 2) {
                return "success-row";
            }
            return "info-row";
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.form.page = val;
            this.getWordList();
        },
    },
    mounted() {
        this.getWordList();
    },
    filters: {
        statusFormat(val) {
            switch (val) {
                case 0:
                    return "未审核";
                case 1:
                    return "已通过";
                case 2:
                    return "驳回";
                default:
                    return "-";
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.user-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .level-list {
        display: flex;
        align-items: center;
        padding: 10px 0;
        ::v-deep .radio {
            margin-bottom: 0;
        }
        .name {
            margin-right: 20px;
        }
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .user-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }
            }
        }
    }
}
</style>
