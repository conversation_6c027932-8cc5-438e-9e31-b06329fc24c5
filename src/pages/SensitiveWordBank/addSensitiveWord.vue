<template>
    <div>
        <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            label-width="0"
            class="demo-ruleForm"
        >
            <el-form-item prop="content">
                <el-input
                    type="textarea"
                    rows="10"
                    placeholder="请输入极限词 （多个极限词使用逗号隔开）"
                    v-model="ruleForm.content"
                    clearable
                    ref="contentInput"
                ></el-input>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="close">取 消</el-button>
            <el-button type="primary" @click="submitForm('ruleForm')"
                >确 定</el-button
            >
        </span>
    </div>
</template>
<script>
export default {
    data() {
        return {
            ruleForm: {
                type: 4,
                level: 1,
                content: "",
            },
            rules: {},
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.$refs.contentInput.focus();
        });
    },
    methods: {
        close() {
            this.$emit("close");
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    // Replace Chinese commas with English commas
                    const processedContent = this.ruleForm.content.replace(/，/g, ',');
                    this.ruleForm.content = processedContent;
                    
                    this.$request.mostwords
                        .addWord(this.ruleForm)
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("新增极限词成功");
                                this.close();
                            }
                        });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.dialog-footer {
    display: flex;
    justify-content: center;
}
</style>
