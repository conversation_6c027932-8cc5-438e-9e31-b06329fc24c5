<template>
    <el-form>
        <el-form-item>
            <el-row type="flex" align="middle">
                <h4>原归属公司</h4>
                <el-select
                    style="margin-left: 10px"
                    v-model="fromCompanyId"
                    placeholder="请选择转出公司"
                    @change="onFromCompanyIdChange"
                >
                    <el-option
                        v-for="companyItem in companyList"
                        :key="companyItem.id"
                        :value="companyItem.id"
                        :label="companyItem.name"
                    />
                </el-select>
            </el-row>
        </el-form-item>
        <div>
            <el-form-item
                v-for="(item, index) in list"
                :key="index"
                :label="index + 1"
                label-width="50px"
            >
                <el-row type="flex">
                    <el-select
                        v-if="fromCompanyId"
                        style="margin-right: 10px"
                        v-model="item.from_fictitious_id"
                        placeholder="请选择转出公司仓库"
                    >
                        <el-option
                            v-for="warehouseItem in getWarehouseList(
                                fromCompanyId
                            )"
                            :key="warehouseItem.id"
                            :value="warehouseItem.id"
                            :label="warehouseItem.name"
                        />
                    </el-select>
                    <el-input
                        style="margin-right: 10px; width: 200px"
                        v-model="item.bar_code"
                        placeholder="请输入简码"
                    ></el-input>
                    <el-input
                        style="margin-right: 10px; width: 200px"
                        v-model="item.nums"
                        placeholder="请输入转出数量"
                    ></el-input>
                    <div>
                        <el-button type="primary" @click="add(index)"
                            >增加</el-button
                        >
                        <el-button
                            v-if="list.length > 1"
                            type="danger"
                            @click="del(index)"
                            >删除</el-button
                        >
                    </div>
                </el-row>
            </el-form-item>
        </div>
        <el-form-item>
            <el-row type="flex" align="middle">
                <h4>新归属公司</h4>
                <el-select
                    style="margin-left: 10px"
                    v-model="toCompanyId"
                    placeholder="请选择转入公司"
                    @change="onToCompanyIdChange"
                >
                    <el-option
                        v-for="companyItem in companyList"
                        :key="companyItem.id"
                        :value="companyItem.id"
                        :label="companyItem.name"
                    />
                </el-select>
            </el-row>
        </el-form-item>
        <div>
            <el-form-item
                v-for="(item, index) in list"
                :key="index"
                :label="index + 1"
                label-width="50px"
            >
                <el-row type="flex">
                    <el-select
                        v-if="toCompanyId"
                        style="margin-right: 10px"
                        v-model="item.to_fictitious_id"
                        placeholder="请选择转入公司仓库"
                    >
                        <el-option
                            v-for="warehouseItem in getWarehouseList(
                                toCompanyId
                            )"
                            :key="warehouseItem.id"
                            :value="warehouseItem.id"
                            :label="warehouseItem.name"
                        />
                    </el-select>
                    <el-input
                        style="margin-right: 10px; width: 200px"
                        v-model="item.bar_code"
                        placeholder="请输入简码"
                        disabled
                    ></el-input>
                    <el-input
                        style="margin-right: 10px; width: 200px"
                        v-model="item.nums"
                        placeholder="请输入转入数量"
                        disabled
                    ></el-input>
                </el-row>
            </el-form-item>
        </div>
        <el-row>
            <el-button type="primary" :disabled="disabled" @click="save"
                >确定</el-button
            >
        </el-row>
    </el-form>
</template>

<script>
export default {
    data: () => ({
        companyList: [],
        fromCompanyId: "",
        toCompanyId: "",
        list: [
            {
                from_company_id: "",
                from_fictitious_id: "",
                bar_code: "",
                nums: "",
                to_company_id: "",
                to_fictitious_id: "",
            },
        ],
    }),
    computed: {
        disabled() {
            return !(
                this.list.every(
                    ({
                        from_fictitious_id,
                        bar_code,
                        nums,
                        to_fictitious_id,
                    }) =>
                        from_fictitious_id &&
                        bar_code &&
                        nums &&
                        to_fictitious_id
                ) &&
                this.fromCompanyId &&
                this.toCompanyId
            );
        },
    },
    methods: {
        init() {
            this.$request.inventoryEdit.getCompanyList().then((res) => {
                if (res.data.error_code == 0) {
                    this.companyList = res?.data?.data?.list || [];
                }
            });
        },
        onFromCompanyIdChange() {
            this.list.forEach((item) => {
                item.from_fictitious_id = "";
            });
        },
        onToCompanyIdChange() {
            this.list.forEach((item) => {
                item.to_fictitious_id = "";
            });
        },
        getWarehouseList(companyId) {
            const findCompany = this.companyList.find(
                (item) => item.id === companyId
            );
            return findCompany && (findCompany.fictitious || []);
        },
        add(index) {
            const max = 20;
            if (this.list.length >= max) {
                this.$message.error(`一次最多修改${max}条`);
                return;
            }
            this.list.splice(index + 1, 0, {
                from_company_id: "",
                from_fictitious_id: "",
                bar_code: "",
                nums: "",
                to_company_id: "",
                to_fictitious_id: "",
            });
        },
        del(index) {
            this.list.splice(index, 1);
        },
        save() {
            const loading = this.$loading({
                lock: true,
                text: "修改中",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)",
            });
            this.list.forEach((item) => {
                item.from_company_id = this.fromCompanyId;
                item.to_company_id = this.toCompanyId;
            });
            this.$request.inventoryEdit
                .batchTransferInventory({ items: this.list })
                .then((res) => {
                    loading.close();
                    if (res.data.error_code == 0) {
                        const list = res.data.data;
                        const errorList = list.filter((item) => !item.status);
                        if (errorList.length) {
                            const alertStr = errorList
                                .map((item) => `<p>${item.error_msg}</p>`)
                                .join("");
                            this.$alert(alertStr, "提示", {
                                dangerouslyUseHTMLString: true,
                            });
                            const errorBarCodeList = errorList.map(
                                (item) => item.bar_code
                            );
                            this.list = this.list.filter((item) =>
                                errorBarCodeList.includes(item.bar_code)
                            );
                        } else {
                            this.$message.success("操作成功");
                            this.list = this.$options.data().list;
                        }
                    }
                });
        },
    },
    created() {
        this.init();
    },
};
</script>
