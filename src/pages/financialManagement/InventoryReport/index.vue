<template>
    <div>
        <el-card shadow="hover">
            <el-form :inline="true" size="mini">
              
                <el-form-item>
                    <el-input
                        v-model="query.short_code"
                        placeholder="简码"
                        clearable
                      
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="query.warehouse_code"
                        placeholder="仓库"
                        clearable
                        class="w-large"
                        filterable
                    >
                        <el-option
                            v-for="item in warehouse"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
               
                <el-form-item>
                    <el-button style="margin-right: 10px;" type="primary" @click="search">查询</el-button>
                    <el-dropdown @command="handleExportType">
                        <el-button type="primary">
                            导出<i class="el-icon-arrow-down el-icon--right"></i>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item :command="1">汇总</el-dropdown-item>
                            <el-dropdown-item :command="2">销售明细</el-dropdown-item>
                            <el-dropdown-item :command="3">采购明细</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover">
            <el-table
                ref="multipleTable"
                :data="table_data"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
               
                <!-- :span-method="handleSpanMethod" -->
                <el-table-column
                    prop="warehouse"
                    label="仓库"
                    width="200"
                ></el-table-column>
                <el-table-column prop="short_code" label="简码" width="100">
                </el-table-column>
                <el-table-column
                    prop="cn_goods_name"
                    label="中文名"
                    min-width="180"
                >
                </el-table-column>
                <el-table-column prop="capacity" label="规格" width="80">

                </el-table-column>
                 <el-table-column prop="unit" label="单位" width="100">
                    
                </el-table-column>
                 <el-table-column prop="purchase_num" label="采购数量" width="100">
                    
                </el-table-column>
                 <el-table-column prop="sale_num" label="销售数量" width="100">
                    
                </el-table-column>
                 <el-table-column prop="allocation_out_num" label="调拨出库" width="100">
                    
                </el-table-column>
                 <el-table-column prop="allocation_in_num" label="调拨入库" width="100">
                    
                </el-table-column>
                 <el-table-column prop="inventory_num" label="现库存" width="100">
                    
                </el-table-column>
               
            </el-table>
        </el-card>
        <div>
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
       
    </div>
</template>

<script>


export default {
   
    data() {
        return {
            
            query: {
                short_code: "",
                warehouse_code: "",
                page: 1,
                limit: 10
            },
            total: 0,
            table_data: [],
            warehouse:[],
        };
    },
   
    mounted() {
        this.initWarhouseOptions();
        this.getDataList();
    },
    methods: {
      
        getDataList() {
            
            this.$request.product.
                OneFlowerOneWorldKanban(this.query)
                .then(res => {
                    if (res.data.error_code == 0) {
                        
                        this.table_data = res.data.data.list;
                        this.total = res.data.data.total;
                    }
                });
         },
       
         initWarhouseOptions() {
            this.$request.article.warehouseList().then((res) => {
                if (res.data.error_code === 0) {
                    this.warehouse = res.data.data.map((item) => ({
                        value: item.erp_id,
                        label: item.fictitious_name,
                    }));
                }
            });
        },
        search() {
            this.query.page = 1;
            this.getDataList();
        },
       
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getDataList();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getDataList();
        },
        handleExportType(type) {
            const params = { ...this.query, type };
            this.$request.product
                .ExportOneFlowerOneWorldKanban(params)
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.$message.success("导出成功,请前往企业微信查看");
                    }
                });
        },
       
      
    }
};
</script>
