<template>
    <!-- style="display: flex;justify-content: left;" -->
    <div>
        <div style="display: flex; justify-content: left">
            <vos-oss
                ref="vos"
                list-type="text"
                :showFileList="false"
                :limit="1"
                :dir="dir"
                :file-list="filelist"
                :fileSize="10"
                filesType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                @on-success="handleSuccess(filelist)"
            >
                <el-button type="primary" size="default">上传附件</el-button>
            </vos-oss>
           
        </div>
    </div>
</template>

<script>
import VosOss from "vos-oss";
import { Loading } from "element-ui";

export default {
    components: {
        VosOss
    },
    data() {
        return {
            filelist: [],
            dir: "vinehoo/vos/commodities/shortcode",
            t: new Date().getTime(),
            detailInfo: [],
            timer: null,
            loadingInstance: null
        };
    },
    beforeDestroy() {
        clearInterval(this.timer);
    },
    methods: {
       
       
        handleSuccess(filelist) {
            console.log("成功", filelist);
            const last = filelist.length - 1;
            this.LogisticsSynchronousUp(filelist[last]);
        },
        async LogisticsSynchronousUp(file) {
            let data = {
                file_url: file
            };
            let res = await this.$request.product.shortCodeLastSaleDate(data);
           
            if (res.data.error_code == 0) {
                
                this.$Message.success("上传成功");
                // this.filelist = [];
                this.$refs.vos.handleviewFileList([]);
            } else {
                this.$refs.vos.handleviewFileList([]);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .el-upload-list {
    margin: 0;
    padding: 0;
    list-style: none;
    width: 300px;
}
.info {
    margin: 10px 0;
    div {
        margin: 5px 0;
    }
}
</style>
