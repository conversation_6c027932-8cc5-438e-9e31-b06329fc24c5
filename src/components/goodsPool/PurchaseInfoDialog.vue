<template>
    <el-dialog :visible="visible" title="采购信息" fullscreen @close="close">
        <el-form ref="formRef" :model="info" :rules="rules">
            <el-form-item label="频道" prop="periods_type">
                <el-radio-group
                    :value="info.periods_type"
                    @input="onPeriodsTypeInput"
                >
                    <el-radio
                        v-for="item in MGoodsTypeText"
                        :key="item.value"
                        :label="item.value"
                        >{{ item.label }}</el-radio
                    >
                </el-radio-group>
            </el-form-item>
            <template
                v-if="info.periods_type !== $options.data().info.periods_type"
            >
                <el-form-item label="进口类型" prop="import_type">
                    <el-radio-group
                        :value="info.import_type"
                        @input="onImportTypeInput"
                    >
                        <el-radio
                            v-for="item in currImportTypeText"
                            :key="item.value"
                            :label="item.value"
                            >{{ item.label }}</el-radio
                        >
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="供应商" prop="supplier_id">
                    <el-select
                        v-model="info.supplier_id"
                        filterable
                        remote
                        placeholder="请输入供应商"
                        :remote-method="supplierIdRemoteMethod"
                        @change="onSupplierIdChange"
                        :loading="supplierRemoteLoading"
                    >
                        <el-option
                            v-for="item in supplierOptions"
                            :key="item.id"
                            :label="item.supplier_name"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item
                    v-if="currPayeeCompanyText.length"
                    label="收款公司"
                    prop="payee_merchant_id"
                >
                    <el-radio-group
                        v-model="info.payee_merchant_id"
                        @change="onPayeeMechantIdChange"
                    >
                        <el-radio
                            v-for="item in currPayeeCompanyText"
                            :key="item.value"
                            :label="item.value"
                            >{{ item.label }}</el-radio
                        >
                    </el-radio-group>
                </el-form-item>
            </template>
            <el-form-item label="礼盒发货">
                <el-radio-group v-model="info.is_gift_box">
                    <el-radio
                        v-for="item in MIsGiftBoxText"
                        :key="item.value"
                        :label="item.value"
                        >{{ item.label }}</el-radio
                    >
                </el-radio-group>
            </el-form-item>
            <el-form-item label="是否代发">
                <el-radio-group v-model="info.is_supplier_delivery">
                    <el-radio
                        v-for="item in MIsSupplierDeliveryText"
                        :key="item.value"
                        :label="item.value"
                        >{{ item.label }}</el-radio
                    >
                </el-radio-group>
            </el-form-item>
            <el-form-item
                v-if="info.is_supplier_delivery === MIsSupplierDelivery.YES"
                label="发货地"
                prop="supplier_delivery_address"
            >
                <el-select
                    :value="
                        info.supplier_delivery_address
                            .split(',')
                            .filter(Boolean)
                    "
                    @change="info.supplier_delivery_address = $event.join()"
                    multiple
                    filterable
                >
                    <el-option
                        v-for="item in supplierDeliveryAddressList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="是否预售">
                <el-radio-group v-model="info.is_presell">
                    <el-radio
                        v-for="item in MIsPresellText"
                        :key="item.value"
                        :label="item.value"
                        >{{ item.label }}</el-radio
                    >
                </el-radio-group>
            </el-form-item>
            <el-form-item label="发货条件">
                <el-radio-group v-model="info.shipping_conditions">
                    <el-radio
                        v-for="item in MShippingConditionsText"
                        :key="item.value"
                        :label="item.value"
                        >{{ item.label }}</el-radio
                    >
                </el-radio-group>
            </el-form-item>
            <el-form-item label="存储条件">
                <el-radio-group v-model="info.storage_conditions">
                    <el-radio
                        v-for="item in MStorageConditionsText"
                        :key="item.value"
                        :label="item.value"
                        >{{ item.label }}</el-radio
                    >
                </el-radio-group>
            </el-form-item>
            <el-form-item label="发货时效">
                <el-radio-group v-model="info.delivery_time_limit">
                    <el-radio
                        v-for="item in MDeliveryTimeLimitText"
                        :key="item.value"
                        :label="item.value"
                        >{{ item.label }}</el-radio
                    >
                </el-radio-group>
            </el-form-item>
            <el-form-item label="发货仓库" prop="warehouse_id">
                <el-select
                    v-model="info.warehouse_id"
                    placeholder="发货仓库"
                    filterable
                    @change="onWarehouseIdChange"
                >
                    <el-option
                        v-for="item in warehouseOptions"
                        :key="item.id"
                        :label="item.fictitious_name"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <ProductRemoteSelect
                ref="productRemoteSelectRef"
                @add="onAddProduct"
            />
            <el-table :data="info.items" border class="mgt-10">
                <el-table-column
                    label="酒款名(英文+中文)"
                    align="center"
                    prop="en_product_name"
                >
                    <template slot-scope="scope">
                        <div>{{ scope.row.en_product_name }}</div>
                        <div>{{ scope.row.product_name }}</div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="简码"
                    align="center"
                    prop="short_code"
                ></el-table-column>
                <el-table-column
                    label="类型"
                    align="center"
                    prop="product_type_name"
                ></el-table-column>
                <el-table-column
                    label="供应商"
                    align="center"
                    prop="supplier"
                ></el-table-column>
                <el-table-column
                    label="国家"
                    align="center"
                    prop="country"
                ></el-table-column>
                <el-table-column
                    label="规格"
                    align="center"
                    prop="capacity"
                ></el-table-column>
                <el-table-column label="成本" align="center">
                    <template slot-scope="scope">
                        <el-form-item
                            :prop="`items[${scope.$index}].costprice`"
                            :rules="rules.costprice"
                        >
                            <el-input
                                v-model="scope.row.costprice"
                                @blur="
                                    onTableInputBlur(scope.$index, 'costprice')
                                "
                            ></el-input>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column label="售卖价格" align="center">
                    <template slot-scope="scope">
                        <el-form-item
                            :prop="`items[${scope.$index}].price`"
                            :rules="rules.price"
                        >
                            <el-input
                                v-model="scope.row.price"
                                @blur="onTableInputBlur(scope.$index, 'price')"
                            ></el-input>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column label="售卖数量" align="center">
                    <template slot-scope="scope">
                        <el-form-item
                            :prop="`items[${scope.$index}].num`"
                            :rules="rules.num"
                        >
                            <el-input
                                v-model="scope.row.num"
                                @blur="onTableInputBlur(scope.$index, 'num')"
                            ></el-input>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column label="小计" align="center" prop="$subtotal">
                </el-table-column>
                <el-table-column
                    label="利润率"
                    align="center"
                    prop="$profitRate"
                ></el-table-column>
                <el-table-column label="实际库存" align="center">
                    <template slot-scope="scope">
                        <el-form-item
                            :prop="`items[${scope.$index}].actual_num`"
                            :rules="rules.actualNum"
                        >
                            <el-input v-model="scope.row.actual_num"></el-input>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                    <template slot-scope="scope">
                        <el-button
                            v-if="info.items.length > 1"
                            type="text"
                            @click="removeItem(scope.$index)"
                            >移除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="mgt-10 mgb-10">
                <el-button
                    v-for="item in MCreatorTypeText"
                    :key="item.value"
                    :type="item.value === creatorType ? 'primary' : 'info'"
                    @click="creatorType = item.value"
                    >{{ item.label }}</el-button
                >
            </div>
            <template v-if="creatorType === MCreatorType.Add">
                <el-form-item label="需要设计图">
                    <el-radio-group v-model="info.is_design">
                        <el-radio
                            v-for="item in MIsDesignText"
                            :key="item.value"
                            :label="item.value"
                            >{{ item.label }}</el-radio
                        >
                    </el-radio-group>
                    <el-select
                        v-model="info.demand_grade"
                        placeholder="写作难易程度"
                        clearable
                        class="mgl-10"
                    >
                        <el-option
                            v-for="item in MDemandGradeText"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="文案需求">
                    <el-input
                        v-model="info.demand"
                        type="textarea"
                        :autosize="{ minRows: 3 }"
                        placeholder="文案需求"
                    />
                </el-form-item>
                <el-form-item label="采购备注">
                    <el-input
                        v-model="info.purchase_remake"
                        type="textarea"
                        :autosize="{ minRows: 3 }"
                        placeholder="采购备注"
                    />
                </el-form-item>
                <el-form-item label="售卖介绍">
                    <el-input
                        v-model="info.selling_point"
                        type="textarea"
                        :autosize="{ minRows: 3 }"
                        placeholder="售卖介绍"
                    />
                </el-form-item>
            </template>
            <template v-else-if="creatorType === MCreatorType.Copy">
                <!-- copy_periods_id -->
                <el-row type="flex">
                    <el-input
                        v-model="goodsId"
                        placeholder="请输入期数"
                        class="w-normal mgr-10"
                    ></el-input>
                    <el-button
                        type="primary"
                        :disabled="!goodsId"
                        @click="findGoods"
                        >查询</el-button
                    >
                </el-row>
                <el-row v-if="goodsPackageList.length" class="mgt-20">
                    <el-col :span="12">
                        <el-table
                            :data="goodsPackageList"
                            border
                            :span-method="arraySpanMethod"
                        >
                            <el-table-column
                                label="商品名称"
                                align="center"
                                prop="title"
                            ></el-table-column>
                            <el-table-column
                                label="套餐"
                                align="center"
                                prop="name"
                            ></el-table-column>
                            <el-table-column label="简码" align="center">
                                <template slot-scope="scope">
                                    <div
                                        v-for="item in scope.row.short_code"
                                        :key="item"
                                    >
                                        {{ item }}
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="售价/成本" align="center">
                                <template slot-scope="scope">
                                    {{ scope.row.costprice }}/{{
                                        scope.row.price
                                    }}
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-col>
                </el-row>
            </template>
            <el-form-item label="期望上架日期" class="mgt-10">
                <el-date-picker
                    v-model="info.expect_time"
                    type="datetime"
                    placeholder="期望上架日期"
                    value-format="yyyy-MM-dd HH:mm:ss"
                />
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button type="primary" @click="submit">提交</el-button>
        </div>
    </el-dialog>
</template>

<script>
import {
    MGoodsType,
    MPayeeCompany,
    MCorpToPayeeCompany,
    MImportType,
    MIsGiftBox,
    MIsSupplierDelivery,
    MIsPresell,
    MShippingConditions,
    MStorageConditions,
    MDeliveryTimeLimit,
    MCreatorType,
    MIsDesign,
} from "@/utils/mapperModel";
import {
    MGoodsTypeText,
    MImportTypeText,
    MPayeeCompanyText,
    MIsGiftBoxText,
    MIsSupplierDeliveryText,
    MIsPresellText,
    MShippingConditionsText,
    MStorageConditionsText,
    MDeliveryTimeLimitText,
    MCreatorTypeText,
    MIsDesignText,
    MDemandGradeText,
} from "@/utils/mapper";
import goodsPoolApi from "@/services/goodsPool";
import * as computedUtils from "@/components/goodsPool/computedUtils";
import ProductRemoteSelect from "./ProductRemoteSelect";

export default {
    components: {
        ProductRemoteSelect,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        row: {
            type: Object,
            default: () => ({}),
        },
    },
    data: () => ({
        MCreatorType,
        MGoodsTypeText,
        MImportTypeText,
        supplierRemoteLoading: false,
        supplierOptions: [],
        currSupplierCorps: "",
        productPayeeCompantText: [],
        MPayeeCompanyText,
        MIsGiftBoxText,
        MIsSupplierDelivery,
        MIsSupplierDeliveryText,
        supplierDeliveryAddressList: [],
        MIsPresellText,
        MShippingConditionsText,
        MStorageConditionsText,
        MDeliveryTimeLimitText,
        warehouseOptions: [],
        MCreatorTypeText,
        MIsDesignText,
        MDemandGradeText,
        info: {
            id: 0,
            periods_type: "",
            import_type: "",
            supplier_id: "",
            supplier: "",
            payee_merchant_id: "",
            payee_merchant_name: "",
            is_gift_box: MIsGiftBox.NO,
            is_supplier_delivery: MIsSupplierDelivery.NO,
            supplier_delivery_address: "",
            is_presell: MIsPresell.NO,
            shipping_conditions: MShippingConditions.Normal,
            storage_conditions: MStorageConditions.Normal,
            delivery_time_limit: MDeliveryTimeLimit.Hour24,
            warehouse_id: "",
            warehouse: "",
            erp_id: "",
            items: [],
            is_design: MIsDesign.YES,
            demand_grade: "",
            demand: "",
            purchase_remake: "",
            selling_point: "",
            expect_time: "",
            copy_periods_id: 0,
        },
        rules: {
            periods_type: [
                {
                    required: true,
                    message: "请选择频道",
                    trigger: "change",
                },
            ],
            import_type: [
                {
                    required: true,
                    message: "请选择进口类型",
                    trigger: "change",
                },
            ],
            supplier_id: [
                {
                    required: true,
                    message: "请输入供应商",
                    trigger: "change",
                },
            ],
            payee_merchant_id: [
                {
                    required: true,
                    message: "请选择收款公司",
                    trigger: "change",
                },
            ],
            supplier_delivery_address: [
                {
                    required: true,
                    message: "请选择发货地",
                    trigger: "change",
                },
            ],
            warehouse_id: [
                {
                    required: true,
                    message: "请选择发货仓库",
                    trigger: "change",
                },
            ],
            costprice: [
                {
                    required: true,
                    pattern:
                        /^[1-9]\d*(\.\d{1,2})?$|^0\.([0-9]\d?|0[0-9])$|^0$/,
                    message: "格式不正确",
                    trigger: "blur",
                },
            ],
            price: [
                {
                    required: true,
                    pattern: /^[1-9]\d*(\.\d{1,2})?$|^0\.([1-9]\d?|0[1-9])$/,
                    message: "格式不正确",
                    trigger: "blur",
                },
            ],
            num: [
                {
                    required: true,
                    pattern: /^[1-9]\d*$/,
                    message: "格式不正确",
                    trigger: "blur",
                },
            ],
            actualNum: [
                {
                    required: true,
                    pattern: /^[1-9]\d*$|^0$/,
                    message: "格式不正确",
                    trigger: "blur",
                },
            ],
        },
        creatorType: MCreatorType.Add,
        goodsId: "",
        goodsPackageList: [],
    }),
    computed: {
        currImportTypeText({ info }) {
            return MImportTypeText.filter((item) => {
                if (info.periods_type === MGoodsType.KJ) {
                    return item.value === MImportType.KJ;
                } else {
                    return item.value !== MImportType.KJ;
                }
            });
        },
        currPayeeCompanyText({
            info,
            productPayeeCompantText,
            currSupplierCorps,
        }) {
            const { periods_type, import_type, supplier_id } = info;
            if ([MGoodsType.MF, MGoodsType.KJ].includes(periods_type)) {
                return MPayeeCompanyText.filter(
                    (item) => item.value === MPayeeCompany.Keji
                );
            }
            if (
                [MGoodsType.SG, MGoodsType.WH].includes(periods_type) &&
                import_type === MImportType.Self &&
                productPayeeCompantText.length
            ) {
                return productPayeeCompantText;
            }
            if (supplier_id && currSupplierCorps) {
                return Object.keys(currSupplierCorps).map((key) => ({
                    value: MCorpToPayeeCompany[key],
                    label: currSupplierCorps[key],
                }));
            }
            return [];
        },
    },
    watch: {
        visible(newVal) {
            if (newVal) {
                const { info, creatorType, goodsId, goodsPackageList } =
                    this.$options.data();
                const { id, items, purchase } = this.row;
                this.info = Object.assign({}, info, purchase, {
                    id,
                    warehouse_id: purchase.warehouse_id
                        ? +purchase.warehouse_id
                        : "",
                    items: items.map((i) => ({
                        costprice: "",
                        price: "",
                        num: "",
                        actual_num: "",
                        ...i,
                    })),
                    expect_time: purchase.expect_time_text || "",
                });
                if (purchase.copy_periods_id) {
                    this.creatorType = MCreatorType.Copy;
                    this.goodsId = purchase.copy_periods_id;
                    this.findGoods();
                } else {
                    this.creatorType = creatorType;
                    this.goodsId = goodsId;
                    this.goodsPackageList = goodsPackageList;
                }
                this.$nextTick(() => {
                    this.$refs.formRef.clearValidate();
                    this.$refs.productRemoteSelectRef.reset();
                });
                this.loadSupplierDeliveryAddressList();
                this.loadProductPayeeCompantText();
                this.supplierIdRemoteMethod(this.info.supplier).then(() => {
                    this.onSupplierIdChange();
                });
                this.loadWarehouseOptions();
            }
        },
    },
    methods: {
        loadSupplierDeliveryAddressList() {
            this.$request.article.getRegionList().then((res) => {
                if (res.data.error_code == 0) {
                    this.supplierDeliveryAddressList = res.data.data.list.map(
                        ({ id, name }) => ({ id, name })
                    );
                }
            });
        },
        loadProductPayeeCompantText() {
            const product_ids = this.row.items
                .map((item) => item.product_id)
                .join();
            goodsPoolApi.searchCorpsByProductId({ product_ids }).then((res) => {
                if (res.data.error_code === 0) {
                    const payeeCompanyList = res.data.data.corps.map(
                        (item) => MCorpToPayeeCompany[item]
                    );
                    this.productPayeeCompantText = MPayeeCompanyText.filter(
                        (item) => payeeCompanyList.includes(item.value)
                    );
                }
            });
        },
        loadWarehouseOptions() {
            this.$request.article
                .warehouseList({
                    channel_types: this.info.periods_type,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.warehouseOptions = res.data.data;
                    }
                });
        },
        onPeriodsTypeInput(periodsType) {
            const {
                import_type,
                supplier_id,
                supplier_name,
                payee_merchant_id,
                payee_merchant_name,
                warehouse_id,
                warehouse,
                erp_id,
            } = this.$options.data().info;
            const resetData = {
                periods_type: periodsType,
                import_type,
                supplier_id,
                supplier_name,
                payee_merchant_id,
                payee_merchant_name,
                warehouse_id,
                warehouse,
                erp_id,
            };
            this.info = Object.assign({}, this.info, resetData);
            this.loadWarehouseOptions();
        },
        onImportTypeInput(importType) {
            const { payee_merchant_id, payee_merchant_name } =
                this.$options.data().info;
            const resetData = {
                import_type: importType,
                payee_merchant_id,
                payee_merchant_name,
            };
            this.info = Object.assign({}, this.info, resetData);
        },
        async supplierIdRemoteMethod(query) {
            if (query) {
                this.supplierRemoteLoading = true;
                const res = await this.$request.article.supplierList({
                    page: 1,
                    limit: 10,
                    keyword: query,
                });
                this.supplierRemoteLoading = false;
                this.supplierOptions = res.data.data.list;
            } else {
                this.supplierOptions = [];
            }
        },
        onSupplierIdChange() {
            const findItem = this.supplierOptions.find(
                (item) => item.id === this.info.supplier_id
            );
            if (!findItem) return;
            const { supplier_name, corps } = findItem;
            this.info.supplier = supplier_name;
            this.currSupplierCorps = corps;
        },
        onPayeeMechantIdChange() {
            this.info.payee_merchant_name = this.currPayeeCompanyText.find(
                (item) => item.value === this.info.payee_merchant_id
            ).label;
        },
        onWarehouseIdChange() {
            const { fictitious_name, erp_id } = this.warehouseOptions.find(
                (item) => item.id === this.info.warehouse_id
            );
            this.info.warehouse = fictitious_name;
            this.info.erp_id = erp_id;
        },
        onAddProduct(product) {
            if (
                this.info.items.some(
                    (item) => item.short_code === product.short_code
                )
            ) {
                this.$message.error("已有该产品简码");
                return;
            }
            this.info.items.push({
                costprice: "",
                price: "",
                num: "",
                actual_num: "",
                ...product,
            });
        },
        onTableInputBlur(index, key) {
            const item = this.info.items[index];
            const validateField = (prop) => {
                return new Promise((resolve) => {
                    this.$refs.formRef.validateField(
                        `items[${index}].${prop}`,
                        (errMsg) => {
                            resolve(errMsg);
                        }
                    );
                });
            };
            if (key === "costprice") {
                const props = ["costprice"];
                if (item.price) props.push("price");
                const validateList = props.map((prop) => validateField(prop));
                Promise.all(validateList).then((resList) => {
                    item.$profitRate = "";
                    if (resList.length === 2 && resList.every((res) => !res)) {
                        item.$profitRate = computedUtils.getProfitRate(
                            this.info.items[index]
                        );
                    }
                    this.info.items.splice(index, 1, item);
                });
            } else if (key === "price") {
                const props = ["price"];
                if (item.costprice) props.push("costprice");
                if (item.num) props.push("num");
                const validateList = props.map((prop) => validateField(prop));
                Promise.all(validateList).then((resList) => {
                    item.$profitRate = "";
                    item.$subtotal = "";
                    if (resList.length === 2 && resList.every((res) => !res)) {
                        if (props[1] === "costprice") {
                            item.$profitRate = computedUtils.getProfitRate(
                                this.info.items[index]
                            );
                        }
                        if (props[1] === "num") {
                            item.$subtotal = computedUtils.getSubtotal(
                                this.info.items[index]
                            );
                        }
                    } else if (resList.length === 3 && !resList[0]) {
                        if (!resList[1]) {
                            item.$profitRate = computedUtils.getProfitRate(
                                this.info.items[index]
                            );
                        }
                        if (!resList[2]) {
                            item.$subtotal = computedUtils.getSubtotal(
                                this.info.items[index]
                            );
                        }
                    }
                    this.info.items.splice(index, 1, item);
                });
            } else if (key === "num") {
                const props = ["num"];
                if (item.price) props.push("price");
                const validateList = props.map((prop) => validateField(prop));
                Promise.all(validateList).then((resList) => {
                    item.$subtotal = "";
                    if (resList.length === 2 && resList.every((res) => !res)) {
                        item.$subtotal = computedUtils.getSubtotal(
                            this.info.items[index]
                        );
                    }
                    this.info.items.splice(index, 1, item);
                });
            }
        },
        removeItem(index) {
            this.info.items.splice(index, 1);
        },
        findGoods() {
            goodsPoolApi.findGoods({ id: this.goodsId }).then((res) => {
                if (res.data.error_code === 0) {
                    const { id, title, packages } = res.data.data.period;
                    const goodsPackageList = packages.map((item) => ({
                        id,
                        title,
                        ...item,
                    }));
                    this.goodsPackageList = goodsPackageList;
                }
            });
        },
        arraySpanMethod({ row, column, rowIndex, columnIndex }) {
            if (columnIndex === 0) {
                if (rowIndex === 0) {
                    return [99, 1];
                } else {
                    return [0, 0];
                }
            }
        },
        close() {
            this.$emit("update:visible", false);
        },
        submit() {
            this.$refs.formRef.validate((valid) => {
                if (!valid) return;
                if (this.creatorType === MCreatorType.Copy) {
                    if (!this.goodsId) {
                        this.$message.error("请输入复制期数");
                        return;
                    }
                    if (!this.goodsPackageList.length) {
                        this.$message.error("暂无该复制期数");
                        return;
                    }
                    if (this.goodsId !== `${this.goodsPackageList[0].id}`) {
                        this.$message.error("搜索期数与展示期数不符");
                        return;
                    }
                    this.info.copy_periods_id = this.goodsId;
                }
                const { $apiBasicParams } = this.row;
                const params = Object.assign(
                    {
                        ...$apiBasicParams,
                    },
                    this.info,
                    {
                        items: this.info.items.map(
                            ({
                                product_id,
                                costprice,
                                price,
                                num,
                                actual_num,
                                sku_id,
                                supplier,
                                supplier_id,
                            }) => ({
                                product_id,
                                costprice,
                                price,
                                num,
                                actual_num,
                                sku_id,
                                supplier,
                                supplier_id,
                            })
                        ),
                    }
                );
                goodsPoolApi.submitPurchaseInfo(params).then((res) => {
                    if (res.data.error_code === 0) {
                        this.$emit("load");
                        this.$message.success("操作成功");
                        this.close();
                    }
                });
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.el-radio {
    margin-bottom: 0;
}

.table-column {
    background: red !important;

    /deep/ {
        .cell {
            overflow: visible;
        }
        .el-form-item {
            margin-bottom: 0;
        }
    }
}
</style>
