import computedNumber from "@/utils/computedNumber";

export const getSubtotal = (product) => {
    const { price, num } = product;
    if (!price || !num) return "";
    return computedNumber(price, "*", num).result;
};

export const getProfitRate = (product) => {
    const { costprice, price } = product;
    if (!costprice || !price) return "";
    const profitRate = computedNumber(price, "-", costprice)
        .next("/", costprice)
        .next("*", 100)
        .result.toFixed(2);
    return `${+profitRate}%`;
};
