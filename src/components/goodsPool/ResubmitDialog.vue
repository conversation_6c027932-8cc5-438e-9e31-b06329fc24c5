<template>
    <el-dialog
        :visible="visible"
        title="重新上架售卖"
        width="420px"
        @close="close"
    >
        <el-form ref="formRef" :model="model" :rules="rules">
            <el-form-item>
                确认提交后会直接到运营确认环节，请问是否继续提交？
            </el-form-item>
            <el-form-item label="期望上架时间" prop="purchase_expect_time">
                <el-date-picker
                    v-model="model.purchase_expect_time"
                    type="datetime"
                    placeholder="期望上架时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                />
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="confirm">确定提交</el-button>
        </div>
    </el-dialog>
</template>

<script>
import goodsPoolApi from "@/services/goodsPool";

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        row: {
            type: Object,
            default: () => ({}),
        },
    },
    data: () => ({
        model: {
            purchase_expect_time: "",
        },
        rules: {
            purchase_expect_time: [
                {
                    required: true,
                    message: "请选择期待上架日期",
                    trigger: "change",
                },
            ],
        },
    }),
    watch: {
        visible(newVal) {
            if (!newVal) return;
            this.model = this.$options.data().model;
            this.$nextTick(() => {
                this.$refs.formRef.clearValidate();
            });
        },
    },
    methods: {
        close() {
            this.$emit("update:visible", false);
        },
        confirm() {
            this.$refs.formRef.validate((valid) => {
                if (!valid) return;
                const { id, $apiBasicParams } = this.row;
                const params = { ...$apiBasicParams, id, ...this.model };
                goodsPoolApi.purchaseResubmit(params).then((res) => {
                    if (res.data.error_code == 0) {
                        this.$emit("load");
                        this.$message.success("操作成功");
                        this.close();
                    }
                });
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.table {
    /deep/ .el-table__header-wrapper {
        display: none;
    }
}
</style>
