<template>
    <el-dialog :visible="visible" :title="title" @close="close">
        <el-select v-model="productId" @change="onProductIdChange">
            <el-option
                v-for="item in productIdOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
        </el-select>
        <el-form label-width="120px" class="mgt-20">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="正标图">
                        <vos-oss
                            ref="vosOssRef1"
                            list-type="picture-card"
                            :showFileList="true"
                            :dir="dir"
                            :file-list="model.frontalLabelImgFileList"
                            :limit="1"
                            :disabled="disabled"
                        >
                            <i class="el-icon-plus" />
                        </vos-oss>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="中文背标图">
                        <vos-oss
                            ref="vosOssRef2"
                            list-type="picture-card"
                            :showFileList="true"
                            :dir="dir"
                            :file-list="model.cnBackLabelImgFileList"
                            :limit="1"
                            :disabled="disabled"
                        >
                            <i class="el-icon-plus" />
                        </vos-oss>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="英文背标图">
                        <vos-oss
                            ref="vosOssRef3"
                            list-type="picture-card"
                            :showFileList="true"
                            :dir="dir"
                            :file-list="model.packageImgFileList"
                            :limit="1"
                            :disabled="disabled"
                        >
                            <i class="el-icon-plus" />
                        </vos-oss>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="包装图">
                        <vos-oss
                            ref="vosOssRef4"
                            list-type="picture-card"
                            :showFileList="true"
                            :dir="dir"
                            :file-list="model.enBackLabelImgFileList"
                            :limit="1"
                            :disabled="disabled"
                        >
                            <i class="el-icon-plus" />
                        </vos-oss>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item>
                        <div slot="label">
                            <div>报关单</div>
                            <div style="font-size: 12px">(或质检报告)</div>
                        </div>
                        <vos-oss
                            ref="vosOssRef5"
                            :dir="dir"
                            :file-list="model.customsPassFileList"
                            :limit="1"
                            :fileSize="20"
                            :multiple="false"
                            list-type="text"
                            filesType=""
                            :showFileList="true"
                            :is_download="true"
                            :showName="true"
                            :disabled="disabled"
                        >
                            <el-button
                                type="primary"
                                size="mini"
                                :disabled="disabled"
                                >点击上传</el-button
                            >
                        </vos-oss>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="卫检">
                        <vos-oss
                            ref="vosOssRef6"
                            :dir="dir"
                            :file-list="model.healthFileList"
                            :limit="1"
                            :fileSize="20"
                            :multiple="false"
                            list-type="text"
                            filesType=""
                            :showFileList="true"
                            :is_download="true"
                            :showName="true"
                            :disabled="disabled"
                        >
                            <el-button
                                type="primary"
                                size="mini"
                                :disabled="disabled"
                                >点击上传</el-button
                            >
                        </vos-oss>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="附件">
                <vos-oss
                    ref="vosOssRef7"
                    :dir="dir"
                    :file-list="model.attachmentFileList"
                    :limit="999"
                    :fileSize="20"
                    :multiple="false"
                    list-type="text"
                    filesType=""
                    :showFileList="true"
                    :is_download="true"
                    :showName="true"
                    disabled
                >
                    <el-button type="primary" size="mini" disabled
                        >点击上传</el-button
                    >
                </vos-oss>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <template v-if="currStatus === 1">
                <el-button type="primary" @click="check(3)">通过</el-button>
                <el-button type="primary" @click="rejectDialogVisible = true"
                    >驳回</el-button
                >
            </template>
            <template v-else-if="currStatus === 0">
                <el-button type="primary" @click="save">保存</el-button>
                <el-button type="primary" @click="submit">提交</el-button>
            </template>
        </div>

        <el-dialog
            :visible.sync="rejectDialogVisible"
            width="420px"
            append-to-body
        >
            <el-input
                v-model="rejectCause"
                type="textarea"
                :rows="2"
                placeholder="请输入驳回原因"
            ></el-input>
            <div slot="footer" class="dialog-footer">
                <el-button @click="rejectDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="check(4)">确定</el-button>
            </div>
        </el-dialog>
    </el-dialog>
</template>

<script>
import VosOss from "vos-oss";
import goodsPoolApi from "@/services/goodsPool";

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        row: {
            type: Object,
            default: () => ({}),
        },
    },
    components: {
        VosOss,
    },
    data: () => ({
        currStatus: 0, // 0 上传资质 1 审核资质 2 查看资质
        dir: "vinehoo/goods-images/",
        productIdOptions: [],
        productId: "",
        certificateList: [],
        model: {
            frontalLabelImgFileList: [],
            cnBackLabelImgFileList: [],
            enBackLabelImgFileList: [],
            packageImgFileList: [],
            customsPassFileList: [],
            healthFileList: [],
            attachmentFileList: [],
        },
        rejectCause: "",
        rejectDialogVisible: false,
    }),
    computed: {
        title({ currStatus }) {
            switch (currStatus) {
                case 0:
                    return "上传产品资质";
                case 1:
                    return "审核产品资质";
                case 2:
                    return "产品资质";
                default:
                    return "";
            }
        },
        disabled({ currStatus }) {
            return !!currStatus;
        },
    },
    watch: {
        visible(newVal) {
            if (!newVal) return;
            const { items, qualification } = this.row;
            this.productIdOptions = items.map(({ product_id, short_code }) => ({
                value: product_id,
                label: short_code,
            }));
            this.productId = this.productIdOptions[0].value;
            this.certificateList = qualification;
            Object.keys(this.$refs).forEach((key) => {
                if (key.includes("vosOssRef")) {
                    this.$refs[key].viewFileList = [];
                }
            });
            this.initModel();
            this.rejectCause = this.$options.data().rejectCause;
        },
    },
    methods: {
        initModel() {
            const {
                frontal_label_img = "",
                cn_back_label_img = "",
                en_back_label_img = "",
                package_img = "",
                customs_pass = "",
                health = "",
                product_attachment = [],
            } = this.certificateList.find(
                (item) => item.product_id === this.productId
            ) || {};
            const getFileList = (str, flag = ",") => {
                return str ? str.split(flag) : [];
            };
            this.model = Object.assign({}, this.$options.data().model, {
                frontalLabelImgFileList: getFileList(frontal_label_img),
                cnBackLabelImgFileList: getFileList(cn_back_label_img),
                enBackLabelImgFileList: getFileList(en_back_label_img),
                packageImgFileList: getFileList(package_img),
                customsPassFileList: getFileList(customs_pass),
                healthFileList: getFileList(health),
                attachmentFileList: [...product_attachment],
            });
        },
        onProductIdChange() {
            Object.keys(this.$refs).forEach((key) => {
                if (key.includes("vosOssRef")) {
                    this.$refs[key].viewFileList = [];
                }
            });
            this.initModel();
        },
        close() {
            this.rejectDialogVisible = false;
            this.$emit("update:visible", false);
        },
        save() {
            const {
                frontalLabelImgFileList,
                cnBackLabelImgFileList,
                enBackLabelImgFileList,
                packageImgFileList,
                customsPassFileList,
                healthFileList,
            } = this.model;
            const { $certificateApiBasicParams, id } = this.row;
            const params = {
                ...$certificateApiBasicParams,
                id,
                product_id: this.productId,
                frontal_label_img: frontalLabelImgFileList.join(),
                cn_back_label_img: cnBackLabelImgFileList.join(),
                en_back_label_img: enBackLabelImgFileList.join(),
                package_img: packageImgFileList.join(),
                customs_pass: customsPassFileList.join(),
                health: healthFileList.join(),
            };
            goodsPoolApi.saveCertificate(params).then((res) => {
                if (res.data.error_code === 0) {
                    const certificateList = res.data.data.qualification;
                    const OSS_DOMAIN =
                        process.env.NODE_ENV == "development"
                            ? "https://images.wineyun.com"
                            : "https://images.vinehoo.com";
                    certificateList.forEach((item) => {
                        Object.keys(item).forEach((key) => {
                            if (
                                [
                                    "frontal_label_img",
                                    "cn_back_label_img",
                                    "en_back_label_img",
                                    "package_img",
                                    "customs_pass",
                                    "health",
                                    "product_attachment",
                                ].includes(key) &&
                                item[key]
                            ) {
                                item[key] = item[key].split(",");
                                item[key] = item[key].map((img) =>
                                    img.includes(OSS_DOMAIN)
                                        ? img
                                        : `${OSS_DOMAIN}${img}`
                                );
                                item[key] = ["product_attachment"].includes(key)
                                    ? item[key]
                                    : item[key].join();
                            }
                        });
                    });
                    this.certificateList = certificateList;
                    this.$emit("load");
                    this.$message.success("操作成功");
                }
            });
        },
        submit() {
            const {
                frontalLabelImgFileList,
                cnBackLabelImgFileList,
                enBackLabelImgFileList,
                packageImgFileList,
                customsPassFileList,
                healthFileList,
            } = this.model;
            const { $certificateApiBasicParams, id } = this.row;
            const params = {
                ...$certificateApiBasicParams,
                id,
                product_id: this.productId,
                frontal_label_img: frontalLabelImgFileList.join(),
                cn_back_label_img: cnBackLabelImgFileList.join(),
                en_back_label_img: enBackLabelImgFileList.join(),
                package_img: packageImgFileList.join(),
                customs_pass: customsPassFileList.join(),
                health: healthFileList.join(),
            };
            goodsPoolApi.submitCertificate(params).then((res) => {
                if (res.data.error_code === 0) {
                    this.$emit("load");
                    this.$message.success("操作成功");
                    this.close();
                }
            });
        },
        check(status) {
            const { $certificateApiBasicParams, id } = this.row;
            const params = {
                ...$certificateApiBasicParams,
                id,
                status,
            };
            if (status === 4) {
                if (!this.rejectCause) {
                    this.$message.error("请填写驳回原因");
                    return;
                }
                params.reject_cause = this.rejectCause;
            }
            goodsPoolApi.checkCertificate(params).then((res) => {
                if (res.data.error_code === 0) {
                    this.$emit("load");
                    this.$message.success("操作成功");
                    this.close();
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>
