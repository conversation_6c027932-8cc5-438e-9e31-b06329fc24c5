<template>
    <el-dialog :visible="visible" title="修改设计师" @close="close">
        <el-form ref="formRef" :model="info" label-width="100px">
            <el-form-item label="设计师">
                <el-select
                    v-model="info.ui_uid"
                    filterable
                    @change="onUiUidChange"
                >
                    <el-option
                        v-for="item in designOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button type="primary" @click="save">保存</el-button>
        </div>
    </el-dialog>
</template>

<script>
import goodsPoolApi from "@/services/goodsPool";
import { mapState } from "vuex";
import { MGoodsType } from "@/utils/mapperModel";

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        row: {
            type: Object,
            default: () => ({}),
        },
    },
    data: () => ({
        info: {
            id: 0,
            ui_uid: "",
            ui_name: "",
        },
    }),
    computed: {
        ...mapState(["designOptions"]),
    },
    watch: {
        visible(newVal) {
            if (!newVal) return;
            const { id, picture_distribution } = this.row;
            const { design_uid = "", creator_name = "" } =
                picture_distribution || {};
            this.info = Object.assign({}, this.$options.data().info, {
                id,
                ui_uid: design_uid,
                ui_name: creator_name,
            });
        },
    },
    methods: {
        onUiUidChange() {
            this.info.ui_name = this.designOptions.find(
                (item) => item.value === this.info.ui_uid
            ).label;
        },
        close() {
            this.$emit("update:visible", false);
        },
        save() {
            const { $apiBasicParams, current_node_info, purchase } = this.row;
            const { process_node_ids, participant_node_ids } =
                current_node_info;
            let target_node_id = "";
            if (purchase.periods_type === MGoodsType.MF) {
                target_node_id = process_node_ids[0]?.id || "";
            } else {
                target_node_id = participant_node_ids[0]?.id || "";
            }
            if (!target_node_id) return;
            goodsPoolApi
                .allocDesignUpdate({
                    ...$apiBasicParams,
                    target_node_id,
                    ...this.info,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.$emit("load");
                        this.$message.success("操作成功");
                        this.close();
                    }
                });
        },
    },
};
</script>

<style lang="scss" scoped></style>
