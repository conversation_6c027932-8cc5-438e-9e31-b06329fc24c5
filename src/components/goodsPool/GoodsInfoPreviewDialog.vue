<template>
    <el-dialog :visible="visible" title="商品详情浏览" @close="close">
        <el-row type="flex" justify="center" align="middle">
            <iframe
                v-if="visible"
                :src="src"
                height="667px"
                width="375px"
            ></iframe>
            <div class="mgl-20">
                <div v-if="visible" id="qrCode" ref="qrCodeDiv"></div>
                <div style="color: #333; text-align: center" class="mgt-10">
                    手机扫码浏览
                </div>
            </div>
        </el-row>
        <div v-if="currStatus" slot="footer">
            <el-date-picker
                v-if="currStatus === 2"
                v-model="expectTime"
                type="datetime"
                placeholder="期望上架时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                class="mgr-10"
            />
            <el-button type="primary" @click="check(3)">通过</el-button>
            <el-button type="primary" @click="rejectDialogVisible = true"
                >驳回</el-button
            >
        </div>
        <el-dialog
            :visible.sync="rejectDialogVisible"
            width="420px"
            append-to-body
        >
            <el-input
                v-model="rejectCause"
                type="textarea"
                :rows="2"
                placeholder="请输入驳回原因"
            ></el-input>
            <div slot="footer" class="dialog-footer">
                <el-button @click="rejectDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="check(4)">确定</el-button>
            </div>
        </el-dialog>
    </el-dialog>
</template>

<script>
import QRCode from "qrcodejs2";
import goodsPoolApi from "@/services/goodsPool";

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        row: {
            type: Object,
            default: () => ({}),
        },
    },
    data: () => ({
        src: "",
        currStatus: 0, // 0 预览 1 文案主管审核 2 采购审核
        expectTime: "",
        rejectCause: "",
        rejectDialogVisible: false,
    }),
    watch: {
        visible(newVal) {
            if (newVal) {
                const { id, purchase } = this.row;
                this.src = `${
                    this.$BASE.Domain
                }/pages/goods-detail/goods-detail?from=3&t=${Date.now()}&id=${id}`;
                // }/pages/goods-detail/goods-detail?from=3&t=${Date.now()}&id=76748`;
                this.$nextTick(() => {
                    this.getQRCode();
                });
                const { expectTime, rejectCause } = this.$options.data();
                this.expectTime = purchase.expect_time_text
                    ? purchase.expect_time_text
                    : expectTime;
                this.rejectCause = rejectCause;
            }
        },
    },
    methods: {
        getQRCode() {
            new QRCode(this.$refs.qrCodeDiv, {
                text: this.src,
                width: 200,
                height: 200,
                colorDark: "#333333",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.L,
            });
        },
        close() {
            this.rejectDialogVisible = false;
            this.$emit("update:visible", false);
        },
        check(status) {
            const { $apiBasicParams, id } = this.row;
            const params = {
                ...$apiBasicParams,
                id,
                status,
            };
            if (status === 4) {
                if (!this.rejectCause) {
                    this.$message.error("请填写驳回原因");
                    return;
                }
                params.reject_cause = this.rejectCause;
            }
            let api = goodsPoolApi.checkGoodsInfo;
            if (this.currStatus === 2) {
                params.expect_time = this.expectTime;
                api = goodsPoolApi.purchaseCheckGoodsInfo;
            }
            api(params).then((res) => {
                if (res.data.error_code === 0) {
                    this.$emit("load");
                    this.$message.success("操作成功");
                    this.close();
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>
