<template>
    <el-popover
        placement="right-end"
        width="400"
        trigger="click"
        @show="onShow"
        @hide="onHide"
    >
        <div style="max-height: 500px; overflow: scroll">
            <el-row
                v-for="(item, index) in list"
                :key="index"
                type="flex"
                justify="space-between"
                style="cursor: pointer"
            >
                <el-row type="flex" align="middle">
                    <img
                        src="@/assets/img/draggable_icon.png"
                        style="width: 20px; height: 20px"
                    />
                    <span
                        style="
                            margin-left: 5px;
                            font-size: 14px;
                            color: #606266;
                            line-height: 40px;
                        "
                    >
                        {{ item.label }}
                    </span>
                </el-row>
                <el-switch v-model="item.switch" />
            </el-row>
        </div>
        <el-button type="warning" slot="reference">设置</el-button>
    </el-popover>
</template>

<script>
import VueDraggable from "vuedraggable";

export default {
    props: {
        list: {
            type: Array,
            default: () => [],
        },
    },
    components: {
        VueDraggable,
    },
    data: () => ({
        draggableList: [],
    }),
    methods: {
        onShow() {
            // this.draggableList = this.list.map((item) => ({ ...item }));
        },
        onHide() {
            localStorage.setItem(
                "goodsPoolTableColConf",
                JSON.stringify(this.list)
            );
            // this.$emit("update:list", this.draggableList);
        },
    },
};
</script>

<style lang="scss" scoped></style>
