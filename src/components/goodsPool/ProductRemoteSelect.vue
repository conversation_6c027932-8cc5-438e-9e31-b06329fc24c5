<template>
    <el-row type="flex" justify="end">
        <el-select
            v-model="productId"
            filterable
            remote
            placeholder="请输入产品简码"
            :remote-method="remoteMethod"
            :loading="loading"
        >
            <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.product_name"
                :value="item.id"
            >
            </el-option>
        </el-select>
        <el-button type="primary" class="mgl-10" @click="onAdd">添加</el-button>
    </el-row>
</template>

<script>
import goodsPoolApi from "@/services/goodsPool";

export default {
    data: () => ({
        productId: "",
        loading: false,
        options: [],
    }),
    methods: {
        remoteMethod(query) {
            if (query) {
                this.loading = true;
                goodsPoolApi
                    .searchProductList({
                        short_code: query,
                    })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.options = res.data.data.list;
                            this.loading = false;
                        }
                    });
            } else {
                this.options = [];
            }
        },
        onAdd() {
            const product = this.options.find(
                (item) => item.id === this.productId
            );
            this.$emit("add", product);
            const { productId, options } = this.$options.data();
            this.productId = productId;
            this.options = options;
        },
        reset() {
            const { productId, loading, options } = this.$options.data();
            this.productId = productId;
            this.loading = loading;
            this.options = options;
        },
    },
};
</script>
