<template>
    <el-dialog :visible="visible" title="文案分发" @close="close">
        <el-form ref="formRef" label-width="120px" :model="info" :rules="rules">
            <el-form-item label="文案负责人员" prop="creator_uid">
                <el-select
                    v-model="info.creator_uid"
                    placeholder="请选择文案负责人员"
                    clearable
                    filterable
                    @change="onCreatorUidChange"
                >
                    <el-option
                        v-for="item in copyWriterOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="创作难度">
                <el-select
                    v-model="info.demand_grade"
                    placeholder="创作程度"
                    clearable
                >
                    <el-option
                        v-for="item in MDemandGradeText"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="预计完成时间">
                <el-date-picker
                    v-model="info.planned_completion_time"
                    type="datetime"
                    placeholder="请选择日期"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button @click="close">取 消</el-button>
            <el-button type="primary" @click="confirm">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { mapState } from "vuex";
import goodsPoolApi from "@/services/goodsPool";
import { MDemandGradeText } from "@/utils/mapper";
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        row: {
            type: Object,
            default: () => ({}),
        },
    },
    data: () => ({
        MDemandGradeText,
        info: {
            id: 0,
            creator_uid: "",
            creator_name: "",
            demand_grade: "",
            planned_completion_time: "",
        },
        rules: {
            creator_uid: [
                {
                    required: true,
                    message: "请选择文案负责人员",
                    trigger: "change",
                },
            ],
        },
    }),
    computed: {
        ...mapState(["copyWriterOptions"]),
    },
    watch: {
        visible(newVal) {
            if (!newVal) return;
            const { id, document_distribution, purchase } = this.row;
            const {
                creator_uid = "",
                creator_name = "",
                planned_completion_time_text = "",
            } = document_distribution || {};
            this.info = Object.assign({}, this.$options.data().info, {
                id,
                creator_uid,
                creator_name,
                demand_grade: purchase.demand_grade,
                planned_completion_time: planned_completion_time_text,
            });
            this.$nextTick(() => {
                this.$refs.formRef.clearValidate();
            });
        },
    },
    methods: {
        onCreatorUidChange() {
            this.info.creator_name = this.copyWriterOptions.find(
                (item) => item.value === this.info.creator_uid
            ).label;
        },
        close() {
            this.$emit("update:visible", false);
        },
        confirm() {
            this.$refs.formRef.validate((valid) => {
                if (!valid) return;
                const { $apiBasicParams, id, current_node_info } = this.row;
                const { process_node_ids } = current_node_info;
                if (!process_node_ids.length) return;
                const { creator_uid, creator_name } = this.info;
                goodsPoolApi
                    .setCopyWriter({
                        ...$apiBasicParams,
                        target_node_id: process_node_ids[0].id,
                        id,
                        creator_uid,
                        creator_name,
                    })
                    .then((setCopyWriterRes) => {
                        if (setCopyWriterRes.data.error_code === 0) {
                            const node_is_done =
                                process_node_ids[0].id === current_node_info.id
                                    ? 0
                                    : 1;
                            const params = Object.assign(
                                { node_is_done },
                                this.row.$apiBasicParams || {},
                                this.info
                            );
                            goodsPoolApi.allocCopyWriter(params).then((res) => {
                                if (res.data.error_code === 0) {
                                    this.$emit("load");
                                    this.$message.success("操作成功");
                                    this.close();
                                }
                            });
                        }
                    });
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>
