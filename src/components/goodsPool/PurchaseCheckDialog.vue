<template>
    <el-dialog :visible="visible" @close="close">
        <el-form
            ref="formRef"
            label-width="120px"
            :model="model"
            :rules="rules"
        >
            <el-form-item label="审核状态" required>
                <el-radio-group v-model="model.status">
                    <el-radio
                        v-for="item in [
                            { value: 3, label: '通过' },
                            { value: 4, label: '驳回' },
                        ]"
                        :key="item.value"
                        :label="item.value"
                        style="margin-bottom: 0"
                        >{{ item.label }}</el-radio
                    >
                </el-radio-group>
            </el-form-item>
            <el-form-item
                v-if="model.status === 3"
                label="期待上架日期"
                prop="expect_time"
            >
                <el-date-picker
                    v-model="model.expect_time"
                    type="datetime"
                    placeholder="请选择日期"
                    value-format="yyyy-MM-dd HH:mm:ss"
                />
            </el-form-item>
            <el-form-item v-if="model.status === 4" label="">
                <el-input
                    v-model="model.reject_cause"
                    type="textarea"
                    :autosize="{ minRows: 2 }"
                    placeholder="请输入驳回原因"
                />
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="confirm">确认</el-button>
        </div>
    </el-dialog>
</template>

<script>
import goodsPoolApi from "@/services/goodsPool";

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        row: {
            type: Object,
            default: () => ({}),
        },
    },
    data: () => ({
        model: {
            id: 0,
            status: 3,
            expect_time: "",
            reject_cause: "",
        },
        rules: {
            expect_time: [
                {
                    required: true,
                    message: "请选择期待上架日期",
                    trigger: "change",
                },
            ],
        },
    }),
    watch: {
        visible(newVal) {
            if (!newVal) return;
            const { purchase } = this.row;
            this.model = {
                ...this.$options.data().model,
                expect_time: purchase.expect_time_text
                    ? purchase.expect_time_text
                    : "",
            };
            this.$nextTick(() => {
                this.$refs.formRef.clearValidate();
            });
        },
    },
    methods: {
        close() {
            this.$emit("update:visible", false);
        },
        confirm() {
            this.$refs.formRef.validate((valid) => {
                if (!valid) return;
                const { id, $apiBasicParams } = this.row;
                const { status, expect_time, reject_cause } = this.model;
                let params = { ...$apiBasicParams, id, status, expect_time };
                if (status === 4)
                    params = { ...$apiBasicParams, id, status, reject_cause };
                goodsPoolApi.purchaseCheck(params).then((res) => {
                    if (res.data.error_code === 0) {
                        this.$emit("load");
                        this.$message.success("操作成功");
                        this.close();
                    }
                });
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>
