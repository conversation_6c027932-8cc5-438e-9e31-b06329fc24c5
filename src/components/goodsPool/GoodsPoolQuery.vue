<template>
    <el-form inline>
        <el-form-item>
            <el-input
                v-model="query.id"
                placeholder="期数"
                clearable
            ></el-input>
        </el-form-item>
        <el-form-item>
            <el-input
                v-model="query.product_name"
                placeholder="品名"
                clearable
            ></el-input>
        </el-form-item>
        <el-form-item>
            <el-input
                v-model="query.short_code"
                placeholder="简码"
                clearable
            ></el-input>
        </el-form-item>
        <el-form-item>
            <el-input
                v-model="query.sku_id"
                placeholder="SKU_ID"
                clearable
            ></el-input>
        </el-form-item>
        <el-form-item>
            <el-select
                v-model="query.creator_uid"
                placeholder="文案"
                clearable
                filterable
            >
                <el-option
                    v-for="item in copyWriterOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-select
                v-model="query.purchase_uid"
                placeholder="采购"
                clearable
                filterable
            >
                <el-option
                    v-for="item in purchaseOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-select
                :value="
                    query.periods_types
                        ? query.periods_types.split(',').map((item) => +item)
                        : []
                "
                placeholder="频道(多选)"
                multiple
                clearable
                @input="onPeriodsTypesChange"
            >
                <el-option
                    v-for="item in MGoodsTypeText"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-select
                v-model="query.transaction_state"
                placeholder="事项状态"
                clearable
            >
                <el-option
                    v-for="item in [
                        '全部',
                        '未提交',
                        '文案主管分配中',
                        '文案创作中',
                        '设计创作中',
                        '文案主管审核中',
                        '采购审核中',
                        '上传资质中',
                        '审核资质中',
                        '采购主管审核中',
                        '运营确认上架中',
                        '运营通过',
                        '运营未排期',
                        '运营延期',
                    ]"
                    :key="item"
                    :label="item"
                    :value="item"
                />
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="$emit('reload')">查询</el-button>
            <!-- <el-button type="warning">设置</el-button> -->
            <slot></slot>
        </el-form-item>
    </el-form>
</template>

<script>
import { mapState } from "vuex";
import { MGoodsTypeText } from "@/utils/mapper";

export default {
    props: {
        query: {
            type: Object,
            default: () => ({}),
        },
    },
    data: () => ({
        MGoodsTypeText,
    }),
    computed: {
        ...mapState(["copyWriterOptions", "purchaseOptions"]),
    },
    methods: {
        onPeriodsTypesChange(value) {
            this.query.periods_types = value.join();
        },
    },
};
</script>

<style lang="scss" scoped></style>
