<template>
    <el-dialog :visible="visible" fullscreen @close="close">
        <el-form ref="formRef" label-width="100px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="商品标题" prop="title">
                        <el-input
                            v-model="info.title"
                            :maxlength="titleMaxLength"
                            :show-word-limit="
                                titleMaxLength === -1 ? false : true
                            "
                        />
                    </el-form-item>
                    <el-form-item label="上传题图" prop="banner_img_file_list">
                        <vos-oss
                            v-if="visible"
                            list-type="picture-card"
                            :showFileList="true"
                            :dir="dir"
                            :file-list="info.banner_img_file_list"
                            :limit="1"
                            @on-success="validateField('banner_img_file_list')"
                        >
                            <i class="el-icon-plus" />
                        </vos-oss>
                    </el-form-item>
                    <el-form-item label="视频封面图">
                        <vos-oss
                            v-if="visible"
                            list-type="picture-card"
                            :showFileList="true"
                            :dir="dir"
                            :file-list="info.video_cover_file_list"
                            :limit="1"
                        >
                            <i class="el-icon-plus" />
                        </vos-oss>
                    </el-form-item>
                    <el-form-item label="视频上传">
                        <vos-vod
                            v-if="visible"
                            ref="vosVodRef"
                            :playurl.sync="info.video"
                            @uploadSuccess="onVideoUploadSuccess"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="一句话介绍" prop="brief">
                        <el-input v-model="info.brief" />
                    </el-form-item>
                    <el-form-item
                        label="上传产品图"
                        prop="product_img_file_list"
                    >
                        <vos-oss
                            v-if="visible"
                            list-type="picture-card"
                            :showFileList="true"
                            :dir="dir"
                            :file-list="info.product_img_file_list"
                            :limit="8"
                            :multiple="true"
                            @on-success="validateField('product_img_file_list')"
                        >
                            <i class="el-icon-plus" />
                        </vos-oss>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="商品详情" prop="detail">
                <Tinymce
                    v-if="visible"
                    ref="editor"
                    v-model.trim="info.detail"
                    :height="300"
                    @blur="validateField('detail')"
                />
            </el-form-item>
        </el-form>
        <div slot="footer">
            <template v-if="currStatus !== 3">
                <el-button type="primary" @click="save">保存</el-button>
                <el-button v-if="currStatus" type="primary" @click="submit"
                    >提交</el-button
                >
            </template>
            <el-button v-else type="primary" @click="update">更新</el-button>
        </div>
    </el-dialog>
</template>

<script>
import VosOss from "vos-oss";
import VosVod from "vos-vod";
import Tinymce from "@/components/Tinymce";
import goodsPoolApi from "@/services/goodsPool";
import { MGoodsType } from "@/utils/mapperModel";
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        row: {
            type: Object,
            default: () => ({}),
        },
    },
    components: {
        VosOss,
        VosVod,
        Tinymce,
    },
    data: () => ({
        MGoodsType,
        currStatus: 0,
        dir: "vinehoo/goods-images/",
        purchaseOptions: [],
        info: {
            id: 0, // 0 保存 1 秒发提交 2 非秒发提交 3 更新
            title: "",
            brief: "",
            banner_img: "",
            banner_img_file_list: [],
            product_img: "",
            product_img_file_list: [],
            video_cover: "",
            video_cover_file_list: [],
            video: "",
            detail: "",
        },
    }),
    computed: {
        titleMaxLength({ row }) {
            const periods_type = row?.purchase?.periods_type;
            return periods_type === MGoodsType.MF ? 30 : -1;
        },
    },
    watch: {
        visible(newVal) {
            if (!newVal) return;
            const { editor = {} } = this.row;
            const {
                title = "",
                brief = "",
                banner_img = "",
                product_img = "",
                video_cover = "",
                video = "",
                detail = "",
            } = editor;
            const info = {
                title,
                brief,
                video,
                detail,
            };
            info.banner_img_file_list = banner_img ? banner_img.split(",") : [];
            info.product_img_file_list = product_img
                ? product_img.split(",")
                : [];
            info.video_cover_file_list = video_cover
                ? video_cover.split(",")
                : [];
            this.info = Object.assign({}, this.$options.data().info, info);
            // this.$nextTick(() => {
            //     this.$refs.formRef.clearValidate();
            // });
            this.$nextTick(() => {
                const vosVodRef = this.$refs.vosVodRef;
                if (vosVodRef) vosVodRef.changePlayurl = this.info.video;
            });
        },
    },
    methods: {
        initPurchaseOptions() {
            this.$request.article
                .purchaseList({
                    type: 3,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        const { list = [] } = res.data.data;
                        this.purchaseOptions = list.map(({ id, realname }) => ({
                            value: id,
                            label: realname,
                        }));
                    }
                });
        },
        validateField(field) {
            // this.$refs.formRef.validateField(field);
        },
        onVideoUploadSuccess(data) {
            this.info.video = data.url;
        },
        close() {
            this.$emit("update:visible", false);
        },
        save() {
            const {
                title,
                brief,
                banner_img_file_list,
                product_img_file_list,
                video_cover_file_list,
                video,
                detail,
            } = this.info;
            const { $apiBasicParams, id } = this.row;
            const params = {
                ...$apiBasicParams,
                id,
                title,
                brief,
                banner_img: banner_img_file_list.join(),
                product_img: product_img_file_list.join(),
                video_cover: video_cover_file_list.join(),
                video,
                detail,
            };
            goodsPoolApi.saveGoodsInfo(params).then((res) => {
                if (res.data.error_code === 0) {
                    this.$emit("load");
                    this.$message.success("操作成功");
                }
            });
        },
        submit() {
            const {
                title,
                brief,
                banner_img_file_list,
                product_img_file_list,
                video_cover_file_list,
                video,
                detail,
            } = this.info;
            if (!title) {
                this.$message.error("请输入商品标题");
                return;
            }
            if (!brief) {
                this.$message.error("请输入一句话介绍");
                return;
            }
            if (this.currStatus === 2) {
                if (!banner_img_file_list.length) {
                    this.$message.error("请上传题图");
                    return;
                }
                if (!product_img_file_list.length) {
                    this.$message.error("请上传产品图");
                    return;
                }
            }
            if (!detail) {
                this.$message.error("请输入商品详情");
                return;
            }
            const { $apiBasicParams, id } = this.row;
            const params = {
                ...$apiBasicParams,
                id,
                title,
                brief,
                banner_img: banner_img_file_list.join(),
                product_img: product_img_file_list.join(),
                video_cover: video_cover_file_list.join(),
                video,
                detail,
            };
            goodsPoolApi.submitGoodsInfo(params).then((res) => {
                if (res.data.error_code === 0) {
                    this.$emit("load");
                    this.$message.success("操作成功");
                    this.close();
                }
            });
        },
        update() {
            const {
                title,
                brief,
                banner_img_file_list,
                product_img_file_list,
                video_cover_file_list,
                video,
                detail,
            } = this.info;
            if (!title) {
                this.$message.error("请输入商品标题");
                return;
            }
            if (!brief) {
                this.$message.error("请输入一句话介绍");
                return;
            }
            if (!banner_img_file_list.length) {
                this.$message.error("请上传题图");
                return;
            }
            if (!product_img_file_list.length) {
                this.$message.error("请上传产品图");
                return;
            }
            if (!detail) {
                this.$message.error("请输入商品详情");
                return;
            }
            const { id } = this.row;
            const params = {
                id,
                title,
                brief,
                banner_img: banner_img_file_list.join(),
                product_img: product_img_file_list.join(),
                video_cover: video_cover_file_list.join(),
                video,
                detail,
            };
            goodsPoolApi.updateGoodsInfo(params).then((res) => {
                if (res.data.error_code === 0) {
                    this.$emit("load");
                    this.$message.success("操作成功");
                    this.close();
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>
