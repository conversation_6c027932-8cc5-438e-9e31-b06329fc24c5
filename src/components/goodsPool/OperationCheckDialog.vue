<template>
    <el-dialog :visible="visible" title="提示" @close="close">
        <el-form
            ref="formRef"
            label-width="100px"
            :model="model"
            :rules="rules"
        >
            <el-form-item label="">
                <el-radio-group v-model="model.status">
                    <el-radio
                        v-for="item in [
                            { value: 3, label: '通过' },
                            { value: 5, label: '延期' },
                        ]"
                        :key="item.value"
                        :label="item.value"
                        style="margin-bottom: 0"
                        >{{ item.label }}</el-radio
                    >
                </el-radio-group>
            </el-form-item>
            <el-form-item
                v-if="model.status === 5"
                label="日期选择"
                prop="delay_time"
            >
                <el-date-picker
                    v-model="model.delay_time"
                    type="datetime"
                    placeholder="请选择日期"
                    value-format="yyyy-MM-dd HH:mm:ss"
                />
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="confirm">通过</el-button>
        </div>
    </el-dialog>
</template>

<script>
import goodsPoolApi from "@/services/goodsPool";

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        row: {
            type: Object,
            default: () => ({}),
        },
    },
    data: () => ({
        model: {
            id: 0,
            status: 3,
            delay_time: "",
        },
        rules: {
            delay_time: [
                {
                    required: true,
                    message: "请选择日期",
                    trigger: "change",
                },
            ],
        },
    }),
    watch: {
        visible(newVal) {
            if (!newVal) return;
            this.model = this.$options.data().model;
            this.$nextTick(() => {
                this.$refs.formRef.clearValidate();
            });
        },
    },
    methods: {
        close() {
            this.$emit("update:visible", false);
        },
        confirm() {
            this.$refs.formRef.validate((valid) => {
                if (!valid) return;
                const { id, $apiBasicParams } = this.row;
                const { status, delay_time } = this.model;
                let params = { ...$apiBasicParams, id, status };
                if (status === 5)
                    params = { ...$apiBasicParams, id, status, delay_time };
                goodsPoolApi.operationCheck(params).then((res) => {
                    if (res.data.error_code === 0) {
                        this.$emit("load");
                        this.$message.success("操作成功");
                        this.close();
                    }
                });
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>
