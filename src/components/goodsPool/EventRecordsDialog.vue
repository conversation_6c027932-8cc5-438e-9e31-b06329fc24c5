<template>
    <el-dialog :visible="visible" title="事项记录" @close="close">
        <el-table :data="list" class="table">
            <el-table-column prop="create_time_text"> </el-table-column>
            <el-table-column prop="name"> </el-table-column>
            <el-table-column prop="operation"> </el-table-column>
            <el-table-column prop="remarks"></el-table-column>
        </el-table>
    </el-dialog>
</template>

<script>
import goodsPoolApi from "@/services/goodsPool";

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        row: {
            type: Object,
            default: () => ({}),
        },
    },
    data: () => ({
        list: [],
    }),
    watch: {
        visible(newVal) {
            if (!newVal) return;
            this.getEventRecords();
        },
    },
    methods: {
        getEventRecords() {
            goodsPoolApi.getEventRecords({ id: this.row.id }).then((res) => {
                if (res.data.error_code === 0) {
                    const { list = [] } = res.data.data;
                    this.list = list;
                }
            });
        },
        close() {
            this.$emit("update:visible", false);
        },
    },
};
</script>

<style lang="scss" scoped>
.table {
    /deep/ .el-table__header-wrapper {
        display: none;
    }
}
</style>
