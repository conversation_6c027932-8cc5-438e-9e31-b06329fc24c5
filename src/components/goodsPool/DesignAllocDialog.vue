<template>
    <el-dialog :visible="visible" :title="title" width="1000px" @close="close">
        <el-form ref="formRef" :model="info" :rules="rules" label-width="100px">
            <template v-if="isMF">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="频道">
                            <el-input
                                v-model="periodsTypeText"
                                disabled
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="采购">
                            <el-input
                                v-model="purchaseName"
                                disabled
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="商品详情" prop="detail">
                    <Tinymce
                        v-if="visible"
                        v-model.trim="info.detail"
                        :height="300"
                        @blur="validateField('detail')"
                    />
                </el-form-item>
            </template>
            <template v-else>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="酒款名" prop="design_title">
                            <el-input
                                v-model="info.design_title"
                                :disabled="disabled"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="类型">
                            <el-input
                                v-model="productCategoryText"
                                disabled
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="频道">
                            <el-input
                                v-model="periodsTypeText"
                                disabled
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="采购">
                            <el-input
                                v-model="purchaseName"
                                disabled
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="中文亮点" prop="design_cn_highlights">
                    <el-input
                        v-model="info.design_cn_highlights"
                        type="textarea"
                        :autosize="{ minRows: 3 }"
                        placeholder="请输入内容"
                        :disabled="disabled"
                    />
                </el-form-item>
                <el-form-item label="评分" prop="design_score">
                    <el-input
                        v-model="info.design_score"
                        type="textarea"
                        :autosize="{ minRows: 3 }"
                        placeholder="请输入内容"
                        :disabled="disabled"
                    />
                </el-form-item>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="风格">
                            <el-input
                                v-model="info.design_style"
                                :disabled="disabled"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="官网">
                            <el-input
                                v-model="info.design_website"
                                :disabled="disabled"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="附件" prop="material_file_list">
                    <vos-oss
                        v-if="visible"
                        :dir="dir"
                        :file-list="info.material_file_list"
                        :limit="99"
                        :fileSize="20"
                        :multiple="false"
                        list-type="text"
                        filesType=""
                        :showFileList="true"
                        :is_download="true"
                        :showName="true"
                        :disabled="disabled"
                    >
                        <el-button type="primary" size="mini"
                            >点击上传</el-button
                        >
                    </vos-oss>
                </el-form-item>
            </template>
            <el-row v-if="!currStatus">
                <el-col :span="12">
                    <el-form-item label="设计师">
                        <el-input
                            v-model="info.creator_name"
                            disabled
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <template v-else>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="题图" prop="banner_img_file_list">
                            <vos-oss
                                v-if="visible"
                                list-type="picture-card"
                                :showFileList="true"
                                :dir="dir"
                                :file-list="info.banner_img_file_list"
                                :limit="1"
                                @on-success="
                                    validateField('banner_img_file_list')
                                "
                            >
                                <i class="el-icon-plus" />
                            </vos-oss>
                        </el-form-item>
                    </el-col>

                    <el-col v-if="isMF" :span="12">
                        <el-form-item
                            label="题图（竖）"
                            prop="horizontal_img_file_list"
                        >
                            <vos-oss
                                v-if="visible"
                                list-type="picture-card"
                                :showFileList="true"
                                :dir="dir"
                                :file-list="info.horizontal_img_file_list"
                                :limit="1"
                                @on-success="
                                    validateField('horizontal_img_file_list')
                                "
                            >
                                <i class="el-icon-plus" />
                            </vos-oss>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item
                    label="产品图（首张为白图）"
                    prop="product_img_file_list"
                >
                    <vos-oss
                        v-if="visible"
                        list-type="picture-card"
                        :showFileList="true"
                        :dir="dir"
                        :file-list="info.product_img_file_list"
                        :limit="8"
                        :multiple="true"
                        @on-success="validateField('product_img_file_list')"
                    >
                        <i class="el-icon-plus" />
                    </vos-oss>
                </el-form-item>
            </template>
        </el-form>
        <div slot="footer">
            <el-button v-if="currStatus === 0" type="primary" @click="alloc"
                >分发</el-button
            >
            <template v-else-if="currStatus === 1">
                <el-button type="primary" @click="save">保存</el-button>
                <el-button type="primary" @click="submit">提交</el-button>
            </template>
            <el-button
                v-else-if="currStatus === 2"
                type="primary"
                @click="update"
                >更新</el-button
            >
        </div>
    </el-dialog>
</template>

<script>
import VosOss from "vos-oss";
import Tinymce from "@/components/Tinymce";
import goodsPoolApi from "@/services/goodsPool";
import { MGoodsType } from "@/utils/mapperModel";

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        row: {
            type: Object,
            default: () => ({}),
        },
    },
    components: {
        VosOss,
        Tinymce,
    },
    data: () => ({
        currStatus: 0, // 0 文案分发 1 设计提交 2 设计修改
        dir: "vinehoo/goods-images/",
        info: {
            id: 0,
            detail: "",
            design_title: "",
            design_cn_highlights: "",
            design_score: "",
            design_style: "",
            design_website: "",
            design_uid: "",
            creator_name: "",
            banner_img: "",
            banner_img_file_list: [],
            horizontal_img: "",
            horizontal_img_file_list: [],
            product_img: "",
            product_img_file_list: [],
            material: "",
            material_file_list: [],
        },
        productCategoryText: "",
        periodsTypeText: "",
        purchaseName: "",
        isMF: false,
        rules: {
            detail: [
                {
                    required: true,
                    message: "请输入商品详情",
                },
            ],
            design_title: [
                {
                    required: true,
                    message: "请输入酒款名",
                },
            ],
            design_cn_highlights: [
                {
                    required: true,
                    message: "请输入中文亮点",
                },
            ],
            design_score: [
                {
                    required: true,
                    message: "请输入评分",
                },
            ],
            banner_img_file_list: [
                {
                    required: true,
                    message: "请上传题图",
                },
            ],
            horizontal_img_file_list: [
                {
                    required: true,
                    message: "请上传题图（竖）",
                },
            ],
            product_img_file_list: [
                {
                    required: true,
                    message: "请上传产品图（首张为白图）",
                },
            ],
            design_uid: [
                {
                    required: true,
                    message: "请选择设计师",
                    trigger: "change",
                },
            ],
        },
    }),
    computed: {
        title({ currStatus }) {
            return currStatus ? "图片上传" : "图片分发";
        },
        disabled({ currStatus }) {
            return !!currStatus;
        },
    },
    watch: {
        visible(newVal) {
            if (!newVal) return;
            const {
                items,
                purchase,
                picture_distribution,
                editor = {},
            } = this.row;
            if (this.currStatus === 0) {
                const { design_uid = "", creator_name = "" } =
                    picture_distribution || {};
                this.info = Object.assign({}, this.$options.data().info, {
                    design_uid,
                    creator_name,
                });
            } else {
                const {
                    design_title = "",
                    design_cn_highlights = "",
                    design_score = "",
                    design_style = "",
                    design_website = "",
                    banner_img = "",
                    horizontal_img = "",
                    product_img = "",
                    material = [],
                } = picture_distribution || {};
                const target = {
                    design_title,
                    design_cn_highlights,
                    design_score,
                    design_style,
                    design_website,
                };
                target.banner_img_file_list = banner_img
                    ? banner_img.split(",")
                    : [];
                target.horizontal_img_file_list = horizontal_img
                    ? horizontal_img.split(",")
                    : [];
                target.product_img_file_list = product_img
                    ? product_img.split(",")
                    : [];
                target.material_file_list = material;
                this.info = Object.assign(
                    {},
                    this.$options.data().info,
                    target
                );
            }
            this.info.design_title = items
                .map(({ product_name }) => product_name)
                .join("+");
            this.productCategoryText = items
                .map(({ product_type_name }) => product_type_name)
                .join("+");
            this.periodsTypeText = this.$options.filters.toText(
                purchase.periods_type,
                "MGoodsTypeText"
            );
            this.purchaseName = purchase.purchase_name;
            this.isMF = purchase.periods_type === MGoodsType.MF;
            if (this.isMF) {
                this.info.detail = editor.detail;
            }
            this.$nextTick(() => {
                this.$refs.formRef.clearValidate();
            });
        },
    },
    methods: {
        validateField(field) {
            this.$refs.formRef.validateField(field);
        },
        close() {
            this.$emit("update:visible", false);
        },
        alloc() {
            this.$refs.formRef.validate((valid) => {
                if (!valid) return;
                const {
                    detail,
                    design_title,
                    design_cn_highlights,
                    design_score,
                    design_style,
                    design_website,
                    design_uid,
                    creator_name,
                    material_file_list,
                } = this.info;
                const { $apiBasicParams, id } = this.row;
                const params = {
                    ...$apiBasicParams,
                    id,
                    design_title,
                    design_cn_highlights,
                    design_score,
                    design_style,
                    design_website,
                    design_uid,
                    creator_name,
                    material: material_file_list.join(),
                };
                if (this.isMF) params.detail = detail;
                goodsPoolApi.allocDesign(params).then((res) => {
                    if (res.data.error_code === 0) {
                        this.$emit("load");
                        this.$message.success("操作成功");
                        this.close();
                    }
                });
            });
        },
        save() {
            const {
                detail,
                design_title,
                design_cn_highlights,
                design_score,
                design_style,
                design_website,
                banner_img_file_list,
                horizontal_img_file_list,
                product_img_file_list,
            } = this.info;
            const { $apiBasicParams, id } = this.row;
            const params = {
                ...$apiBasicParams,
                id,
                design_title,
                design_cn_highlights,
                design_score,
                design_style,
                design_website,
                banner_img: banner_img_file_list.join(),
                horizontal_img: horizontal_img_file_list.join(),
                product_img: product_img_file_list.join(),
            };
            if (this.isMF) params.detail = detail;
            goodsPoolApi.allocDesign(params).then((res) => {
                if (res.data.error_code === 0) {
                    this.$emit("load");
                    this.$message.success("操作成功");
                }
            });
        },
        submit() {
            this.$refs.formRef.validate((valid) => {
                if (!valid) return;
                const {
                    detail,
                    design_title,
                    design_cn_highlights,
                    design_score,
                    design_style,
                    design_website,
                    banner_img_file_list,
                    horizontal_img_file_list,
                    product_img_file_list,
                } = this.info;
                const { $apiBasicParams, id } = this.row;
                const params = {
                    ...$apiBasicParams,
                    id,
                    design_title,
                    design_cn_highlights,
                    design_score,
                    design_style,
                    design_website,
                    banner_img: banner_img_file_list.join(),
                    horizontal_img: horizontal_img_file_list.join(),
                    product_img: product_img_file_list.join(),
                };
                if (this.isMF) params.detail = detail;
                goodsPoolApi.designSubmit(params).then((res) => {
                    if (res.data.error_code === 0) {
                        this.$emit("load");
                        this.$message.success("操作成功");
                        this.close();
                    }
                });
            });
        },
        update() {
            this.$refs.formRef.validate((valid) => {
                if (!valid) return;
                const {
                    detail,
                    design_title,
                    design_cn_highlights,
                    design_score,
                    design_style,
                    design_website,
                    banner_img_file_list,
                    horizontal_img_file_list,
                    product_img_file_list,
                } = this.info;
                const { $apiBasicParams, id } = this.row;
                const params = {
                    ...$apiBasicParams,
                    id,
                    design_title,
                    design_cn_highlights,
                    design_score,
                    design_style,
                    design_website,
                    banner_img: banner_img_file_list.join(),
                    horizontal_img: horizontal_img_file_list.join(),
                    product_img: product_img_file_list.join(),
                };
                if (this.isMF) params.detail = detail;
                goodsPoolApi.allocDesign(params).then((res) => {
                    if (res.data.error_code === 0) {
                        this.$emit("load");
                        this.$message.success("操作成功");
                        this.close();
                    }
                });
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>
