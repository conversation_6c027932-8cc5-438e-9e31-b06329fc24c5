<template>
    <div>
      <!-- 是编辑的时候 -->

      <!-- 新增的时候 -->
      <div class="add">
        <el-form-item label="添加商品" prop="path_id" label-width="150px">
          <el-radio v-model="add_methodnew" :label="0">手动</el-radio>
          <el-radio v-model="add_methodnew" :label="1">自动</el-radio>
        </el-form-item>
        <div v-if="add_methodnew==1">  <el-form-item label="" prop="path_id" label-width="150px">
          <el-checkbox-group v-model="auto_add_typeNew" @change="changeAutoAddType">
            <el-checkbox :label="1">国家</el-checkbox>
            <el-checkbox :label="2">产区</el-checkbox>
            <el-checkbox :label="3">酒庄</el-checkbox>
            <!-- <el-checkbox :label="4">标签</el-checkbox> -->
            <el-checkbox :label="5">类型</el-checkbox>
            <el-checkbox :label="6">标题</el-checkbox>
            <el-checkbox :label="7">葡萄品种</el-checkbox>
            <el-checkbox :label="8">库存</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="定义内容" prop="path_id" label-width="150px">
          <!-- 国家选择 -->
          <div v-if="auto_add_typeNew.includes(1)" style="margin-bottom: 10px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">国家:</label>
            <el-select
              v-model="chooseOptions[1]"
              filterable
              style="width: 280px"
              multiple
              remote
              clearable
              reserve-keyword
              placeholder="请输入国家"
              :remote-method="getCountryList"
              @change="countryChange"
            >
              <el-option
                v-for="item in CountryOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>

          <!-- 产区选择 -->
          <div v-if="auto_add_typeNew.includes(2)" style="margin-bottom: 10px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">产区:</label>
            <el-select
              v-model="chooseOptions[2]"
              filterable
              remote
              multiple
              style="width: 280px"
              clearable
              reserve-keyword
              placeholder="请输入产区"
              :remote-method="getRegionList"
            >
              <el-option
                v-for="item in RegionOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>

          <!-- 酒庄选择 -->
          <div v-if="auto_add_typeNew.includes(3)" style="margin-bottom: 10px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">酒庄:</label>
            <el-select
              v-model="chooseOptions[3]"
              filterable
              style="width: 280px"
              clearable
              remote
              reserve-keyword
              placeholder="请输入酒庄"
              :remote-method="getWineryList"
              multiple
            >
              <el-option
                v-for="item in WineryOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>

          <!-- 标签选择 -->
          <div v-if="auto_add_typeNew.includes(4)" style="margin-bottom: 10px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">标签:</label>
            <el-select
              v-model="chooseOptions[4]"
              filterable
              style="width: 280px"
              clearable
              remote
              placeholder="请输入标签"
              multiple
            >
              <el-option
                v-for="item in tagOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>

          <!-- 类型选择 -->
          <div v-if="auto_add_typeNew.includes(5)" style="margin-bottom: 10px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">类型:</label>
            <el-cascader
              :options="typeList"
              v-model="chooseOptions[5]"
              filterable
              style="width: 280px"
              :show-all-levels="false"
              :props="{
                  label: 'name',
                  value: 'id',
                  checkStrictly: true,
                  multiple: true,
                  emitPath:false
              }"
            >
            </el-cascader>
          </div>

          <!-- 标题选择 -->
          <div v-if="auto_add_typeNew.includes(6)" style="margin-bottom: 10px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">标题:</label>
            <el-input
              class="input-new-tag"
              v-if="inputVisible"
              v-model="inputValue"
              ref="saveTagInput"
              size="small"
              @keyup.enter.native="handleInputConfirm"
              @blur="handleInputConfirm"
            >
            </el-input>
            <el-button v-else style="width: 180px;" size="small" @click="showInput">请输入标题</el-button>
            <el-tag
              :key="tag"
              v-for="tag in (chooseOptions[6] || [])"
              closable
              :disable-transitions="false"
              @close="handleClose(tag, 6)">
              {{tag}}
            </el-tag>
          </div>

          <!-- 葡萄品种选择 -->
          <div v-if="auto_add_typeNew.includes(7)" style="margin-bottom: 10px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">葡萄品种:</label>
            <el-select
              v-model="chooseOptions[7]"
              filterable
              style="width: 280px"
              clearable
              remote
              reserve-keyword
              placeholder="请输入葡萄品种"
              :remote-method="getGrapeList"
              multiple
            >
              <el-option
                v-for="item in GrapeOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>

          <!-- 库存选择 -->
          <div v-if="auto_add_typeNew.includes(8)" style="margin-bottom: 10px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">库存:</label>
            <el-input-number
              size="large"
              v-model="chooseOptions[8]"
              :min="1"
              :step="1"
              :step-strictly="true"
              placeholder="请输入库存"
            >
            </el-input-number>
          </div>
        </el-form-item></div>

      </div>
    </div>
  </template>

  <script>
  export default {
    props: ["add_method", "auto_add_type", "auto_add_content", "isEdit"],

    data() {
      return {
        add_methodnew: 0,
        auto_add_typeNew: [], // 改为数组，支持多选
        CountryOptions: [], //搜索国家列表
        RegionOptions: [], //搜索产区列表
        WineryOptions: [], //搜索酒庄列表
        GrapeOptions:[],//葡萄品种
        tagOptions: [], //标签列表
        typeList: [], //类型列表
        chooseOptions: {}, //改为对象，按类型分组存储选中的列表
        AllCountryOptions: [], //已搜索国家列表
        AllRegionOptions: [], //已搜索产区列表
        AllWineryOptions: [], //已搜索酒庄列表
        AllGrapeOptions: [], //搜索过的所有葡萄品种
        inputVisible: false,
        inputValue: '',
        originTypeList:[],
      };
    },
    watch: {},

    mounted() {
      setTimeout(() => {
        if(this.isEdit){
         this.add_methodnew = this.add_method;

         // 处理新的数据结构
         if (Array.isArray(this.auto_add_type)) {
           // 新格式：auto_add_type 是数组
           this.auto_add_typeNew = this.auto_add_type.map(item => parseInt(item));

           // auto_add_content 是对象格式
           if (this.auto_add_content && typeof this.auto_add_content === 'object') {
             Object.keys(this.auto_add_content).forEach(type => {
               const typeNum = parseInt(type);
               const content = this.auto_add_content[type];

               if (typeNum === 6) {
                 // 标题类型，存储名称
                 this.chooseOptions[typeNum] = content.map(item => item.name);
               } else if (typeNum === 8) {
                 // 库存类型，存储数值
                 this.chooseOptions[typeNum] = content[0] ? parseInt(content[0].name) : 1;
               } else {
                 // 其他类型，存储ID
                 this.chooseOptions[typeNum] = content.map(item => item.id);
               }

               // 设置对应的选项列表
               if (typeNum === 1) {
                 this.CountryOptions = content;
                 this.AllCountryOptions = content;
               } else if (typeNum === 2) {
                 this.RegionOptions = content;
                 this.AllRegionOptions = content;
               } else if (typeNum === 3) {
                 this.WineryOptions = content;
                 this.AllWineryOptions = content;
               } else if (typeNum === 7) {
                 this.GrapeOptions = content;
                 this.AllGrapeOptions = content;
               }
             });
           }
         } else {
           // 兼容旧格式：auto_add_type 是单个值
           this.auto_add_typeNew = this.auto_add_type ? [this.auto_add_type] : [];

           if (this.auto_add_content && Array.isArray(this.auto_add_content)) {
             const typeNum = this.auto_add_type;
             if (typeNum === 6) {
               this.chooseOptions[typeNum] = this.auto_add_content.map(item => item.name);
             } else if (typeNum === 8) {
               this.chooseOptions[typeNum] = this.auto_add_content[0] ? parseInt(this.auto_add_content[0].name) : 1;
             } else {
               this.chooseOptions[typeNum] = this.auto_add_content.map(item => item.id);
             }

             // 设置对应的选项列表
             if (typeNum === 1) {
               this.CountryOptions = this.auto_add_content;
               this.AllCountryOptions = this.auto_add_content;
             } else if (typeNum === 2) {
               this.RegionOptions = this.auto_add_content;
               this.AllRegionOptions = this.auto_add_content;
             } else if (typeNum === 3) {
               this.WineryOptions = this.auto_add_content;
               this.AllWineryOptions = this.auto_add_content;
             } else if (typeNum === 7) {
               this.GrapeOptions = this.auto_add_content;
               this.AllGrapeOptions = this.auto_add_content;
             }
           }
         }

         console.log('编辑数据初始化:', {
           auto_add_typeNew: this.auto_add_typeNew,
           chooseOptions: this.chooseOptions,
           auto_add_content: this.auto_add_content
         });
      }
              },500);

      this.getProductCategory();
    //   this.getRecommendLabel();
      // if (this.isEdit) {
      //     setTimeout(() => {
      //         console.log("传过来的rowData", this.rowData);
      //         if (this.rowData.path != undefined) {
      //             this.path = this.rowData.path;
      //         } else {
      //             this.path = this.rowData.path_id;
      //         }
      //         if (typeof this.rowData.client[0] == "string") {
      //             this.EditClient = this.rowData.client.split(",");
      //         } else {
      //             this.EditClient = this.rowData.client.map(item => {
      //                 return String(item.id);
      //             });
      //         }
      //         console.log("popip", this.pathDetail);
      //         this.isDisableClient = JSON.parse(
      //             JSON.stringify(
      //                 this.pathDetail.find(item => item.id == this.path)
      //             )
      //         ).client.join(",");
      //         // this.isDisableClient = JSON.stringify(this.EditClient);
      //         // this.isDisableClient = JSON.parse(this.isDisableClient);
      //         if (this.rowData.ad_path_value_param != undefined) {
      //             this.EditParam = this.rowData.ad_path_value_param;
      //         } else {
      //             this.EditParam = this.rowData.la_path_value_param;
      //         }
      //     }, 1000);
      // }
      // this.getClientPath();
      // this.pathNum = this.path_id; //路径id
    },
    methods: {
      changeAutoAddType(value) {
        console.log('选择的类型:', value);
        this.auto_add_typeNew = value;

        // 初始化新选择类型的数据
        value.forEach(type => {
          if (!this.chooseOptions[type]) {
            if (type === 6) {
              // 标题类型初始化为数组
              this.chooseOptions[type] = [];
            } else if (type === 8) {
              // 库存类型初始化为数字
              this.chooseOptions[type] = 1;
            } else {
              // 其他类型初始化为数组
              this.chooseOptions[type] = [];
            }
          }
        });

        // 清理未选择类型的数据
        Object.keys(this.chooseOptions).forEach(type => {
          if (!value.includes(parseInt(type))) {
            delete this.chooseOptions[type];
          }
        });
      },
      //国家
      getCountryList(keyword) {
        console.log(keyword);
        if (keyword) {
          this.$request.recommendLabel.getCountryList({
            keyword,
            page: 1,
            limit: 10,
          }).then((res) => {
            if (res.data.error_code == 0) {
              this.CountryOptions = res.data.data.list.map((item) => {
                  return {
                      id: item.id,
                      name: item.country_name_cn + item.country_name_en
                  };
                });

              this.CountryOptions.forEach(item => {
              const existingItem = this.AllCountryOptions.find(bItem => bItem.id === item.id);
              if (!existingItem) {
                this.AllCountryOptions.push(item);
              }
            });
            }
          });
        }
      },
      //酒庄
      getWineryList(keyword) {
        if (keyword) {
          this.$request.recommendLabel
            .getWineryList({
              keyword,
              page: 1,
              limit: 10,
            })
            .then((res) => {
              if (res.data.error_code == 0) {
                this.WineryOptions = res.data.data.list.map((item) => {
                  return {
                      id: item.id,
                      name:  item.winery_name_cn + item.winery_name_en
                  };
                });
                this.WineryOptions.forEach(item => {
              const existingItem = this.AllWineryOptions.find(bItem => bItem.id === item.id);
              if (!existingItem) {
                this.AllWineryOptions.push(item);
              }
            });
              }
            });
        }
      },
      getGrapeList(keyword) {
            if (keyword) {
                this.$request.recommendLabel
                    .getGrapeList({
                        keyword,
                        page: 1,
                        limit: 99,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                          this.GrapeOptions = res.data.data.list.map((item) => {
                          return {
                              id: item.id,
                              name:  item.gname_cn + item.gname_en
                          };
                        });
                        this.GrapeOptions.forEach(item => {
                      const existingItem = this.AllGrapeOptions.find(bItem => bItem.id === item.id);
                      if (!existingItem) {
                        this.AllGrapeOptions.push(item);
                      }
                    });
                        }
                    });
            }
        },
      //产区
      getRegionList(keyword) {
        if (keyword) {
          this.$request.recommendLabel
            .getRegionList({
              keyword,
              page: 1,
              limit: 10,
            })
            .then((res) => {
              if (res.data.error_code == 0) {
                this.RegionOptions = res.data.data.list.map((item) => {
                  return {
                      id: item.id,
                      name: item.regions_name_cn + item.regions_name_en
                  };
                });
                this.RegionOptions.forEach(item => {
              const existingItem = this.AllRegionOptions.find(bItem => bItem.id === item.id);
              if (!existingItem) {
                this.AllRegionOptions.push(item);
              }
            });
              }
            });
        }
      },
      getProductCategory() {
              return new Promise((resolve, reject) => {
                  this.$request.recommendLabel
                      .getProductCategory({
                          pid: 1,
                      })
                      .then((res) => {
                          if (res.data.error_code == 0) {
                              this.originTypeList = res.data.data.list;
                              const list = res.data.data.list.map((item) => ({
                                  ...item,
                              }));
                              const info = list.reduce((map, node) => {
                                  map[node.id] = node;
                                  if (list.some((item) => item.fid === node.id)) {
                                      node.children = [];
                                  }
                                  return map;
                              }, {});
                              this.typeList = list.filter((node) => {
                                  info[node.fid] &&
                                      info[node.fid].children.push(node);
                                  return !node.fid;
                              });
                              console.log("typeList", this.typeList);

                              resolve("ok");
                          }
                      })
                      .catch((error) => {
                          reject(error);
                      });
              });
          },

          getRecommendLabel() {
              this.$request.recommendLabel
                  .labelList({ page: 1, limit: 999, type: 2 })
                  .then((res) => {
                      if (res.data.error_code === 0) {
                          this.tagOptions = res.data.data.list;
                      }
                  });
          },

          handleClose(tag, type) {
          if (this.chooseOptions[type] && Array.isArray(this.chooseOptions[type])) {
            const index = this.chooseOptions[type].indexOf(tag);
            if (index > -1) {
              this.chooseOptions[type].splice(index, 1);
            }
          }
        },
        countryChange(data){
          console.log('changeCount', data);
        },
        showInput() {
          this.inputVisible = true;
          this.$nextTick(_ => {
            this.$refs.saveTagInput.$refs.input.focus();
          });
        },

        handleInputConfirm() {
          let inputValue = this.inputValue;
          if (inputValue) {
            if (!this.chooseOptions[6]) {
              this.chooseOptions[6] = [];
            }
            this.chooseOptions[6].push(inputValue);
          }
          this.inputVisible = false;
          this.inputValue = '';
        },

      // //编辑时返回的数据
      getEditData() {
        let add_methodnew = this.add_methodnew;
        let auto_add_typeNew = this.auto_add_typeNew;

        // 新的数据格式
        let auto_add_type_result = auto_add_typeNew.map(item => item.toString());
        let auto_add_content_result = {};

        // 遍历每个选中的类型
        auto_add_typeNew.forEach(type => {
          const typeStr = type.toString();
          const selectedOptions = this.chooseOptions[type];

          if (!selectedOptions) {
            auto_add_content_result[typeStr] = [];
            return;
          }

          let resultArr = [];

          if (type === 1) {
            // 国家
            resultArr = selectedOptions.map(item => {
              let found = this.AllCountryOptions.find(bItem => bItem.id == item);
              return found ? { id: item, name: found.name } : { id: item, name: item };
            });
          } else if (type === 2) {
            // 产区
            resultArr = selectedOptions.map(item => {
              let found = this.AllRegionOptions.find(bItem => bItem.id == item);
              return found ? { id: item, name: found.name } : { id: item, name: item };
            });
          } else if (type === 3) {
            // 酒庄
            resultArr = selectedOptions.map(item => {
              let found = this.AllWineryOptions.find(bItem => bItem.id == item);
              return found ? { id: item, name: found.name } : { id: item, name: item };
            });
          } else if (type === 4) {
            // 标签
            resultArr = selectedOptions.map(item => {
              let found = this.tagOptions.find(bItem => bItem.id == item);
              return found ? { id: item, name: found.name } : { id: item, name: item };
            });
          } else if (type === 5) {
            // 类型
            resultArr = selectedOptions.map(item => {
              let found = this.originTypeList.find(bItem => bItem.id == item);
              return found ? { id: item, name: found.name } : { id: item, name: item };
            });
          } else if (type === 6) {
            // 标题
            resultArr = selectedOptions.map(item => {
              return { name: item, id: 0 };
            });
          } else if (type === 7) {
            // 葡萄品种
            resultArr = selectedOptions.map(item => {
              let found = this.AllGrapeOptions.find(bItem => bItem.id == item);
              return found ? { id: item, name: found.name } : { id: item, name: item };
            });
          } else if (type === 8) {
            // 库存
            resultArr = [{ name: selectedOptions.toString(), id: 0 }];
          }

          auto_add_content_result[typeStr] = resultArr.filter(item => item !== null);
        });

        console.log('返回的数据:', {
          add_methodnew,
          auto_add_type: auto_add_type_result,
          auto_add_content: auto_add_content_result
        });

        return {
          add_methodnew,
          auto_add_typeNew: auto_add_type_result,
          resultArr: auto_add_content_result
        };
      }

    },
  };
  </script>

  <style lang="scss" scoped>
  /deep/ .el-form-item {
    margin-bottom: 5px;
  }
  </style>

