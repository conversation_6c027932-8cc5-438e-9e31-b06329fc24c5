<template>
  <div class="right-side-menu" :class="{ collapsed: isCollapsed }">
    <div class="menu-toggle" @click="toggleMenu">
      <i :class="isCollapsed ? 'el-icon-arrow-left' : 'el-icon-arrow-right'"></i>
    </div>
    <div class="menu-content">
      <el-tooltip :content="isCollapsed ? '实时销售数据' : ''" placement="left" :disabled="!isCollapsed">
        <div class="menu-item" @click="handleMenuClick('realtime-sales')">
          <i class="el-icon-data-line"></i>
          <span v-show="!isCollapsed">实时销售数据</span>
          <i class="el-icon-right" v-show="!isCollapsed"></i>
        </div>
      </el-tooltip>
      <el-tooltip :content="isCollapsed ? '搜索商品' : ''" placement="left" :disabled="!isCollapsed">
        <div class="menu-item" @click="handleMenuClick('search-goods')">
          <i class="el-icon-search"></i>
          <span v-show="!isCollapsed">搜索商品</span>
          <i class="el-icon-right" v-show="!isCollapsed"></i>
        </div>
      </el-tooltip>
    </div>
  </div>
</template>

<script>
import Cookies from 'js-cookie'

const MENU_STATE_KEY = 'right_menu_collapsed'

export default {
  name: 'RightSideMenu',
  data() {
    return {
      isCollapsed: false
    }
  },
  created() {
    // 从cookie中读取状态
    const savedState = Cookies.get(MENU_STATE_KEY)
    if (savedState !== undefined) {
      this.isCollapsed = savedState === 'true'
    }
  },
  methods: {
    toggleMenu() {
      this.isCollapsed = !this.isCollapsed
      // 保存状态到cookie，有效期30天
      Cookies.set(MENU_STATE_KEY, this.isCollapsed.toString(), { expires: 30 })
    },
    handleMenuClick(menuItem) {
      this.$emit('menu-click', menuItem)
    }
  }
}
</script>

<style scoped>
.right-side-menu {
  position: fixed;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background-color: #fff;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  transition: width 0.3s;
  width: 200px;
  z-index: 1000;
  border-radius: 4px 0 0 4px;
}

.right-side-menu.collapsed {
  width: 50px;
}

.menu-toggle {
  position: absolute;
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 40px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  border-radius: 4px 0 0 4px;
}

.menu-content {
  padding: 10px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
  margin-bottom: 5px;
}

.menu-item:hover {
  background-color: #f5f7fa;
}

.menu-item i {
  font-size: 18px;
  margin-right: 10px;
}

.menu-item .el-icon-right {
  position: absolute;
  right: 10px;
  margin-right: 0;
  font-size: 14px;
  opacity: 0.5;
}

.menu-item span {
  white-space: nowrap;
  flex: 1;
}
</style> 