<template>
    <el-dialog title="备注列表" :visible="visible" :before-close="closeDialog">
        <el-button size="mini" type="warning" @click="dialogVisible = true"
            >新增</el-button
        >
        <el-table border size="mini" :data="list" class="mgt-20">
            <el-table-column prop="id" label="ID" align="center" />
            <el-table-column prop="remark" label="备注" align="center" />
            <el-table-column prop="creator" label="操作人" align="center" />
            <el-table-column
                prop="created_time"
                label="操作时间"
                align="center"
            />
        </el-table>
        <el-row type="flex" justify="center" class="mgt-20">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </el-row>

        <el-dialog
            v-if="dialogVisible"
            :visible.sync="dialogVisible"
            title="新增备注"
            append-to-body
        >
            <el-form ref="formRef" :model="model" :rules="rules">
                <el-form-item prop="remark">
                    <el-input
                        v-model="model.remark"
                        type="textarea"
                        placeholder="请输入备注"
                        :autosize="{ minRows: 2 }"
                    />
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="onAdd">确定</el-button>
            </div>
        </el-dialog>
    </el-dialog>
</template>

<script>
import followProductPlatformApi from "@/services/followProductPlatform";

export default {
    props: {
        visible: {
            default: false,
            type: Boolean,
        },
        id: {
            type: Number,
            default: 0,
        },
    },
    data: () => ({
        query: {
            page: 1,
            limit: 10,
            id: 0,
        },
        list: [],
        total: 0,
        dialogVisible: false,
        model: {
            id: 0,
            remark: "",
        },
        rules: {
            remark: [
                {
                    required: true,
                    message: "请输入备注",
                    trigger: "blur",
                },
            ],
        },
    }),
    created() {
        this.query.id = this.id;
        this.model.id = this.id;
        this.load();
    },
    methods: {
        closeDialog() {
            this.$emit("update:visible", false);
        },
        load() {
            followProductPlatformApi.getRemarkList(this.query).then((res) => {
                if (res.data.error_code == 0) {
                    const { list = [], total = 0 } = res.data.data;
                    this.list = list;
                    this.total = total;
                }
            });
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
        onAdd() {
            this.$refs.formRef.validate((valid) => {
                if (!valid) return;
                followProductPlatformApi.addRemark(this.model).then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message.success("添加成功");
                        this.$emit("load");
                        this.load();
                        this.dialogVisible = false;
                    }
                });
            });
        },
    },
};
</script>
<style lang="scss" scoped></style>
