<template>
    <el-dialog :visible="visible" :before-close="closeDialog">
        <el-row type="flex" justify="center">
            <vos-oss
                v-if="visible"
                :dir="dir"
                :file-list="fileList"
                :limit="1"
                :fileSize="10"
                :multiple="true"
                list-type="text"
                filesType=""
                :showFileList="true"
                :is_download="true"
                drag
            >
                <div class="el-upload-dragger">
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text"><em>点击上传</em></div>
                </div>
            </vos-oss>
        </el-row>
        <el-row slot="footer" type="flex" justify="space-between">
            <el-button type="primary" @click="onDownload">下载模版</el-button>
            <div>
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="onConfirm">确定</el-button>
            </div>
        </el-row>
    </el-dialog>
</template>

<script>
import VosOss from "vos-oss";
import followProductPlatformApi from "@/services/followProductPlatform";

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        kanban_type: {
            type: Number,
            default: 0,
        },
    },
    components: {
        VosOss,
    },
    data: () => ({
        dir: "vinehoo/vos/orders/tempStorage",
        fileList: [],
    }),
    methods: {
        closeDialog() {
            this.$emit("update:visible", false);
        },
        onConfirm() {
            if (!this.fileList.length) {
                this.$message.error("请先上传文件");
                return;
            }
            followProductPlatformApi
                .importFollowList({ file: this.fileList.join(","),kanban_type:this.kanban_type })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message.success("导入成功");
                        this.$emit("load");
                        this.closeDialog();
                    }
                });
        },
        onDownload() {
            window.open(
                "https://images.vinehoo.com/vinehoo/vos/commodities/%20%E4%BA%A7%E5%93%81%E9%94%80%E5%94%AE%E8%B7%9F%E8%BF%9B%E5%85%B3%E6%B3%A8%E7%9C%8B%E6%9D%BF%20%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx"
            );
        },
    },
};
</script>

<style lang="scss" scoped></style>
