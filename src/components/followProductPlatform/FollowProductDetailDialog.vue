<template>
    <el-dialog
        :visible="visible"
        title="添加跟进产品"
        :before-close="closeDialog"
    >
        <el-form
            ref="formRef"
            :model="model"
            :rules="rules"
            label-width="120px"
        >
            <el-form-item label="简码" prop="short_code">
                <el-input
                    v-model="model.short_code"
                    placeholder="请输入简码"
                    class="mgr-10 w-large"
                    @keyup.enter.native="onSearchInfo"
                ></el-input>
                <el-button type="primary" @click="onSearchInfo">查询</el-button>
            </el-form-item>
            <template v-if="info">
                <el-form-item label="销售期数">
                    <div
                        v-for="item in info.period"
                        :key="item.period"
                        class="mgb-10"
                    >
                        <el-link
                            :underline="false"
                            @click="onCopy(item.period)"
                        >
                            {{ item.period }}
                        </el-link>
                        <el-button
                            :type="item.onsaleStatusBtnType"
                            class="mgl-10"
                            >{{ item.periodTypeText }}
                            {{ item.onsaleStatusText }}</el-button
                        >
                    </div>
                </el-form-item>
                <el-form-item label="最新一期供应商">
                    {{ info.supplier }}
                </el-form-item>
                <el-form-item label="采购信息">
                    {{ info.importTypeText }}
                    {{ info.buyer_name }}
                </el-form-item>
                <el-form-item label="产品信息">
                    <el-form label-width="100px" label-position="left">
                        <el-form-item label="酒庄">{{
                            info.winery_name_cn
                        }}</el-form-item>
                        <el-form-item label="国家">{{
                            info.country_name_cn
                        }}</el-form-item>
                        <el-form-item label="产品类型">{{
                            info.product_type_name
                        }}</el-form-item>
                        <el-form-item label="中文名">{{
                            info.cn_product_name
                        }}</el-form-item>
                        <el-form-item label="英文名">{{
                            info.en_product_name
                        }}</el-form-item>
                    </el-form>
                </el-form-item>
                <el-form-item label="商品套餐信息">
                    <el-form label-width="100px" label-position="left">
                        <el-form-item
                            v-for="item in info.packages"
                            :key="item.id"
                            :label="item.package_name"
                            >{{ item.price }}</el-form-item
                        >
                    </el-form>
                </el-form-item>
                <el-form-item label="采购数量" prop="purchase_nums">
                    <el-input
                        v-model="model.purchase_nums"
                        placeholder="请输入采购数量"
                        class="mgr-10 w-large"
                    ></el-input>
                    <el-button type="primary"
                        >萌牙库存：{{ info.my_inventory }}</el-button
                    >
                    <el-button type="warning"
                        >已售瓶数：{{
                            info.real_sale_num
                        }}（不含退款）</el-button
                    >
                </el-form-item>
                <el-form-item label="运营" prop="operation_id">
                    <el-select
                        v-model="model.operation_id"
                        placeholder="请选择运营"
                        @change="onOperationIdChange"
                    >
                        <el-option
                            v-for="item in operationOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="计划完成期限"
                    prop="planned_completion_time"
                >
                    <el-date-picker
                        v-model="model.planned_completion_time"
                        type="date"
                        placeholder="请选择计划完成期限"
                        value-format="yyyy-MM-dd"
                    ></el-date-picker>
                </el-form-item>
            </template>
        </el-form>
        <div slot="footer">
            <el-button @click="closeDialog">取消</el-button>
            <el-button type="primary" @click="onConfirm">确定</el-button>
        </div>
    </el-dialog>
</template>

<script>
import followProductPlatformApi from "@/services/followProductPlatform";
import copy from "copy-to-clipboard";

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        followProductDetail: {
            type: Object,
            default: null,
        },
        kanban_type: {
            type: Number,
            default: 0,
        },
    },
    data: () => ({
        operationOptions: [],
        model: {
            short_code: "",
            purchase_nums: "",
            operation_id: "",
            operation_name: "",
            planned_completion_time: "",
        },
        rules: {
            short_code: [{ required: true, message: "请输入简码" }],
            purchase_nums: [
                { required: true, message: "请输入采购数量" },
                {
                    pattern: /^[1-9]\d*$/,
                    message: "请输入正确的采购数量",
                },
            ],
            operation_id: [{ required: true, message: "请选择运营" }],
            planned_completion_time: [
                { required: true, message: "请选择计划完成期限" },
            ],
        },
        info: null,
    }),
    created() {
        if (this.followProductDetail) {
            this.model = { ...this.followProductDetail };
            this.onSearchInfo();
        }
        this.initOperationOptions();
    },
    methods: {
        closeDialog() {
            this.$emit("update:visible", false);
        },
        onConfirm() {
            if (!this.info) {
                this.$message.error("请先查询信息");
                return;
            }
            if (this.model.short_code !== this.info.short_code) {
                this.$message.error("简码与查询信息不匹配");
                return;
            }
            this.model.kanban_type = this.kanban_type;
            this.$refs.formRef.validate((valid) => {
                if (!valid) return;
                const params = Object.assign({}, this.info, this.model);
                followProductPlatformApi.addFollowItem(params).then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功");
                        this.$emit("load");
                        this.closeDialog();
                    }
                });
            });
        },
        onSearchInfo() {
            if (!this.model.short_code) {
                this.$message.error("请输入简码");
                return;
            }
            followProductPlatformApi
                .searchInfoByShortCode({ short_code: this.model.short_code,
                    kanban_type:this.kanban_type })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        const info = res.data.data;
                        const importTypeText =
                            { 0: "自采", 1: "地采", 2: "跨境" }[
                                info.import_type
                            ] || "";
                        info.period?.forEach((item) => {
                            item.periodTypeText =
                                {
                                    0: "闪购",
                                    1: "秒发",
                                    2: "跨境",
                                    3: "尾货",
                                }[item.periods_type] || "";
                            item.onsaleStatusBtnType =
                                item.onsale_status === 2 ? "danger" : "info";
                            item.onsaleStatusText =
                                { 2: "在售" }[item.onsale_status] || "下架";
                        });
                        Object.assign(info, { importTypeText });
                        this.info = info;
                    }
                });
        },
        onCopy(data) {
            copy(data);
            this.$message.success("复制成功");
        },
        onOperationIdChange() {
            this.model.operation_name =
                this.operationOptions.find(
                    (item) => item.value === this.model.operation_id
                )?.label || "";
        },
        initOperationOptions() {
            this.$request.article
                .purchaseList({
                    type: 14,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        const { list = [] } = res.data.data;
                        this.operationOptions = list.map(
                            ({ id, realname }) => ({
                                value: id,
                                label: realname,
                            })
                        );
                    }
                });
        },
    },
};
</script>

<style lang="scss" scoped></style>
