<template>
    <div>
        <el-dialog
            title="批量标签编辑"
            :visible.sync="batchTagVisible"
            width="40%"
            :append-to-body="true"
            @close="closeBatchTag"
            :close-on-click-modal="false"
        >
            <div class="batch-tag-info">
                <p>
                    已选择
                    <span class="selected-count">{{ selectedCount }}</span>
                    个商品
                </p>
            </div>
            <el-form :inline="false" size="mini">
                <el-form-item label="操作类型">
                    <el-radio-group
                        v-model="operationType"
                        @change="onOperationTypeChange"
                    >
                        <el-radio label="add">添加标签</el-radio>
                        <el-radio label="delete">删除标签</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item
                    v-if="operationType === 'add'"
                    label="选择要添加的标签"
                >
                    <el-select
                        v-model="selectedTags"
                        placeholder="请选择商品标签"
                        multiple
                        filterable
                        style="width: 100%"
                    >
                        <el-option
                            v-for="item in goodTagOption"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item
                    v-if="operationType === 'delete'"
                    label="选择要删除的标签"
                >
                    <el-select
                        v-model="selectedDeleteTags"
                        placeholder="请选择要删除的标签"
                        multiple
                        filterable
                        style="width: 100%"
                    >
                        <el-option
                            v-for="item in availableDeleteTags"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                    <div
                        class="delete-tags-hint"
                        v-if="availableDeleteTags.length === 0"
                    >
                        <span style="color: #999; font-size: 12px">
                            所选商品没有标签可删除
                        </span>
                    </div>
                </el-form-item>
            </el-form>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button size="mini" @click="batchTagVisible = false"
                    >取消</el-button
                >
                <el-button
                    size="mini"
                    type="primary"
                    @click="confirmBatchOperation"
                    :disabled="!canConfirm"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "BatchGoodTags",

    data() {
        return {
            batchTagVisible: false,
            selectedTags: [],
            selectedDeleteTags: [],
            goodTagOption: [],
            availableDeleteTags: [],
            selectedIds: [],
            selectedCount: 0,
            operationType: "add", // 'add' 或 'delete'
        };
    },

    computed: {
        canConfirm() {
            if (this.operationType === "add") {
                return this.selectedTags.length > 0;
            } else {
                return this.selectedDeleteTags.length > 0;
            }
        },
    },

    mounted() {
        this.getRecommendLabel();
    },

    methods: {
        closeBatchTag() {
            this.selectedTags = [];
            this.selectedDeleteTags = [];
            this.availableDeleteTags = [];
            this.selectedIds = [];
            this.selectedCount = 0;
            this.operationType = "add";
            this.batchTagVisible = false;
        },
        getRecommendLabel() {
            this.$request.recommendLabel
                .labelList({ page: 1, limit: 999, type: 2 })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.goodTagOption = res.data.data.list;
                    }
                });
        },

        // 操作类型改变时的处理
        onOperationTypeChange() {
            this.selectedTags = [];
            this.selectedDeleteTags = [];
            if (this.operationType === "delete") {
                this.getAvailableDeleteTags();
            }
        },

        // 获取可删除的标签（选中商品的全部标签，去重）
        getAvailableDeleteTags() {
            if (this.selectedIds.length === 0) {
                this.availableDeleteTags = [];
                return;
            }

            // 收集所有选中商品的标签
            let allTags = [];
            this.selectedIds.forEach((item) => {
                if (
                    item.label_arr &&
                    Array.isArray(item.label_arr) &&
                    item.label_arr.length > 0
                ) {
                    let tagIds = item.label_arr.map((tag) => {
                        // 确保ID是字符串格式，便于后续比较
                        return String(tag.id);
                    });
                    allTags.push(...tagIds);
                }
            });

            if (allTags.length === 0) {
                this.availableDeleteTags = [];
                return;
            }

            // 对所有标签ID进行去重
            let uniqueTagIds = [...new Set(allTags)];

            // 根据标签ID获取标签详情
            this.availableDeleteTags = this.goodTagOption.filter((tag) => {
                let tagIdStr = String(tag.id);
                return uniqueTagIds.includes(tagIdStr);
            });
        },
        // 统一的批量操作确认方法
        confirmBatchOperation() {
            if (this.selectedIds.length === 0) {
                this.$message.error("未选择任何商品");
                return;
            }

            if (this.operationType === "add") {
                this.confirmAddBatchTag();
            } else {
                this.confirmDeleteBatchTag();
            }
        },

        // 批量添加标签
        confirmAddBatchTag() {
            if (this.selectedTags.length === 0) {
                this.$message.error("请至少选择一个标签");
                return;
            }

            const label = this.selectedTags.join(",");
            const ids = this.selectedIds.map((item) => item.id).join(",");

            // 使用批量更新接口11
            this.$request.recommendLabel
                .batchGoodLabelUpdate({
                    id: ids,
                    label: label,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.$emit("batchUpdateTagsSuccess");
                        this.$message.success("批量添加标签成功");
                        this.closeBatchTag();
                    } else {
                        this.$message.error(
                            res.data.message || "批量添加标签失败"
                        );
                    }
                })
                .catch(() => {
                    this.$message.error("批量添加标签失败");
                });
        },

        // 批量删除标签
        confirmDeleteBatchTag() {
            if (this.selectedDeleteTags.length === 0) {
                this.$message.error("请至少选择一个要删除的标签");
                return;
            }

            const delLabel = this.selectedDeleteTags.join(",");
            const ids = this.selectedIds.map((item) => item.id).join(",");

            // 使用批量更新接口，传递 del_label 参数
            this.$request.recommendLabel
                .batchGoodLabelUpdate({
                    id: ids,
                    del_label: delLabel,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.$emit("batchUpdateTagsSuccess");
                        this.$message.success("批量删除标签成功");
                        this.closeBatchTag();
                    } else {
                        this.$message.error(
                            res.data.message || "批量删除标签失败"
                        );
                    }
                })
                .catch(() => {
                    this.$message.error("批量删除标签失败");
                });
        },
        show(selectedItems) {
            this.selectedIds = selectedItems;
            this.selectedCount = selectedItems.length;
            this.selectedTags = [];
            this.selectedDeleteTags = [];
            this.operationType = "add";
            this.availableDeleteTags = [];
            this.batchTagVisible = true;
        },
    },
};
</script>

<style lang="scss" scoped>
.batch-tag-info {
    margin-bottom: 15px;
    font-size: 14px;

    .selected-count {
        color: #409eff;
        font-weight: bold;
    }
}

.delete-tags-hint {
    margin-top: 8px;
    text-align: center;
}
</style>
