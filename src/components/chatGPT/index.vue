<template>
    <div class="chat_box">
        <el-popover
            ref="history"
            placement="right"
            width="250"
            trigger="click"
            popper-class="history_popover"
        >
            <!-- v-model="historyRecordVisible" -->
            <!-- 历史记录 -->
            <div
                class="content_box"
                style="height: 200px; overflow-y: auto; overflow-x: hidden"
                v-if="historyRoom.length != 0"
            >
                <div
                    class="history_item"
                    v-for="item in historyRoom"
                    :key="item.id"
                    @click="lookHistoryRecord(item)"
                >
                    {{ item.name }}
                </div>
            </div>
            <el-empty description="暂无数据" :image-size="30" v-else></el-empty>
            <!-- @click="historyRecord" -->
            <div
                class="history_record"
                slot="reference"
                @click="getHistoryRoom"
            >
                <i class="el-icon-time"></i>
            </div>
        </el-popover>
        <el-tabs
            v-model="editableTabsValue"
            type="card"
            editable
            @edit="handleTabsEdit"
            @tab-click="changeTabs"
            :before-leave="beforeLeave"
        >
            <el-tab-pane
                :key="item.id"
                v-for="item in editableTabs"
                :label="item.name"
                :name="String(item.id)"
            >
                <span slot="label">
                    <span v-if="item.name.length > 30" :title="item.name"
                        >{{ item.name.slice(0, 30) }}...</span
                    >
                    <span v-else>{{ item.name }}</span>
                </span>
                <div
                    class="m-t-10"
                    style="height: 100%; overflow-y: auto; overflow-x: hidden"
                    ref="contentRef"
                >
                    <div
                        class="m-b-15"
                        v-for="inItem in chatList"
                        :key="inItem.id"
                    >
                        <div class="f_box m-l-35">
                            <div>
                                <el-image
                                    style="
                                        width: 40px;
                                        height: 40px;
                                        border-radius: 10%;
                                    "
                                    :src="userinfo.avatar"
                                    fit="contain"
                                ></el-image>
                            </div>
                            <div class="m-l-10 my_content">
                                <div class="create_time">
                                    {{ inItem.create_time }}
                                </div>
                                <div>
                                    <!-- {{
                                        inItem.instruct.instruct_name
                                            ? inItem.instruct.instruct_name +
                                              ": "
                                            : ""
                                    }} -->
                                    {{ inItem.req_content }}
                                </div>
                            </div>
                        </div>
                        <div
                            class="f_box m-t-10"
                            style="
                                background-color: #dedddd29;
                                padding: 10px 10px;
                            "
                        >
                            <el-checkbox
                                v-model="inItem.checked"
                                @change="changCheck"
                            ></el-checkbox>
                            <div class="m-l-10">
                                <el-image
                                    style="
                                        width: 40px;
                                        height: 40px;
                                        border-radius: 10%;
                                    "
                                    :src="`${require('../../assets/chat/chatVinehoo.png')}`"
                                    fit="contain"
                                ></el-image>
                            </div>

                            <div class="m-l-10">
                                <div
                                    class="loading"
                                    v-if="inItem.isShowAnimation"
                                >
                                    <div class="dot1"></div>
                                    <div class="dot2"></div>
                                    <div class="dot3"></div>
                                </div>
                                <div v-else>
                                    <el-button
                                        type="primary"
                                        size="mini"
                                        v-if="inItem.retry"
                                        @click="send(1)"
                                        >重试</el-button
                                    >
                                    <div
                                        v-else
                                        v-html="inItem.resp_content"
                                    ></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- <div v-if="loadingText == '没有更多了'" class="load-more">
                        {{ loadingText }}
                    </div> -->
                </div>
            </el-tab-pane>
        </el-tabs>
        <!-- {{ messages }} -->
        <!-- 输入 -->
        <div class="m-t-20 m-b-10 f_box">
            <div class="m-t-5 m-r-10" style="font-size: 12px">
                剩余tokens
                <span style="color: #e6a23c">{{ maxWordsNum }}</span>
                ,剩余可提问次数<span style="color: #e6a23c">
                    {{ searchCntNum }}</span
                >
            </div>
            <div>
                <el-popover
                    placement="right"
                    title=""
                    trigger="click"
                    ref="instructRef"
                    popper-class="instruct_popover"
                >
                    <!-- v-model="visible" -->
                    <div>
                        <el-cascader-panel
                            ref="cascaderPanel"
                            v-model="customizeVal"
                            :options="instructList"
                            @change="chooseInstruct"
                        ></el-cascader-panel>
                    </div>
                    <!-- @click="visible = !visible" -->
                    <el-button
                        slot="reference"
                        type="primary"
                        size="mini"
                        :disabled="flag == 1"
                        >便捷指令 >
                    </el-button>
                </el-popover>

                <el-button
                    type="warning"
                    size="mini"
                    class="m-l-5"
                    @click="copy"
                    :disabled="flag == 1"
                    >复制</el-button
                >
            </div>
        </div>

        <div>
            <!-- <el-checkbox
                v-model="allChecked"
                class="m-r-10"
                @change="changeAllCheck"
                >全选</el-checkbox
            > -->
            <el-input
                type="textarea"
                :autosize="{ minRows: 1, maxRows: 6 }"
                v-model="contentVal"
                placeholder="请输入内容"
                size="mini"
                style="width: 95%"
                class="m-r-10 contentVal_box"
                ref="contentVal"
                @input="contentValInput"
                @keydown.native="keyDownInput"
            >
            </el-input>
            <el-tag type="info" class="m-b-10" size="medium" @click="send">
                <i class="el-icon-s-promotion"></i>
            </el-tag>
        </div>
        <!-- 自定义指令 -->
        <div>
            <el-dialog
                title="自定义指令"
                :visible.sync="customizeDialogVisible"
                width="30%"
                :close-on-click-modal="false"
                :before-close="customizeHandleClose"
                append-to-body
            >
                <CustomizeInstructList
                    v-if="customizeDialogVisible"
                    @customizeHandleClose="customizeHandleClose"
                />
            </el-dialog>
        </div>
    </div>
</template>

<script>
import moment from "moment";
import copy from "copy-to-clipboard";
import CustomizeInstructList from "./customizeInstructList.vue";
export default {
    components: {
        CustomizeInstructList,
    },
    data() {
        return {
            customizeDialogVisible: false,
            historyRecordVisible: false,
            visible: false,
            allChecked: false,
            contentVal: "",
            backupContentVal: "",
            editableTabsValue: "",
            editableTabs: [
                //聊天室
            ],
            historyRoom: [],
            backupEditableTabs: [], //聊天室备份
            // tabIndex: 0,
            customizeVal: [],

            backupCustomizeVal: [], //备份选择的指令
            instructList: [],
            chatList: [],
            chatArr: [],
            checkList: [],
            userinfo: {},
            timer: null,
            maxWordsNum: 50000,
            messages: "",
            eventSource: null,
            flag: 0,
            uid: "",
            searchCntNum: "",
            timer: null,
            // limit: 10, //每页限制
            chatTotal_num: 0, // 数量
            page: 1, //当前页
            totalPage: 1, // 总页数
            loadingText: "加载更多", //加载更多、加载中、没有更多了
            dialogpage: 1,
            totalDialogPage: 1,
            dialogLimit: 1000,
        };
    },
    mounted() {
        this.userinfo = JSON.parse(localStorage.getItem("userinfo"));
        this.instructsByUser();
        this.roomsByUser();
        this.decrypt();
        this.searchCnt();
        // window.addEventListener("error", function (e) {
        //     console.error("Network error:", e);
        // });
    },
    beforeDestroy() {
        // let ele = this.$refs.contentRef;
        // // let eleIndex = "";
        // this.editableTabs.map((index) => {
        //     ele[index].removeEventListener("scroll", this.watchContainerScroll);
        // });
    },
    methods: {
        //查询剩余提问次数
        async searchCnt() {
            let res = await this.$request.chatGPT.searchCnt();
            if (res.data.error_code == 0) {
                this.searchCntNum = res.data.data.cnt;
            }
        },
        //是否全选
        changeAllCheck() {
            this.chatList.map((item) => {
                // item.checked = this.allChecked;
                item.checked = false;
                return item;
            });
            this.tokensCount();
        },
        changCheck() {
            // let checkNum = this.chatList.filter((item) => item.checked == true);
            // if (checkNum.length == this.chatList.length) {
            //     this.allChecked = true;
            // } else {
            //     this.allChecked = false;
            // }
            this.tokensCount();
            this.isAllCheck();
        },
        copy() {
            let checkNum = this.chatList.filter((item) => item.checked == true);
            let text = "";
            checkNum.map((item) => {
                text = text + item.resp_content;
            });
            const replacedText = text
                .replace(/<div class='line_feed'><div\s*\/?>/g, "")
                .replace(/&nbsp;/g, " ");
            if (replacedText) {
                copy(replacedText);
                this.$message.success("复制成功");
            } else {
                this.$message.warning("请勾选要复制的文本");
            }
        },
        //查看历史聊天室
        lookHistoryRecord(item) {
            if (this.flag == 1) {
                return;
            }
            let obj = this.editableTabs.find((a) => a.id == item.id);
            if (!obj) {
                this.editableTabs.push({
                    name: item.name,
                    id: item.id,
                });
            }
            this.editableTabsValue = String(item.id);
            // this.historyRecordVisible = false;
            this.$refs.history.doClose();
            this.logByRoom();
        },
        //获取指令
        async instructsByUser() {
            let res = await this.$request.chatGPT.instructsByUser({
                contain_sys: 1,
            });
            if (res.data.error_code == 0) {
                let arr = res.data.data.list;
                let translateArr = arr.filter((item) => {
                    return item.pid == 10;
                });
                let textPolishArr = arr.filter((item) => {
                    return item.pid == 7;
                });
                this.instructList = arr
                    .map((item) => {
                        if (item.id == 10) {
                            item.children = translateArr;
                        }
                        if (item.id == 7) {
                            item.children = textPolishArr;
                        }
                        item.value = item.id;
                        item.label = item.name;
                        return item;
                    })
                    .filter((a) => a.pid != 10 && a.pid != 7);
                this.instructList.push({ value: 0, label: "自定义指令" });
            }
        },
        //选择指令
        chooseInstruct(val) {
            this.backupCustomizeVal = JSON.parse(
                JSON.stringify(this.customizeVal)
            );
            let checkedArr = this.chatList.filter(
                (item) => item.checked == true
            );
            if (val[0] == 0) {
                this.customizeDialogVisible = true;
            } else {
                let text = "";
                if (val.length > 1) {
                    this.instructList.map((item) => {
                        if (item.value == val[0]) {
                            if (item.children) {
                                item.children.map((a) => {
                                    if (a.value == val[1]) {
                                        text = a.content;
                                    }
                                });
                            } else {
                                text = item.content;
                            }
                        }
                    });
                } else {
                    text = this.instructList.find((item) => {
                        return item.value == val[0];
                    }).content;
                }
                this.contentVal = `${text}${checkedArr.length != 0 ? "" : ":"}`;
            }
            this.backupContentVal = this.contentVal;
            // this.visible = false;
            this.$refs.instructRef.doClose();
            this.$refs.cascaderPanel.clearCheckedNodes();
            this.$refs.cascaderPanel.activePath = [];
            if (this.$refs.cascaderPanel.menus.length == 2) {
                this.$refs.cascaderPanel.menus.pop();
            }
            this.customizeVal = [];
            this.$refs.contentVal.focus();
            this.tokensCount();
            if (checkedArr.length != 0) {
                this.send();
            }
        },
        //点击历史时获取聊天室
        async getHistoryRoom() {
            let res = await this.$request.chatGPT.roomsByUser();
            if (res.data.error_code == 0) {
                this.historyRoom = res.data.data.list;
            }
        },
        //获取聊天室
        async roomsByUser() {
            let res = await this.$request.chatGPT.roomsByUser();
            if (res.data.error_code == 0) {
                if (res.data.data.list.length == 0) {
                    let newTabName = new Date().getTime() + "";
                    this.editableTabs.push({
                        name: "New chat",
                        id: newTabName,
                    });
                    this.editableTabsValue = newTabName;
                    this.chatList = [];
                    this.maxWordsNum = this.$options.data().maxWordsNum;
                } else {
                    this.editableTabs = JSON.parse(
                        JSON.stringify(res.data.data.list)
                    ).splice(0, 3);
                    this.backupEditableTabs = JSON.parse(
                        JSON.stringify(res.data.data.list)
                    );
                    this.editableTabsValue = String(this.editableTabs[0].id);
                    this.logByRoom();
                }
            }
        },
        //禁止tab切换
        beforeLeave() {
            if (this.flag == 1) {
                return false;
            }
        },
        //切换tab时
        changeTabs() {
            // let ele = this.$refs.contentRef;
            // let eleIndex = "";
            // console.log("editableTabsValue", this.editableTabsValue);
            // this.editableTabs.map((item, index) => {
            //     if (item.id == this.editableTabsValue) {
            //         eleIndex = index - 1;
            //     }
            // });
            // this.$nextTick(() => {
            //     ele[eleIndex].removeEventListener(
            //         "scroll",
            //         this.watchContainerScroll
            //     );
            // });
            // this.chatArr = [];
            // this.dialogpage = 1;
            if (this.flag == 1) {
                this.$message.warning("内容正在生成中，请等待");
                return;
            }
            let obj = this.backupEditableTabs.find(
                (item) => item.id == this.editableTabsValue
            );
            if (obj) {
                this.logByRoom();
            } else {
                this.chatList = [];
            }
            this.tokensCount();
        },
        //获取聊天室内容
        async logByRoom() {
            let res = await this.$request.chatGPT.logByRoom({
                room_id: Number(this.editableTabsValue),
                module: "commodities",
                page: this.dialogpage,
                limit: this.dialogLimit,
            });
            if (res.data.error_code == 0) {
                const dresult = res.data.data;
                // console.log(
                //     "当前业和总业数",
                //     this.dialogpage,
                //     this.totalDialogPage
                // );
                // this.totalDialogPage = Math.ceil(
                //     dresult.total / this.dialogLimit
                // ); //计算总页数
                // if (this.dialogpage == 1) {
                //     this.chatArr = dresult.list;
                // } else {
                //     this.chatArr = [...this.chatArr, ...dresult.list];
                // }
                // this.loadingText =
                //     this.dialogpage == this.totalDialogPage
                //         ? "没有更多了"
                //         : "加载更多"; //如果当前页等于总页数，则说明全部数据加载完成，提示“没有更多”，否则提示“加载更多”

                this.chatList = JSON.parse(JSON.stringify(dresult.list)).map(
                    (item) => {
                        item.checked = false;
                        item.isShowAnimation = false;
                        item.retry = false;
                        item.instruct = JSON.parse(item.instruct);
                        item.resp_content = item.resp_content
                            .replace(/ /g, "&nbsp;&nbsp;")
                            .replace(/\n/g, "<div class='line_feed'><div/>");
                        return item;
                    }
                );
                this.changCheck();
                this.scrollSite();
            }
        },
        // 监听容器滚动条位置
        watchContainerScroll(ele) {
            let clientHeight = ele.clientHeight; //容器高度
            let scrollHeight = ele.scrollHeight; //滚动条高度
            let scrollTop = ele.scrollTop; //滚动条距离顶部位置
            // 1. 判断加载状态,如果loadingText === '没有更多了' return
            // 2.将loadingText设置为 ‘加载中’ this.loadingText = ‘加载中’
            // 3. 将当前也自增 this.page++
            // 4. 更新列表
            if (scrollTop + clientHeight >= scrollHeight - 1) {
                console.log("到底了");
                // console.log(this.loadingText);
                if (this.loadingText != "加载更多") {
                    return;
                } else {
                    this.loadingText = "加载中...";
                    this.dialogpage++;
                    this.logByRoom();
                }
            }
        },
        //是否选择
        isAllCheck() {
            let checkedArr = this.chatList.filter(
                (item) => item.checked == true && item.retry == false
            );
            if (checkedArr.length != 0) {
                return this.chatList
                    .map((item) => {
                        if (item.checked == true) {
                            return item.id;
                        }
                    })
                    .filter((a) => a != undefined);
            } else {
                return this.chatList
                    .map((item) => {
                        if (item.checked == false && item.retry == false) {
                            return item.id;
                        }
                    })
                    .filter((a) => a != undefined);
            }
        },
        //发送请求
        connect(data) {
            let DomainName = "";
            if (process.env.NODE_ENV == "development") {
                DomainName = "https://chatgpt.vinehoo.com";
            } else {
                DomainName = "https://chatgpt.vinehoo.com";
            }
            // http://154.221.18.40:9999/
            // http://192.168.31.25:8889
            this.eventSource = new EventSource(
                `${DomainName}/event?uid=${this.uid}&q=${encodeURIComponent(
                    JSON.stringify(data)
                )}`,
                {
                    headers: {
                        "Access-Control-Allow-Origin": "*",
                    },
                }
            );

            this.contentVal = "";
            let isClose = 0;
            this.chatList.push({
                // id: res.data.data.id,
                id: new Date().getTime(),
                req_content: this.backupContentVal,
                // req_content: "写一篇简短关于葡萄酒的文章",
                create_time: moment(new Date().valueOf()).format(
                    "yyyy-MM-DD HH:mm:ss"
                ),
                resp_content: this.messages,
                // resp_content: res.data.data.content,
                instruct: {
                    instruct_name: this.instructList.find((item) => {
                        return item.value == this.backupCustomizeVal[0];
                    })
                        ? this.instructList.find((item) => {
                              return item.value == this.backupCustomizeVal[0];
                          }).label
                        : "",
                },
                retry: false,
                checked: false,
                isShowAnimation: true,
            });
            this.eventSource.onmessage = (ev) => {
                this.messages = this.messages + ev.data;
                this.$set(
                    this.chatList[this.chatList.length - 1],
                    "isShowAnimation",
                    false
                );
                this.$set(
                    this.chatList[this.chatList.length - 1],
                    "resp_content",
                    this.messages
                );
                //让滚动条滚到底部
                this.scrollSite();
            };
            this.eventSource.onerror = (ev) => {
                console.log("onerror", ev);
                if (isClose != 1) {
                    this.$set(
                        this.chatList[this.chatList.length - 1],
                        "isShowAnimation",
                        false
                    );
                    this.$set(
                        this.chatList[this.chatList.length - 1],
                        "retry",
                        true
                    );
                }
                if (ev.currentTarget.readyState === 0) {
                    this.eventSource.close();
                }
                this.flag = 0;
            };
            this.eventSource.addEventListener("close", (ev) => {
                console.log("结束", ev);
                isClose = 1;
                let query = JSON.parse(ev.data);
                this.$set(
                    this.chatList[this.chatList.length - 1],
                    "isShowAnimation",
                    false
                );
                this.$set(
                    this.chatList[this.chatList.length - 1],
                    "id",
                    query.id
                );
                this.changeAllCheck();
                this.backupCustomizeVal = [];
                this.backupContentVal = "";
                this.flage = 0;
                this.searchCnt();
                this.changeAllCheck();
                this.roomsByUser();
            });
            this.eventSource.addEventListener("param_err", (ev) => {
                this.$message.error(ev.data);
            });
            this.eventSource.addEventListener("err", (ev) => {
                this.$set(
                    this.chatList[this.chatList.length - 1],
                    "isShowAnimation",
                    false
                );
                this.$set(
                    this.chatList[this.chatList.length - 1],
                    "retry",
                    true
                );
                this.$message.error(ev.data);
            });
            this.eventSource.onopen = (ev) => {
                console.log(ev);
            };
        },
        //发送
        send(type = 0) {
            //让滚动条滚到底部
            this.scrollSite();
            if (!this.backupContentVal) {
                return;
            }
            if (this.maxWordsNum < 0 || this.maxWordsNum == 0) {
                this.$message.error("tokens已超出限制");
                return;
            }
            if (this.flag == 1) {
                this.$message.warning("内容正在生成中，请等待");
                return;
            }
            if (this.searchCntNum == 0) {
                this.$message.warning("提问次数为0，请联系管理员");
                return;
            }

            this.backupContentVal = this.backupContentVal.trim();
            if (this.backupContentVal && this.backupContentVal != "") {
                let isHaveinstruct = this.instructList.find(
                    (item) => this.backupContentVal.indexOf(item.content) != -1
                );
                if (!isHaveinstruct) {
                    this.backupCustomizeVal = [];
                }
                let data = {
                    room_source: "", //聊天室来源
                    req_content: this.backupContentVal, //聊天内容
                    is_instruct: this.backupCustomizeVal.length != 0 ? 1 : 0, //是否使用指令  1是 0不是
                    room_module: "commodities", //聊天室所属模块
                    source_code: "", //来源标识 例如 在期数1中开起的聊天
                    token_ct: this.maxWordsNum, //剩余token数
                };
                data.context_ids = this.isAllCheck() ? this.isAllCheck() : []; //关联上文日志id数组
                if (data.is_instruct == 1) {
                    // instruct_id:'', //指令id 如果is_instruct为1 此字段比传
                    data.instruct_id =
                        this.backupCustomizeVal.length == 2
                            ? this.backupCustomizeVal[1]
                            : this.backupCustomizeVal[0];
                }
                let obj = this.backupEditableTabs.find(
                    (item) => item.id == this.editableTabsValue
                );
                if (obj) {
                    // is_new_room: 0, //是否新创建聊天室 1是 0不是
                    //     room_id: 4, //聊天室id  如果is_new_room不是新建的聊天室 必传
                    data.is_new_room = 0;
                    data.room_id = Number(this.editableTabsValue);
                } else {
                    data.is_new_room = 1;
                }
                console.log("data", data);
                this.messages = "";
                if (type == 1) {
                    this.chatList.pop();
                }
                this.$refs.contentVal.blur();
                this.flag = 1;
                this.connect(data);
            }
        },
        //统计文本数量
        tokensCount() {
            let content = "";
            let checkedArr = this.chatList.filter(
                (item) => item.checked == true
            );
            if (checkedArr.length != 0) {
                checkedArr.map((item) => {
                    content =
                        content + item.req_content + item.resp_content + "上文";
                    // +
                    // item.instruct.instruct_name;
                });
            } else {
                this.chatList.map((item) => {
                    if (item.checked == false) {
                        content =
                            content + item.req_content + item.resp_content;
                    }
                });
            }
            content = content + this.contentVal;
            const replacedText = content
                .replace(/<div class='line_feed'><div\s*\/?>/g, "\n")
                .replace(/&nbsp;/g, " ");
            this.$request.chatGPT
                .tokensCount({ content: replacedText })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.maxWordsNum = this.$options.data().maxWordsNum;
                        this.maxWordsNum =
                            this.maxWordsNum - res.data.count < 0
                                ? 0
                                : this.maxWordsNum - res.data.count;
                    }
                });
        },
        //输入问题时
        contentValInput() {
            this.backupContentVal = this.contentVal; //聊天内容备份
            if (this.timer !== null) {
                clearTimeout(this.timer);
            }
            this.timer = setTimeout(() => {
                this.tokensCount();
            }, 600);
        },
        // 自定义指令
        customizeHandleClose() {
            this.customizeDialogVisible = false;
            this.instructsByUser();
        },
        //历史记录
        historyRecord() {
            this.historyRecordVisible = !this.historyRecordVisible;
        },
        //删除tab
        delTab(type, targetName) {
            let tabs = this.editableTabs;
            let activeName = this.editableTabsValue;
            if (activeName == targetName) {
                tabs.forEach((tab, index) => {
                    if (tab.id == targetName) {
                        let nextTab = tabs[index + 1] || tabs[index - 1];
                        if (nextTab) {
                            activeName = String(nextTab.id);
                        }
                    }
                });
            }
            if (type == 1) {
                this.$request.chatGPT
                    .deleteRoom({ ids: [targetName] })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.$message.success("删除成功");
                            this.roomsByUser();
                        }
                    });
            }
            this.editableTabsValue = activeName;
            this.editableTabs = tabs.filter((tab) => tab.id != targetName);
            this.logByRoom();
        },
        handleTabsEdit(targetName, action) {
            if (this.flag == 1) {
                return;
            }
            if (action === "add") {
                // this.tabIndex = this.editableTabs.length;
                // ++this.tabIndex +
                let newTabName = new Date().getTime() + "";
                this.editableTabs.push({
                    name: "New chat",
                    id: newTabName,
                });
                this.editableTabsValue = newTabName;
                this.chatList = [];
            }
            this.tokensCount();
            if (action === "remove") {
                if (this.editableTabs.length == 1) {
                    if (this.chatList.length == 0) {
                        console.log("---");
                        return;
                    }
                }
                let obj = this.backupEditableTabs.find(
                    (a) => a.id == targetName
                );
                if (!obj) {
                    // this.$message.warning("暂无此对话");
                    this.delTab(0, targetName);
                    return;
                }
                this.$confirm("确认删除此对话吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        this.delTab(1, targetName);
                    })
                    .catch(() => {
                        console.log("错误");
                    });
            }
        },
        //控制滚动条到底部
        scrollSite() {
            let ele = this.$refs.contentRef;
            let eleIndex = "";
            this.editableTabs.map((item, index) => {
                if (item.id == this.editableTabsValue) {
                    eleIndex = index;
                }
            });
            this.$nextTick(() => {
                if (ele[eleIndex]) {
                    ele[eleIndex].scrollTop = ele[eleIndex].scrollHeight;
                }
                // ele[eleIndex].addEventListener(
                //     "scroll",
                //     this.watchContainerScroll(ele[eleIndex])
                // );
            });
        },
        //解密
        async decrypt() {
            let u_id = this.userinfo.uid;
            let data = {
                orig_data: [u_id],
            };
            let res = await this.$request.chatGPT.decrypt(data);
            if (res.data.error_code == 0) {
                this.uid = res.data.data[u_id];
            }
        },
        keyDownInput(e) {
            // console.log("ref", this.$refs.contentVal);
            // console.log(this.$refs.contentVal.isComposing);
            if (
                e.keyCode == 13 &&
                this.contentVal.length > 0 &&
                this.$refs.contentVal.isComposing === false
            ) {
                this.send();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
/deep/.el-tabs__new-tab {
    // float: right;
    border: 1px solid #000;
    height: 18px;
    width: 18px;
    line-height: 18px;
    margin: 12px 0 9px 10px;
    border-radius: 3px;
    text-align: center;
    font-size: 12px;
    color: #000;
    cursor: pointer;
    -webkit-transition: all 0.15s;
    transition: all 0.15s;
    margin-right: 40px;
}
/deep/.el-tabs--card > .el-tabs__header .el-tabs__item {
    // border-bottom: 1px solid transparent;
    // border-left: 1px solid #e4e7ed;
    -webkit-transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
        padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
        padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    // max-width: 300px;
    // overflow: hidden;
    // white-space: nowrap;
    // text-overflow: ellipsis;
    // -o-text-overflow: ellipsis;
}
/deep/.el-tabs--card > .el-tabs__header .el-tabs__item .el-icon-close {
    position: relative;
    font-size: 12px;
    width: 0;
    height: 14px;
    vertical-align: middle;
    line-height: 15px;
    overflow: hidden;
    top: -1px;
    right: -10px;
    -webkit-transform-origin: 100% 50%;
    transform-origin: 100% 50%;
}
/deep/.el-tag.el-tag--info {
    background-color: #60616e;
    border-color: #60616e;
    color: #909399;
}
/deep/.el-textarea__inner {
    display: block;
    resize: vertical;
    padding: 5px 15px;
    line-height: 1.5;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
    font-size: inherit;
    color: #fff;
    background-color: #40414e;
    border: 1px solid #40414e;
    border-radius: 4px;
    -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}
/deep/.el-textarea__inner::-webkit-scrollbar {
    width: 0;
    height: 0;
}
/deep/.el-tabs--card > .el-tabs__header .el-tabs__nav {
    border: 1px solid #444659;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
/deep/.el-tabs--card > .el-tabs__header .el-tabs__item {
    // border-bottom: 1px solid #444659;
    border-left: 1px solid #444659;
    -webkit-transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
        padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
        padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}
/deep/.el-tabs--card > .el-tabs__header {
    border-bottom: 1px solid #444659;
}
// /deep/.el-tabs--top.el-tabs--card
//     > .el-tabs__header
//     .el-tabs__item:nth-child(2) {
//     padding-left: 20px;
//     color: #fff;
// }
/deep/.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
    border-bottom-color: transparent;
    color: #333333;
    background-color: #dddddd;
}
/deep/.el-tabs--card > .el-tabs__header .el-tabs__item {
    border-left: 1px solid #444659;
    -webkit-transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
        padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
        padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    color: #fff;
}
/deep/ .el-dialog {
    position: relative;
    margin: 0 auto 50px;
    background: #343540 !important;
    border-radius: 2px;
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 30%);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 30%);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 50%;
}
/deep/.el-dialog__title {
    line-height: 24px;
    font-size: 18px;
    color: #fff;
}
/deep/.el-popover {
    position: absolute;
    background: #343540;
    min-width: 150px;
    border-radius: 4px;
    border: 1px solid #343540;
    padding: 12px;
    z-index: 2000;
    color: #606266;
    line-height: 1.4;
    text-align: justify;
    font-size: 14px;
    -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 10%);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 10%);
    word-break: break-all;
}
/deep/.el-tabs__new-tab .el-icon-plus {
    -webkit-transform: scale(0.8, 0.8);
    transform: scale(0.8, 0.8);
    color: #fff;
}
/deep/.el-tabs__new-tab {
    border: 1px solid #fff;
    height: 18px;
    width: 18px;
    line-height: 18px;
    margin: 12px 0 9px 10px;
    border-radius: 3px;
    text-align: center;
    font-size: 12px;
    color: #000;
    cursor: pointer;
    -webkit-transition: all 0.15s;
    transition: all 0.15s;
    margin-right: 40px;
}
// /deep/.el-cascader-panel.is-bordered {
//     border: 1px solid #343540;
//     border-radius: 4px;
// }
// /deep/.el-cascader-menu {
//     min-width: 180px;
//     -webkit-box-sizing: border-box;
//     box-sizing: border-box;
//     color: #606266;
//     border-right: solid 1px #343540;
// }
// /deep/ .el-cascader-node:not(.is-disabled):hover {
//     background: #dedddd29;
// }
/* 隐藏滚动条 */
::-webkit-scrollbar {
    display: none;
}
/* 隐藏滚动条 */
/* Microsoft Edge浏览器 */
@supports (-ms-ime-align: auto) {
    /* 如果是Microsoft Edge浏览器，则隐藏滚动条 */
    ::-webkit-scrollbar {
        display: none;
    }
}
/* 可以滚动的内容 */
.content_box {
    /* Microsoft Edge浏览器 */
    -ms-overflow-style: none;
}
.contentVal_box {
    overflow: auto;
    scrollbar-width: none; /* 隐藏滚动条 */
    -ms-overflow-style: none; /* 隐藏IE11滚动条 */
}
/* 对于WebKit浏览器 */
.contentVal_box::-webkit-scrollbar {
    width: 0;
    height: 0;
}
.chat_box {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #343540;
    overflow: hidden;
}
/deep/ .el-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    &__content {
        flex: 1;
    }
}
/deep/ .el-tab-pane {
    height: 100%;
}
.f_box {
    display: flex;
    justify-content: left;
}
i {
    cursor: pointer;
}
.history_record {
    position: absolute;
    top: 12px;
    right: 0;
    z-index: 99;
    i {
        font-size: 20px;
        font-weight: bold;
    }
}
.history_item {
    padding: 5px 5px;
    margin-right: 4px;
    color: #747373;
    cursor: pointer;
}
.history_item:hover {
    background-color: #cfcccc3a;
}
.load-more {
    text-align: center;
    color: #747373;
}
.loading {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: left;
    height: 30px;
}
.dot1,
.dot2,
.dot3 {
    background: #fff;
    width: 5px;
    height: 5px;
    border: double;
    border-color: black;
    border-radius: 50%;
    margin: 5px;
}
.dot1 {
    animation: jump 1.6s -0.32s linear infinite;
    background: #b9b8b8;
}
.dot2 {
    animation: jump 1.6s -0.16s linear infinite;
    background: #747373;
}
.dot3 {
    animation: jump 1.6s linear infinite;
    background: #202020;
}

@keyframes jump {
    0%,
    80%,
    100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    }
    40% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}
div {
    font-size: 15.5px;
    font-weight: 440;
    color: rgba(52, 53, 65, var(--tw-text-opacity));
    font-family: Söhne, ui-sans-serif, system-ui, -apple-system, Segoe UI,
        Roboto, Ubuntu, Cantarell, Noto Sans, sans-serif, Helvetica Neue, Arial,
        Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol,
        Noto Color emojiEmojiEmoji;
    color: #fff;
}
.create_time {
    color: #c0c4cc;
    font-size: 14px;
    opacity: 0;
}
.my_content:hover {
    .create_time {
        opacity: 1;
    }
}
.line_feed {
    background-color: #444659;
}
</style>
