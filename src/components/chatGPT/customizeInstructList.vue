<template>
    <div>
        <div v-if="myInstructs.length != 0">
            <div>
                <b>我的指令</b>
                <span style="font-size: 12px"> (点击标签可编辑对应指令)</span>
                <!-- <el-tag
                    effect="plain"
                    size="mini"
                    @click="editCustomize"
                    style="cursor: pointer"
                    >{{ isEdit ? "进入编辑" : "完成编辑" }}</el-tag
                > -->
            </div>
            <div class="m-t-10">
                <el-popover
                    placement="bottom"
                    width="350"
                    trigger="manual"
                    v-model="visible"
                >
                    <div>
                        <el-form
                            :model="editRuleForm"
                            :rules="editRules"
                            ref="editRuleForm"
                            label-width="110px"
                            class="demo-ruleForm"
                            size="mini"
                        >
                            <el-form-item label="指令名称" prop="name">
                                <el-input
                                    v-model.trim="editRuleForm.name"
                                ></el-input>
                            </el-form-item>
                            <el-form-item label="指令具体内容" prop="content">
                                <el-input
                                    v-model.trim="editRuleForm.content"
                                ></el-input>
                            </el-form-item>

                            <el-form-item
                                style="
                                    display: flex;
                                    justify-content: center;
                                    margin-left: -110px;
                                "
                            >
                                <el-button @click="visible = false"
                                    >取消</el-button
                                >
                                <el-button
                                    type="primary"
                                    @click="submitForm('editRuleForm', 1)"
                                    >确定</el-button
                                >
                            </el-form-item>
                        </el-form>
                    </div>
                    <el-tag
                        size="mini"
                        closable
                        v-for="item in myInstructs"
                        :key="item.id"
                        slot="reference"
                        @click="editCustomize(item)"
                        class="m-r-10"
                        style="cursor: pointer"
                        @close="handleClose(item)"
                    >
                        {{ item.name }}
                    </el-tag>
                    <!-- <el-button @click="visible = !visible">手动激活</el-button> -->
                </el-popover>
            </div>
        </div>

        <el-button
            type="success"
            size="mini"
            @click="
                () => {
                    addCustomize = true;
                }
            "
            class="m-b-10 m-t-10"
            >新增指令</el-button
        >
        <div v-if="addCustomize">
            <el-form
                :model="addRuleForm"
                :rules="addRules"
                ref="addRuleForm"
                label-width="110px"
                class="demo-ruleForm"
                size="mini"
            >
                <el-form-item class="add" label="指令名称" prop="name">
                    <el-input
                        class="add"
                        v-model.trim="addRuleForm.name"
                        placeholder="请输入指令名称"
                    ></el-input>
                </el-form-item>
                <el-form-item class="add" label="指令具体内容" prop="content">
                    <el-input
                        class="add"
                        v-model.trim="addRuleForm.content"
                        placeholder="请输入指令具体内容"
                    ></el-input>
                </el-form-item>

                <el-form-item
                    style="
                        display: flex;
                        justify-content: center;
                        margin-left: -110px;
                    "
                >
                    <el-button @click="addCustomize = false">取消</el-button>
                    <el-button
                        type="primary"
                        @click="submitForm('addRuleForm', 0)"
                        >确定</el-button
                    >
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            visible: false,
            // isEdit: true,
            addCustomize: false,
            myInstructs: [],
            instructsId: "",
            addRuleForm: {
                name: "",
                content: "",
            },
            addRules: {
                name: [
                    {
                        required: true,
                        message: "请输入指令名称",
                        trigger: "blur",
                    },
                ],
                content: [
                    {
                        required: true,
                        message: "请输入指令具体内容",
                        trigger: "blur",
                    },
                ],
            },
            editRuleForm: {
                name: "",
                content: "",
            },
            editRules: {
                name: [
                    {
                        required: true,
                        message: "请输入指令名称",
                        trigger: "blur",
                    },
                ],
                content: [
                    {
                        required: true,
                        message: "请输入指令具体内容",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    mounted() {
        this.instructsByUser();
    },
    methods: {
        close() {
            this.$emit("customizeHandleClose");
        },
        async instructsByUser() {
            let res = await this.$request.chatGPT.instructsByUser({
                contain_sys: 0,
            });
            if (res.data.error_code == 0) {
                this.myInstructs = res.data.data.list;
            }
        },
        //删除指令
        async handleClose(item) {
            let res = await this.$request.chatGPT.deleteInstructs({
                ids: [item.id],
            });
            if (res.data.error_code == 0) {
                this.$message.success("删除成功");
                this.instructsByUser();
            }
        },
        //新增/编辑自定义指令
        submitForm(formName, type) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {};
                    let request_methods = "";
                    if (type == 0) {
                        data.instructs = [this.addRuleForm];
                        request_methods = "createInstructs";
                    }
                    if (type == 1) {
                        data.name = this.editRuleForm.name;
                        data.content = this.editRuleForm.content;
                        data.id = this.instructsId;
                        request_methods = "updateInstructs";
                    }
                    this.$request.chatGPT[request_methods](data).then((res) => {
                        if (res.data.error_code == 0) {
                            if (type == 1) {
                                this.visible = false;
                            }
                            this.$message.success(
                                type == 0 ? "新增成功" : "编辑成功"
                            );
                            this.addRuleForm = {};
                            this.instructsByUser();
                        }
                    });
                } else {
                    return false;
                }
            });
        },
        editCustomize(item) {
            console.log("ooo", item);
            this.editRuleForm.name = item.name;
            this.editRuleForm.content = item.content;
            this.visible = !this.visible;
            this.instructsId = item.id;
        },
    },
};
</script>

<style lang="scss" scoped>
.f_box_space_between {
    display: flex;
    justify-content: space-between;
}
// /deep/.add .el-input__inner {
//     -webkit-appearance: none;
//     background-color: #40414e;
//     border-radius: 4px;
//     border: 1px solid #40414e;
//     -webkit-box-sizing: border-box;
//     box-sizing: border-box;
//     color: #fff;
//     display: inline-block;
//     font-size: inherit;
//     height: 35px;
//     line-height: 35px;
//     outline: 0;
//     padding: 0 15px;
//     -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
//     transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
//     width: 100%;
// }
/deep/.add.el-input--mini .el-input__inner {
    height: 28px;
    line-height: 28px;
    background: #40414e;
    border: 1px solid #40414e;
    color: #fff;
}
/deep/.add.el-form-item--mini .el-form-item__label {
    line-height: 28px;
    color: #fff;
}
</style>
