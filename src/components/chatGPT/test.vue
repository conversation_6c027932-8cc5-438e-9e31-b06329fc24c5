<template>
    <div>
        <p v-for="(message, index) in messages" :key="index">{{ message }}</p>
        <button @click="connect(true)">点击</button>
    </div>
</template>

<script>
export default {
    data() {
        return {
            messages: {},
            eventSource: null,
            reconnect: true,
        };
    },
    mounted() {
        this.connect(true);
    },
    methods: {
        connect(flag) {
            this.reconnect = flag;
            if (!this.reconnect) return;
            this.eventSource = new EventSource(
                'http://*************:8889/event?uid=1&q={"room_source":"arti1cle","req_content":"你好","is_new_room":0,"room_id":4,"context_ids":[],"is_instruct":0,"instruct_id":0}'
            );
            this.eventSource.onmessage = (ev) => {
                console.log(ev);
                // document
                //     .getElementById("log")
                //     .insertAdjacentHTML("beforeend", ev.data);
            };
            this.eventSource.onerror = (ev) => {
                console.log("readyState = " + ev.currentTarget.readyState);
                if (ev.currentTarget.readyState === 0) {
                    this.eventSource.close();
                }
            };
            // this.eventSource.addEventListener("message", (ev) => {
            //     console.log(ev);
            // });
            this.eventSource.addEventListener("close", (ev) => {
                console.log("结束", ev);
                // this.eventSource.close();
            });
            this.eventSource.addEventListener("param_err", (ev) => {
                console.log("参数错误", ev);
            });

            this.eventSource.onopen = (ev) => {
                console.log(ev);
            };
        },
        handleClose() {
            // 使用保存的数据
            console.log("保存的数据", this.messages);
        },
        aClose() {
            this.eventSource.addEventListener("close", function (ev) {
                console.log("结束-----", ev);
                // this.eventSource.close();
            });
        },
    },
    beforeDestroy() {
        if (this.eventSource) {
            this.eventSource.close();
        }
    },
};
</script>
