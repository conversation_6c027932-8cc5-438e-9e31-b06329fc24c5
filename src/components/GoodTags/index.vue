<template>
    <div>
        <el-dialog
            title="商品标签编辑"
            :visible.sync="goodTagVisible"
            width="30%"
            :append-to-body="true"
            @close="closeGoodTag"
            :close-on-click-modal="false"
        >
            <el-form :inline="false" size="mini">
                <el-form-item
                    v-for="(item, key) in goodTagList"
                    :key="item.uni_key"
                >
                    <el-select
                        v-model="item.id"
                        placeholder="请输入商品标签"
                        clearable
                        filterable
                    >
                        <el-option
                            v-for="item in goodTagOption"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                    <el-button
                        type="danger"
                        size="mini"
                        @click="delGoodTagItem(key)"
                        icon="el-icon-delete"
                        v-if="goodTagList.length > 1"
                        style="margin-left: 10px"
                    ></el-button>
                </el-form-item>
                <el-button type="primary" size="mini" @click="addGoodTagItem"
                    >添加</el-button
                >
            </el-form>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button size="mini" @click="goodTagVisible = false"
                    >取消</el-button
                >
                <el-button size="mini" type="primary" @click="confirmAddGoodTag"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "Vue2CommoditiesIndex",

    data() {
        return {
            goodTagVisible: false,
            goodTagList: [],
            goodTagOption: [],
            id: "",
            periods_type: "",
        };
    },

    mounted() {
        this.getRecommendLabel();
    },

    methods: {
        closeGoodTag() {
            this.goodTagList = [];
            this.id = "";
            this.periods_type = "";
            this.goodTagVisible = false;
        },
        getRecommendLabel() {
            this.$request.recommendLabel
                .labelList({ page: 1, limit: 999, type: 2 })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.goodTagOption = res.data.data.list;
                    }
                });
        },
        addGoodTagItem() {
            this.goodTagList.push({
                id: "",
                uni_key: this.generateRandomString(),
            });
        },
        delGoodTagItem(key) {
            this.goodTagList.splice(key, 1);
        },
        confirmAddGoodTag() {
            //判断是否有空
            // for (let i = 0; i < this.goodTagList.length; i++) {
            //     if (this.goodTagList[i].id === "") {
            //         this.$message.error("商品标签不能为空");
            //         return;
            //     }
            // }
            let params = {
                id: this.id,
                periods_type: this.periods_type,
                label: this.goodTagList.map((item) => item.id).join(","),
            };
            console.log(params);
            this.$request.recommendLabel.goodLabelUpdate(params).then((res) => {
                if (res.data.error_code === 0) {
                    let tags_arr = [];
                    tags_arr = this.goodTagList.map((item) => {
                        return {
                            id: item.id,
                            name: this.goodTagOption.find(
                                (item2) => item2.id === item.id
                            ).name,
                        };
                    });
                    this.$emit("updateTagsSuccess", tags_arr);
                    this.$message.success("添加成功");
                    this.goodTagVisible = false;
                    this.goodTagList = [];
                }
            });
        },
        show(row) {
            this.id = row.id;
            this.periods_type = row.periods_type;
            if (row.label_arr.length > 0) {
                this.goodTagList = row.label_arr.map((item) => {
                    return {
                        id: item.id,
                        uni_key: this.generateRandomString(),
                    };
                });
            } else {
                this.goodTagList = [
                    {
                        id: "",
                        uni_key: this.generateRandomString(),
                    },
                ];
            }
            this.goodTagVisible = true;
        },
        generateRandomString() {
            const characters =
                "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            let randomString = "";
            for (let i = 0; i < 10; i++) {
                const randomIndex = Math.floor(
                    Math.random() * characters.length
                );
                randomString += characters[randomIndex];
            }
            return randomString;
        },
    },
};
</script>

<style lang="scss" scoped></style>
