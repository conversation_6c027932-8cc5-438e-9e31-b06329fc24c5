<template>
    <div>
        <el-popover
            placement="bottom"
            title=""
            min-width="200"
            trigger="manual"
            v-model="visible"
        >
            <div>
                <div class="face_title">
                    <div
                        class="face_tab"
                        :class="fIndex == i ? 'face_current' : ''"
                        v-for="(v, i) in facetitle"
                        @click="
                            () => {
                                fIndex = i;
                            }
                        "
                        :key="i"
                    >
                        {{ v }}
                    </div>
                </div>
                <!-- 兔头 -->
                <div>
                    <div class="rabbit_img_wrap" v-show="fIndex == 0">
                        <div
                            @click="handelFace(item)"
                            class="rabbit_img"
                            v-for="(item, index) in rabbitHeadList"
                            :key="index"
                        >
                            <img
                                style="width: 100%; height: 100%"
                                :src="`${require(`../../assets/emoticon/${item}.gif`)}`"
                            />
                        </div>
                    </div>
                </div>
                <!-- 兔子 -->
                <div>
                    <div class="rabbit_img_wrap" v-show="fIndex == 1">
                        <div
                            @click="handelFace(item)"
                            class="rabbit_img"
                            v-for="(item, index) in rabbitList"
                            :key="index"
                        >
                            <img
                                style="width: 100%; height: 100%"
                                :src="`${require(`../../assets/emoticon/${item}.gif`)}`"
                            />
                        </div>
                    </div>
                </div>
                <!-- emoji -->
                <div>
                    <div class="face_wrap" v-show="fIndex == 2">
                        <div
                            @click="handelEmoji(item)"
                            class="faces"
                            v-for="(item, index) in emoji"
                            :key="index"
                        >
                            {{ item }}
                        </div>
                    </div>
                </div>
            </div>
            <img
                slot="reference"
                @click="visible = !visible"
                src="./img/face.png"
                alt=""
            />
        </el-popover>
        <div style="position: relative" v-if="chooseVisible">
            <div class="del" @click="del()">
                <img
                    style="width: 100%; height: 100%"
                    src="./img/del.png"
                    alt=""
                />
            </div>
            <el-popover
                placement="right"
                title=""
                width="150"
                trigger="manual"
                v-model="chooseVisible"
            >
                <div v-if="chooseVisible">
                    <img
                        style="width: 90px; height: 90px; margin-left: 15px"
                        :src="`${require(`../../assets/emoticon/${chooseFace}.gif`)}`"
                    />
                </div>
                <div slot="reference" style="margin-bottom: 5px"></div>
                <!-- <img
                slot="reference"
                @click="visible = !visible"
                src="./img/face.png"
                alt=""
            /> -->
            </el-popover>
        </div>
    </div>
</template>

<script>
import emoj from "../../tools/rabbitEmoji";
export default {
    data() {
        return {
            visible: false,
            fIndex: 0,
            facetitle: ["兔头", "兔子", "Emoji"],
            rabbitHeadList: [
                "rh1",
                "rh2",
                "rh3",
                "rh4",
                "rh5",
                "rh6",
                "rh7",
                "rh8",
                "rh9",
                "rh10",
                "rh11",
                "rh12",
                "rh13",
                "rh14",
                "rh15",
                "rh16",
            ],
            rabbitList: [
                "rb1",
                "rb2",
                "rb3",
                "rb4",
                "rb5",
                "rb6",
                "rb7",
                "rb8",
                "rb9",
                "rb10",
                "rb11",
                "rb12",
                "rb13",
                "rb14",
                "rb15",
                "rb16",
                "rb17",
                "rb18",
                "rb19",
                "rb20",
                "rb21",
                "rb22",
                "rb23",
                "rb24",
                "rb25",
                "rb26",
                "rb27",
                "rb28",
                "rb29",
                "rb30",
                "rb31",
                "rb32",
            ],
            emoji: [
                "😀",
                "😁",
                "😂",
                "🤣",
                "😃",
                "😄",
                "😅",
                "😆",
                "😉",
                "😊",
                "😋",
                "😎",
                "😍",
                "😘",
                "😗",
                "😙",
                "😚",
                "☺️",
                "🙂",
                "🤗",
                "🤩",
                "🤔",
                "🤨",
                "😐",
                "😑",
                "😶",
                "🙄",
                "😏",
                "😣",
                "😥",
                "😮",
                "🤐",
                "😯",
                "😪",
                "😫",
                "😴",
                "😌",
                "😛",
                "😜",
                "😝",
                "🤤",
                "😒",
                "😓",
                "😔",
                "😕",
                "🙃",
                "🤑",
                "😲",
                "☹️",
                "🙁",
                "😖",
                "😞",
                "😟",
                "😤",
                "😢",
                "😭",
                "😦",
                "😧",
                "😨",
                "😩",
                "🤯",
                "😬",
                "😰",
                "😱",
                "😳",
                "🤪",
                "😵",
                "😡",
                "😠",
                "🤬",
                "😷",
                "🤒",
                "🤕",
                "🤢",
                "🤮",
                "🤧",
                "😇",
                "🤠",
                "🤡",
                "🤥",
                "🤫",
                "🤭",
                "🧐",
                "🤓",
                "😈",
                "👿",
                "👹",
                "👺",
                "💀",
                "👻",
                "👽",
                "🤖",
                "💩",
                "😺",
                "😸",
                "😹",
                "😻",
                "😼",
                "😽",
                "🙀",
                "😿",
                "😾",
            ], //emoji表情包
            chooseVisible: false,
            chooseFace: "",
            chooseFaceTitle: "",
        };
    },
    computed: {
        // 获取兔头表情包
        getEmojiMap() {
            return emoj;
        },
    },
    methods: {
        findKey(map, searchValue) {
            for (let [key, value] of map.entries()) {
                if (value === searchValue) return key;
            }
        },
        handelFace(item) {
            console.log("表情", this.findKey(this.getEmojiMap, item));
            this.chooseFace = item;
            this.visible = false;
            this.chooseVisible = true;
            this.chooseFaceTitle = this.findKey(this.getEmojiMap, item);
        },

        del() {
            this.chooseVisible = false;
            this.chooseFaceTitle = "";
        },
        handelEmoji(item) {
            this.$emit("getEmoji", item);
            this.visible = false;
        },
    },
};
</script>

<style lang="scss" scoped>
.face_title {
    display: flex;
    justify-content: left;
    .face_tab {
        width: 50px;
        height: 30px;
        border: 1px solid #dcdfe6;
        text-align: center;
        line-height: 30px;
    }
    .face_current {
        border-bottom: 2px solid #fa7d3c;
    }
}
.face_wrap {
    width: 420px;
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
    .faces {
        width: 36px;
        height: 36px;
        text-align: center;
        line-height: 36px;
        border: 1px solid #e8e8e8;
        overflow: hidden;
        margin: -1px 0 0 -1px;
        cursor: pointer;
    }
    .faces:hover {
        border: 1px solid #eb7350;
        background: #fff9ec;
        position: relative;
        z-index: 2;
    }
}
.rabbit_img_wrap {
    width: 470px;
    margin-top: 20px;
    overflow: auto;
    height: 300px;
    display: flex;
    justify-content: left;
    flex-wrap: wrap;
    .rabbit_img {
        width: 90px;
        height: 90px;
        text-align: center;
        line-height: 36px;
        border: 1px solid #e8e8e8;
        overflow: hidden;
        margin: 0px -1px -1px 0px;
        cursor: pointer;
    }

    .rabbit_img:hover {
        border: 1px solid #eb7350;
        background: #fff9ec;
        position: relative;
        z-index: 2;
    }
}
.del {
    width: 30px;
    height: 30px;
    position: absolute;
    top: 0;
    left: 150px;
}
</style>
