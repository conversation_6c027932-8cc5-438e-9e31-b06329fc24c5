<template>
    <div @click="handleShare" style="display: flex; align-items: center; cursor: pointer; color: #409eff;">
        {{ text }}
        <el-tooltip
            class="item"
            effect="dark"
            :content="tooltipContent"
            placement="top"
        >
            <i class="fas fa-question-circle" style="margin-left: 4px;" />
        </el-tooltip>
    </div>
</template>

<script>
import copy from "copy-to-clipboard";

export default {
    name: 'ShareLink',
    props: {
        id: {
            type: [String, Number],
            required: true
        },
        encryptId: {
            type: String,
            default: ''
        },
        text: {
            type: String,
            default: '分享链接'
        },
        tooltipContent: {
            type: String,
            default: '自动生成带追踪标识符的购买链接'
        }
    },
    methods: {
        handleShare() {
            try {
                const userinfo = JSON.parse(localStorage.getItem('userinfo'));
                const uid = userinfo?.original_uid || '';
                
                if (!uid) {
                    this.$message.error('未能成功获取到您的用户ID，请退出中台重新登录。');
                    return;
                }

                const currentUrl = window.location.href.toLowerCase();
                const domain = currentUrl.includes('127.0.0.1') || currentUrl.includes('localhost') || currentUrl.includes('test') 
                    ? 'https://test.wineyun.com'
                    : 'https://www.vinehoo.com';

                const decryptUrl = currentUrl.includes('127.0.0.1') || currentUrl.includes('localhost') || currentUrl.includes('test')
                    ? 'https://test-wine.wineyun.com/go-wechat/wechat/v3/decrypt/channel'
                    : 'https://callback.vinehoo.com/go-wechat/wechat/v3/decrypt/channel';

                const generateShareUrl = (id, auth = '') => {
                    let url = `${domain}/pages/goods-detail/goods-detail?id=${id}&source_platform=vinehoo&source_event=share&source_user=${uid}`;
                    if (auth) {
                        url += `&auth=${auth}`;
                    }
                    return url;
                };

                if (this.encryptId) {
                    fetch(`${decryptUrl}?id=${this.encryptId}`, {
                        method: 'GET'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.error_code === 0 && data.data) {
                            const shareUrl = generateShareUrl(data.data.id, data.data.auth);
                            copy(shareUrl);
                            this.$message.success("链接已复制到剪贴板");
                        } else {
                            this.$message.error("解密失败");
                        }
                    })
                    .catch(error => {
                        console.error('Decryption error:', error);
                        this.$message.error("解密请求失败");
                    });
                } else {
                    const shareUrl = generateShareUrl(this.id);
                    copy(shareUrl);
                    this.$message.success("链接已复制到剪贴板");
                }
            } catch (error) {
                console.error('Share error:', error);
                this.$message.error("生成分享链接失败");
            }
        }
    }
};
</script> 