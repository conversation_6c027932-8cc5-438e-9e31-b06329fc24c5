<template>
    <div>
        <el-upload
            ref="uploads2"
            action
            list-type="picture-card"
            :file-list="file_list"
            :before-upload="beforeAvatarUpload"
            :on-remove="
                file => {
                    handleRemoveMore(file, 2);
                }
            "
            :on-exceed="
                file => {
                    handleExceed(file, 2);
                }
            "
            accept="image/jpeg, image/png"
            :http-request="uploadImg"
            :multiple="true"
            :class="{ hide: hideUpload }"
        >
            <i class="el-icon-plus" />
            <div slot="file" slot-scope="{ file }" style="height: 100%;">
                <img
                    class="el-upload-list__item-thumbnail"
                    :src="file.url"
                    alt=""
                />
                <span class="el-upload-list__item-actions">
                    <span
                        class="el-upload-list__item-preview"
                        @click="handlePictureCardPreview(file)"
                    >
                        <i class="el-icon-zoom-in"></i>
                    </span>
                    <span
                        class="el-upload-list__item-delete"
                        @click="handleRemoveMore(file, 2)"
                    >
                        <i class="el-icon-delete"></i>
                    </span>
                </span>
            </div>
        </el-upload>
    </div>
</template>
<style>
.bigImg_wrap {
    position: relative;
}

.bigImg_mask {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    color: #ffffff;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-right: 3px;
}

.bigImg_mask:hover {
    background-color: rgba(0, 0, 0, 0.5);
}

.zoom_big {
    display: none;
}

.bigImg_mask:hover .zoom_big {
    display: block;
}

.el-upload-list .is-ready {
    display: none !important;
}
</style>
<script>
import OSS from "ali-oss";
export default {
    props: {
        value: {
            type: Array,
            default: []
        }
    },
    data() {
        return {
            image: "",
            imgs: {
                image: ""
            },
            hideUpload: false,
            imgVisible: false,
            imgUrl: "",
            file_list: []
        };
    },
    // computed: {
    //     file_list() {
    //         this.value.map(item => {
    //             this.file_list.push({
    //                 url: item
    //             })
    //         })
    //     }
    // },
    watch: {
        value(newval) {
            let flag = false;
            newval.map(item => {
                this.file_list.map(child => {
                    if (item.url == child.url) {
                        flag = true;
                    }
                });
                if (!flag) {
                    this.file_list.push({
                        url: item
                    });
                }
            });
        }
    },
    mounted() {},
    methods: {
        handlePictureCardPreview(file) {
            this.imgVisible = true;
        },
        handlePreview() {
            this.imgVisible = !this.imgVisible;
        },
        //提交
        uploadImg(option) {
            let oss = {
                region: "oss-cn-zhangjiakou.aliyuncs.com",
                accessKeyId: "LTAI5t88V4fhDjxmuuvmmV5r",
                accessKeySecret: "******************************",
                bucket: "vinehoo-test"
            };
            if (process.env.NODE_ENV == "staging") {
                oss.region = "oss-cn-zhangjiakou-internal.aliyuncs.com";
            }
            if (process.env.NODE_ENV == "production") {
                oss.region = "oss-accelerate.aliyuncs.com";
                oss.accessKeyId = "LTAI5tCTTF2TderhYBGjCHQ6";
                oss.accessKeySecret = "******************************";
                oss.bucket = "vinehoo";
            }
            try {
                let vm = this;
                vm.disabled = true;
                const client = new OSS(oss);
                const file = option.file;
                // console.log(file,file.name.split('.').pop(),5555555555)
                client
                    .put("vinehoo/news/" + file.name, file)
                    .then(({ res }) => {
                        console.log(res);
                        if (res.statusCode === 200) {
                            this.file_list.push({
                                url: res.requestUrls[0]
                            });
                            const list = JSON.parse(
                                JSON.stringify(this.file_list)
                            );
                            this.$emit("input", list);
                            this.dispatch(
                                "ElFormItem",
                                "el.form.change",
                                res.requestUrls
                            );
                            //   console.log(this.file_list, 666666666)
                        } else {
                            vm.disabled = false;
                            option.onError("上传失败");
                        }
                    })
                    .catch(error => {
                        vm.disabled = false;
                        option.onError("上传失败");
                    });
            } catch (error) {
                this.disabled = false;
                option.onError("上传失败");
            }
        },
        handleExceed(files, type) {
            // this.$message.warning(
            //     `当前限制选择${type == 1 ? 8 : 2}个文件，本次选择了${
            //     files.length
            // }个文件,共选择了${files.length}个文件`
            // );
        },
        handleRemoveMore(file, type) {
            this.file_list.forEach((item, index) => {
                if (item.uid == file.uid) {
                    this.file_list.splice(index, 1);
                }
            });
            this.hideUpload = false;
        },
        beforeAvatarUpload(file) {
            const isJPG = file.type === "image/jpeg";
            const isPNG = file.type === "image/png";
            const isGIF = file.type === "image/gif";
            const isLt2M = file.size / 1024 / 1024 < 3;
            if (!(isJPG || isPNG || isGIF)) {
                this.$message.error("上传头像图片只能是 JPG/PNG 格式!");
                return false;
            }
            if (!isLt2M) {
                this.$message.error("不能上传大于3M的图片");
                return false;
            }
            return (isJPG || isPNG || isGIF) && isLt2M;
        },

        // elementUI mixins - emitter 中拷贝的
        dispatch(componentName, eventName, params) {
            var parent = this.$parent || this.$root;
            var name = parent.$options.componentName;

            while (parent && (!name || name !== componentName)) {
                parent = parent.$parent;

                if (parent) {
                    name = parent.$options.componentName;
                }
            }
            if (parent) {
                parent.$emit.apply(parent, [eventName].concat(params));
            }
        }
    }
};
</script>
<style>
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px !important;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
