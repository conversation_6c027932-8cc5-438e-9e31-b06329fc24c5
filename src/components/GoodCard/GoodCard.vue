<template>
  <div class="good-card">
    <div class="good-cardinfo">
      <div class="good-title">
        <div class="tag-title">
          <div class="good-tag">
            <el-tag type="primary" size="mini">地采</el-tag>
            <el-tag type="primary" size="mini">预售</el-tag>
            <el-tag type="warning" size="mini">代发</el-tag>
            <el-tag type="success" size="mini">渠道</el-tag>
          </div>
          <div class="good-title">
            <p>{{ "title" }}</p>
          </div>
        </div>
      </div>
      <div class="good-content">
        <div class="good-info">
          <p>国家：意大利</p>
          <p>类型：干红</p>
          <p>容量：750ml</p>
          <p>售价：￥188.0</p>
        </div>
        <div class="good-popularize">
          <p>文案：吴思明</p>
          <p>采购：夏振炎</p>
          <p>运营：程洪洲</p>
          <p>审核：朱成浩</p>
        </div>
        <div class="good-time">
          <p>上架：2020-07-10 20：20：20</p>
          <p>开售：2020-07-10 20：20：20</p>
          <p>下架：2020-07-10 20：20：20</p>
          <p>发货：2020-07-10 20：20：20</p>
        </div>
        <div class="good-stock">
          <p>库存：24</p>
          <p>已售：14</p>
          <p>已购：7单/8份</p>
          <p>前端：9/18</p>
        </div>
        <div class="good-saleinfo">
          <p>套餐：2</p>
          <div class="good-remark">
            <p>备注:时间，备注人</p>
            <el-button type="primary" size="mini">查看</el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="good-status">
      <p>{{ "待上架" }}</p>
    </div>
    <!-- <div class="opration">
        <el-input v-model="num"  size="mini" style="width:50px"></el-input>
        <el-button type="primary" size="default">待审核</el-button>
        <el-button type="primary" size="default">大人说</el-button>
        
    </div> -->
  </div>
</template>
<script>

export default {
    data(){
        return{
            num:""
        }
    }
}
</script>

<style lang="scss" scoped>
.good-card {
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  .good-cardinfo {
      width: 80%;
    .good-title {
      display: flex;
      justify-content: space-between;
      .tag-title {
        display: flex;
      }
    }
    .good-content {
      display: flex;
      justify-content: space-between;
      .good-saleinfo {
        .good-remark {
          display: flex;
          align-items: center;
          height: 30px;
          & > p {
            margin-bottom: 0;
          }
          & > .el-button {
            margin-left: 20px;
          }
        }
      }
    }
  }
  .good-status{
      width: 50px;
  }
//   .opration{
//       &>.el-button{
//           height: 20px;
//       }
//   }
}
</style>