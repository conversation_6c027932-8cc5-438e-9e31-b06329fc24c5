<template>
  <div class="sales-statistics" v-if="shouldShow">
    <div class="sales-text">
      <span class="channel-item flash-sale">闪购(含尾货):¥{{ formatNumber(flashSale.amount) }}/{{ flashSale.orders }}(单)</span>
      <span class="divider">/</span>
      <span class="channel-item cross-border">跨境:¥{{ formatNumber(crossBorder.amount) }}/{{ crossBorder.orders }}(单)</span>
      <span class="divider">/</span>
      <span class="channel-item instant">秒发:¥{{ formatNumber(instant.amount) }}/{{ instant.orders }}(单)</span>
      <span class="divider">/</span>
      <span class="channel-item clearance">尾货:¥{{ formatNumber(clearance.amount) }}/{{ clearance.orders }}(单)</span>
    </div>
    <div class="mini-chart" ref="chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getRealTimeSalesStatistics } from '@/services/sales'

export default {
  name: 'SalesStatistics',
  data() {
    return {
      chart: null,
      flashSale: { amount: 0, orders: 0 },
      crossBorder: { amount: 0, orders: 0 },
      instant: { amount: 0, orders: 0 },
      clearance: { amount: 0, orders: 0 },
      salesData: [],
      loading: false,
      shouldShow: false,
      checkTimer: null,
      dataTimer: null
    }
  },
  created() {
    // 检查当前路径
    this.checkShouldShow()
    // 每秒检查一次路径
    this.checkTimer = setInterval(() => {
      this.checkShouldShow()
    }, 1000)
  },
  mounted() {
    if (this.shouldShow) {
      this.initChart()
      this.fetchSalesData()
      // 每30秒刷新一次数据
      this.dataTimer = setInterval(() => {
        this.fetchSalesData()
      }, 30 * 1000)
    }
  },
  watch: {
    shouldShow: {
      handler(newVal) {
        if (newVal) {
          // 显示时初始化图表和数据
          this.$nextTick(() => {
            this.initChart()
            this.fetchSalesData()
            if (!this.dataTimer) {
              this.dataTimer = setInterval(() => {
                this.fetchSalesData()
              }, 30 * 1000)
            }
          })
        } else {
          // 不显示时清理定时器和图表
          if (this.dataTimer) {
            clearInterval(this.dataTimer)
            this.dataTimer = null
          }
          if (this.chart) {
            this.chart.dispose()
            this.chart = null
          }
        }
      },
      immediate: true
    }
  },
  beforeDestroy() {
    if (this.dataTimer) {
      clearInterval(this.dataTimer)
    }
    if (this.checkTimer) {
      clearInterval(this.checkTimer)
    }
    if (this.chart) {
      this.chart.dispose()
    }
  },
  methods: {
    checkShouldShow() {
      const currentPath = window.location.pathname
      this.shouldShow = currentPath === '/commodities/allGoods'
    },
    formatNumber(num) {
      return num.toLocaleString()
    },
    initChart() {
      this.chart = echarts.init(this.$refs.chart)
      this.updateChart([])
    },
    updateChart(data) {
      const hours = data.map(item => item.hour.split(' ')[1])
      
      const option = {
        grid: {
          top: 5,
          right: 5,
          bottom: 5,
          left: 5,
          containLabel: false
        },
        xAxis: {
          type: 'category',
          show: false,
          data: hours
        },
        yAxis: {
          type: 'value',
          show: false
        },
        series: [
          {
            name: '闪购',
            data: data.map(item => item.payment_amount || 0),
            type: 'line',
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0,
                  color: 'rgba(64,158,255,0.2)'
                }, {
                  offset: 1,
                  color: 'rgba(64,158,255,0)'
                }]
              }
            }
          },
          {
            name: '跨境',
            data: data.map(item => item.cross_border_amount || 0),
            type: 'line',
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#67C23A'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0,
                  color: 'rgba(103,194,58,0.2)'
                }, {
                  offset: 1,
                  color: 'rgba(103,194,58,0)'
                }]
              }
            }
          },
          {
            name: '秒发',
            data: data.map(item => item.instant_amount || 0),
            type: 'line',
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#E6A23C'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0,
                  color: 'rgba(230,162,60,0.2)'
                }, {
                  offset: 1,
                  color: 'rgba(230,162,60,0)'
                }]
              }
            }
          },
          {
            name: '尾货',
            data: data.map(item => item.clearance_amount || 0),
            type: 'line',
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#F56C6C'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0,
                  color: 'rgba(245,108,108,0.2)'
                }, {
                  offset: 1,
                  color: 'rgba(245,108,108,0)'
                }]
              }
            }
          }
        ]
      }
      
      this.chart && this.chart.setOption(option)
    },
    async fetchSalesData() {
      if (this.loading || !this.shouldShow) return
      this.loading = true
      try {
        // 获取所有频道的数据
        const [flashSaleData, crossBorderData, instantData, clearanceData] = await Promise.all([
          this.fetchChannelData(0), // 闪购（含尾货）
          this.fetchChannelData(1), // 跨境
          this.fetchChannelData(2), // 秒发
          this.fetchChannelData(3)  // 尾货
        ])

        // 更新各频道数据
        this.flashSale = flashSaleData.totals
        this.crossBorder = crossBorderData.totals
        this.instant = instantData.totals
        this.clearance = clearanceData.totals

        // 合并所有频道的小时数据用于图表显示
        const mergedData = flashSaleData.hourly.map((item, index) => ({
          hour: item.hour,
          payment_amount: item.payment_amount || 0,
          cross_border_amount: crossBorderData.hourly[index]?.payment_amount || 0,
          instant_amount: instantData.hourly[index]?.payment_amount || 0,
          clearance_amount: clearanceData.hourly[index]?.payment_amount || 0
        }))

        this.updateChart(mergedData)
      } catch (error) {
        console.error('Failed to fetch sales data:', error)
        this.$message.error('获取销售数据失败')
      } finally {
        this.loading = false
      }
    },
    // 获取指定频道的销售数据
    async fetchChannelData(periodType) {
      const now = new Date()
      const startDate = new Date(now)
      startDate.setHours(0, 0, 0, 0)
      const endDate = new Date(now)
      endDate.setHours(23, 0, 0, 0)

      const params = {
        start_hour_time: `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}-${String(startDate.getDate()).padStart(2, '0')} 00`,
        end_hour_time: `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')} 23`,
        period_type: periodType.toString(),
        import_type: '',
        buyer: '',
        source_event: '',
        goods_type: [0],
        log: '',
        page: 1,
        page_nums: 24,
        user_type: 0
      }

      const response = await getRealTimeSalesStatistics(params)
      if (response.data.error_code === 0) {
        const data = response.data.data.general_json || []
        // 计算总金额和订单数
        const totals = data.reduce((acc, curr) => ({
          amount: acc.amount + (curr.payment_amount || 0),
          orders: acc.orders + (curr.order_counts || 0)
        }), { amount: 0, orders: 0 })

        return {
          hourly: data,
          totals: totals
        }
      }
      return {
        hourly: [],
        totals: { amount: 0, orders: 0 }
      }
    }
  }
}
</script>

<style scoped>
.sales-statistics {
  display: flex;
  align-items: center;
  margin-right: 20px;
  padding: 8px 0;
  min-width: 600px;
}

.sales-text {
  font-size: 13px;
  margin-right: 15px;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.channel-item {
  display: inline-block;
  font-weight: 500;
}

.flash-sale {
  color: #409EFF;
}

.cross-border {
  color: #67C23A;
}

.instant {
  color: #E6A23C;
}

.clearance {
  color: #F56C6C;
}

.divider {
  color: #909399;
  margin: 0 8px;
  font-weight: normal;
}

.mini-chart {
  width: 120px;
  height: 40px;
  flex-shrink: 0;
  border-left: 1px solid #ebeef5;
  margin-left: 15px;
  padding-left: 15px;
}
</style> 