<template>
    <div>
        <!-- begin #header -->
        <div class="header navbar-default" id="header">
            <!-- begin navbar-header -->
            <div class="navbar-header">
                <span class="navbar-brand" to="/dashboard/v2"
                    ><i
                        class="el-icon-s-home"
                        @click="selectPlatform"
                        style="
                            margin-right: 4px;
                            font-size: 22px;
                            cursor: pointer;
                        "
                    ></i>
                    <b>商品管理</b>
                </span>
            </div>

            <!-- end navbar-header -->

            <!-- begin header-nav -->
            <ul class="navbar-nav navbar-right" style="align-items: center">
                <li class="nav-item" v-if="shouldShowSalesStats">
                    <SalesStatistics />
                </li>
                <li class="nav-item">
                    <div class="vinechat-button" @click="openChatGPT">
                        <span class="icon">💬</span>
                        <span class="font"> VineChat(GPT-4o) </span>
                        <!-- <el-button
                            type="danger"
                            size="mini"
                            @click="chatGPTdialogVisible = true"
                            >VinehooChat</el-button
                        > -->
                    </div>
                </li>
                <!-- <li class="navbar-form">
                    <form name="search_form" v-on:submit="checkForm">
                        <div class="form-group">
                            <input
                                class="form-control"
                                placeholder="Enter keyword"
                                type="text"
                            />
                            <button class="btn btn-search" type="submit">
                                <i class="fa fa-search"></i>
                            </button>
                        </div>
                    </form>
                </li> -->
                <!-- <b-nav-item-dropdown
                    menu-class="media-list dropdown-menu-right"
                    no-caret
                    toggle-class="f-s-14"
                >
                    <template slot="button-content">
                        <i class="fa fa-bell"></i>
                        <span class="label">0</span>
                    </template>
                    <b-dropdown-header>NOTIFICATIONS (0)</b-dropdown-header>
                    <b-dropdown-item
                        class="text-center width-300"
                        href="javascript:;"
                    >
                        No notification found
                    </b-dropdown-item>
                </b-nav-item-dropdown> -->

                <b-nav-item-dropdown
                    class="dropdown navbar-user"
                    menu-class="dropdown-menu-right"
                    no-caret
                >
                    <template slot="button-content">
                        <img :src="userinfo.avatar" alt="" />
                        <span style="font-size: 14px" class="d-md-inline">{{
                            userinfo.realname
                        }}</span>
                        <b class="caret"></b>
                    </template>

                    <b-dropdown-item @click="selectPlatform"
                        >切换系统</b-dropdown-item
                    >
                    <b-dropdown-divider></b-dropdown-divider>
                    <b-dropdown-item @click="logOut">退出登录</b-dropdown-item>
                </b-nav-item-dropdown>
                <li
                    class="divider d-none d-md-block"
                    v-if="pageOptions.pageWithTwoSidebar"
                ></li>
                <li
                    class="d-none d-md-block"
                    v-if="pageOptions.pageWithTwoSidebar"
                >
                    <a
                        class="f-s-14"
                        href="javascript:;"
                        v-on:click="toggleRightSidebarCollapsed"
                    >
                        <i class="fa fa-th"></i>
                    </a>
                </li>
            </ul>

            <!-- end header navigation right -->
        </div>
        <!-- end #header -->

        <!-- chatGPT -->
        <el-dialog
            title="VineChat"
            :visible.sync="chatGPTdialogVisible"
            :close-on-click-modal="false"
            fullscreen
            :before-close="chatGPThandleClose"
        >
            <ChatGPT v-if="chatGPTdialogVisible"></ChatGPT>
            <!-- <Test v-if="chatGPTdialogVisible" /> -->
        </el-dialog>
    </div>
</template>

<script>
import PageOptions from "../../config/PageOptions.vue";
import Cookies from "js-cookie";
import ChatGPT from "../chatGPT/index.vue";
import SalesStatistics from "../sales/SalesStatistics.vue";
// import Test from "../chatGPT/test.vue";
export default {
    name: "Header",
    components: {
        ChatGPT,
        SalesStatistics,
        // Test,
    },
    data() {
        return {
            chatGPTdialogVisible: false,
            userinfo: {
                realname: "",
                avatar: "",
                title: "",
            },
            pageOptions: PageOptions,
        };
    },
    computed: {
        shouldShowSalesStats() {
            const currentPath = window.location.pathname;
            console.log("Current path:", currentPath);
            return currentPath === "/commodities/allGoods";
        },
    },
    mounted() {
        this.getUserInfo();
    },
    methods: {
        openChatGPT() {
            window.open("http://251872w5u8.zicp.fun/");
        },
        chatGPThandleClose() {
            this.chatGPTdialogVisible = false;
        },
        getUserInfo() {
            const userinfo = localStorage.getItem("userinfo");
            this.userinfo = JSON.parse(userinfo);
        },
        selectPlatform() {
            window.location.replace("/login/systems");
        },
        logOut() {
            window.location.replace("/login/login");
            Cookies.remove("token");
            Cookies.remove("uid");
        },
        toggleMobileSidebar() {
            this.pageOptions.pageMobileSidebarToggled =
                !this.pageOptions.pageMobileSidebarToggled;
        },
        toggleMobileRightSidebar() {
            this.pageOptions.pageMobileRightSidebarToggled =
                !this.pageOptions.pageMobileRightSidebarToggled;
        },
        toggleMobileTopMenu() {
            this.pageOptions.pageMobileTopMenu =
                !this.pageOptions.pageMobileTopMenu;
        },
        toggleMobileMegaMenu() {
            this.pageOptions.pageMobileMegaMenu =
                !this.pageOptions.pageMobileMegaMenu;
        },
        toggleRightSidebar() {
            this.pageOptions.pageRightSidebarToggled =
                !this.pageOptions.pageRightSidebarToggled;
        },
        toggleRightSidebarCollapsed() {
            this.pageOptions.pageRightSidebarCollapsed =
                !this.pageOptions.pageRightSidebarCollapsed;
        },
        checkForm: function (e) {
            e.preventDefault();
            this.$router.push({ path: "/extra/search" });
        },
    },
};
</script>
<style lang="scss" scoped>
.font {
    font-size: 14px;
    font-weight: bold;
}
.vinechat-button {
    background-color: #ff0000;
    color: #ffffff;
    // width: 110px;
    height: 40px;
    border: none;
    padding: 10px 20px;
    font-size: 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 15px;
}

.vinechat-button:hover {
    background-color: #d90000;
}

.vinechat-button:active {
    background-color: #b00000;
}

.vinechat-button .icon {
    margin-right: 8px;
}
.top {
    margin-top: 5vh !important;
}
.chatBtn {
    width: 110px;
    height: 30px;
    cursor: pointer;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-top: 8px;
}
/deep/ .el-dialog {
    display: flex;
    flex-direction: column;
    background: #343540 !important;
}
/deep/.el-dialog__title {
    line-height: 24px;
    font-size: 18px;
    color: #fff;
}
/deep/.el-dialog__body {
    flex: 1;
    overflow: hidden;
}
/deep/.el-popover {
    position: absolute;
    background: #343540;
    min-width: 150px;
    border-radius: 4px;
    border: 1px solid #343540;
    padding: 12px;
    z-index: 2000;
    color: #606266;
    line-height: 1.4;
    text-align: justify;
    font-size: 14px;
    -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 10%);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 10%);
    word-break: break-all;
}

.nav-item {
    display: flex;
    align-items: center;
}
</style>
