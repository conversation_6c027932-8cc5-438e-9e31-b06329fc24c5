<template>
    <div class="approval-flow">
        <ItemNode
            :node="node"
            :parentNode="parentNode"
            :isChildrenNodeDialogFirst="isChildrenNodeDialogFirst"
        ></ItemNode>
        <EndNode></EndNode>
    </div>
</template>

<script>
import ItemNode from "./ItemNode.vue";
import EndNode from "./EndNode.vue";
export default {
    props: {
        node: {
            type: Object,
            default: () => ({}),
        },
        parentNode: {
            type: Object,
            default: () => null,
        },
        isChildrenNodeDialogFirst: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        ItemNode,
        EndNode,
    },
};
</script>

<style lang="scss" scoped>
.approval-flow {
    display: inline-block;
    background: #f8f8f8;
}
</style>
