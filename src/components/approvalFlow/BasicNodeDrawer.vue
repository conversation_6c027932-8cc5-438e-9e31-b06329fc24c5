<template>
    <el-drawer
        :visible="visible"
        @update:visible="$emit('update:visible', false)"
        append-to-body
        :with-header="false"
        @close="onClose"
    >
        <el-form label-width="100px" style="padding: 20px 10px">
            <el-form-item label="节点ID">
                <el-input v-model="node.id" disabled></el-input>
            </el-form-item>
            <el-form-item label="节点名称">
                <el-input v-model="node.name"></el-input>
            </el-form-item>
            <el-form-item label="自定义ID">
                <el-input v-model="node.customer_id"></el-input>
            </el-form-item>
            <el-form-item v-if="isChildrenNode" label="是否必须">
                <el-radio-group v-model="node.must_process">
                    <el-radio class="mgb-0" :label="true">是</el-radio>
                    <el-radio class="mgb-0" :label="false">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item v-if="isApproval" label="审批模式">
                <el-radio-group v-model="node.approval_mode">
                    <el-radio class="mgb-0" :label="1">或签</el-radio>
                    <el-radio class="mgb-0" :label="2">会签</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="操作人">
                <UserList :list="node.process_info"></UserList>
            </el-form-item>
            <el-form-item label="参与人">
                <UserList :list="node.participant"></UserList>
            </el-form-item>
            <template v-if="isApproval">
                <el-form-item label="通过通知">
                    <UserList :list="node.approval_notice" isCheck></UserList>
                </el-form-item>
                <el-form-item label="拒绝通知">
                    <UserList :list="node.denied_notice" isCheck></UserList>
                </el-form-item>
                <el-form-item label="驳回返回">
                    <el-input v-model="node.denied_node_id"></el-input>
                </el-form-item>
            </template>
            <el-form-item label="设置办理">
                <el-input
                    :value="processNodeStr"
                    @input="onStrInput($event, 'process_node_ids')"
                    placeholder="多个用英文逗号分隔"
                ></el-input>
            </el-form-item>
            <el-form-item label="设置参与">
                <el-input
                    :value="participantNodeStr"
                    @input="onStrInput($event, 'participant_node_ids')"
                    placeholder="多个用英文逗号分隔"
                ></el-input>
            </el-form-item>
            <el-form-item label="子流程code">
                <el-input
                    :value="node.child_flow_code"
                    @input="onChildFlowCodeInput"
                ></el-input>
                <el-form-item label="是否需要完成流程" label-width="150px">
                    <el-checkbox
                        :value="node.check_child_flow_complete"
                        @input="onCheckChildFlowCompleteInput"
                    ></el-checkbox>
                </el-form-item>
            </el-form-item>
        </el-form>
    </el-drawer>
</template>

<script>
import { mapActions } from "vuex";
import UserList from "./UserList.vue";

export default {
    name: "BasicNodeDrawer",
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        node: {
            type: Object,
            default: () => ({}),
        },
        isChildrenNode: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        UserList,
    },
    data: () => ({}),
    computed: {
        isHaddle({ node }) {
            return node.mode === "Handle";
        },
        isApproval({ node }) {
            return node.mode === "Approval";
        },
        processNodeStr() {
            return this.node.process_node_ids.join();
        },
        participantNodeStr() {
            return this.node.participant_node_ids.join();
        },
    },
    watch: {
        visible(newVal) {
            if (newVal) {
                if (!this.node.approval_notice) {
                    this.$set(this.node, "approval_notice", []);
                }
                if (!this.node.denied_notice) {
                    this.$set(this.node, "denied_notice", []);
                }
                this.getRoleList();
                this.getAdminList();
            }
        },
    },
    methods: {
        ...mapActions(["getRoleList", "getAdminList"]),
        onStrInput(value, key) {
            this.node[key] = value ? value.split(",") : [];
        },
        onClose() {},
        onChildFlowCodeInput(value) {
            this.$set(this.node, "child_flow_code", value);
        },
        onCheckChildFlowCompleteInput(value) {
            this.$set(this.node, "check_child_flow_complete", value);
        },
    },
};
</script>

<style lang="scss" scoped></style>
