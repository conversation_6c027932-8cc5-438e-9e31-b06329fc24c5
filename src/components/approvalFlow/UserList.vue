<template>
    <div>
        <el-button
            v-if="!list.length"
            type="primary"
            size="mini"
            @click="create"
            >创建</el-button
        >

        <el-row
            v-for="(item, index) in list"
            :key="index"
            type="flex"
            class="mgb-10"
        >
            <el-select
                v-model="item.genre"
                placeholder="请选择"
                class="mgr-10 w-mini"
            >
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.text"
                    :value="item.value"
                />
            </el-select>
            <div :class="{ 'mgr-10': item.genre }">
                <el-select
                    v-if="item.genre === 1"
                    :value="item.vos_uid || ''"
                    filterable
                    placeholder="请选择"
                    @change="onSelectAdminChange($event, index)"
                    class="w-mini"
                >
                    <el-option
                        v-for="item in adminList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
                <el-select
                    v-else-if="item.genre === 2"
                    :value="+item.role_id || ''"
                    filterable
                    placeholder="请选择"
                    @change="onSelectRoleChange($event, index)"
                    class="w-mini"
                >
                    <el-option
                        v-for="item in roleList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
                <el-input
                    v-else-if="item.genre === 4"
                    v-model="item.join_node_id"
                    class="w-mini"
                ></el-input>
            </div>
            <el-button type="primary" size="mini" @click="add(index)"
                >添加</el-button
            >
            <br />
            <el-button
                v-if="list.length"
                type="danger"
                size="mini"
                @click="remove(index)"
                >删除</el-button
            >
        </el-row>
    </div>
</template>

<script>
import { mapState } from "vuex";
const getUser = () => ({
    genre: "",
    we_com_id: "",
    name: "",
    vos_uid: 0,
    role_id: "",
    join_node_id: "",
});
export default {
    props: {
        list: {
            type: Array,
            default: () => [],
        },
        isCheck: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        ...mapState(["roleList", "adminList"]),
        options() {
            if (this.isCheck) {
                return [
                    {
                        value: 1,
                        text: "管理员",
                    },
                ];
            }
            return [
                {
                    value: 1,
                    text: "管理员",
                },
                {
                    value: 2,
                    text: "角色",
                },
                {
                    value: 3,
                    text: "发起者",
                },
                {
                    value: 4,
                    text: "关联的节点ID",
                },
            ];
        },
    },
    methods: {
        create() {
            this.list.push(getUser());
        },
        add(index) {
            this.list.splice(index + 1, 0, getUser());
        },
        remove(index) {
            this.list.splice(index, 1);
        },
        onSelectAdminChange(value, index) {
            const findAdmin = this.adminList.find(({ id }) => value === id);
            const { id, userid, name } = findAdmin;
            Object.assign(this.list[index], {
                vos_uid: id,
                we_com_id: userid,
                name,
            });
        },
        onSelectRoleChange(value, index) {
            const findRole = this.roleList.find(({ id }) => value === id);
            const { id, name } = findRole;
            Object.assign(this.list[index], {
                role_id: `${id}`,
                name,
            });
        },
    },
};
</script>
