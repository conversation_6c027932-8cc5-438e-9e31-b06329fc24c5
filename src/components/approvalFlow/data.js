export const getNode = (mode = "Handle") => {
    let name;
    switch (mode) {
        case "Handle":
            name = "办理人";
            break;
        case "Approval":
            name = "审批人";
            break;
    }
    const node = {
        id: `${Date.now()}`,
        name,
        customer_id: "",
        next: null,
        children: null,
        condition: null,
        mode,
        approval_mode: 1,
        process_info: [],
        participant: [],
        approval_notice: [],
        denied_notice: [],
        must_process: true,
        process_node_ids: [],
        participant_node_ids: [],
        denied_node_id: "",
        child_flow_code: "",
        check_child_flow_complete: false,
    };
    return node;
};

export const getConditionItemNode = () => {
    const conditionItemNode = {
        id: `${Date.now()}`,
        name: "ConditionItemNode",
        next: null,
        children: null,
        condition: null,
        mode: "ConditionItem",
        approval_mode: 1,
        process_info: [],
        participant: [],
        approval_notice: [],
        denied_notice: [],
        process_node_ids: [],
        participant_node_ids: [],
        denied_node_id: "",
        child_flow_code: "",
        check_child_flow_complete: "",
        values: [
            // { key: "频道", value: [] },
            // { key: "是否复制", value: [] },
        ],
    };
    return conditionItemNode;
};

export const getConditionNode = () => {
    const conditionNode = {
        id: `${Date.now()}`,
        name: "条件分支",
        next: null,
        children: null,
        condition: [getConditionItemNode(), getConditionItemNode()],
        mode: "Condition",
        approval_mode: 1,
        process_info: [],
        approval_notice: [],
        denied_notice: [],
    };
    return conditionNode;
};
