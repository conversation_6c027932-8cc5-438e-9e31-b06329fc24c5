<template>
    <el-dialog
        :title="node.name"
        :visible="visible"
        @update:visible="$emit('update:visible', false)"
        width="80%"
        append-to-body
        class="children-node-dialog"
    >
        <div class="children-node-dialog__body">
            <ApprovalFlow
                :node="node"
                :parentNode="parentNode"
                isChildrenNodeDialogFirst
            ></ApprovalFlow>
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        node: {
            type: Object,
            default: () => ({}),
        },
        parentNode: {
            type: Object,
            default: () => ({}),
        },
    },
    components: {
        ApprovalFlow: () => import("./index.vue"),
    },
};
</script>

<style lang="scss" scoped>
.children-node-dialog {
    /deep/ .el-dialog {
        padding-bottom: 10px;
        background: #f8f8f8;
        &__body {
            padding: 0;
            display: flex;
            justify-content: center;
        }

        &__footer {
            padding: 10px;
        }
    }

    &__body {
        max-width: 100%;
        overflow: scroll;
    }
}
</style>
