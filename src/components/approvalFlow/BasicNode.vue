<template>
    <NodeContainer :node="node" :isChildrenNode="isChildrenNode">
        <div
            class="basic-node"
            :class="{
                'is-hidden-arrow': !parentNode || isChildrenNode,
            }"
        >
            <div
                class="basic-node__wrapper"
                :class="{
                    'is-dotted-box':
                        isShowChildrenList &&
                        node.children &&
                        node.children.length,
                }"
            >
                <div class="basic-node__container" @click="drawer = true">
                    <div
                        class="basic-node__title"
                        :class="{ 'is-approval': isApproval }"
                    >
                        <div>
                            <span>{{ name }}</span>
                            <template v-if="isApproval">
                                <span v-if="node.approval_mode === 1"
                                    >-或签</span
                                >
                                <span v-else-if="node.approval_mode === 2"
                                    >-会签</span
                                >
                            </template>
                        </div>
                        <div class="basic-node__btns">
                            <el-button
                                v-if="isChildrenNode"
                                type="primary"
                                size="mini"
                                @click.stop="visible = true"
                                style="padding: 2px 4px"
                                >子节点</el-button
                            >
                            <!-- <el-button
                                v-if="isHaddle && parentNode"
                                type="primary"
                                size="mini"
                                @click.stop="onAddChildrenNode"
                                style="padding: 2px 4px"
                                >添加</el-button
                            > -->
                            <el-button
                                v-if="parentNode && !isChildrenNodeDialogFirst"
                                type="danger"
                                size="mini"
                                @click.stop="onDeleteBasicNode"
                                style="padding: 2px 4px"
                                >删除</el-button
                            >
                        </div>
                    </div>
                    <div class="basic-node__content">
                        <div
                            v-for="(item, index) in node.process_info"
                            :key="index"
                        >
                            <template v-if="item.genre === 1">
                                <span>管理员：</span>
                                <span>{{ item.name }}</span>
                            </template>

                            <template v-else-if="item.genre === 2">
                                <span>角色：</span>
                                <span>{{ item.name }}</span>
                            </template>

                            <template v-else-if="item.genre === 3">
                                <span>发起者</span>
                            </template>

                            <template v-else-if="item.genre === 4">
                                <span>关联的节点ID：</span>
                                <span>{{ item.join_node_id }}</span>
                            </template>
                        </div>
                    </div>
                </div>
                <div
                    v-if="
                        isShowChildrenList &&
                        node.children &&
                        node.children.length
                    "
                    class="basic-node__children"
                >
                    <BasicNode
                        v-for="(item, index) in node.children"
                        :key="index"
                        :node="item"
                        :parentNode="node"
                        isChildrenNode
                        :index="index"
                        :isShowChildrenList="false"
                    ></BasicNode>
                </div>
            </div>
        </div>
        <BasicNodeDrawer
            :visible.sync="drawer"
            :node="node"
            :isChildrenNode="isChildrenNode"
        ></BasicNodeDrawer>
        <ChildrenNodeDialog
            :visible.sync="visible"
            :node="node"
            :parentNode="parentNode"
        >
        </ChildrenNodeDialog>
    </NodeContainer>
</template>

<script>
import NodeContainer from "./NodeContainer.vue";
import BasicNodeDrawer from "./BasicNodeDrawer.vue";
import ChildrenNodeDialog from "./ChidrenNodeDialog.vue";
import { getNode } from "./data";
export default {
    name: "BasicNode",
    props: {
        node: {
            type: Object,
            default: () => ({}),
        },
        parentNode: {
            type: Object,
            default: () => ({}),
        },
        isChildrenNode: {
            type: Boolean,
            default: false,
        },
        index: {
            type: Number,
            default: 0,
        },
        isShowChildrenList: {
            type: Boolean,
            default: true,
        },
        isChildrenNodeDialogFirst: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        NodeContainer,
        BasicNodeDrawer,
        ChildrenNodeDialog,
    },
    computed: {
        name({ node }) {
            const { name, mode } = node;
            if (name) return name;
            switch (mode) {
                case "Handle":
                    return "办理人";
                case "Approval":
                    return "审批人";
                default:
                    return "";
            }
        },
        isHaddle({ node }) {
            return node.mode === "Handle";
        },
        isApproval({ node }) {
            return node.mode === "Approval";
        },
    },
    data: () => ({
        drawer: false,
        visible: false,
    }),
    methods: {
        onAddChildrenNode() {
            const node = getNode();
            if (this.isChildrenNode) {
                this.parentNode.children.splice(this.index, 0, node);
            } else {
                if (this.node.children) {
                    this.node.children.unshift(node);
                } else {
                    // this.node.children = [node];
                    this.$set(this.node, "children", [node]);
                }
            }
        },
        onDeleteBasicNode() {
            const message =
                this.node.children && this.node.children.length
                    ? "该节点包括同级节点将被删除，确认继续?"
                    : "该节点将被删除，确认继续?";
            this.$confirm(message, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                if (this.isChildrenNode) {
                    this.parentNode.children.splice(this.index, 1);
                } else {
                    this.parentNode.next = this.node.next;
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.basic-node {
    position: relative;

    &::before {
        content: "";
        position: absolute;
        left: 50%;
        transform: translate(-50%, -100%);
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 8px 5px 0;
        border-color: #dbdbdb transparent transparent;
    }

    &.is-hidden-arrow {
        &::before {
            display: none;
        }
    }

    &__wrapper {
        display: flex;
        &.is-dotted-box {
            max-width: 40vw;
            overflow: scroll;
            padding: 10px;
            border: 1px dotted #e80404;
            background: #f8f8f8;
        }
    }

    &__container {
        flex-shrink: 0;
        width: 240px;
        cursor: pointer;

        &:hover {
            .basic-node__btns {
                display: block;
            }
        }
    }

    &__btns {
        display: none;
    }

    &__title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        min-height: 36px;
        padding: 5px 14px;
        font-size: 14px;
        color: #3d91eb;
        background: #e2f0ff;

        &.is-approval {
            color: #ff0000;
            background: #f7e9e9;
        }
    }

    &__content {
        min-height: 60px;
        padding: 5px 14px;
        background: #fff;
    }

    &__children {
        display: flex;

        > div {
            position: relative;
            margin-left: 50px;

            &::before {
                content: "";
                position: absolute;
                width: 50px;
                border-top: 2px dotted #dbdbdb;
                left: 0;
                top: 50%;
                transform: translatex(-100%);
            }
        }
    }
}
</style>
