<template>
    <div>
        <ConditionNode
            v-if="node.mode === 'Condition'"
            :node="node"
            :parentNode="parentNode"
        ></ConditionNode>
        <BasicNode
            v-else
            :node="node"
            :parentNode="parentNode"
            :isChildrenNodeDialogFirst="isChildrenNodeDialogFirst"
        ></BasicNode>
        <ItemNode
            v-if="node.next"
            :node="node.next"
            :parentNode="node"
        ></ItemNode>
    </div>
</template>

<script>
import BasicNode from "./BasicNode.vue";
import ConditionNode from "./ConditionNode.vue";
export default {
    name: "ItemNode",
    props: {
        node: {
            type: Object,
            default: () => ({}),
        },
        parentNode: {
            type: Object,
            default: () => null,
        },
        isChildrenNodeDialogFirst: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        BasicNode,
        ConditionNode,
    },
};
</script>

<style lang="scss" scoped></style>
