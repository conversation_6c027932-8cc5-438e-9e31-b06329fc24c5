<template>
    <div class="end-node">
        <el-button>结束</el-button>
    </div>
</template>

<script>
export default {
    name: "EndNode",
};
</script>

<style lang="scss" scoped>
.end-node {
    display: flex;
    justify-content: center;

    &::before {
        content: "";
        position: absolute;
        transform: translateY(-100%);
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 8px 5px 0;
        border-color: #dbdbdb transparent transparent;
    }
}
</style>
