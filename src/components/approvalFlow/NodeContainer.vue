<template>
    <div class="node-container">
        <slot></slot>
        <div v-if="!isEndNode && !isChildrenNode" class="node-container__add">
            <div class="node-container__add-btn">
                <el-popover placement="right" trigger="hover">
                    <div>
                        <el-button type="text" @click="onAddNode('Handle')"
                            >办理节点</el-button
                        >
                    </div>
                    <div>
                        <el-button type="text" @click="onAddNode('Approval')"
                            >审批节点</el-button
                        >
                    </div>
                    <div v-if="!isHiddenConditionAdd">
                        <el-button type="text" @click="onAddNode('Condition')"
                            >条件节点</el-button
                        >
                    </div>
                    <el-button slot="reference" type="primary" size="mini"
                        >添加</el-button
                    >
                </el-popover>
            </div>
        </div>
    </div>
</template>

<script>
import { getNode, getConditionNode } from "./data";
export default {
    props: {
        node: {
            type: Object,
            default: () => ({}),
        },
        isEndNode: {
            type: Boolean,
            default: false,
        },
        isChildrenNode: {
            type: Boolean,
            default: false,
        },
        isHiddenConditionAdd: {
            type: Boolean,
            default: false,
        },
    },
    methods: {
        onAddNode(mode) {
            switch (mode) {
                case "Handle":
                case "Approval":
                    const node = getNode(mode);
                    if (this.node.next) {
                        node.next = this.node.next;
                    }
                    this.node.next = node;
                    break;
                case "Condition":
                    const conditionNode = getConditionNode();
                    conditionNode.condition[0].name = "条件1";
                    conditionNode.condition[1].name = "默认条件";
                    if (this.node.next) {
                        conditionNode.next = this.node.next;
                    }
                    this.node.next = conditionNode;
                    break;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.node-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    &__add {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 150px;

        &::before {
            content: "";
            position: absolute;
            width: 2px;
            height: 100%;
            background: #dbdbdb;
        }

        &-btn {
            z-index: 1;
        }
    }
}
</style>
