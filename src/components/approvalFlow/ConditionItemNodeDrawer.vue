<template>
    <el-drawer
        :visible="visible"
        @update:visible="$emit('update:visible', false)"
        append-to-body
        :with-header="false"
        @close="onClose"
    >
        <el-form label-width="100px" style="padding: 20px">
            <el-form-item label="节点名称">
                <el-input v-model="node.name"></el-input>
            </el-form-item>
            <el-form-item :label="values[0].key">
                <el-checkbox-group v-model="values[0].value">
                    <el-checkbox label="闪购">闪购</el-checkbox>
                    <el-checkbox label="秒发">秒发</el-checkbox>
                    <el-checkbox label="跨境">跨境</el-checkbox>
                    <el-checkbox label="尾货">尾货</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item :label="values[1].key">
                <el-radio-group
                    :value="values[1].value[0]"
                    @input="values[1].value = [$event]"
                >
                    <el-radio class="mgb-0" label="是">是</el-radio>
                    <el-radio class="mgb-0" label="否">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item v-if="parentNode.condition.length > 2" label="优先级">
                <el-radio-group v-model="level">
                    <el-radio
                        v-for="item in parentNode.condition.length - 1"
                        :key="item"
                        class="mgb-0"
                        :label="item"
                        >{{ item }}</el-radio
                    >
                </el-radio-group>
            </el-form-item>
        </el-form>
    </el-drawer>
</template>

<script>
export default {
    name: "ConditionColNodeDrawer",
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        node: {
            type: Object,
            default: () => ({}),
        },
        parentNode: {
            type: Object,
            default: () => ({}),
        },
        index: {
            type: Number,
            default: 0,
        },
    },
    data: () => ({
        level: 0,
        values: [
            { key: "频道", value: [] },
            { key: "是否复制", value: [] },
        ],
    }),
    watch: {
        visible(newVal) {
            if (newVal) {
                this.level = this.index + 1;
                this.values = this.$options
                    .data()
                    .values.map(({ key, value }) => {
                        const findItem = this.node.values.find(
                            (item) => item.key === key
                        );
                        if (findItem)
                            return { key, value: [...findItem.value] };
                        return { key, value };
                    });
            }
        },
    },
    methods: {
        onClose() {
            const filteredValues = this.values.filter(
                (item) => item.value.length
            );
            this.node.values = filteredValues;
            if (this.index + 1 === this.level) return;
            const condition = this.parentNode.condition;
            const node = condition[this.index];
            const swapIndex = this.level - 1;
            const swapNode = condition[swapIndex];
            this.parentNode.condition.splice(this.index, 1, swapNode);
            this.parentNode.condition.splice(swapIndex, 1, node);
        },
    },
};
</script>

<style lang="scss" scoped></style>
