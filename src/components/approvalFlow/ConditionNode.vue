<template>
    <div>
        <NodeContainer :node="node" isHiddenConditionAdd>
            <div class="condition-node">
                <div class="condition-node__btn">
                    <el-button
                        size="mini"
                        type="primary"
                        round
                        @click="onAddCondition"
                        >添加条件</el-button
                    >
                </div>
                <div class="condition-node__items">
                    <ConditionItemNode
                        v-for="(item, index) in node.condition"
                        :key="index"
                        :node="item"
                        :parentNode="node"
                        :index="index"
                        @del="onDelCondition(index)"
                    ></ConditionItemNode>
                </div>
            </div>
        </NodeContainer>
    </div>
</template>

<script>
import NodeContainer from "./NodeContainer.vue";
import ConditionItemNode from "./ConditionItemNode.vue";
import { getConditionItemNode } from "./data";
export default {
    name: "ConditionNode",
    props: {
        node: {
            type: Object,
            default: () => ({}),
        },
        parentNode: {
            type: Object,
            default: () => ({}),
        },
    },
    components: {
        NodeContainer,
        ConditionItemNode,
    },
    methods: {
        onAddCondition() {
            const start = this.node.condition.length - 1;
            const conditionItemNode = getConditionItemNode();
            conditionItemNode.name = `条件${start + 1}`;
            this.node.condition.splice(start, 0, conditionItemNode);
        },
        onDelCondition(index) {
            const length = this.node.condition.length;
            const message =
                length > 2
                    ? "该条件分支下的所有节点将被删除，确认继续?"
                    : "该条件分支及默认条件分支下的所有节点将被删除，确认继续?";
            this.$confirm(message, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                if (length > 2) {
                    this.node.condition.splice(index, 1);
                } else {
                    this.parentNode.next = this.node.next;
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.condition-node {
    position: relative;

    &__btn {
        position: absolute;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
    }

    &__items {
        display: flex;
    }
}
</style>
