<template>
    <div class="condition-col-node">
        <div class="top-line"></div>
        <div class="bottom-line"></div>
        <div class="condition-col-node__wrapper">
            <NodeContainer :node="node" isHiddenConditionAdd>
                <div
                    class="condition"
                    :class="{ 'is-last': isLast }"
                    @click="onShowDrawer"
                >
                    <div class="condition__title">
                        <div class="condition__tag">
                            <el-tag type="danger" size="mini">条件</el-tag>
                            <el-tag type="warning" size="mini"
                                >优先级{{ index + 1 }}</el-tag
                            >
                        </div>
                        <span>{{ node.name }}</span>
                        <div class="condition__btns">
                            <el-button
                                v-if="!isLast"
                                type="danger"
                                size="mini"
                                @click.stop="onDel"
                                style="padding: 2px 4px"
                                >删除</el-button
                            >
                        </div>
                    </div>
                    <div class="condition__content">
                        <div v-if="isLast">
                            未满足其他条件分支的情况，将使用默认流程
                        </div>
                        <div v-else>
                            <div
                                v-for="(item, index) in node.values"
                                :key="index"
                            >
                                {{ item.key }}：{{ item.value }}
                            </div>
                        </div>
                    </div>
                </div>
                <ConditionItemNodeDrawer
                    :visible.sync="drawer"
                    :node="node"
                    :parentNode="parentNode"
                    :index="index"
                ></ConditionItemNodeDrawer>
            </NodeContainer>
            <ItemNode
                v-if="node && (node.next || node.condition)"
                :node="node.next || node.condition"
                :parentNode="node"
            ></ItemNode>
        </div>
    </div>
</template>

<script>
import NodeContainer from "./NodeContainer.vue";
import ConditionItemNodeDrawer from "./ConditionItemNodeDrawer.vue";
export default {
    name: "ConditionItemNode",
    props: {
        node: {
            type: Object,
            default: () => ({}),
        },
        parentNode: {
            type: Object,
            default: () => ({}),
        },
        index: {
            type: Number,
            default: 0,
        },
    },
    components: {
        NodeContainer,
        ItemNode: () => import("./ItemNode"),
        ConditionItemNodeDrawer,
    },
    computed: {
        isLast({ parentNode, index }) {
            return parentNode.condition.length === index + 1;
        },
    },
    data: () => ({
        drawer: false,
    }),
    methods: {
        onShowDrawer() {
            if (this.isLast) return;
            this.drawer = true;
        },
        onDel() {
            this.$emit("del");
        },
    },
};
</script>

<style lang="scss" scoped>
.condition-col-node {
    position: relative;
    padding: 0 20px;
    background: #f8f8f8;

    &::before {
        content: "";
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        display: block;
        width: 2px;
        height: 100%;
        background: #dbdbdb;
    }

    &:first-of-type > {
        .top-line,
        .bottom-line {
            left: auto;
            right: 0;
            width: 50%;
        }
    }

    &:last-of-type > {
        .top-line,
        .bottom-line {
            width: 50%;
        }
    }

    &__wrapper {
        position: relative;
    }
}

.top-line,
.bottom-line {
    position: absolute;
    left: 0;
    width: 100%;
    height: 2px;
    background: #dbdbdb;
}

.bottom-line {
    bottom: 0;
}

.condition {
    position: relative;
    margin-top: 50px;
    width: 240px;
    cursor: pointer;

    &.is-last {
        cursor: default;
    }

    &::before {
        content: "";
        position: absolute;
        left: 50%;
        transform: translate(-50%, -100%);
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 8px 5px 0;
        border-color: #dbdbdb transparent transparent;
    }

    &:hover {
        .condition__btns {
            display: block;
        }
    }

    &__btns {
        display: none;
    }

    &__title {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        min-height: 36px;
        padding: 5px 14px;
        font-size: 14px;
        color: #a5a5a4;
        background: #f1f1f1;
    }

    &__tag {
        position: absolute;
        left: 0;
        top: 0;
        transform: translate(0, -100%);

        .el-tag + .el-tag {
            margin-left: 5px;
        }
    }

    &__content {
        min-height: 60px;
        padding: 5px 14px;
        background: #fff;
    }
}
</style>
