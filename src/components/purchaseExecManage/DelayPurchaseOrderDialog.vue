<template>
    <el-dialog :visible="visible" :before-close="closeDialog">
        <el-form
            ref="formRef"
            :model="params"
            :rules="rules"
            label-width="120px"
        >
            <el-form-item label="下次下单时间" prop="estimate_purchase">
                <el-date-picker
                    v-model="params.estimate_purchase"
                    type="datetime"
                    placeholder="请选择下次下单时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button type="primary" @click="onDelay">延期下单</el-button>
        </div>
    </el-dialog>
</template>

<script>
import purchaseExecManageApi from "@/services/purchaseExecManage";

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        id: {
            type: Number,
            default: 0,
        },
    },
    data: () => ({
        params: {
            id: 0,
            estimate_purchase: "",
        },
        rules: {
            estimate_purchase: [
                {
                    required: true,
                    message: "请选择下次下单时间",
                },
            ],
        },
    }),
    watch: {
        visible(newVal) {
            if (newVal) {
                this.params = Object.assign(this.$options.data().params, {
                    id: this.id,
                });
                this.$nextTick(() => {
                    this.$refs.formRef.clearValidate();
                });
            }
        },
    },
    methods: {
        closeDialog() {
            this.$emit("update:visible", false);
        },
        onDelay() {
            this.$refs.formRef.validate((valid) => {
                if (!valid) return;
                purchaseExecManageApi
                    .delayPurchaseOrder(this.params)
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.$message.success("操作成功");
                            this.$emit("load");
                        }
                    });
            });
        },
    },
};
</script>
