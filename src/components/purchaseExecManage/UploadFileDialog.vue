<template>
    <el-dialog :visible="visible" :before-close="closeDialog">
        <el-row type="flex" justify="center">
            <vos-oss
                v-if="visible"
                :dir="dir"
                :file-list="fileList"
                :limit="1"
                :fileSize="10"
                :multiple="true"
                list-type="text"
                filesType=""
                :showFileList="true"
                :is_download="true"
                drag
            >
                <div class="el-upload-dragger">
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text"><em>点击上传</em></div>
                </div>
            </vos-oss>
        </el-row>
        <el-row slot="footer" type="flex" justify="space-between">
            <el-button type="primary" @click="onDownload">下载模版</el-button>
            <div>
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="onConfirm">确定</el-button>
            </div>
        </el-row>
    </el-dialog>
</template>

<script>
import VosOss from "vos-oss";
import purchaseExecManageApi from "@/services/purchaseExecManage";

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        kanban_type: {
            type: Number,
            default: 0,
        },
    },
    components: {
        VosOss,
    },
    data: () => ({
        dir: "vinehoo/vos/orders/tempStorage",
        fileList: [],
    }),
    methods: {
        closeDialog() {
            this.$emit("update:visible", false);
        },
        onConfirm() {
            if (!this.fileList.length) {
                this.$message.error("请先上传文件");
                return;
            }
            purchaseExecManageApi
                .preparePurchaseImportExcel({ url: this.fileList.join(",") })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message.success("导入成功");
                        this.$emit("load");
                        this.closeDialog();
                    }
                });
        },
        onDownload() {
            window.open(
                "https://images.wineyun.com/template/备货导入模板.xlsx"
            );
        },
    },
};
</script>

<style lang="scss" scoped></style>
