<template>
    <el-dialog :visible="visible" width="80%" :before-close="closeDialog">
        <el-form inline label-width="100px">
            <el-row>
                <el-form-item label="上架：">{{
                    info.onsale_time
                }}</el-form-item>
                <el-form-item label="下架：">{{
                    info.sold_out_time
                }}</el-form-item>
                <el-form-item label="发货：">{{
                    info.predict_shipment_time
                }}</el-form-item>
                <el-form-item label="下次采购：">{{
                    info.estimate_purchase
                }}</el-form-item>
                <el-form-item label="超卖：">{{
                    info.oversell_shipment_time
                }}</el-form-item>
            </el-row>
            <el-row>
                <el-form-item label="文案：">{{
                    info.creator_name
                }}</el-form-item>
                <el-form-item label="采购：">{{
                    info.buyer_name
                }}</el-form-item>
                <el-form-item label="运营：">{{
                    info.operation_name
                }}</el-form-item>
                <el-form-item label="审核：">{{
                    info.operation_review_name
                }}</el-form-item>
            </el-row>
        </el-form>
        <el-button type="primary" size="small" @click="onAdd"
            >新增备注</el-button
        >
        <el-table :data="list" size="mini" border style="margin-top: 10px">
            <el-table-column prop="period" label="期数" align="center">
            </el-table-column>
            <el-table-column prop="remark" align="center" label="备注内容">
            </el-table-column>
            <el-table-column prop="operator_name" label="备注人" align="center">
            </el-table-column>

            <el-table-column
                prop="created_time"
                label="创建时间"
                align="center"
            >
            </el-table-column>
        </el-table>
        <el-row type="flex" justify="center" style="margin-top: 20px">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </el-row>
    </el-dialog>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        period: {
            type: Number,
            default: 0,
        },
    },
    data: () => ({
        query: {
            page: 1,
            limit: 10,
        },
        list: [],
        total: 0,
        info: {},
    }),
    watch: {
        visible(newVal) {
            if (newVal) {
                this.query = Object.assign(this.$options.data().query, {
                    period: this.period,
                });
                this.load();
            }
        },
    },
    methods: {
        closeDialog() {
            this.$emit("update:visible", false);
        },
        load() {
            this.$request.article.remarkList(this.query).then((res) => {
                if (res.data.error_code == 0) {
                    const { list = [], total = 0, info = {} } = res.data.data;
                    this.list = list;
                    this.total = total;
                    this.info = info;
                }
            });
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
        onAdd() {
            this.$prompt("请输入备注信息", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                inputValidator: (val) => !!val,
                inputErrorMessage: "请输入备注信息",
            })
                .then(async ({ value }) => {
                    const { id: period, periods_type } = this.info;
                    const data = {
                        period,
                        periods_type,
                        remark: value,
                    };
                    const res = await this.$request.article.createRemark(data);
                    if (res.data.error_code == 0) {
                        this.$message.success("添加成功");
                        this.load();
                    }
                })
                .catch(() => {});
        },
    },
};
</script>

<style lang="scss" scoped>
.el-form-item {
    margin-bottom: 0;
}
</style>
